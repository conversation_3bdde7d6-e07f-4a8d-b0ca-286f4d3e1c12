package com.eleven.cms.vo;

import com.alibaba.druid.util.DaemonThreadFactory;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.eleven.cms.vo.VrbtCombinResult.VRBT_FUN_STATUS_NONE;

/**
 * Author: <EMAIL>
 * Date: 2019/12/16 11:24
 * Desc:远程调用接口返回json结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RemoteResult {
    public static final  ObjectMapper MAPPER =  new ObjectMapper();
    public static final String  CODE_OK = "000000";
    public static final String  IS_MEMBER = "0";
    public static final String  IS_CRBT_MEMBER = "1";
    public static final String  IS_CRBT_FUN = "1";
    //status String 必填 状态 00创建中/未订购 01正常/已订购 03订购中 04已退订 05已失效/已过期 06订购失败 07已暂停
    public static final String  IS_SXH_MEMBER = "01";
    public static final String  CODE_FAIL = "999999";

    private static final RemoteResult SUCC = new RemoteResult(CODE_OK,"成功");

    public interface BasicView {};
    public interface LoginView extends BasicView {};
    public interface StatusQueryView extends BasicView {};
    public interface ToneFreeMonthOrderView extends BasicView {};

    //resCode	true	String	结果代码
    //resMsg	false	String	结果描述
    //000000	成功
    //100001	请求发生错误
    @JsonProperty("resCode")
    @JsonView(BasicView.class)
    private String resCode;

    @JsonView(BasicView.class)
    @JsonProperty("resMsg")
    private String resMsg;

    @JsonView(LoginView.class)
    @JsonProperty("loginUrl")
    private String loginUrl;

    @JsonView(LoginView.class)
    @JsonProperty("token")
    private String token;

    @JsonView(LoginView.class)
    @JsonProperty("autoLoginState")
    private String autoLoginState;

    @JsonView(LoginView.class)
    @JsonProperty("msisdn")
    private String msisdn;

    @JsonView(StatusQueryView.class)
    @JsonProperty("status")
    private String status;

    @JsonView(StatusQueryView.class)
    @JsonProperty("vrbtStatus")
    private String vrbtStatus;

    @JsonView(StatusQueryView.class)
    @JsonProperty("validTime")
    private String validTime;

    @JsonView(ToneFreeMonthOrderView.class)
    @JsonProperty("settingID")
    private String settingID;

    public boolean isOK(){
        return CODE_OK.equals(this.getResCode());
    }

    public RemoteResult(String resCode, String resMsg) {
        this.resCode = resCode;
        this.resMsg = resMsg;
    }

    public static RemoteResult success(){
        return SUCC;
    }

    public static RemoteResult fail(String message){
        return new RemoteResult(CODE_FAIL,message);
    }

    public static String failJson(String message){
        return MAPPER.createObjectNode().put("resCode",CODE_FAIL).put("resMsg",message).toString();
    }
    public static RemoteResult fail(String resCode,String message){
        return new RemoteResult(resCode,message);
    }

    @JsonIgnore
    public boolean isVrbtFun(){
        return CODE_OK.equals(this.getResCode()) && (vrbtStatus!=null && !VRBT_FUN_STATUS_NONE.equals(vrbtStatus) || status!=null && !VRBT_FUN_STATUS_NONE.equals(status));
    }

    @JsonIgnore
    public boolean isCrbtMember(){
        return CODE_OK.equals(this.getResCode()) && IS_CRBT_MEMBER.equals(this.getStatus());
    }

    @JsonIgnore
    public boolean isCrbtFun(){
        return CODE_OK.equals(this.getResCode()) && IS_CRBT_FUN.equals(this.getStatus());
    }

    @JsonIgnore
    public boolean isBjhyMember(){
        return CODE_OK.equals(this.getResCode()) && IS_MEMBER.equals(this.getStatus());
    }

    @JsonIgnore
    public boolean isSchMember(){
        return CODE_OK.equals(this.getResCode()) && IS_MEMBER.equals(this.getStatus());
    }

    @JsonIgnore
    public boolean isSubMember() {
        return CODE_OK.equals(this.getResCode()) && IS_CRBT_MEMBER.equals(this.getStatus());
    }
    @JsonIgnore
    public boolean isVrbtMember(){
        return CODE_OK.equals(this.getResCode()) && !IS_MEMBER.equals(this.getStatus());
    }

    public boolean isCpmbMember(){
        return CODE_OK.equals(this.getResCode());
    }

    public boolean isAsMember(){
        return CODE_OK.equals(this.getResCode());
    }

    /**
     * 直接使用白金会员订购关系查询,不再使用此方法
     * @return
     */
    //public boolean isSxhMember() {
    //    return CODE_OK.equals(this.getResCode()) && IS_SXH_MEMBER.equals(this.getStatus());
    //}

    public String expr(){
        return this.resCode+":"+this.getResMsg();
    }


    public static void main(String[] args) throws JsonProcessingException {
         String test = "{\"ok\":true,\"resCode\":\"000000\",\"resMsg\":\"成功\",\"status\":\"1\"}";
        RemoteResult remoteResult = MAPPER.readValue(test, RemoteResult.class);
        System.out.println(MAPPER.writeValueAsString(remoteResult));
    }
}
