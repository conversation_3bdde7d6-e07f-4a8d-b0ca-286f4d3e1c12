package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 咪咕互娱包月查询
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5 10:07
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MiGuHuYuMonthlyResult implements Serializable {
    public static final String CODE_OK ="000000";
    public static final Integer MONTHLY_CONTINUOUS_OK =1;
    /**
     * returnCode : 000000
     * message : 请求成功
     * resultData : [{"appChannel":"test","packageId":"006097657004","packageName":"yh-新增渠道订购列表-01","subTime":"2024-06-03 13:37:53","expireTime":"2024-07-04 13:37:53","monthlyContinuous":0},{"appChannel":"test1","packageId":"006095967001","packageName":"1个月会员（点播）11","subTime":"2024-06-03 10:59:15","expireTime":"2024-09-28 04:15:59","monthlyContinuous":0}]
     * serverTime : 1717415400830
     */

    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("message")
    private String message;
    @JsonProperty("serverTime")
    private long serverTime;
    @JsonProperty("resultData")
    private List<ResultData> resultData;
    public boolean isOK() {
        return CODE_OK.equals(this.getReturnCode()) && !this.getResultData().isEmpty();
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ResultData implements Serializable {
        /**
         * appChannel : test
         * packageId : 006097657004
         * packageName : yh-新增渠道订购列表-01
         * subTime : 2024-06-03 13:37:53
         * expireTime : 2024-07-04 13:37:53
         * monthlyContinuous : 0
         */

        @JsonProperty("appChannel")
        private String appChannel;
        @JsonProperty("packageId")
        private String packageId;
        @JsonProperty("packageName")
        private String packageName;
        @JsonProperty("subTime")
        private String subTime;
        @JsonProperty("expireTime")
        private String expireTime;
        @JsonProperty("monthlyContinuous")
        private Integer monthlyContinuous;
        public boolean isOK() {
            return MONTHLY_CONTINUOUS_OK.equals(this.getMonthlyContinuous());
        }
    }
}
