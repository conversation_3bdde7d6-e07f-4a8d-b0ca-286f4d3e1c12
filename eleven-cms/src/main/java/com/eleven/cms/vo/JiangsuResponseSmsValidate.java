package com.eleven.cms.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-03-08 11:38
 */
@Data
@JacksonXmlRootElement(localName="Request")
@NoArgsConstructor
@AllArgsConstructor
public class JiangsuResponseSmsValidate {
    @JacksonXmlProperty(localName = "Datetime")
    private String datetime;

    @JacksonXmlProperty(localName = "Content")
    private Content content;

    private String SUB_OK = "0";

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        @JacksonXmlProperty(localName = "retCode")
        private String retCode;
        @JacksonXmlProperty(localName = "retMsg")
        private String retMsg;
    }

    public boolean isOk() {
        return content != null && SUB_OK.equals(content.retCode);
    }

    public static final JiangsuResponseSmsValidate FAIL_RESULT = new JiangsuResponseSmsValidate();

}
