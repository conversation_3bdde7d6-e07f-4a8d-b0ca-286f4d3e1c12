package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

/**
 * Author: <EMAIL>
 * Date: 2020/12/31 10:54
 * Desc: 联通回调数据封装
 * 只开包月的通知:
 *    {"noticeType":"order","notice":{"returnCode":"0000","notify":{"billingId":"6820123001845955","callNumber":"18613222501","channelId":"3000008270","createTime":"20201230105835","devId":"401520","effectiveTime":"20201230105838","capId":"6820123001845955","id":"1201230102501903450","orderDirection":0,"orderType":1,"productId":"4900720600","provinceCode":"081","remoteSys":2,"returnCode":"000000","status":1,"updateTime":"20201230105838"}}}
 *    {"noticeType":"order","notice":{"returnCode":"0000","notify":{"billingId":"6821010401087848","callNumber":"18613222501","channelId":"3000008270","createTime":"20210104143925","devId":"401520","effectiveTime":"20210104143927","capId":"6821010401087848","id":"1210104142501304591","orderDirection":0,"orderType":1,"productId":"4900720600","provinceCode":"081","remoteSys":2,"returnCode":"000000","status":1,"updateTime":"20210104143927"}}}
 * 开包月同时设置铃音:
 *    {"noticeType":"order","notice":{"returnCode":"0000","description":"订购并设置成功","orderId":"1201231102501000551"}}
 *    {"noticeType":"order","notice":{"returnCode":"0000","description":"success","orderId":"1210104162501312943"}}
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiantongNotice {

    public static final String NOTICE_TYPE_ORDER = "order";

    /**
     * noticeType : order
     * notice : {"returnCode":"0000","notify":{"billingId":"6820123001845955","callNumber":"18613222501","channelId":"3000008270","createTime":"20201230105835","devId":"401520","effectiveTime":"20201230105838","capId":"6820123001845955","id":"1201230102501903450","orderDirection":0,"orderType":1,"productId":"4900720600","provinceCode":"081","remoteSys":2,"returnCode":"000000","status":1,"updateTime":"20201230105838"}}
     */

    @JsonProperty("noticeType")
    private String noticeType;
    @JsonProperty("notice")
    private Notice notice;

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public Notice getNotice() {
        return notice;
    }

    public void setNotice(Notice notice) {
        this.notice = notice;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Notice {
        /**
         * returnCode : 0000
         * notify : {"billingId":"6820123001845955","callNumber":"18613222501","channelId":"3000008270","createTime":"20201230105835","devId":"401520","effectiveTime":"20201230105838","capId":"6820123001845955","id":"1201230102501903450","orderDirection":0,"orderType":1,"productId":"4900720600","provinceCode":"081","remoteSys":2,"returnCode":"000000","status":1,"updateTime":"20201230105838"}
         */

        @JsonProperty("returnCode")
        private String returnCode;

        // 如果是订购并设置铃音,会放置如下两个参数,同时不会有notify对象
        // description : 订购并设置成功
        // orderId: 1201231102501000551
        @JsonProperty("description")
        private String description;
        @JsonProperty("orderId")
        private String orderId;

        @JsonProperty("notify")
        private Notify notify;

        public String getReturnCode() {
            return returnCode;
        }

        public void setReturnCode(String returnCode) {
            this.returnCode = returnCode;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public Notify getNotify() {
            return notify;
        }

        public void setNotify(Notify notify) {
            this.notify = notify;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Notify {
            /**
             * billingId : 6820123001845955
             * callNumber : 18613222501
             * channelId : 3000008270
             * createTime : 20201230105835
             * devId : 401520
             * effectiveTime : 20201230105838
             * capId : 6820123001845955
             * id : 1201230102501903450
             * orderDirection : 0
             * orderType : 1
             * productId : 4900720600
             * provinceCode : 081
             * remoteSys : 2
             * returnCode : 000000
             * status : 1
             * updateTime : 20201230105838
             */

            @JsonProperty("billingId")
            private String billingId;
            @JsonProperty("callNumber")
            private String callNumber;
            @JsonProperty("channelId")
            private String channelId;
            @JsonProperty("createTime")
            private String createTime;
            @JsonProperty("devId")
            private String devId;
            @JsonProperty("effectiveTime")
            private String effectiveTime;
            @JsonProperty("capId")
            private String capId;
            @JsonProperty("id")
            private String id;
            @JsonProperty("orderDirection")
            private int orderDirection;
            @JsonProperty("orderType")
            private int orderType;
            @JsonProperty("productId")
            private String productId;
            @JsonProperty("provinceCode")
            private String provinceCode;
            @JsonProperty("remoteSys")
            private int remoteSys;
            @JsonProperty("returnCode")
            private String returnCode;
            @JsonProperty("status")
            private int status;
            @JsonProperty("updateTime")
            private String updateTime;

            public String getBillingId() {
                return billingId;
            }

            public void setBillingId(String billingId) {
                this.billingId = billingId;
            }

            public String getCallNumber() {
                return callNumber;
            }

            public void setCallNumber(String callNumber) {
                this.callNumber = callNumber;
            }

            public String getChannelId() {
                return channelId;
            }

            public void setChannelId(String channelId) {
                this.channelId = channelId;
            }

            public String getCreateTime() {
                return createTime;
            }

            public void setCreateTime(String createTime) {
                this.createTime = createTime;
            }

            public String getDevId() {
                return devId;
            }

            public void setDevId(String devId) {
                this.devId = devId;
            }

            public String getEffectiveTime() {
                return effectiveTime;
            }

            public void setEffectiveTime(String effectiveTime) {
                this.effectiveTime = effectiveTime;
            }

            public String getCapId() {
                return capId;
            }

            public void setCapId(String capId) {
                this.capId = capId;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public int getOrderDirection() {
                return orderDirection;
            }

            public void setOrderDirection(int orderDirection) {
                this.orderDirection = orderDirection;
            }

            public int getOrderType() {
                return orderType;
            }

            public void setOrderType(int orderType) {
                this.orderType = orderType;
            }

            public String getProductId() {
                return productId;
            }

            public void setProductId(String productId) {
                this.productId = productId;
            }

            public String getProvinceCode() {
                return provinceCode;
            }

            public void setProvinceCode(String provinceCode) {
                this.provinceCode = provinceCode;
            }

            public int getRemoteSys() {
                return remoteSys;
            }

            public void setRemoteSys(int remoteSys) {
                this.remoteSys = remoteSys;
            }

            public String getReturnCode() {
                return returnCode;
            }

            public void setReturnCode(String returnCode) {
                this.returnCode = returnCode;
            }

            public int getStatus() {
                return status;
            }

            public void setStatus(int status) {
                this.status = status;
            }

            public String getUpdateTime() {
                return updateTime;
            }

            public void setUpdateTime(String updateTime) {
                this.updateTime = updateTime;
            }

        }
    }

    public static void main(String[] args) throws JsonProcessingException {
        String jsonData = "{\"noticeType\":\"order\",\"notice\":{\"returnCode\":\"0000\",\"notify\":{\"billingId\":\"6820123001845955\",\"callNumber\":\"18613222501\",\"channelId\":\"3000008270\",\"createTime\":\"20201230105835\",\"devId\":\"401520\",\"effectiveTime\":\"20201230105838\",\"capId\":\"6820123001845955\",\"id\":\"1201230102501903450\",\"orderDirection\":0,\"orderType\":1,\"productId\":\"4900720600\",\"provinceCode\":\"081\",\"remoteSys\":2,\"returnCode\":\"000000\",\"status\":1,\"updateTime\":\"20201230105838\"}}}";
        //String jsonData = "{\"noticeType\":\"order\",\"notice\":{\"returnCode\":\"0000\",\"description\":\"订购并设置成功\",\"orderId\":\"1201231102501000551\"}}";
        final LiantongNotice liantongNotice = new ObjectMapper().readValue(jsonData, LiantongNotice.class);
        System.out.println("liantongNotice = " + liantongNotice);
    }
}
