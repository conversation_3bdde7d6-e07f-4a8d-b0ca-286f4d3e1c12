package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecg.common.constant.CommonConstant;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/2 12:02
 **/
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DuoDianRechargeResult implements Serializable {
    public static final String CODE_OK = "0000";
    /**
     * code : 0000
     * result : 兑换成功
     * time : 1560909762142
     * data : {"businessNo":"xxx","timeStamp":"xxx"}
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("result")
    private String result;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("time")
    private long time;
    @JsonProperty("data")
    private Data data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * businessNo : xxx
         * timeStamp : xxx
         */

        @JsonProperty("businessNo")
        private String businessNo;
        @JsonProperty("timeStamp")
        private String timeStamp;
    }
    public boolean isOK() {
        return CODE_OK.equals(this.getCode()) && this.getData()!=null;
    }


    public static final DuoDianRechargeResult FAIL_RESULT = new DuoDianRechargeResult("9999",null, "通讯失败", 0,null);

}
