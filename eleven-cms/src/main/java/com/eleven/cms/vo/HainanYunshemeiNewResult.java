package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2024-05-30 11:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HainanYunshemeiNewResult {

    public static final Integer CODE_OK = 200;
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String message;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static HainanYunshemeiNewResult fail() {
        return HainanYunshemeiNewResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }
}
