package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 天翼通讯助理响应结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 16:18
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TianyiCommAssistResult implements Serializable {

    public static final String  CODE_OK = "200";
    /**
     * code : 200
     * msg : 订单创建成功
     * data : {"orderUrl":"https://txzl-test.118114.net/aiTxzlWechat/xlx/#/home?             accountName=e8fnRzxCFhW6kNaMf%2Fn8wg%3D%3D&txzl=x%2FHz52LT%2FuBpCgnhwlC%2FlQ%3D%3D&p             roductCode=J%2FNHsV9C%2F18sEjtbNeP8KA%3D%3D","id":"CDYL1694153926925"}
     * timestamp : *************
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("timestamp")
    private Long timestamp;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * orderUrl : https://txzl-test.118114.net/aiTxzlWechat/xlx/#/home?             accountName=e8fnRzxCFhW6kNaMf%2Fn8wg%3D%3D&txzl=x%2FHz52LT%2FuBpCgnhwlC%2FlQ%3D%3D&p             roductCode=J%2FNHsV9C%2F18sEjtbNeP8KA%3D%3D
         * id : CDYL1694153926925
         */

        @JsonProperty("orderUrl")
        private String orderUrl;
        @JsonProperty("id")
        private String id;
    }
}
