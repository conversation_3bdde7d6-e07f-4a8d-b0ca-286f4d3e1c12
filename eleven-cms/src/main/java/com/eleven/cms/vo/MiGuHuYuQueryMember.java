package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/14 17:59
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MiGuHuYuQueryMember implements Serializable {

    public static final String RESP_CODE_OK = "000000";
    public static final Integer AUTO_STATUS=1;
    /**
     * returnCode : 000000
     * message : 请求成功
     * resultData : {"unbindTime":null,"autoStatus":0,"subStatus":1}
     * serverTime : 1718371136367
     */

    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("message")
    private String message;
    @JsonProperty("resultData")
    private ResultData resultData;
    @JsonProperty("serverTime")
    private long serverTime;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getReturnCode()) && this.getResultData()!=null;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ResultData implements Serializable {
        /**
         * unbindTime : null
         * autoStatus : 0
         * subStatus : 1
         */

        @JsonProperty("unbindTime")
        private Object unbindTime;
        @JsonProperty("autoStatus")
        private int autoStatus;
        @JsonProperty("subStatus")
        private int subStatus;

        public boolean isOK() {
            return AUTO_STATUS.equals(this.getAutoStatus());
        }
    }
}
