package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: cai lei
 * @create: 2022-06-07 14:49
 */

@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JunboLlbResult  implements Serializable {
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private JunboLlbData data;

    private static final String CALL_SUC_CODE = "0000";
    private static final String OPERATION_SUC_CODE = "0";

    public static final JunboLlbResult FAIL_RESULT = new JunboLlbResult("9999", "通讯失败", null);
    public boolean isOperationOk() {
        return CALL_SUC_CODE.equals(this.getCode()) && this.getData() != null && OPERATION_SUC_CODE.equals(this.getData().getResponseCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public class JunboLlbData implements Serializable{
        @JsonProperty("orderCode")
        private String orderCode;
        @JsonProperty("responseCode")
        private String responseCode;
        @JsonProperty("msg")
        private String msg;
        @JsonProperty("subMediaCode")
        private String subMediaCode;
        @JsonProperty("url")
        private String url;
        @JsonProperty("routeCode")
        private String routeCode;
        @JsonProperty("sellerId")
        private String sellerId;
    }

}
