package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-08-12 15:39
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XinjiangYidongYouranResult {
    private String respCode;
    private String respDesc;
    private Result result;

    private static final String SUC_CODE = "0";
    public static final XinjiangYidongYouranResult FAIL_RESULT = new XinjiangYidongYouranResult("9999", "通讯失败", null);

    @Data
    @NoArgsConstructor
    public static class Result {
        @JsonProperty("X_RESULTCODE")
        private String xResultCode;
        @JsonProperty("X_RESULTINFO")
        private String xResultInfo;
    }

    public boolean isOk() {
        return SUC_CODE.equals(respCode) && result != null && SUC_CODE.equals(result.getXResultCode());
    }

}
