package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Author: <EMAIL>
 * Date: 2020/4/1 17:00
 * Desc:Todo
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatangSmsResult {

    /**
     * code : 0
     * msg : 成功
     * data : {"taskeid":"T1011220639000170512059"}
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private Data data;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {
        /**
         * taskeid : T1011220639000170512059
         */

        @JsonProperty("taskeid")
        private String taskeId;

        public String getTaskeId() {
            return taskeId;
        }

        public void setTaskeId(String taskeId) {
            this.taskeId = taskeId;
        }
    }
}
