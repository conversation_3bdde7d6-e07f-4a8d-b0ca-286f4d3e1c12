package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2023-5-25 16:20:37
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WoReadUnsubscribeResult {

    public static final String CODE_OK = "0000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    /*0000代表正常，其它代表异常*/
    @JsonProperty("code")
    private String code;
    @JsonProperty("innercode")
    private String innercode;
    @JsonProperty("message")
    private String message;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static WoReadUnsubscribeResult fail() {
        return WoReadUnsubscribeResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }
}
