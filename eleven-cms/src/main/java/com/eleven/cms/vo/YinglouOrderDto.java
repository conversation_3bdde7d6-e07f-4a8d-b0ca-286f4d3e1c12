package com.eleven.cms.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/26 17:04
 **/
@Data
public class YinglouOrderDto {
    /*日期*/
    @TableField(exist = false)
    private String payOrderTime;
    /*总数*/
    @TableField(exist = false)
    private String totalPayOrder;
    /*月份*/
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(exist = false)
    private String payTime;
    /*年月日开始*/
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(exist = false)
    private String payTimeBegin;
    /*年月日结束*/
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(exist = false)
    private String payTimeEnd;
    /*用户名称*/
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(exist = false)
    private String payUserName;

}
