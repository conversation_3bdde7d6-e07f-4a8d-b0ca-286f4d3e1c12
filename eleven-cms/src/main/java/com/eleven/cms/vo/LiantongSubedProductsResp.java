package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2020/12/30 14:09
 * Desc:联通沃音乐开放平台响应封装(已订包月产品)
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiantongSubedProductsResp extends LiantongResp {

    public static LiantongSubedProductsResp ERROR_RESP = new LiantongSubedProductsResp(RETURN_CODE_ERROR,DESCRIPTION_ERROR,null,null,null);
    /**
     * returnCode : 000000
     * description :
     * subedProducts : [{"productId":"4900711100","productType":"0","cantUnSubscribeReason":"渠道未授权操作该产品","status":"1","subTime":"20201026151436","unSubscribeable":false},{"productId":"4900718300","productType":"0","cantUnSubscribeReason":"渠道未授权操作该产品","status":"4","subTime":"20201229105129","unSubTime":"20201229113600","unSubscribeable":false},{"productId":"4900720600","productName":"酷炫彩铃6元包月","productType":"0","status":"4","subTime":"20201229104014","unSubTime":"20201229113524","unSubscribeable":true}]
     */

    @JsonProperty("subedProducts")
    private List<SubedProducts> subedProducts;

    public LiantongSubedProductsResp(String returnCode, String description, String orderId, String url,
                                     List<SubedProducts> subedProducts) {
        super(returnCode, description, orderId, url);
        this.subedProducts = subedProducts;
    }

    public List<SubedProducts> getSubedProducts() {
        return subedProducts;
    }

    public void setSubedProducts(List<SubedProducts> subedProducts) {
        this.subedProducts = subedProducts;
    }

    public static LiantongSubedProductsResp error() {
        return ERROR_RESP;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SubedProducts {
        /**
         * productId : 4900711100
         * productType : 0
         * cantUnSubscribeReason : 渠道未授权操作该产品
         * status : 1
         * subTime : 20201026151436
         * unSubscribeable : false
         * unSubTime : 20201229113600
         * productName : 酷炫彩铃6元包月
         */

        @JsonProperty("productId")
        private String productId;
        @JsonProperty("productType")
        private String productType;
        @JsonProperty("cantUnSubscribeReason")
        private String cantUnSubscribeReason;
        @JsonProperty("status")
        private String status;
        @JsonProperty("subTime")
        private String subTime;
        @JsonProperty("unSubscribeable")
        private boolean unSubscribeable;
        @JsonProperty("unSubTime")
        private String unSubTime;
        @JsonProperty("productName")
        private String productName;

        public String getProductId() {
            return productId;
        }

        public void setProductId(String productId) {
            this.productId = productId;
        }

        public String getProductType() {
            return productType;
        }

        public void setProductType(String productType) {
            this.productType = productType;
        }

        public String getCantUnSubscribeReason() {
            return cantUnSubscribeReason;
        }

        public void setCantUnSubscribeReason(String cantUnSubscribeReason) {
            this.cantUnSubscribeReason = cantUnSubscribeReason;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getSubTime() {
            return subTime;
        }

        public void setSubTime(String subTime) {
            this.subTime = subTime;
        }

        public boolean isUnSubscribeable() {
            return unSubscribeable;
        }

        public void setUnSubscribeable(boolean unSubscribeable) {
            this.unSubscribeable = unSubscribeable;
        }

        public String getUnSubTime() {
            return unSubTime;
        }

        public void setUnSubTime(String unSubTime) {
            this.unSubTime = unSubTime;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }
    }
}
