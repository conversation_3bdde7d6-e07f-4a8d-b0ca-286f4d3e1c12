package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 破解计费返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DianxinVrbtResult {

    public static final DianxinVrbtResult FAIL_RESULT = new DianxinVrbtResult("9999", "通讯失败", null);

    /**
     * code : 0
     * message : success
     * transId : 202003261022439000
     */

    @JsonProperty("rescode")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("tradeid")
    private String transId;

    public static DianxinVrbtResult fail() {
        return FAIL_RESULT;
    }

    public boolean isOK() {
        return "0".equals(code);
    }
}
