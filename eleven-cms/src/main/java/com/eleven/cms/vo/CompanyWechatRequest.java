package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/9 14:39
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CompanyWechatRequest implements Serializable {
    public static final Integer CODE_OK =0;
    /**
     * errcode : 0
     * errmsg : ok
     * type : file
     * media_id : 1G6nrLmr5EC3MMb_-zK1dDdzmd0p7cNliYu9V5w7o8K0
     * created_at : 1380000000
     */

    @JsonProperty("errcode")
    private Integer errcode;
    @JsonProperty("errmsg")
    private String errmsg;
    @JsonProperty("type")
    private String type;
    @JsonProperty("media_id")
    private String mediaId;
    @JsonProperty("created_at")
    private String createdAt;
    public boolean isOK() {
        return CODE_OK.equals(errcode);
    }
}
