package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GuangdongYidongYueyueResult {

    public static final Integer CODE_OK = 11;
    public static final Integer SUB_OK = 1;
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";

    private Integer status;
    private String msg;
    private String ordernum;
    private String sysordernum;
    private JsonNode dataobj;

    public boolean isSubOK() {
        return SUB_OK.equals(status);
    }
    public boolean isCodeOk() {
        return CODE_OK.equals(status);
    }

    public static GuangdongYidongYueyueResult fail() {
        return GuangdongYidongYueyueResult.builder().status(CODE_FAIL).msg(MSG_FAIL).build();
    }

}
