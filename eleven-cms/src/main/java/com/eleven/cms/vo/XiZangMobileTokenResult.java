package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jodd.util.StringUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/28 10:43
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XiZangMobileTokenResult implements Serializable {

    /**
     * access_token : 8a48adeb-7679-4c44-a496-ca0a8011af7e
     * token_type : bearer
     * expires_in : 14518
     */

    @JsonProperty("access_token")
    private String accessToken;
    @JsonProperty("token_type")
    private String tokenType;
    @JsonProperty("expires_in")
    private Integer expiresIn;

    public boolean isOK() {
        return  StringUtil.isNotBlank(this.getAccessToken());
    }
}
