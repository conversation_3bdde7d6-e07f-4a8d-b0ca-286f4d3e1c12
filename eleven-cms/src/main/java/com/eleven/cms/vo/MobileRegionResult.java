package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Author: <EMAIL>
 * Date: 2020/4/20 15:41
 * Desc:号码归属地查询结果
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MobileRegionResult {

    //运营商品牌code
    public static final String ISP_YIDONG = "1";   // 移动
    public static final String ISP_DINGYUE = "2";   // 移动-订阅库(仅用于设置默认彩铃选择)
    public static final String ISP_LIANTONG = "3"; // 联通
    public static final String ISP_DIANXIN = "4";  //电信

    /**
     * {"code":"0000","msg":"成功","phone_code":"1326876","province":"广东","province_id":"200","city":"东莞","city_code":"0769","area_code":"441900","operator":"3","opt_desc":"联通"}
     *
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("phone_code")
    private String phoneCode;
    @JsonProperty("province")
    private String province;
    @JsonProperty("province_id")
    private String provinceId;
    @JsonProperty("city")
    private String city;
    @JsonProperty("city_code")
    private String cityCode;
    @JsonProperty("area_code")
    private String areaCode;
    /**
     * 运营商,1移动用户,3联通用户,4电信用户
     */
    @JsonProperty("operator")
    private String operator;
    @JsonProperty("opt_desc")
    private String optDesc;

    public boolean isSuccess(){
        return "0000".equals(this.code);
    }

    public boolean isIspYidong(){
        return ISP_YIDONG.equals(operator);
    }

    public boolean isIspDianxin(){
        return ISP_DIANXIN.equals(operator);
    }

    public boolean isIspLiantong(){
        return ISP_LIANTONG.equals(operator);
    }

    public boolean isIspValid(){
        return ISP_YIDONG.equals(operator) || ISP_LIANTONG.equals(operator);
    }

}
