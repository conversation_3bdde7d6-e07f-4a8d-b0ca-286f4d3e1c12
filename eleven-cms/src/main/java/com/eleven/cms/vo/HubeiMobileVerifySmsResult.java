package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: lihb
 * @create: 2024-3-27 16:13:05
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HubeiMobileVerifySmsResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("retcode")
    private String retcode;
    @JsonProperty("retmsg")
    private String retmsg;
    @JsonProperty("seq")
    private String seq;
    @JsonProperty("sessionid")
    private String sessionId;
    @JsonProperty("usessionid")
    private String usessionId;
    @JsonProperty("udeadtime")
    private String udeadtime;

    public boolean isOK() {
        return CODE_OK.equals(retcode);
    }

    public static HubeiMobileVerifySmsResult fail() {
        return HubeiMobileVerifySmsResult.builder().retcode(CODE_FAIL).retmsg(MSG_FAIL).build();
    }
}
