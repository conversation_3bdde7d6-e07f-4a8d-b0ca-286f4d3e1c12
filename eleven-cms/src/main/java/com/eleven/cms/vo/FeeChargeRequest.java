package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 话费充值回调结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/18 10:18
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeChargeRequest implements Serializable {
    //商户订单号
    @JsonProperty("orderId")
    private String orderId;
    //产品编码
    @JsonProperty("productCode")
    private String productCode;
    //交易流水号
    @JsonProperty("tradeNo")
    private String tradeNo;
    //商户号
    @JsonProperty("salerId")
    private String salerId;
    //订单充值金额
    @JsonProperty("transAmount")
    private String transAmount;
    //充值号码
    @JsonProperty("mobile")
    private String mobile;
    //实付支付金额
    @JsonProperty("actualAmount")
    private String actualAmount;
    //折扣
    @JsonProperty("salerDiscount")
    private String salerDiscount;
    //支付状态  0:申请中，1：充值成功，2：充值失败
    @JsonProperty("payStatus")
    private String payStatus;
    //支付时间
    @JsonProperty("payTime")
    private String payTime;
    //部分通道有，当这个值为空时不参与加密
    @JsonProperty("pzOrderNo")
    private String pzOrderNo;
    @JsonProperty("sign")
    private String sign;
}
