package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CaixunResult {

    public static final Integer CODE_OK = 200;
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("retCode")
    private Integer code;
    @JsonProperty("retMsg")
    private String message;
    @JsonProperty("data")
    private Result data;

    @Data
    public static class Result {
        private String guid;
        private String token;
        private String orderId;
    }

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static CaixunResult fail() {
        return CaixunResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }

}
