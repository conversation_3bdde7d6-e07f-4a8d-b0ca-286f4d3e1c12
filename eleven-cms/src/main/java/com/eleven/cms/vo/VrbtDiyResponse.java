package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VrbtDiyResponse implements Serializable {

    public static final String  CODE_OK = "000000";
    public static final String  CODE_FAIL = "100001";
    public static final String  MSG_OK = "succ";
    public static final String  MSG_FAIL = "fail";

    private String returnCode;
    private String description;

    public static VrbtDiyResponse build(String returnCode,String description) {
        return VrbtDiyResponse.builder().returnCode(returnCode).description(description).build();
    }
    public static VrbtDiyResponse success() {
        return VrbtDiyResponse.builder().returnCode(CODE_OK).description(MSG_OK).build();
    }
    public static VrbtDiyResponse fail() {
        return VrbtDiyResponse.builder().returnCode(CODE_FAIL).description(MSG_FAIL).build();
    }

}
