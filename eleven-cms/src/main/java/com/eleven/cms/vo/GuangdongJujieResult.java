package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 破解计费返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
public class GuangdongJujieResult {


    public static final Integer CODE_FAIL = 9999;
    public static final Integer CODE_SMS_SUCCESS = 100; //获取验证码成功
    public static final Integer CODE_ORDER_SUCCESS = 101; //101为接收成功
    public static final Integer CODE_HANDLE_SUCCESS = 102; //102为办理成功


    public static final GuangdongJujieResult FAIL_RESULT = GuangdongJujieResult.builder().biz(new Biz(9999, "通讯失败", "")).build();

    /**
     * code : 0
     * message : success
     * transId : 202003261022439000
     */

    @JsonProperty("biz")
    private Biz biz;

    public boolean isSmsOK() {
        return CODE_SMS_SUCCESS.equals(getBiz().status);
    }

    public boolean isOrderOK() {
        return CODE_ORDER_SUCCESS.equals(getBiz().status);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Biz {
        @JsonProperty("status")
        private Integer status;
        @JsonProperty("msg")
        private String msg;
        @JsonProperty("smsSerialNum")
        private String smsSerialNum;
    }
}
