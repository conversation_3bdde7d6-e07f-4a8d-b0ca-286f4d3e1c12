package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 16:14
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XiMiLeQueryOrderResult implements Serializable {
    public static final String RESP_CODE_OK = "200";
    /**
     * accountId : ***********
     * createTime : 2020-01-01 16:22:04
     * orderId : c669692dc5714fe0b6e10368d7c7db30
     * payAmount : 99.8
     * retCode : 200
     * retMsg : 充值成功
     * sign :
     * sysOrderId : 20200101162203432432432
     */

    @JsonProperty("accountId")
    private String accountId;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("payAmount")
    private double payAmount;
    @JsonProperty("retCode")
    private String retCode;
    @JsonProperty("retMsg")
    private String retMsg;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("sysOrderId")
    private String sysOrderId;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getRetCode());
    }
}
