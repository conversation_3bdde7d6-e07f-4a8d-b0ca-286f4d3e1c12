package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GuangdongYidongVrbtDataResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("respcode")
    private String respcode;
    @JsonProperty("respdesc")
    private String respdesc;
    @JsonProperty("resptype")
    private String resptype;

    public boolean isOK() {
        return CODE_OK.equals(respcode);
    }

    public static GuangdongYidongVrbtDataResult fail() {
        return GuangdongYidongVrbtDataResult.builder().respcode(CODE_FAIL).respdesc(MSG_FAIL).build();
    }

}
