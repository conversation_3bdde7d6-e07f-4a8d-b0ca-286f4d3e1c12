package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class YunnanMobileGaojieResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    /*0代表正常，其它代表异常*/
    @JsonProperty("code")
    private String code;
    @JsonProperty("data")
    private Result data;
    @JsonProperty("msg")
    private String msg;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static YunnanMobileGaojieResult fail() {
        return YunnanMobileGaojieResult.builder().code(CODE_FAIL).msg(MSG_FAIL).build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result{
        @JsonProperty("phone")
        private String phone;
        @JsonProperty("offerId")
        private String offerId;
        @JsonProperty("flowId")
        private String flowId;
        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("smsId")
        private String smsId;
        @JsonProperty("channel")
        private String channel;
        @JsonProperty("subChannel")
        private String subChannel;
        @JsonProperty("pushChannel")
        private String pushChannel;
        @JsonProperty("fluent")
        private String fluent;
    }
}
