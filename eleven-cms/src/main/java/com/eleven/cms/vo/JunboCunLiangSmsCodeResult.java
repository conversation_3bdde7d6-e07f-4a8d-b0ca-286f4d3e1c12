package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/13 16:51
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JunboCunLiangSmsCodeResult implements Serializable {
    public static final String  CODE_OK ="0000";
    public static final String  RESPONSE_OK="0";
    /**
     * success : true
     * code : 0000
     * message : 执行成功
     * data : {"msg":"根据【手机号码：18520293808】没有找到用户对应的路由数据","responseCode":"1","orderCode":"","subMediaCode":"1","url":"","routeCode":"flow_yd_hunan","sellerId":"","operatorMsg":"局方校验信息;局方下单信息"}
     */

    @JsonProperty("success")
    private boolean success;
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private Data data;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * msg : 根据【手机号码：18520293808】没有找到用户对应的路由数据
         * responseCode : 1
         * orderCode :
         * subMediaCode : 1
         * url :
         * routeCode : flow_yd_hunan
         * sellerId :
         * operatorMsg : 局方校验信息;局方下单信息
         */

        @JsonProperty("msg")
        private String msg;
        @JsonProperty("responseCode")
        private String responseCode;
        @JsonProperty("orderCode")
        private String orderCode;
        @JsonProperty("subMediaCode")
        private String subMediaCode;
        @JsonProperty("url")
        private String url;
        @JsonProperty("routeCode")
        private String routeCode;
        @JsonProperty("sellerId")
        private String sellerId;
        @JsonProperty("operatorMsg")
        private String operatorMsg;
        public boolean isOK(){
            return RESPONSE_OK.equals(this.getResponseCode());
        }
    }
}
