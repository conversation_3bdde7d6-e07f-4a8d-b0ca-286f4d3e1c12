package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 讯飞视频彩铃三方支付0元订购结果
 *
 * @author: cai lei
 * @create: 2021-12-23 15:08
 */

@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XunfeiVrbtZeroOrderResult {
    
    private String retcode;
    private String desc;

    public static final XunfeiVrbtZeroOrderResult FAIL_RESULT = new XunfeiVrbtZeroOrderResult("9999", "通讯失败");

    public boolean isOK() {
        return "0000".equals(retcode);
    }

    public static XunfeiVrbtZeroOrderResult fail() {
        return FAIL_RESULT;
    }
}
