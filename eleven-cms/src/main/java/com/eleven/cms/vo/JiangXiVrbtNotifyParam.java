package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO 江西视频彩铃订单通知
 * @Date 2023/10/13 15:57
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangXiVrbtNotifyParam  implements Serializable {

    /**
     * sign : 7402D9067CD511895C5521A57E26813B
     * orderStatus : FAILED
     * message : 流量下单异常:errorCode:5037errorMsg:业务受理失败！当前的受理商品：(310095600772)互联网视频彩铃_抖音版G关联的前向限制校验失败，原因：[1]检查规则类型项为[合约订购,当前主套餐资费校验],取值为([28,*]),不能通过检查.限制提示,本周期主套餐资费月租须等于指定金额
     * customerOrderNo : 18c7d1309a220f43066572e36dd62ec7
     * ts : 1697190295138
     */

    @JsonProperty("sign")
    private String sign;
    @JsonProperty("orderStatus")
    private String orderStatus;
    @JsonProperty("message")
    private String message;
    @JsonProperty("customerOrderNo")
    private String customerOrderNo;
    @JsonProperty("ts")
    private long ts;
}
