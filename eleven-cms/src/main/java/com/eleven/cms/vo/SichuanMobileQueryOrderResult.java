package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/17 10:34
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SichuanMobileQueryOrderResult implements Serializable {

    public static final String CODE_OK = "0000";

    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";
    public static final String STATUS_SUCCESS = "1";
    /**
     * result : {"time":"2025-01-24 19:40:11","phone":"183****8272","retStatus":1,"status":"true","orderId":"1000042720250124193941000008"}
     * res_code : 0000
     * res_msg : 成功
     */

    @JsonProperty("result")
    private Result result;
    @JsonProperty("res_code")
    private String resCode;
    @JsonProperty("res_msg")
    private String resMsg;
    public boolean isOK() {
        return CODE_OK.equals(this.getResCode()) ;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Result implements Serializable {
        /**
         * time : 2025-01-24 19:40:11
         * phone : 183****8272
         * retStatus : 1
         * status : true
         * orderId : 1000042720250124193941000008
         */

        @JsonProperty("time")
        private String time;
        @JsonProperty("phone")
        private String phone;
        @JsonProperty("retStatus")
        private Integer retStatus;
        @JsonProperty("status")
        private String status;
        @JsonProperty("orderId")
        private String orderId;
        public boolean isOK() {
            return (this.getRetStatus()!=null && this.getRetStatus()==1) || (this.getRetStatus()==null && "true".equals(this.getStatus())) ;
        }
    }

    public static SichuanMobileQueryOrderResult fail() {
        return SichuanMobileQueryOrderResult.builder().resCode(CODE_FAIL).resMsg(MSG_FAIL).build();
    }
}
