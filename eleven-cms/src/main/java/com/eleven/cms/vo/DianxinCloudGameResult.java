package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 电信云游戏响应
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/7 17:17
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DianxinCloudGameResult implements Serializable {
    public static final String  CODE_OK = "000000";
    /**
     * errCode : 000000
     * resData : {"sequenceId":"202312201441554437506687"}
     */

    @JsonProperty("errCode")
    private String errCode;

    @JsonProperty("errMsg")
    private String errMsg;

    @JsonProperty("resData")
    private ResData resData;
    public boolean isOK(){
        return CODE_OK.equals(this.getErrCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ResData implements Serializable {
        /**
         * sequenceId : 202312201441554437506687
         */

        @JsonProperty("sequenceId")
        private String sequenceId;
    }
}
