package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/15 10:51
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoCityShop implements Serializable {

    /**
     * page : 1
     * total : 805
     * list : [{"id":495430,"name":"SE餐厅（店铺名）","latitude":"30.659599","longitude":"104.065820","cityCode":"510100","address":"四川省成都市青羊区人民中路一段16号四川科技馆SE餐厅","phoneNumber":"07070071"}]
     */

    @JsonProperty("page")
    private int page;
    @JsonProperty("total")
    private int total;
    @JsonProperty("list")
    private java.util.List<List> list;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class List implements Serializable {
        /**
         * id : 495430
         * name : SE餐厅（店铺名）
         * latitude : 30.659599
         * longitude : 104.065820
         * cityCode : 510100
         * address : 四川省成都市青羊区人民中路一段16号四川科技馆SE餐厅
         * phoneNumber : 07070071
         */

        @JsonProperty("id")
        private int id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("latitude")
        private String latitude;
        @JsonProperty("longitude")
        private String longitude;
        @JsonProperty("cityCode")
        private String cityCode;
        @JsonProperty("address")
        private String address;
        @JsonProperty("phoneNumber")
        private String phoneNumber;
    }
}
