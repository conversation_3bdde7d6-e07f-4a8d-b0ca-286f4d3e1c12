package com.eleven.cms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
@Data
@ApiModel(value="cms_member_rights对象", description="视听会员权益")
public class RightsPackList {
    /**权益Id*/
    @Excel(name = "权益Id", width = 15)
    @ApiModelProperty(value = "权益Id")
    private String rightsId;
    /**充值couponId*/
    @Excel(name = "充值couponId", width = 15)
    @ApiModelProperty(value = "充值couponId")
    private String couponId;
    /**权益名称*/
    @Excel(name = "权益名称", width = 15)
    @ApiModelProperty(value = "权益名称")
    private String rightsName;
    /**产品价格（单位：分）*/
    @Excel(name = "产品价格（单位：分）", width = 15)
    @ApiModelProperty(value = "产品价格（单位：分）")
    private Integer productPrice;
    /**是否输入账号:0=否,1=是*/
    @Excel(name = "是否输入账号:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否输入账号:0=否,1=是")
    private Integer isAccount;

    /**充值方式:0=直充,1=券码*/
    @Excel(name = "充值方式:0=直充,1=券码", width = 15)
    @ApiModelProperty(value = "充值方式:0=直充,1=券码")
    private Integer rechargeState;
    /**产品原价（单位：分）*/
    @Excel(name = "产品原价（单位：分）", width = 15)
    @ApiModelProperty(value = "产品原价（单位：分）")
    private Integer originalPrice;

    /**权益开关:0=关,1=开*/
    @Excel(name = "权益开关:0=关,1=开", width = 15)
    @ApiModelProperty(value = "权益开关:0=关,1=开")
    private Integer rightsSwitchs;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**归属公司*/
    @Excel(name = "归属公司", width = 15)
    @ApiModelProperty(value = "归属公司")
    private String companyOwner;
}
