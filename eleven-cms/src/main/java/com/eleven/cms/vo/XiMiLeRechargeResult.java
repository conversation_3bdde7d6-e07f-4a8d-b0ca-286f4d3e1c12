package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 15:48
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XiMiLeRechargeResult implements Serializable {
    public static final String RESP_CODE_OK = "200";
    /**
     * orderId : 2020010100002104204265
     * retCode : 200
     * retMsg : 受理成功
     */

    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("retCode")
    private String retCode;
    @JsonProperty("retMsg")
    private String retMsg;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getRetCode());
    }
}
