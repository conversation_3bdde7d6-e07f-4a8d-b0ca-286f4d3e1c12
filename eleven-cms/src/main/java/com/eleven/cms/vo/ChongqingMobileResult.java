package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChongqingMobileResult {

    public static final Integer CODE_OK = 0;
    public static final String CODE_SUCCESS = "0000";
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("errNo")
    private Integer resCode;
    @JsonProperty("message")
    private String resMsg;
    @JsonProperty("tz")
    private String tz; //时区
    @JsonProperty("data")
    private ResultData data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResultData {
        @JsonProperty("respCode")
        private String respCode;
        @JsonProperty("respMsg")
        private String respMsg;
        @JsonProperty("data")
        private String data;
    }

    public boolean isOK() {
        return CODE_OK.equals(resCode) && CODE_SUCCESS.equals(data.getRespCode());
    }

    public static ChongqingMobileResult fail() {
        return ChongqingMobileResult.builder().resCode(CODE_FAIL).resMsg(MSG_FAIL).build();
    }
}
