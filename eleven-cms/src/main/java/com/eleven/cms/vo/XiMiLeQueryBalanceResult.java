package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 16:00
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XiMiLeQueryBalanceResult implements Serializable {
    public static final String RESP_CODE_OK = "200";
    /**
     * balance : 998.5
     * retCode : 200
     * retMsg : 查询成功
     */

    @JsonProperty("balance")
    private String balance;
    @JsonProperty("retCode")
    private String retCode;
    @JsonProperty("retMsg")
    private String retMsg;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getRetCode());
    }
}
