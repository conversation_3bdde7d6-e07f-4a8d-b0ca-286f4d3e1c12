package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 17:23
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KaSaiStockRequest implements Serializable {

    /**
     * linkNum : fiH1UAjo
     * orderNo : ffa8a8729d7c4f4bbaf99dfe8b8c102e
     * statusDesc : 成功
     * createTime : 1701241285000
     * mobile : 18584090313
     * name : QS-山河清影20元
     * orderNum : cd1aa29fe240c18115fba210e35a84d6
     * message : 成功
     * status : 1
     */
    @JsonProperty("linkNum")
    private String linkNum;
    @JsonProperty("orderNo")
    private String orderNo;
    @JsonProperty("statusDesc")
    private String statusDesc;
    @JsonProperty("createTime")
    private Long createTime;
    @JsonProperty("mobile")
    private String mobile;
    @JsonProperty("name")
    private String name;
    @JsonProperty("orderNum")
    private String orderNum;
    @JsonProperty("message")
    private String message;
    @JsonProperty("status")
    private Integer status;
}
