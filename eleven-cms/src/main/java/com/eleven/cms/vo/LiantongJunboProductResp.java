package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2020/12/29 14:17
 * Desc: 联通沃音乐开放平台响应封装
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiantongJunboProductResp {

     public static final String RETURN_CODE_SUCC = "0";
     public static final String RETURN_CODE_ERROR = "1111";
     public static final String DESCRIPTION_ERROR = "接口通讯失败";
     public static LiantongJunboProductResp ERROR_RESP = new LiantongJunboProductResp(RETURN_CODE_ERROR,DESCRIPTION_ERROR,null);

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("result")
    private List<LiantongVrbtJunboProduct> result;

    public LiantongJunboProductResp code(String code){
        this.setCode(code);
        return this;
    }

    public LiantongJunboProductResp msg(String msg){
        this.setMsg(msg);
        return this;
    }

    public LiantongJunboProductResp result(List<LiantongVrbtJunboProduct> result){
        this.setResult(result);
        return this;
    }

    public boolean isOK(){
        return RETURN_CODE_SUCC.equals(code);
    }
}
