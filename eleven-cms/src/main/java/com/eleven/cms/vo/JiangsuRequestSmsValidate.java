package com.eleven.cms.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Builder;
import lombok.Data;

/**
 * @author: cai lei
 * @create: 2023-03-08 11:38
 */
@Data
@JacksonXmlRootElement(localName="Request")
public class JiangsuRequestSmsValidate {
    @JacksonXmlProperty(localName = "Datetime")
    private String datetime;

    @JacksonXmlProperty(localName = "Content")
    private Content content;

    @Data
    @Builder
    public static class Content {
        @JacksonXmlProperty(localName = "orderId")
        private String orderId;
        @JacksonXmlProperty(localName = "randomCode")
        private String randomCode;
    }
}
