package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 软游通业务提交验证码
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/23 9:45
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RuanYouTongSubmitCodeResult implements Serializable {
    public static final Integer  CODE_OK =1;
    /**
     * code : 1
     * errmsg : 成功
     */

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("errmsg")
    private String errmsg;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode());
    }
}
