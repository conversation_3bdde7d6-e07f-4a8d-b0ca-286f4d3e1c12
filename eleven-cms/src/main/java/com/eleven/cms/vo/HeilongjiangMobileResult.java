package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonFilter("myFilter")
public class HeilongjiangMobileResult {

    public static final Integer CODE_OK = 0;
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private Integer resCode;
    @JsonProperty("msg")
    private String resMsg;
    @JsonProperty("data")
    private HeilongjiangMobileResultData data;


    public static HeilongjiangMobileResult fail() {
        return HeilongjiangMobileResult.builder().resCode(CODE_FAIL).resMsg(MSG_FAIL).data(null).build();
    }

    @Data
    public static class HeilongjiangMobileResultData {
        @JsonProperty("ErrMsg")
        private String errMsg;
        @JsonProperty("ErrCode")
        private Integer errCode;
    }

    public boolean isGetOK() {
        return CODE_OK.equals(resCode) && data != null && CODE_OK.equals(data.getErrCode());
    }

    public boolean isSubOK() {
        return CODE_OK.equals(resCode);
    }
}
