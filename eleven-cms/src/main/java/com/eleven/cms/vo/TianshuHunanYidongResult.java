package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-11-08 10:38
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TianshuHunanYidongResult {
    private String code;
    private String msg;

    public static final TianshuHunanYidongResult FAIL_RESULT = new TianshuHunanYidongResult("999", "通讯失败");

    public boolean isOK() {
        return "0".equals(code);
    }

}
