package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2023-5-11 15:44:08
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WangyiyunMmUnsubscribeResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("status")
    private String status;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("res")
    private String res;

    public boolean isOK() {
        return CODE_OK.equals(status);
    }

    public static WangyiyunMmUnsubscribeResult fail() {
        return WangyiyunMmUnsubscribeResult.builder().status(CODE_FAIL).msg(MSG_FAIL).build();
    }
}
