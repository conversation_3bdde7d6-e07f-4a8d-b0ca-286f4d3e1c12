package com.eleven.cms.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-03-08 11:38
 */
@Data
@JacksonXmlRootElement(localName="Response")
@NoArgsConstructor
@AllArgsConstructor
public class JiangsuResponseCallback {
    @JacksonXmlProperty(localName = "Datetime")
    private String datetime;

    @JacksonXmlProperty(localName = "Content")
    private Content content;

    private static final String CODE_OK = "0";
    private static final String MSG_OK = "0";

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        @JacksonXmlProperty(localName = "retCode")
        private String retCode;
        @JacksonXmlProperty(localName = "retMsg")
        private String retMsg;
    }

    public static JiangsuResponseCallback OK(){
        JiangsuResponseCallback responseCallback = new JiangsuResponseCallback();
        responseCallback.setDatetime(String.valueOf(System.currentTimeMillis()));
        responseCallback.setContent(Content.builder().retCode(CODE_OK).retMsg(MSG_OK).build());
        return responseCallback;
    }
}
