package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/10 9:59
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeChargePlusRequest implements Serializable {

    /**
     * crmDistributorId :
     * orderStatus :
     * errDesc :
     * zmtOrderId :
     * serialno :
     */

    @JsonProperty("crmDistributorId")
    private String crmDistributorId;
    @JsonProperty("orderStatus")
    private String orderStatus;
    @JsonProperty("errDesc")
    private String errDesc;
    @JsonProperty("zmtOrderId")
    private String zmtOrderId;
    @JsonProperty("serialno")
    private String serialno;
}
