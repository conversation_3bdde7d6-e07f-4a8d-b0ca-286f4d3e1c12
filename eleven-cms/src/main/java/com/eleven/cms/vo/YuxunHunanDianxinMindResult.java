package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-11-03 15:42
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class YuxunHunanDianxinMindResult {
    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private YuxunHunanDianxinMindResultData data;

    @Data
    public static class YuxunHunanDianxinMindResultData {
        private String ret;
        private String orderNo;
        @JsonProperty("URL")
        private String url;
    }


    private static final Integer SUC_CODE = 0;

    public static final YuxunHunanDianxinMindResult FAIL_RESULT = new YuxunHunanDianxinMindResult(9999, "通讯失败", null);

    public boolean isOk() {
        return SUC_CODE.equals(code);
    }

}
