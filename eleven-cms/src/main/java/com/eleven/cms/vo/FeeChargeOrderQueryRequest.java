package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 话费充值订单查询结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/18 10:17
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeChargeOrderQueryRequest  implements Serializable {
    public static final String CODE_OK = "0";
    //0:成功；其他:失败
    @JsonProperty("code")
    private String code;
    //描述
    @JsonProperty("msg")
    private String msg;
    //商户号
    @JsonProperty("salerId")
    private String salerId;
    //商户名称
    @JsonProperty("salerName")
    private String salerName;
    //支付状态  0:申请中，1：充值成功，2：充值失败
    @JsonProperty("payStatus")
    private String payStatus;
    //交易金额
    @JsonProperty("transAmount")
    private String transAmount;
    //折扣
    @JsonProperty("salerDiscount")
    private String salerDiscount;
    //实付金额
    @JsonProperty("actualAmount")
    private String actualAmount;
    //商户订单号
    @JsonProperty("orderId")
    private String orderId;
    //交易流水号
    @JsonProperty("tradeNo")
    private String tradeNo;
    //凭证号（部分通道存在）
    @JsonProperty("pzOrderNo")
    private String pzOrderNo;
    @JsonProperty("sign")
    private String sign;
    public boolean isOK() {
        return CODE_OK.equals(code);
    }



}
