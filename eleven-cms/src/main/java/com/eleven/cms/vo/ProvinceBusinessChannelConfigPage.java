package com.eleven.cms.vo;

import java.util.List;

import com.eleven.cms.entity.ProvinceBusinessChannelInfo;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: cms_province_business_channel_config
 * @Author: jeecg-boot
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@Data
@ApiModel(value="cms_province_business_channel_configPage对象", description="cms_province_business_channel_config")
public class ProvinceBusinessChannelConfigPage {

	/**id*/
	@ApiModelProperty(value = "id")
	private String id;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
	@ApiModelProperty(value = "渠道号")
	private String channel;
	/**备注*/
	@Excel(name = "备注", width = 15)
	@ApiModelProperty(value = "备注")
	private String remark;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**修改人*/
	@ApiModelProperty(value = "修改人")
	private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "修改时间")
	private Date updateTime;

    @Excel(name = "总限量", width = 15)
    @ApiModelProperty(value = "总限量")
    private Integer limitTotalAmount;
    @Excel(name = "向宏总限量", width = 15)
    @ApiModelProperty(value = "向宏总限量")
    private Integer xianghongTotalLimit;
    /**限量接收短信手机号*/
    @Excel(name = "小峰总限量", width = 15)
    @ApiModelProperty(value = "小峰总限量")
    private Integer xiaofengTotalLimit;
    /**限量接收短信手机号*/
    @Excel(name = "cpa总限量", width = 15)
    @ApiModelProperty(value = "cpa总限量")
    private Integer cpaTotalLimit;
    @Excel(name = "董松炜总限量", width = 15)
    @ApiModelProperty(value = "董松炜总限量")
    private Integer dongswTotalLimit;
    /**cpa接收短信手机号*/
    @Excel(name = "cpa接收短信手机号", width = 15)
    @ApiModelProperty(value = "cpa接收短信手机号")
    private String receiveMobile;
    /**cpa接收短信手机号*/
    @Excel(name = "cpa接收短信手机号", width = 15)
    @ApiModelProperty(value = "cpa接收短信手机号")
    private String cpaReceiveMobile;
	
	@ExcelCollection(name="cms_province_business_channel_info")
	@ApiModelProperty(value = "cms_province_business_channel_info")
	private List<ProvinceBusinessChannelInfo> provinceBusinessChannelInfoList;
	
}
