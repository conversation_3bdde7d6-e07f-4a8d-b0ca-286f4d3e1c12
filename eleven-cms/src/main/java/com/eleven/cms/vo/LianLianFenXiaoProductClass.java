package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/15 10:54
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoProductClass implements Serializable {

    /**
     * id : 131
     * name : 餐饮类
     * parentId : 0
     * level : 1
     * sort : 1
     * childList : [{"id":132,"name":"中餐","parentId":131,"level":2,"sort":1,"childList":[{"id":296,"name":"川菜","parentId":132,"level":3,"sort":4},{"id":297,"name":"粤菜","parentId":132,"level":3,"sort":5}]}]
     */

    @JsonProperty("id")
    private int id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("parentId")
    private int parentId;
    @JsonProperty("level")
    private int level;
    @JsonProperty("sort")
    private int sort;
    @JsonProperty("childList")
    private List<ChildListX> childList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ChildListX implements Serializable {
        /**
         * id : 132
         * name : 中餐
         * parentId : 131
         * level : 2
         * sort : 1
         * childList : [{"id":296,"name":"川菜","parentId":132,"level":3,"sort":4},{"id":297,"name":"粤菜","parentId":132,"level":3,"sort":5}]
         */

        @JsonProperty("id")
        private int id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("parentId")
        private int parentId;
        @JsonProperty("level")
        private int level;
        @JsonProperty("sort")
        private int sort;
        @JsonProperty("childList")
        private List<ChildList> childList;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ChildList implements Serializable {
            /**
             * id : 296
             * name : 川菜
             * parentId : 132
             * level : 3
             * sort : 4
             */

            @JsonProperty("id")
            private int id;
            @JsonProperty("name")
            private String name;
            @JsonProperty("parentId")
            private int parentId;
            @JsonProperty("level")
            private int level;
            @JsonProperty("sort")
            private int sort;
        }
    }
}
