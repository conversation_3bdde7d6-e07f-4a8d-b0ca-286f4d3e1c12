package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-06-07 14:49
 */

@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KuaimaResult {
    private Integer code;
    private String message;
    private KuaimaOrderResult data;

    private  static final Integer SUC_CODE = 200;


    public static final KuaimaResult FAIL_RESULT = new KuaimaResult(9999, "通讯失败", null);


    public boolean isOk() {
        return SUC_CODE.equals(code);
    }
    @Data
    public class KuaimaOrderResult{
        private String appId;
        @JsonProperty(value = "serial_number")
        private String serialNumber;
    }
}
