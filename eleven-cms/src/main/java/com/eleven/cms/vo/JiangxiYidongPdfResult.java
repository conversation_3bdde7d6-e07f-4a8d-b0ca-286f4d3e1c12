package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-05-25 14:46
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangxiYidongPdfResult {

    private String ret;
    private String msg;
    private Data data;

    private static final String CALL_SUC_CODE = "0";
    private static final String RESULT_SUC_OK = "0";

    public static final JiangxiYidongPdfResult FAIL_RESULT = new JiangxiYidongPdfResult("9999", "通讯失败", null);

    @lombok.Data
    @NoArgsConstructor
    public static class Data {
        private ResultMsg resultMsg;
    }

    @lombok.Data
    @NoArgsConstructor
    public static class ResultMsg {
        private String result;
        private String respCode;

        @Override
        public String toString() {
            return "ResultMsg{ respCode='" + respCode + '\'' + '}';
        }
    }


    public boolean isOk() {
        return CALL_SUC_CODE.equals(ret) && data != null && data.getResultMsg() != null &&
                RESULT_SUC_OK.equals(data.getResultMsg().getRespCode());
    }




}
