package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FengzhushouSichuanYidongSmsCodeResult {

    public static final Integer CODE_OK = 0;
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private FengzhushouSichuanYidongData data;


    public static FengzhushouSichuanYidongSmsCodeResult fail() {
        return FengzhushouSichuanYidongSmsCodeResult.builder().code(CODE_FAIL).msg(MSG_FAIL).data(null).build();
    }

    @Data
    public static class FengzhushouSichuanYidongData {
        @JsonProperty("paramInfo")
        private String paramInfo;
        @JsonProperty("ext")
        FengzhushouSichuanYidongDataExt ext;
    }

    @Data
    public static class FengzhushouSichuanYidongDataExt {
        @JsonProperty("serial_number")
        private String serialNumber;
    }

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

}
