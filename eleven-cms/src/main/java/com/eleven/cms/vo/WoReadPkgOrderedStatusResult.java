package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2023-5-25 16:20:37
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WoReadPkgOrderedStatusResult {

    public static final String CODE_OK = "0000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    /*0000代表正常，其它代表异常*/
    @JsonProperty("code")
    private String code;
    @JsonProperty("innercode")
    private String innercode;
    private String resMessage;
    private WoReadPkgOrderedStatusMessage data;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static WoReadPkgOrderedStatusResult fail() {
        return WoReadPkgOrderedStatusResult.builder().code(CODE_FAIL).resMessage(MSG_FAIL).build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WoReadPkgOrderedStatusMessage{

        //类型：Number  必有字段  订购方式:1-个人定购 2-他人赠送 3-推荐 5-反向
        @JsonProperty("subscribemode")
        private String subscribemode;
        //类型：Number  必有字段  备注：产品编号
        @JsonProperty("productpkgindex")
        private String productpkgindex;
        //类型：Number  必有字段  备注：用户索引
        @JsonProperty("allindex")
        private String allindex;
        //类型：Number  必有字段  备注：支付方式
        @JsonProperty("paytype")
        private String paytype;
        //类型：Number  必有字段  备注：周期参数
        @JsonProperty("cycparam")
        private String cycparam;
        //类型:String  备注：生效时间 yyyyMMddHHmmss
        @JsonProperty("effectivetime")
        private String effectivetime;
        //类型：Number  必有字段  备注：订购状态 2正常 3已退订
        @JsonProperty("status")
        private String status;
        //失效时间
        @JsonProperty("expiretime")
        private String expiretime;

    }
}
