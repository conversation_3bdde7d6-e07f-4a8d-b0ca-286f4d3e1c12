package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GuizhouMobileCodeResult {

    /*@JsonProperty("authCode")
    private String authCode;*/
    @JsonProperty("serialNumber")
    private String serialNumber;
    @JsonProperty("stepCode")
    private String stepCode;
    @JsonProperty("syncUrl")
    private String syncUrl;
}
