package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lihb
 * @create: 2023年7月14日15:17:15
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HeyuYidongVrbtResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    public static final String OPERATE_OK = "000000";


    private String code;
    private String message;
    private Result result;


    public static HeyuYidongVrbtResult fail() {
        return HeyuYidongVrbtResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }

    @Data
    public static class Result {
        private String orderId;
        private String resCode;
        private String resMsg;
    }


    public boolean isGetSmsOK() {
        return CODE_OK.equals(code) && result != null && OPERATE_OK.equals(result.getResCode());
    }

    public boolean isSubSmsOK() {
        return CODE_OK.equals(code) && result != null && OPERATE_OK.equals(result.getResCode());
    }
}
