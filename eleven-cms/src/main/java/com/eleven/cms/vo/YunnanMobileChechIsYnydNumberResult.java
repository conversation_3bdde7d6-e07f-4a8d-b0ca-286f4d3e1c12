package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class YunnanMobileChechIsYnydNumberResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    /*0代表正常，其它代表异常*/
    @JsonProperty("respCode")
    private String respCode;
    @JsonProperty("respDesc")
    private String respDesc;
    @JsonProperty("result")
    private Result result;

    public boolean isOK() {
        return CODE_OK.equals(respCode);
    }

    public static YunnanMobileChechIsYnydNumberResult fail() {
        return YunnanMobileChechIsYnydNumberResult.builder().respCode(CODE_FAIL).respDesc(MSG_FAIL).respCode("false").build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result{
        @JsonProperty("X_RESULTINFO")
        private String resultInfo;
        @JsonProperty("X_RESULTCODE")
        private String resultCode;
        @JsonProperty("X_NODE_NAME")
        private String nodeName;
        @JsonProperty("REGION_ID")
        private String regionId;
        @JsonProperty("CHECK_CODE")
        private String checkCode;
        @JsonProperty("CHECK_INFO")
        private String checkInfo;
        public boolean isOK() {
            return CODE_OK.equals(resultCode);
        }
        public static Result fail() {
            return Result.builder().resultCode(CODE_FAIL).resultInfo(MSG_FAIL).resultCode("false").build();
        }
    }
}
