package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 话费充值结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/18 10:18
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeChargeOrderRequest  implements Serializable {
    public static final String CODE_OK = "0";
    //0:成功；其他:失败
    @JsonProperty("code")
    private String code;
    //描述
    @JsonProperty("msg")
    private String msg;
    //商户号
    @JsonProperty("salerId")
    private String salerId;
    //商户名称
    @JsonProperty("salerName")
    private String salerName;
    //交易金额
    @JsonProperty("transAmount")
    private String transAmount;
    //实付金额
    @JsonProperty("actualAmount")
    private String actualAmount;
    //折扣
    @JsonProperty("salerDiscount")
    private String salerDiscount;
    //充值号码
    @JsonProperty("mobile")
    private String mobile;
    //商户订单号
    @JsonProperty("orderId")
    private String orderId;
    //产品编码
    @JsonProperty("productCode")
    private String productCode;
    @JsonProperty("sign")
    private String sign;
    public boolean isOK() {
        return CODE_OK.equals(code);
    }
}
