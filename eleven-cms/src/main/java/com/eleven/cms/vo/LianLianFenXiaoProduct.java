package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/12 17:01
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoProduct implements Serializable {
    /**
     * page : 1
     * total : 2
     * list : [{"productId":62714003,"locationId":1,"name":"老电商X","onlyName":"老电商X","shareText":"老电商X","title":"老电商X","channelMallImg":"","saleAmount":0,"stockAmount":1000,"itemStock":0,"salePrice":8000,"originPrice":8000,"productSimpleDesc":"老电商X","isSoldOut":0,"faceImg":"https://debug.lianlianlvyou.com/lianlian/common/e38564d8452746e2991daea8aed73f86.jpg","shareImg":"https://debug.lianlianlvyou.com/lianlian/common/e38564d8452746e2991daea8aed73f86.jpg","attention":"","bookingType":0,"bookingShowAddress":1,"orderShowIdCard":0,"orderShowDate":0,"beginTime":"2024-03-22 00:00:00","endTime":"2024-04-30 23:59:59","validBeginDate":"2024-03-22 00:00:00","validEndDate":"2024-04-30 23:59:59","bookingBeginDate":null,"releaseTime":"2024-03-22 11:28:00","singleMin":1,"singleMax":10,"ecommerce":0,"categoryPath":"生活服务类-宠物类-宠物店","codeType":1,"productCategoryId":208,"categoryName":"宠物店","qualificationsList":["http://ll-oss-test.oss-cn-beijing.aliyuncs.com/ll-center/merchant/8762948803.jpg?Expires=2029306024&OSSAccessKeyId=LTAI5tMhQHrww5n9to5nrULd&Signature=km1bYrjFDwYl63%2BxPvIuPhu04zM%3D"],"itemList":[{"itemId":4661768,"subTitle":"套1","salePrice":8000,"originPrice":8000,"channelPrice":5900,"stock":0,"singleMax":10,"codeAmount":1,"visibleTime":null,"beginVisibleTime":null}],"shopList":[{"id":132773406,"name":"北京麦当劳食品有限公司","latitude":"39.997347","longitude":"116.354319","cityCode":"110100","address":"北京市海淀区清华东路与学清路辅路交叉口南420米","phoneNumber":"13550087714"}],"noticeVOList":[{"titles":"预约方式","platform":0,"platformName":"通用展示","option":[{"getChecked":true,"optionName":"下单24小时内根据下单顺序依次配送（可以指定具体收货时间，XX年XX月XX日XX点）"}]}],"productItemsContentMap":{"4661768":[{"id":null,"productId":62714003,"productItemId":4661768,"contentDesc":"品1","numberAndUnit":"1份","originPrice":8000,"contentType":2,"status":0,"createDate":"2024-03-22 11:26:01","updateTime":"2024-03-22 11:28:00"}]},"dateInfoDTO":{"beginTime":"2024-03-22 00:00:00","endTime":"2024-04-30 23:59:59","validBeginDate":"2024-03-22 00:00:00","validEndDate":"2024-04-30 23:59:59","releaseTime":"2024-03-22 11:28:00"},"imgList":[{"url":"https://debug.lianlianlvyou.com/lianlian/common/825d4d031fe44df5bc73697d077d109a.jpg","sort":0},{"url":"https://debug.lianlianlvyou.com/lianlian/common/4276559a282941339ec95f50ddeda306.jpg","sort":1}]},{"productId":62713977,"locationId":1,"name":"新上单旅游渠道","onlyName":"新上单旅游渠道","shareText":"新上单旅游渠道","title":"新上单旅游渠道","channelMallImg":"","saleAmount":0,"stockAmount":1000,"itemStock":0,"salePrice":5000,"originPrice":8000,"productSimpleDesc":"新上单旅游渠道","isSoldOut":0,"faceImg":"https://debug.lianlianlvyou.com/lianlian/common/20d472713b304ba2962fa9e666007935.jpg","shareImg":"https://debug.lianlianlvyou.com/lianlian/common/20d472713b304ba2962fa9e666007935.jpg","attention":"","bookingType":0,"bookingShowAddress":0,"orderShowIdCard":0,"orderShowDate":0,"beginTime":"2024-03-11 00:00:00","endTime":"2024-04-30 23:59:59","validBeginDate":"2024-03-11 00:00:00","validEndDate":"2024-04-30 23:59:59","bookingBeginDate":null,"releaseTime":"2024-03-11 17:16:47","singleMin":1,"singleMax":10,"ecommerce":0,"categoryPath":"餐饮类-中餐-川菜","codeType":1,"productCategoryId":296,"categoryName":"川菜","qualificationsList":["http://ll-oss-test.oss-cn-beijing.aliyuncs.com/ll-center/merchant/8762948803.jpg?Expires=2029306024&OSSAccessKeyId=LTAI5tMhQHrww5n9to5nrULd&Signature=km1bYrjFDwYl63%2BxPvIuPhu04zM%3D"],"itemList":[{"itemId":4661699,"subTitle":"套1","salePrice":5000,"originPrice":8000,"channelPrice":2200,"stock":0,"singleMax":10,"codeAmount":1,"visibleTime":null,"beginVisibleTime":null}],"shopList":[{"id":132773406,"name":"北京麦当劳食品有限公司","latitude":"39.997347","longitude":"116.354319","cityCode":"110100","address":"北京市海淀区清华东路与学清路辅路交叉口南420米","phoneNumber":"13550087714"}],"noticeVOList":[{"titles":"预约方式","platform":1,"platformName":"微信展示","option":[{"getChecked":true,"optionName":"至少提前X天短信网址预约，高峰期需等位"},{"getChecked":true,"optionName":"周一至周五无需预约，周末节假日至少提前X天短信网址预约，高峰期需等位"}]}],"productItemsContentMap":{"4661699":[{"id":381731,"productId":62713977,"productItemId":4661699,"contentDesc":"套1","numberAndUnit":"1","originPrice":8000,"contentType":2,"status":0,"createDate":"2024-03-11 16:43:00","updateTime":"2024-03-11 16:43:00"}]},"dateInfoDTO":{"beginTime":"2024-03-11 00:00:00","endTime":"2024-04-30 23:59:59","validBeginDate":"2024-03-11 00:00:00","validEndDate":"2024-04-30 23:59:59","releaseTime":"2024-03-11 17:16:47"},"imgList":[{"url":"https://debug.lianlianlvyou.com/lianlian/common/014e37591cd84bc1885acb979370c656.jpg","sort":0},{"url":"https://debug.lianlianlvyou.com/lianlian/common/4ab7b4602f28459f92538417f269e201.jpg","sort":1}]}]
     */

    @JsonProperty("page")
    private Integer page;
    @JsonProperty("total")
    private Integer total;
    @JsonProperty("list")
    private List<Product> list;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Product implements Serializable {
        /**
         * productId : 62714003
         * locationId : 1
         * name : 老电商X
         * onlyName : 老电商X
         * shareText : 老电商X
         * title : 老电商X
         * channelMallImg :
         * saleAmount : 0
         * stockAmount : 1000
         * itemStock : 0
         * salePrice : 8000
         * originPrice : 8000
         * productSimpleDesc : 老电商X
         * isSoldOut : 0
         * faceImg : https://debug.lianlianlvyou.com/lianlian/common/e38564d8452746e2991daea8aed73f86.jpg
         * shareImg : https://debug.lianlianlvyou.com/lianlian/common/e38564d8452746e2991daea8aed73f86.jpg
         * attention :
         * bookingType : 0
         * bookingShowAddress : 1
         * orderShowIdCard : 0
         * orderShowDate : 0
         * beginTime : 2024-03-22 00:00:00
         * endTime : 2024-04-30 23:59:59
         * validBeginDate : 2024-03-22 00:00:00
         * validEndDate : 2024-04-30 23:59:59
         * bookingBeginDate : null
         * releaseTime : 2024-03-22 11:28:00
         * singleMin : 1
         * singleMax : 10
         * ecommerce : 0
         * categoryPath : 生活服务类-宠物类-宠物店
         * codeType : 1
         * productCategoryId : 208
         * categoryName : 宠物店
         * qualificationsList : ["http://ll-oss-test.oss-cn-beijing.aliyuncs.com/ll-center/merchant/8762948803.jpg?Expires=2029306024&OSSAccessKeyId=LTAI5tMhQHrww5n9to5nrULd&Signature=km1bYrjFDwYl63%2BxPvIuPhu04zM%3D"]
         * itemList : [{"itemId":4661768,"subTitle":"套1","salePrice":8000,"originPrice":8000,"channelPrice":5900,"stock":0,"singleMax":10,"codeAmount":1,"visibleTime":null,"beginVisibleTime":null}]
         * shopList : [{"id":132773406,"name":"北京麦当劳食品有限公司","latitude":"39.997347","longitude":"116.354319","cityCode":"110100","address":"北京市海淀区清华东路与学清路辅路交叉口南420米","phoneNumber":"13550087714"}]
         * noticeVOList : [{"titles":"预约方式","platform":0,"platformName":"通用展示","option":[{"getChecked":true,"optionName":"下单24小时内根据下单顺序依次配送（可以指定具体收货时间，XX年XX月XX日XX点）"}]}]
         * productItemsContentMap : {"4661768":[{"id":null,"productId":62714003,"productItemId":4661768,"contentDesc":"品1","numberAndUnit":"1份","originPrice":8000,"contentType":2,"status":0,"createDate":"2024-03-22 11:26:01","updateTime":"2024-03-22 11:28:00"}]}
         * dateInfoDTO : {"beginTime":"2024-03-22 00:00:00","endTime":"2024-04-30 23:59:59","validBeginDate":"2024-03-22 00:00:00","validEndDate":"2024-04-30 23:59:59","releaseTime":"2024-03-22 11:28:00"}
         * imgList : [{"url":"https://debug.lianlianlvyou.com/lianlian/common/825d4d031fe44df5bc73697d077d109a.jpg","sort":0},{"url":"https://debug.lianlianlvyou.com/lianlian/common/4276559a282941339ec95f50ddeda306.jpg","sort":1}]
         */

        @JsonProperty("productId")
        private Integer productId;
        @JsonProperty("locationId")
        private Integer locationId;
        @JsonProperty("name")
        private String name;
        @JsonProperty("onlyName")
        private String onlyName;
        @JsonProperty("shareText")
        private String shareText;
        @JsonProperty("title")
        private String title;
        @JsonProperty("channelMallImg")
        private String channelMallImg;
        @JsonProperty("saleAmount")
        private Integer saleAmount;
        @JsonProperty("stockAmount")
        private Integer stockAmount;
        @JsonProperty("itemStock")
        private Integer itemStock;
        @JsonProperty("salePrice")
        private Integer salePrice;
        @JsonProperty("originPrice")
        private Integer originPrice;
        @JsonProperty("productSimpleDesc")
        private String productSimpleDesc;
        @JsonProperty("isSoldOut")
        private Integer isSoldOut;
        @JsonProperty("faceImg")
        private String faceImg;
        @JsonProperty("shareImg")
        private String shareImg;
        @JsonProperty("attention")
        private String attention;
        @JsonProperty("bookingType")
        private Integer bookingType;
        @JsonProperty("bookingShowAddress")
        private Integer bookingShowAddress;
        @JsonProperty("orderShowIdCard")
        private Integer orderShowIdCard;
        @JsonProperty("orderShowDate")
        private Integer orderShowDate;
        @JsonProperty("beginTime")
        private String beginTime;
        @JsonProperty("endTime")
        private String endTime;
        @JsonProperty("validBeginDate")
        private String validBeginDate;
        @JsonProperty("validEndDate")
        private String validEndDate;
        @JsonProperty("bookingBeginDate")
        private String bookingBeginDate;
        @JsonProperty("releaseTime")
        private String releaseTime;
        @JsonProperty("singleMin")
        private Integer singleMin;
        @JsonProperty("singleMax")
        private Integer singleMax;
        @JsonProperty("ecommerce")
        private Integer ecommerce;
        @JsonProperty("categoryPath")
        private String categoryPath;
        @JsonProperty("codeType")
        private Integer codeType;
        @JsonProperty("productCategoryId")
        private Integer productCategoryId;
        @JsonProperty("categoryName")
        private String categoryName;
        @JsonProperty("productItemsContentMap")
        private Map<String,List<ProductItemsContentMap>> productItemsContentMap;
        @JsonProperty("dateInfoDTO")
        private DateInfoDTO dateInfoDTO;
        @JsonProperty("qualificationsList")
        private List<String> qualificationsList;
        @JsonProperty("itemList")
        private List<ItemList> itemList;
        @JsonProperty("shopList")
        private List<ShopList> shopList;
        @JsonProperty("noticeVOList")
        private List<NoticeVOList> noticeVOList;
        @JsonProperty("imgList")
        private List<ImgList> imgList;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ProductItemsContentMap implements Serializable {
            /**
             * id : null
             * productId : 62714003
             * productItemId : 4661768
             * contentDesc : 品1
             * numberAndUnit : 1份
             * originPrice : 8000
             * contentType : 2
             * status : 0
             * createDate : 2024-03-22 11:26:01
             * updateTime : 2024-03-22 11:28:00
             */

            @JsonProperty("id")
            private Object id;
            @JsonProperty("productId")
            private Integer productId;
            @JsonProperty("productItemId")
            private Integer productItemId;
            @JsonProperty("contentDesc")
            private String contentDesc;
            @JsonProperty("numberAndUnit")
            private String numberAndUnit;
            @JsonProperty("originPrice")
            private Integer originPrice;
            @JsonProperty("contentType")
            private Integer contentType;
            @JsonProperty("status")
            private Integer status;
            @JsonProperty("createDate")
            private String createDate;
            @JsonProperty("updateTime")
            private String updateTime;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class DateInfoDTO implements Serializable {
            /**
             * beginTime : 2024-03-22 00:00:00
             * endTime : 2024-04-30 23:59:59
             * validBeginDate : 2024-03-22 00:00:00
             * validEndDate : 2024-04-30 23:59:59
             * releaseTime : 2024-03-22 11:28:00
             */

            @JsonProperty("beginTime")
            private String beginTime;
            @JsonProperty("endTime")
            private String endTime;
            @JsonProperty("validBeginDate")
            private String validBeginDate;
            @JsonProperty("validEndDate")
            private String validEndDate;
            @JsonProperty("releaseTime")
            private String releaseTime;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ItemList implements Serializable {
            /**
             * itemId : 4661768
             * subTitle : 套1
             * salePrice : 8000
             * originPrice : 8000
             * channelPrice : 5900
             * stock : 0
             * singleMax : 10
             * codeAmount : 1
             * visibleTime : null
             * beginVisibleTime : null
             */

            @JsonProperty("itemId")
            private Integer itemId;
            @JsonProperty("subTitle")
            private String subTitle;
            @JsonProperty("salePrice")
            private Integer salePrice;
            @JsonProperty("originPrice")
            private Integer originPrice;
            @JsonProperty("channelPrice")
            private Integer channelPrice;
            @JsonProperty("stock")
            private Integer stock;
            @JsonProperty("singleMax")
            private Integer singleMax;
            @JsonProperty("codeAmount")
            private Integer codeAmount;
            @JsonProperty("visibleTime")
            private Object visibleTime;
            @JsonProperty("beginVisibleTime")
            private Object beginVisibleTime;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ShopList implements Serializable {
            /**
             * id : 132773406
             * name : 北京麦当劳食品有限公司
             * latitude : 39.997347
             * longitude : 116.354319
             * cityCode : 110100
             * address : 北京市海淀区清华东路与学清路辅路交叉口南420米
             * phoneNumber : 13550087714
             */

            @JsonProperty("id")
            private Integer id;
            @JsonProperty("name")
            private String name;
            @JsonProperty("latitude")
            private String latitude;
            @JsonProperty("longitude")
            private String longitude;
            @JsonProperty("cityCode")
            private String cityCode;
            @JsonProperty("address")
            private String address;
            @JsonProperty("phoneNumber")
            private String phoneNumber;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class NoticeVOList implements Serializable {
            /**
             * titles : 预约方式
             * platform : 0
             * platformName : 通用展示
             * option : [{"getChecked":true,"optionName":"下单24小时内根据下单顺序依次配送（可以指定具体收货时间，XX年XX月XX日XX点）"}]
             */

            @JsonProperty("titles")
            private String titles;
            @JsonProperty("platform")
            private Integer platform;
            @JsonProperty("platformName")
            private String platformName;
            @JsonProperty("option")
            private List<Option> option;

            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class Option implements Serializable {
                /**
                 * getChecked : true
                 * optionName : 下单24小时内根据下单顺序依次配送（可以指定具体收货时间，XX年XX月XX日XX点）
                 */

                @JsonProperty("getChecked")
                private boolean getChecked;
                @JsonProperty("optionName")
                private String optionName;
            }
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ImgList implements Serializable {
            /**
             * url : https://debug.lianlianlvyou.com/lianlian/common/825d4d031fe44df5bc73697d077d109a.jpg
             * sort : 0
             */

            @JsonProperty("url")
            private String url;
            @JsonProperty("sort")
            private Integer sort;
        }
    }


}
