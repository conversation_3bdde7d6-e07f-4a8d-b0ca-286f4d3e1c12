package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2020/12/29 14:17
 * Desc: 联通沃音乐开放平台响应封装
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiantongJunboResp {

     public static final String RETURN_CODE_SUCC = "0";
     public static final String RETURN_CODE_ERROR = "1111";
     public static final String DESCRIPTION_ERROR = "接口通讯失败";
     public static LiantongJunboResp ERROR_RESP = new LiantongJunboResp(RETURN_CODE_ERROR,DESCRIPTION_ERROR,null);

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("result")
    private LiantongVrbtJunboSubScribe result;

    public LiantongJunboResp code(String code){
        this.setCode(code);
        return this;
    }

    public LiantongJunboResp msg(String msg){
        this.setMsg(msg);
        return this;
    }

    public LiantongJunboResp result(LiantongVrbtJunboSubScribe result){
        this.setResult(result);
        return this;
    }

    public boolean isCodeOK(){
        return RETURN_CODE_SUCC.equals(code);
    }


}
