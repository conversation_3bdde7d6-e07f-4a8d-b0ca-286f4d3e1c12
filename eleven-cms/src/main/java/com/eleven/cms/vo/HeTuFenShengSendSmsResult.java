package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 河图分省发送验证码响应
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 17:00
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HeTuFenShengSendSmsResult  implements Serializable {
    public static final String RESP_CODE_OK = "000000";
    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("message")
    private String message;
    @JsonProperty("resultData")
    private Object resultData;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getReturnCode());
    }

}
