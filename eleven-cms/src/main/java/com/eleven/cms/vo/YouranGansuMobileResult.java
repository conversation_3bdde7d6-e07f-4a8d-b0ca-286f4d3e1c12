package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class YouranGansuMobileResult {

    public static final String CODE_OK = "111000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("retCode")
    private String retCode;
    @JsonProperty("respMsg")
    private String respMsg;
    @JsonProperty("data")
    private Data data;

    @lombok.Data
    public class Data {
        @JsonProperty("identifyingKey")
        private String identifyingKey;
    }

    public boolean isOK() {
        return CODE_OK.equals(retCode);
    }

    public static YouranGansuMobileResult fail() {
        return YouranGansuMobileResult.builder().retCode(CODE_FAIL).respMsg(MSG_FAIL).build();
    }
}
