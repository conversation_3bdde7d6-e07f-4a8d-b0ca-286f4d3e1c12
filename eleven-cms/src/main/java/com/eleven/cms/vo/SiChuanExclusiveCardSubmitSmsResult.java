package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/14 11:56
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SiChuanExclusiveCardSubmitSmsResult implements Serializable {
    private static final String CODE_OK ="00000";
    /**
     * data : null
     * resultCode : 9999
     * resultMsg : 短信验证码失效
     * serialID : null
     * orderNO : null
     */

    @JsonProperty("data")
    private Object data;
    @JsonProperty("resultCode")
    private String resultCode;
    @JsonProperty("resultMsg")
    private String resultMsg;
    @JsonProperty("serialID")
    private String serialID;
    @JsonProperty("orderNO")
    private String orderNO;

    public boolean isOK(){
        return this.getResultCode().equals(CODE_OK);
    }

}
