package com.eleven.cms.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-03-08 11:38
 */
@Data
@JacksonXmlRootElement(localName="Response")
@NoArgsConstructor
@AllArgsConstructor
public class JiangsuResponseCreateOrder {
    @JacksonXmlProperty(localName = "Datetime")
    private String datetime;

    @JacksonXmlProperty(localName = "Content")
    private Content content;

    private String CODE_OK = "0000";
    private String CREATE_OK = "1";

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        @JacksonXmlProperty(localName = "retCode")
        private String retCode;
        @JacksonXmlProperty(localName = "retMsg")
        private String retMsg;
        @JacksonXmlProperty(localName = "orderId")
        private String orderId;
        @JacksonXmlProperty(localName = "status")
        private String status;
    }
    public boolean isOk() {
        return content != null && CODE_OK.equals(content.retCode) && CREATE_OK.equals(content.getStatus());
    }

    public static final JiangsuResponseCreateOrder FAIL_RESULT = new JiangsuResponseCreateOrder();
}
