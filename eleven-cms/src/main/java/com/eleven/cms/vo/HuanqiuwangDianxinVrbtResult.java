package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-01-05 10:59
 */



//{"res_code":"0","res_message":"成功","order_no":"8225fd9d3d7646238be3594d47a4fed2","fee_url":"https://crbt.qimingwenhua.net/videoring/ct/detail/submitorder.html?id=8225fd9d3d7646238be3594d47a4fed2"}


@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HuanqiuwangDianxinVrbtResult {

    public static final HuanqiuwangDianxinVrbtResult FAIL_RESULT = new HuanqiuwangDianxinVrbtResult("", "通讯失败", "1", "");

    @JsonProperty("order_no")
    private String orderId;
    @JsonProperty("res_message")
    private String msg;
    @JsonProperty("res_code")
    private String result;
    @JsonProperty("data")
    private String data;


    public static HuanqiuwangDianxinVrbtResult fail() {
        return FAIL_RESULT;
    }

    public boolean isOK() {
        return "0".equals(result);
    }
}
