package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HebeiMobileResult {

    public static final Integer CODE_OK = 0;
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private Integer resCode;
    @JsonProperty("msg")
    private String resMsg;

    public boolean isOK() {
        return CODE_OK.equals(resCode);
    }

    public static HebeiMobileResult fail() {
        return HebeiMobileResult.builder().resCode(CODE_FAIL).resMsg(MSG_FAIL).build();
    }
}
