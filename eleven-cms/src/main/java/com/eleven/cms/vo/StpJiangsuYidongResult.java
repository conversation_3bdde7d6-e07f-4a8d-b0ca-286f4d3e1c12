package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lihb
 * @create: 2023年7月14日15:17:15
 */

@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
public class StpJiangsuYidongResult {

    public static final Integer CODE_OK = 1;
    public static final Integer CODE_FAIL = 9999;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private Data data;


    public boolean isOK() {
        return CODE_OK.equals(data.getCode());
    }

    public static StpJiangsuYidongResult fail() {
        return StpJiangsuYidongResult.builder().code(CODE_FAIL).msg(MSG_FAIL).build();
    }

    @lombok.Data
    @NoArgsConstructor
    public static class Data {
        private Integer code;
        private String msg;
    }

}
