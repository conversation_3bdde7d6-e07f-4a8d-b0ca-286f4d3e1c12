package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/17 10:49
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SichuanMobilePointAnalysiResult implements Serializable {

    public static final String CODE_OK = "0000";

    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";
    /**
     * res_code : 0000
     * res_msg : 成功
     * res_info : {"phone":"134****5656","status":true,"time":"2020-01-01 13:56:32"}
     */

    @JsonProperty("res_code")
    private String resCode;
    @JsonProperty("res_msg")
    private String resMsg;
    @JsonProperty("result")
    private List<Result> result;
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Result implements Serializable {
        /**
         * phone : 134****5656
         * status : true
         * time : 2020-01-01 13:56:32
         */

        @JsonProperty("groupId")
        private String groupId;
        @JsonProperty("dgNum")
        private String dgNum;
        @JsonProperty("dayId")
        private String dayId;
        @JsonProperty("groupPlace")
        private String groupPlace;
        @JsonProperty("lcv")
        private String lcv;
    }
    public boolean isOK() {
        return CODE_OK.equals(resCode) && this.getResult().isEmpty();
    }
    public static SichuanMobilePointAnalysiResult fail() {
        return SichuanMobilePointAnalysiResult.builder().resCode(CODE_FAIL).resMsg(MSG_FAIL).build();
    }
}
