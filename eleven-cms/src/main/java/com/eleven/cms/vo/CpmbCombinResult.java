package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2019/12/16 11:24
 * Desc:远程调用接口返回json结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CpmbCombinResult {
    public static final String  CODE_OK = "000000";
    public static final String  CODE_FAIL = "999999";
    public static final String  MSG_OK = "成功";

    // funStatus 1:未开通视频彩铃功能。2：已开通视频彩铃功能，但视频彩铃播放功能处于关闭状态。3：已开通视频彩铃功能，且视频彩铃播放功能处于开启状态。
    public static final String  VRBT_FUN_STATUS_NONE = "1";
    ////视频彩铃功能开通方式 passive:被动，active:主动
    //public static final String  VRBT_FUN_WAY_PASSIVE = "passive";
    ////视频彩铃功能开通方式 passive:被动，active:主动
    //public static final String  VRBT_FUN_WAY_ACTIVE = "active";

    //resCode	true	String	结果代码
    //resMsg	false	String	结果描述
    //000000	成功
    //100001	请求发生错误
    @JsonProperty("resCode")
    private String resCode;

    @JsonProperty("resMsg")
    private String resMsg;
    //登录token
    @JsonProperty("token")
    private String token;
    //是否已包月
    @JsonProperty("isMonth")
    private boolean isMonth;

    //是否需要被叫视频彩铃功能操作(是:操作被叫,否:操作主叫)
    //@JsonProperty("isVrbtPassive")
    //private boolean isVrbtPassive;
    //是否有被叫/主叫视频功能,和上面的isVrbtPassive匹配,是被叫就查的被叫功能,是主叫的就查的主叫功能
    @JsonProperty("isVrbtFun")
    private boolean isVrbtFun;
    /**
     * 是否需要开通彩铃特惠包(四川需要开启)
     */
    @JsonProperty("needCrbtTehuiPack")
    private boolean needCrbtTehuiPack;

    /**
     * 是否已开通彩铃特惠包
     */
    @JsonProperty("isCrbtTehuiPack")
    private boolean isCrbtTehuiPack;

    public boolean isOK(){
        return CODE_OK.equals(this.getResCode());
    }


    public CpmbCombinResult(String resCode, String resMsg) {
        this.resCode = resCode;
        this.resMsg = resMsg;
    }

    public static CpmbCombinResult success(String token, boolean isMonth, boolean isVrbtFun, boolean needCrbtTehuiPack, boolean isCrbtTehuiPack){
        return new CpmbCombinResult(CODE_OK,MSG_OK,token,isMonth, isVrbtFun, needCrbtTehuiPack, isCrbtTehuiPack);
    }

    public static CpmbCombinResult fail(String message){
        return new CpmbCombinResult(CODE_FAIL,message);
    }

}
