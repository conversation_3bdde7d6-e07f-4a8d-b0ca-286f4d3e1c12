package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GansuMobileCheckResult implements Serializable {

    public static final String CODE_OK = "111000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";
    /**
     * data :
     * respMsg : 校验处理成功！
     * retCode : 111000
     */

    @JsonProperty("data")
    private Object data;
    @JsonProperty("respMsg")
    private String respMsg;
    @JsonProperty("retCode")
    private String retCode;


    public boolean isOK() {
        return CODE_OK.equals(retCode);
    }

    public static GansuMobileCheckResult fail() {
        return GansuMobileCheckResult.builder().retCode(CODE_FAIL).respMsg(MSG_FAIL).build();
    }

}
