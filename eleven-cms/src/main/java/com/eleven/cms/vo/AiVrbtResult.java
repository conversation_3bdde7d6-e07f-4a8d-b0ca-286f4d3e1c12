package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * AI视频彩铃结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/16 11:14
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AiVrbtResult implements Serializable {
    public static final String CODE_OK = "200";
    /**
     * code : 200
     * msg : SUCCESS
     * data : {"msg":"SUCCESS","sn":"","tradeid":"cd34949702844b8994b4b254cf2668df","rescode":"0"}
     * timestamp : 1705317119529
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("timestamp")
    private long timestamp;
    /**
     * 判断是否成功
     */
    public boolean isOK() {
        return CODE_OK.equals(this.getCode()) ;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * msg : SUCCESS
         * sn :
         * tradeid : cd34949702844b8994b4b254cf2668df
         * rescode : 0
         */

        @JsonProperty("msg")
        private String msg;
        @JsonProperty("sn")
        private String sn;
        @JsonProperty("tradeid")
        private String tradeid;
        @JsonProperty("rescode")
        private String rescode;
    }
}
