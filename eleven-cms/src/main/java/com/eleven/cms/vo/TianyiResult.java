package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 电信天翼空间回调返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TianyiResult {

    private String code;
    private String msg;
    private String data;

    public static TianyiResult success(String phone) {
        return new TianyiResult("200", "处理成功", phone);
    }

}
