package com.eleven.cms.vo;

import java.util.List;
import com.eleven.cms.entity.OutsideConfig;
import com.eleven.cms.entity.OutsideBusinessConfig;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: cms_outside_config
 * @Author: jeecg-boot
 * @Date:   2023-10-10
 * @Version: V1.0
 */
@Data
@ApiModel(value="cms_outside_configPage对象", description="cms_outside_config")
public class OutsideConfigPage {

	/**id*/
	@ApiModelProperty(value = "id")
	private String id;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
    /**
     * 外部渠道
     */
    @Excel(name = "外部渠道", width = 15)
    @ApiModelProperty(value = "外部渠道")
    private String outChannel;
    /**
     * 通知地址
     */
    @Excel(name = "通知地址", width = 15)
    @ApiModelProperty(value = "通知地址")
    private String notifyUrl;
    /**
     * 通知地址
     */
    @Excel(name = "1小时退订通知地址", width = 15)
    @ApiModelProperty(value = "1小时退订通知地址")
    private String unsubNotifyUrl;

    /**
     * 通知地址
     */
    @Excel(name = "次日退订通知地址", width = 15)
    @ApiModelProperty(value = "次日退订通知地址")
    private String dayUnsubNotifyUrl;
    /**
     * 扣量比例
     */
    @Excel(name = "扣量比例", width = 15)
    @ApiModelProperty(value = "扣量比例")
    private Integer deductionRatio;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "更新日期")
	private Date updateTime;
	/**1启用0禁用*/
	@Excel(name = "1启用0禁用", width = 15)
	@ApiModelProperty(value = "1启用0禁用")
	private Integer status;

    /**1启用0禁用*/
    @Excel(name = "业务类型id", width = 15)
    @ApiModelProperty(value = "业务类型id")
    private String bizTypeId;

	@ExcelCollection(name="cms_outside_business_config")
	@ApiModelProperty(value = "cms_outside_business_config")
	private List<OutsideBusinessConfig> outsideBusinessConfigList;




    /**是否破解：1=是，0=否*/
    @Excel(name = "是否破解：1=是，0=否", width = 15)
    @ApiModelProperty(value = "是否破解：1=是，0=否")
    private Integer isCrack;

    /**报备bean*/
    @Excel(name = "报备bean", width = 15)
    @ApiModelProperty(value = "报备bean")
    private String reportBean;

}
