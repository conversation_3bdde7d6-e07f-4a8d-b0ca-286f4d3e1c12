package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GansuMobileServiceResult implements Serializable {

    public static final String CODE_OK = "111000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";
    /**
     * data : {"offerInfoList":[{"OFFER_INFO":{"CUST_ID":"93814335**654871","CREATE_OP_ID":"820077","VALID_DATE":"20230509110041","UNI_CHANNEL_NAME":"*****有限公司","IS_MAIN":"0","APPLY_CHANNEL":"","OFFER_TYPE":"10","CHANNEL_CODE":"1012","CAN_UN_SUB":"1","IS_BUNDLE":"1","MGMT_DISTRICT":"943","CREATE_ORG_ID":"31017002","EXPIRE_DATE":"20250501000000","PACKAGE_FLAG":"0","ACCESS_NUM":"19514****8","DONE_DATE":"20230509110043","DONE_CODE":"5620866517","OFFER_NAME":"[62201632]视频彩铃-9.9元美播版（含30GB抖音快手定向流量+咪咕音乐白金会员）","APPLY_CHANNEL_NAME":"","MajorType":"1","OFFER_TYPE_DESC":"个人预缴类商品（活动）","SUBBER_ID":"433032698998403","OFFER_ID":"306100013242","SO_ORG_ID":"31017002","UNI_CHANNEL_ID":"2931931002230400112","CHANNEL_NAME":"省内电渠","MGMT_COUNTY":"943","BRAND":"0","CREATE_DATE":"20230509110043","SRC_SYSTEM_TYPE":"","OUT_OFFER_ID":"2023130000045948","OFFER_INS_ID":"433052792193603","SUBSCRIBER_INS_ID":"433032698998403","IS_ROOT":"1","SO_OP_ID":"820077","SO_DATE":"20230509110043"}}]}
     * respMsg : success
     * retCode : 111000
     */

    @JsonProperty("data")
    private Data data;
    @JsonProperty("respMsg")
    private String respMsg;
    @JsonProperty("retCode")
    private String retCode;
    public boolean isOK() {
        return CODE_OK.equals(retCode);
    }

    public static GansuMobileServiceResult fail() {
        return GansuMobileServiceResult.builder().retCode(CODE_FAIL).respMsg(MSG_FAIL).build();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        @JsonProperty("offerInfoList")
        private List<OfferInfoList> offerInfoList;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @lombok.Data
        public static class OfferInfoList implements Serializable {
            /**
             * OFFER_INFO : {"CUST_ID":"93814335**654871","CREATE_OP_ID":"820077","VALID_DATE":"20230509110041","UNI_CHANNEL_NAME":"*****有限公司","IS_MAIN":"0","APPLY_CHANNEL":"","OFFER_TYPE":"10","CHANNEL_CODE":"1012","CAN_UN_SUB":"1","IS_BUNDLE":"1","MGMT_DISTRICT":"943","CREATE_ORG_ID":"31017002","EXPIRE_DATE":"20250501000000","PACKAGE_FLAG":"0","ACCESS_NUM":"19514****8","DONE_DATE":"20230509110043","DONE_CODE":"5620866517","OFFER_NAME":"[62201632]视频彩铃-9.9元美播版（含30GB抖音快手定向流量+咪咕音乐白金会员）","APPLY_CHANNEL_NAME":"","MajorType":"1","OFFER_TYPE_DESC":"个人预缴类商品（活动）","SUBBER_ID":"433032698998403","OFFER_ID":"306100013242","SO_ORG_ID":"31017002","UNI_CHANNEL_ID":"2931931002230400112","CHANNEL_NAME":"省内电渠","MGMT_COUNTY":"943","BRAND":"0","CREATE_DATE":"20230509110043","SRC_SYSTEM_TYPE":"","OUT_OFFER_ID":"2023130000045948","OFFER_INS_ID":"433052792193603","SUBSCRIBER_INS_ID":"433032698998403","IS_ROOT":"1","SO_OP_ID":"820077","SO_DATE":"20230509110043"}
             */

            @JsonProperty("OFFER_INFO")
            private OfferInfo offerInfo;

            @JsonIgnoreProperties(ignoreUnknown = true)
            @lombok.Data
            public static class OfferInfo implements Serializable {
                /**
                 * CUST_ID : 93814335**654871
                 * CREATE_OP_ID : 820077
                 * VALID_DATE : 20230509110041
                 * UNI_CHANNEL_NAME : *****有限公司
                 * IS_MAIN : 0
                 * APPLY_CHANNEL :
                 * OFFER_TYPE : 10
                 * CHANNEL_CODE : 1012
                 * CAN_UN_SUB : 1
                 * IS_BUNDLE : 1
                 * MGMT_DISTRICT : 943
                 * CREATE_ORG_ID : 31017002
                 * EXPIRE_DATE : 20250501000000
                 * PACKAGE_FLAG : 0
                 * ACCESS_NUM : 19514****8
                 * DONE_DATE : 20230509110043
                 * DONE_CODE : 5620866517
                 * OFFER_NAME : [62201632]视频彩铃-9.9元美播版（含30GB抖音快手定向流量+咪咕音乐白金会员）
                 * APPLY_CHANNEL_NAME :
                 * MajorType : 1
                 * OFFER_TYPE_DESC : 个人预缴类商品（活动）
                 * SUBBER_ID : 433032698998403
                 * OFFER_ID : 306100013242
                 * SO_ORG_ID : 31017002
                 * UNI_CHANNEL_ID : 2931931002230400112
                 * CHANNEL_NAME : 省内电渠
                 * MGMT_COUNTY : 943
                 * BRAND : 0
                 * CREATE_DATE : 20230509110043
                 * SRC_SYSTEM_TYPE :
                 * OUT_OFFER_ID : 2023130000045948
                 * OFFER_INS_ID : 433052792193603
                 * SUBSCRIBER_INS_ID : 433032698998403
                 * IS_ROOT : 1
                 * SO_OP_ID : 820077
                 * SO_DATE : 20230509110043
                 */

                @JsonProperty("CUST_ID")
                private String custId;
                @JsonProperty("CREATE_OP_ID")
                private String createOpId;
                @JsonProperty("VALID_DATE")
                private String validDate;
                @JsonProperty("UNI_CHANNEL_NAME")
                private String uniChannelName;
                @JsonProperty("IS_MAIN")
                private String isMain;
                @JsonProperty("APPLY_CHANNEL")
                private String applyChannel;
                @JsonProperty("OFFER_TYPE")
                private String offerType;
                @JsonProperty("CHANNEL_CODE")
                private String channelCode;
                @JsonProperty("CAN_UN_SUB")
                private String canUnSub;
                @JsonProperty("IS_BUNDLE")
                private String isBundle;
                @JsonProperty("MGMT_DISTRICT")
                private String mgmtDistrict;
                @JsonProperty("CREATE_ORG_ID")
                private String createOrgId;
                @JsonProperty("EXPIRE_DATE")
                private String expireDate;
                @JsonProperty("PACKAGE_FLAG")
                private String packageFlag;
                @JsonProperty("ACCESS_NUM")
                private String accessNum;
                @JsonProperty("DONE_DATE")
                private String doneDate;
                @JsonProperty("DONE_CODE")
                private String doneCode;
                @JsonProperty("OFFER_NAME")
                private String offerName;
                @JsonProperty("APPLY_CHANNEL_NAME")
                private String applyChannelName;
                @JsonProperty("MajorType")
                private String MajorType;
                @JsonProperty("OFFER_TYPE_DESC")
                private String offerTypeDesc;
                @JsonProperty("SUBBER_ID")
                private String subberId;
                @JsonProperty("OFFER_ID")
                private String offerId;
                @JsonProperty("SO_ORG_ID")
                private String soOrgId;
                @JsonProperty("UNI_CHANNEL_ID")
                private String uniChannelId;
                @JsonProperty("CHANNEL_NAME")
                private String channelName;
                @JsonProperty("MGMT_COUNTY")
                private String mgmtCounty;
                @JsonProperty("BRAND")
                private String brand;
                @JsonProperty("CREATE_DATE")
                private String createDate;
                @JsonProperty("SRC_SYSTEM_TYPE")
                private String srcSystemType;
                @JsonProperty("OUT_OFFER_ID")
                private String outOfferId;
                @JsonProperty("OFFER_INS_ID")
                private String offerInsId;
                @JsonProperty("SUBSCRIBER_INS_ID")
                private String subscriberInsId;
                @JsonProperty("IS_ROOT")
                private String isRoot;
                @JsonProperty("SO_OP_ID")
                private String soOpId;
                @JsonProperty("SO_DATE")
                private String soDate;
            }
        }
    }


}
