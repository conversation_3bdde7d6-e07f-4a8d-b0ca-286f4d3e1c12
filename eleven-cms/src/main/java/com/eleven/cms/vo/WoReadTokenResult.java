package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2022-9-20 10:59:49
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WoReadTokenResult {

    public static final String CODE_OK = "0000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    /*0000代表正常，9999代表异常*/
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("key")
    private String key;
    @JsonProperty("key_type")
    private String key_type;
    @JsonProperty("expires_in")
    private Integer expires_in;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static WoReadTokenResult fail() {
        return WoReadTokenResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }
}
