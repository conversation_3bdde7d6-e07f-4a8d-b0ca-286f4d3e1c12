package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-08-12 15:39
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XinjiangYidongResult {
    private String respCode;
    private String respDesc;

    private static final String SUC_CODE = "0";
    public static final XinjiangYidongResult FAIL_RESULT = new XinjiangYidongResult("9999", "通讯失败");

    public boolean isOk() {
        return SUC_CODE.equals(respCode);
    }

}
