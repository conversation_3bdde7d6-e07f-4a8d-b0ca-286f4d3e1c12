package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2020/12/30 14:09
 * Desc:联通沃音乐开放平台响应封装(已订包月产品)
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiantongUserRingDepotResp extends LiantongResp {

    public static LiantongUserRingDepotResp ERROR_RESP = new LiantongUserRingDepotResp(RETURN_CODE_ERROR,DESCRIPTION_ERROR,null,null,null);

    public LiantongUserRingDepotResp(String returnCode, String description, String orderId, String url, List<UserDepotList> userDepotList) {
        super(returnCode, description, orderId, url);
        this.userDepotList = userDepotList;
    }

    @JsonProperty("userDepotList")
    private List<UserDepotList> userDepotList;

    public List<UserDepotList> getUserDepotList() {
        return userDepotList;
    }

    public void setUserDepotList(List<UserDepotList> userDepotList) {
        this.userDepotList = userDepotList;
    }

    public static LiantongUserRingDepotResp error() {
        return ERROR_RESP;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserDepotList {
        /**
         * ringId : 80768000202011189336520
         * ringName : 如果我学了电焊是不是就能让你眼前一亮
         * ringType : 1
         * type : 1
         * settingFlag : 1
         * orderTime : 20201230103702
         * ringIndex : 1556618
         */

        @JsonProperty("ringId")
        private String ringId;
        @JsonProperty("ringName")
        private String ringName;
        @JsonProperty("ringType")
        private String ringType;
        @JsonProperty("type")
        private String type;
        @JsonProperty("settingFlag")
        private String settingFlag;
        @JsonProperty("orderTime")
        private String orderTime;
        @JsonProperty("ringIndex")
        private int ringIndex;

        public String getRingId() {
            return ringId;
        }

        public void setRingId(String ringId) {
            this.ringId = ringId;
        }

        public String getRingName() {
            return ringName;
        }

        public void setRingName(String ringName) {
            this.ringName = ringName;
        }

        public String getRingType() {
            return ringType;
        }

        public void setRingType(String ringType) {
            this.ringType = ringType;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getSettingFlag() {
            return settingFlag;
        }

        public void setSettingFlag(String settingFlag) {
            this.settingFlag = settingFlag;
        }

        public String getOrderTime() {
            return orderTime;
        }

        public void setOrderTime(String orderTime) {
            this.orderTime = orderTime;
        }

        public int getRingIndex() {
            return ringIndex;
        }

        public void setRingIndex(int ringIndex) {
            this.ringIndex = ringIndex;
        }
    }
}
