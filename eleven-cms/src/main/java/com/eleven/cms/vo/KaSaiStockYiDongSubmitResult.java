package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 10:51
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KaSaiStockYiDongSubmitResult implements Serializable {
    public static final Integer  CODE_OK = 200;
    /**
     * code : 200
     * data : {"orderNo":"gkldkw4p5","id":25646909,"retryOrder":false}
     * message : null
     * cmccCode : null
     */

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("message")
    private String message;
    @JsonProperty("cmccCode")
    private Object cmccCode;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * orderNo : gkldkw4p5
         * id : 25646909
         * retryOrder : false
         */

        @JsonProperty("orderNo")
        private String orderNo;
        @JsonProperty("id")
        private Integer id;
        @JsonProperty("retryOrder")
        private Boolean retryOrder;
    }
}
