package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-06-07 14:49
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class KuaimaShanxiResult {
    public static final Integer CODE_OK = 0;
    public static final Integer CODE_FAIL = 1;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("time_stamp")
    private String timeStamp;
    @JsonProperty("message")
    private String message;
    @JsonProperty("biz_content")
    private String bizContent;
    @JsonProperty("sign")
    private String sign;
    private KuaimaShanxiBizContent bizContentResult;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static KuaimaShanxiResult fail() {
        return KuaimaShanxiResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class KuaimaShanxiBizContent{
        @JsonProperty("order_id")
        private String orderId;
        @JsonProperty("handle_date")
        private String handleDate;
        @JsonProperty("pass_flag")
        private String passFlag;
        @JsonProperty("order_msg")
        private String orderMsg;
    }
}
