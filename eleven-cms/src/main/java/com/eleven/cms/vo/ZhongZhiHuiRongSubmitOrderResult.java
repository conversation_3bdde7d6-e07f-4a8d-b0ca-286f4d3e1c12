package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/16 16:21
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ZhongZhiHuiRongSubmitOrderResult implements Serializable {
    public static final int  CODE_OK =200;
    /**
     * msg : {"status":"success","code":"00000","message":"success","orderId":"121f4c42146548d797ee785f6ef6eb17","province":"四川"}
     * code : 200
     */

    @JsonProperty("msg")
    private String msg;
    @JsonProperty("code")
    private int code;
    public boolean isOK(){
        return CODE_OK==this.getCode();
    }
}
