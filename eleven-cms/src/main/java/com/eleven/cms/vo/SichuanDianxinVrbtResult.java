package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 四川电信视频彩铃返回封装
 *
 * @author: cai lei
 * @create: 2021-12-14 14:57
 */

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SichuanDianxinVrbtResult {

    private static final String CODE_OK = "0";
    private static final String CODE_FAIL = "-1";

    @JsonProperty("head")
    private Head head;
    @JsonProperty("body")
    private Body body;

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Head {
        @JsonProperty("cloudSessionID")
        private String cloudSessionID;
        @JsonProperty("requestTime")
        private String requestTime;
        @JsonProperty("respTime")
        private String respTime;
        @JsonProperty("usdTime")
        private String usdTime;
        @JsonProperty("ticket")
        private String ticket;
        @JsonProperty("respCode")
        private String respCode;
        @JsonProperty("respMsg")
        private String respMsg;
        @JsonProperty("targetCenter")
        private String targetCenter;
        @JsonProperty("targetMethod")
        private String targetMethod;
        @JsonProperty("channelCode")
        private String channelCode;
        @JsonProperty("userIp")
        private String userIp;
        @JsonProperty("usdCached")
        private String usdCached;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Body {
        @JsonProperty("retCode")
        private String retCode;
        @JsonProperty("retMsg")
        private String retMsg;
        @JsonProperty("message")
        private Message message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Message {
        @JsonProperty("offerCombIds")
        private String offerCombIds;
        @JsonProperty("webOrder")
        private String webOrder;
        @JsonProperty("orderStatus")
        private String orderStatus;
        @JsonProperty("orderDate")
        private String orderDate;

    }

    public static SichuanDianxinVrbtResult fail() {
        Head failHead = Head.builder().respCode(CODE_FAIL).build();
        Body failBody = Body.builder().retCode(CODE_FAIL).build();
        return SichuanDianxinVrbtResult.builder().head(failHead).body(failBody).build();
    }

    public boolean isOk() {
        return CODE_OK.equals(getHead().getRespCode()) && CODE_OK.equals(getBody().getRetCode());
    }

//    public static void main(String[] args) throws JsonProcessingException {
//        String json = "{\n" +
//                "\t\"head\": {\n" +
//                "\t\t\"requestTime\": null,\n" +
//                "\t\t\"respTime\": null,\n" +
//                "\t\t\"usdTime\": null,\n" +
//                "\t\t\"ticket\": null,\n" +
//                "\t\t\"respCode\": 0,\n" +
//                "\t\t\"respMsg\": \"成功\",\n" +
//                "\t\t\"targetCenter\": null,\n" +
//                "\t\t\"targetMethod\": null,\n" +
//                "\t\t\"channelCode\": null,\n" +
//                "\t\t\"userIp\": \"\",\n" +
//                "\t\t\"usdCached\": null,\n" +
//                "\t\t\"token\": null,\n" +
//                "\t\t\"cloudSessionID\": null\n" +
//                "\t},\n" +
//                "\t\"body\": {\n" +
//                "\t\t\"retCode\": \"0\",\n" +
//                "\t\t\"message\": null,\n" +
//                "\t\t\"retMsg\": \"成功\"\n" +
//                "\t}\n" +
//                "}";
//
//        ObjectMapper objectMapper = new ObjectMapper();
//        SichuanDianxinVrbtResult sichuanDianxinVrbtResult = objectMapper.readValue(json, SichuanDianxinVrbtResult.class);
//        System.out.println(sichuanDianxinVrbtResult.isOk());
//
//        System.out.println(SichuanDianxinVrbtResult.fail().isOk());
//    }
}
