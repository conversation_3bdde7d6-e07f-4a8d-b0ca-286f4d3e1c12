package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/12 16:58
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoResult implements Serializable {
    public static final Integer  CODE_OK =200;
    /**
     * success : true
     * code : 200
     * message : 请求响应成功!
     * data : {"sign":"3fd16c3af1efd19d25e7c4490307cd34","encryptedData":"JqVkolU17YHGSDMcVgSbmS2InEEeimGq2Pi3nkz09VA=","timestamp":1671434138633,"channelId":4}
     */

    @JsonProperty("success")
    private Boolean success;
    @JsonProperty("code")
    private Integer code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private Data data;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode()) && success==true && this.getData()!=null;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * sign : 3fd16c3af1efd19d25e7c4490307cd34
         * encryptedData : JqVkolU17YHGSDMcVgSbmS2InEEeimGq2Pi3nkz09VA=
         * timestamp : 1671434138633
         * channelId : 4
         */

        @JsonProperty("sign")
        private String sign;
        @JsonProperty("encryptedData")
        private String encryptedData;
        @JsonProperty("timestamp")
        private long timestamp;
        @JsonProperty("channelId")
        private int channelId;
    }
}
