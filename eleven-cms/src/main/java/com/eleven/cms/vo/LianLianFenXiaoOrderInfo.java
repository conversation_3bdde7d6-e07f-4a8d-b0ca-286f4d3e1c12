package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/23 10:40
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoOrderInfo implements Serializable {

    /**
     * customerName : 王大力
     * customerPhoneNumber : 17630353825
     * idCard :
     * channelOrderId : C221206000034000004
     * thirdPartyOrderNo : test_202212060001
     * quantity : 1
     * codeAmount : 1
     * productId : 3288598
     * detailUrl : https://qd.llzby.vip/s/KahZFMeJ
     * qrCodeImgUrl : https://debug.lianlianlvyou.com/n/item/qrcode/4596559/6961ce48005341be213621d5017fcdc9.png
     * memo :
     * logisticsStatusList : [{"orderId":"S221206000034","expressStatus":1}]
     * orderDetailList : [{"orderId":"S221206000034","corderId":"S221206000034","itemId":4595962,"code":"***************","qrCodeImgUrl":"https://debug.lianlianlvyou.com/n/item/qrcode/4596559/6961ce48005341be213621d5017fcdc9.png","status":412,"bookingType":0,"refundDate":"2022-12-06 19:45:15","importTime":"2022-12-06 19:37:38","address":""}]
     */

    @JsonProperty("customerName")
    private String customerName;
    @JsonProperty("customerPhoneNumber")
    private String customerPhoneNumber;
    @JsonProperty("idCard")
    private String idCard;
    @JsonProperty("channelOrderId")
    private String channelOrderId;
    @JsonProperty("thirdPartyOrderNo")
    private String thirdPartyOrderNo;
    @JsonProperty("quantity")
    private int quantity;
    @JsonProperty("codeAmount")
    private int codeAmount;
    @JsonProperty("productId")
    private int productId;
    @JsonProperty("detailUrl")
    private String detailUrl;
    @JsonProperty("qrCodeImgUrl")
    private String qrCodeImgUrl;
    @JsonProperty("memo")
    private String memo;
    @JsonProperty("logisticsStatusList")
    private List<LogisticsStatusList> logisticsStatusList;
    @JsonProperty("orderDetailList")
    private List<OrderDetailList> orderDetailList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class LogisticsStatusList implements Serializable {
        /**
         * orderId : S221206000034
         * expressStatus : 1
         */

        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("expressStatus")
        private int expressStatus;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class OrderDetailList implements Serializable {
        /**
         * orderId : S221206000034
         * corderId : S221206000034
         * itemId : 4595962
         * code : ***************
         * qrCodeImgUrl : https://debug.lianlianlvyou.com/n/item/qrcode/4596559/6961ce48005341be213621d5017fcdc9.png
         * status : 412
         * bookingType : 0
         * refundDate : 2022-12-06 19:45:15
         * importTime : 2022-12-06 19:37:38
         * address :
         */

        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("corderId")
        private String corderId;
        @JsonProperty("itemId")
        private int itemId;
        @JsonProperty("code")
        private String code;
        @JsonProperty("qrCodeImgUrl")
        private String qrCodeImgUrl;
        @JsonProperty("status")
        private int status;
        @JsonProperty("bookingType")
        private int bookingType;
        @JsonProperty("refundDate")
        private String refundDate;
        @JsonProperty("importTime")
        private String importTime;
        @JsonProperty("address")
        private String address;
    }
}
