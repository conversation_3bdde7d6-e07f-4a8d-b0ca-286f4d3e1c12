package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-06-21 11:40
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LbtxSichuanResult {
    private String result;
    private String message;
    private Integer statusCode;

    private static final Integer SUC_CODE = 200;

    public boolean isOk() {
        return SUC_CODE.equals(statusCode);
    }

    public static final LbtxSichuanResult FAIL_RESULT = new LbtxSichuanResult("", "通讯失败", 999);


}
