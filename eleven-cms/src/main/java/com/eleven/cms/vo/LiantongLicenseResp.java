package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LiantongLicenseResp implements Serializable {


    /**
     * resultCode : SUCCESS
     * startTime : 2022-04-25 11:54:22
     * endTime : 2022-04-26 12:24:12
     * resultMsg : 成功
     */

    @JsonProperty("resultCode")
    private String resultCode;
    @JsonProperty("startTime")
    private String startTime;
    @JsonProperty("endTime")
    private String endTime;
    @JsonProperty("resultMsg")
    private String resultMsg;
}
