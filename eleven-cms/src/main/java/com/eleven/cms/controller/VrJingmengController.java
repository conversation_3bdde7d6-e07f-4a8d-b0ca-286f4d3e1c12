package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.VrJingmeng;
import com.eleven.cms.service.IVrJingmengService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: cms_vr_jingmeng
 * @Author: jeecg-boot
 * @Date:   2024-03-18
 * @Version: V1.0
 */
@Api(tags="cms_vr_jingmeng")
@RestController
@RequestMapping("/cms/vrJingmeng")
@Slf4j
public class VrJingmengController extends JeecgController<VrJingmeng, IVrJingmengService> {
	@Autowired
	private IVrJingmengService vrJingmengService;

	/**
	 * 分页列表查询
	 *
	 * @param vrJingmeng
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_vr_jingmeng-分页列表查询")
	@ApiOperation(value="cms_vr_jingmeng-分页列表查询", notes="cms_vr_jingmeng-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(VrJingmeng vrJingmeng,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<VrJingmeng> queryWrapper = QueryGenerator.initQueryWrapper(vrJingmeng, req.getParameterMap());
		Page<VrJingmeng> page = new Page<VrJingmeng>(pageNo, pageSize);
		IPage<VrJingmeng> pageList = vrJingmengService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param vrJingmeng
	 * @return
	 */
	//@AutoLog(value = "cms_vr_jingmeng-添加")
	@ApiOperation(value="cms_vr_jingmeng-添加", notes="cms_vr_jingmeng-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody VrJingmeng vrJingmeng) {
		vrJingmengService.save(vrJingmeng);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param vrJingmeng
	 * @return
	 */
	//@AutoLog(value = "cms_vr_jingmeng-编辑")
	@ApiOperation(value="cms_vr_jingmeng-编辑", notes="cms_vr_jingmeng-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody VrJingmeng vrJingmeng) {
		vrJingmengService.updateById(vrJingmeng);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_vr_jingmeng-通过id删除")
	@ApiOperation(value="cms_vr_jingmeng-通过id删除", notes="cms_vr_jingmeng-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		vrJingmengService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_vr_jingmeng-批量删除")
	@ApiOperation(value="cms_vr_jingmeng-批量删除", notes="cms_vr_jingmeng-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.vrJingmengService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_vr_jingmeng-通过id查询")
	@ApiOperation(value="cms_vr_jingmeng-通过id查询", notes="cms_vr_jingmeng-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		VrJingmeng vrJingmeng = vrJingmengService.getById(id);
		if(vrJingmeng==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(vrJingmeng);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param vrJingmeng
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, VrJingmeng vrJingmeng) {
        return super.exportXls(request, vrJingmeng, VrJingmeng.class, "cms_vr_jingmeng");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, VrJingmeng.class);
    }

}
