package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.dto.YinglouUserDto;
import com.eleven.cms.entity.YinglouUser;
import com.eleven.cms.service.IYinglouUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

import static com.eleven.cms.util.BizConstant.SQL_LIMIT_ONE;

/**
 * @Description: 影楼用户表
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
@Api(tags="影楼用户表")
@RestController
@RequestMapping("/cms/yinglouUser")
@Slf4j
public class YinglouUserController extends JeecgController<YinglouUser, IYinglouUserService> {
	@Autowired
	private IYinglouUserService yinglouUserService;

	/**
	 * 分页列表查询
	 *
	 * @param yinglouUser
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "影楼用户表-分页列表查询")
	@ApiOperation(value="影楼用户表-分页列表查询", notes="影楼用户表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(YinglouUser yinglouUser,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<YinglouUser> queryWrapper = QueryGenerator.initQueryWrapper(yinglouUser, req.getParameterMap());
		Page<YinglouUser> page = new Page<YinglouUser>(pageNo, pageSize);
		IPage<YinglouUser> pageList = yinglouUserService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param yinglouUser
	 * @return
	 */
	//@AutoLog(value = "影楼用户表-添加")
	@ApiOperation(value="影楼用户表-添加", notes="影楼用户表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody YinglouUser yinglouUser) {
		boolean user=yinglouUserService.lambdaQuery().select(YinglouUser::getNumberId).eq(YinglouUser::getUserName,yinglouUser.getUserName()).count()>0;
		if(user){
			return Result.error("用户已创建!");
		}
		if(yinglouUser.getLevels().equals(0)){
			int userCount=yinglouUserService.lambdaQuery().eq(YinglouUser::getLevels,yinglouUser.getLevels()).count();
			yinglouUser.setNumberId("A00"+(userCount+1));
		}else{
			int userCount=yinglouUserService.lambdaQuery().eq(YinglouUser::getLevels,yinglouUser.getLevels()).eq(YinglouUser::getMajorUser,yinglouUser.getMajorUser()).count();
			if(yinglouUser.getLevels()==1){
				yinglouUser.setNumberId("B00"+(userCount+1));
			}else{
				yinglouUser.setNumberId("C00"+(userCount+1));
			}
		}
		if(StringUtils.isEmpty(yinglouUser.getUserAlias())){
			yinglouUser.setUserAlias(yinglouUser.getUserName());
		}
		if(!StringUtils.isEmpty(yinglouUser.getMajorUser())){
			boolean isMajorUser=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,yinglouUser.getMajorUser()).eq(YinglouUser::getLevels,((int)yinglouUser.getLevels()-1)).count()<=0;
			if(isMajorUser){
				return Result.error("上级账号选择错误!");
			}
		}

		String sign = DigestUtils.md5DigestAsHex((yinglouUser.getUserName()+yinglouUser.getPassWord()).getBytes(StandardCharsets.UTF_8));
		yinglouUser.setPassWord(sign);
		yinglouUserService.save(yinglouUser);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param yinglouUser
	 * @return
	 */
	//@AutoLog(value = "影楼用户表-编辑")
	@ApiOperation(value="影楼用户表-编辑", notes="影楼用户表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody YinglouUser yinglouUser) {
		YinglouUser user=yinglouUserService.lambdaQuery().select(YinglouUser::getNumberId).eq(YinglouUser::getId,yinglouUser.getId()).orderByDesc(YinglouUser::getCreateTime).last(SQL_LIMIT_ONE).one();
		if(user==null){
			return Result.error("用户已删除!");
		}
		if(StringUtils.isEmpty(yinglouUser.getUserAlias())){
			yinglouUser.setUserAlias(yinglouUser.getUserName());
		}
		if(!StringUtils.isEmpty(yinglouUser.getMajorUser())){
			boolean isMajorUser=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,yinglouUser.getMajorUser()).eq(YinglouUser::getLevels,((int)yinglouUser.getLevels()-1)).count()<=0;
			if(isMajorUser){
				return Result.error("上级账号选择错误!");
			}
		}
		yinglouUser.setNumberId(user.getNumberId());
		String sign = DigestUtils.md5DigestAsHex((yinglouUser.getUserName()+yinglouUser.getPassWord()).getBytes(StandardCharsets.UTF_8));
		yinglouUser.setPassWord(sign);
		yinglouUserService.updateById(yinglouUser);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "影楼用户表-通过id删除")
	@ApiOperation(value="影楼用户表-通过id删除", notes="影楼用户表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		yinglouUserService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "影楼用户表-批量删除")
	@ApiOperation(value="影楼用户表-批量删除", notes="影楼用户表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.yinglouUserService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "影楼用户表-通过id查询")
	@ApiOperation(value="影楼用户表-通过id查询", notes="影楼用户表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		YinglouUser yinglouUser = yinglouUserService.getById(id);
		if(yinglouUser==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(yinglouUser);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param yinglouUser
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, YinglouUser yinglouUser) {
        return super.exportXls(request, yinglouUser, YinglouUser.class, "影楼用户表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, YinglouUser.class);
    }
	/**
	 * 业务列表查询
	 * @return
	 */
	@RequestMapping(value = "/queryMajorUser", method = RequestMethod.GET)
	public Result<List<YinglouUserDto>> queryMajorUser() {
		Result<List<YinglouUserDto>> result = new Result<>();
		List<YinglouUserDto> list = yinglouUserService.queryMajorUser();
		result.setResult(list);
		result.setSuccess(true);
		return result;
	}
}
