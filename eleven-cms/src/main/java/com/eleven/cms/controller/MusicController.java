package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.entity.Music;
import com.eleven.cms.service.IMusicService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 歌曲
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
@Api(tags="歌曲")
@RestController
@RequestMapping("/cms/music")
@Slf4j
public class MusicController extends JeecgController<Music, IMusicService> {
	@Autowired
	private IMusicService musicService;
	
	/**
	 * 分页列表查询
	 *
	 * @param music
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "歌曲-分页列表查询")
	@ApiOperation(value="歌曲-分页列表查询", notes="歌曲-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(Music music,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<Music> queryWrapper = QueryGenerator.initQueryWrapper(music, req.getParameterMap());
		Page<Music> page = new Page<Music>(pageNo, pageSize);
		IPage<Music> pageList = musicService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param music
	 * @return
	 */
	//@AutoLog(value = "歌曲-添加")
	@ApiOperation(value="歌曲-添加", notes="歌曲-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody Music music) {
		musicService.save(music);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param music
	 * @return
	 */
	//@AutoLog(value = "歌曲-编辑")
	@ApiOperation(value="歌曲-编辑", notes="歌曲-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody Music music) {
		musicService.updateById(music);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "歌曲-通过id删除")
	@ApiOperation(value="歌曲-通过id删除", notes="歌曲-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		musicService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "歌曲-批量删除")
	@ApiOperation(value="歌曲-批量删除", notes="歌曲-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.musicService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

     /**
      *   通过id删除
      *
      * @param id
      * @return
      */
     //@AutoLog(value = "歌曲-通过id抓取图片")
     @ApiOperation(value="歌曲-通过id抓取图片", notes="歌曲-通过id抓取图片")
     @GetMapping(value = "/fetchImg")
     public Result<?> fetchImg(@RequestParam(name="id",required=true) String id) {
         musicService.updateProductById(id);
         return Result.ok("已抓取图片!");
     }

     /**
      *  批量删除
      *
      * @param ids
      * @return
      */
     //@AutoLog(value = "歌曲-批量抓取图片")
     @ApiOperation(value="歌曲-批量抓取图片", notes="歌曲-批量抓取图片")
     @GetMapping(value = "/fetchImgBatch")
     public Result<?> fetchImgBatch(@RequestParam(name="ids",required=true) String ids) {
         this.musicService.updateProductByIdList(Arrays.asList(ids.split(",")));
         return Result.ok("已批量抓取图片!");
     }
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "歌曲-通过id查询")
	@ApiOperation(value="歌曲-通过id查询", notes="歌曲-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		Music music = musicService.getById(id);
		if(music==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(music);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param music
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Music music) {
        return super.exportXls(request, music, Music.class, "歌曲");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return this.importExcel(request, response, Music.class);
    }

     /**
      * 通过excel导入数据(可以根据版权id更新)
      *
      * @param request
      * @param response
      * @param clazz
      * @return
      */
     @Override
     public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response, Class<Music> clazz) {
         MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
         Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
         for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
             MultipartFile file = entity.getValue();// 获取上传文件对象
             ImportParams params = new ImportParams();
             params.setTitleRows(2);
             params.setHeadRows(1);
             params.setNeedSave(true);
             try {
                 List<Music> list = ExcelImportUtil.importExcel(file.getInputStream(), clazz, params);
                 //update-begin-author:taoyan date:20190528 for:批量插入数据
                 long start = System.currentTimeMillis();
                 //musicService.saveBatch(list);
                 list.forEach(musicService::fillProductInfo);
                 musicService.saveOrUpdateBatch(list);
                 //400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
                 //1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
                 log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                 //update-end-author:taoyan date:20190528 for:批量插入数据
                 return Result.ok("文件导入成功！数据行数：" + list.size());
             } catch (Exception e) {
                 log.error(e.getMessage(), e);
                 return Result.error("文件导入失败:" + e.getMessage());
             } finally {
                 try {
                     file.getInputStream().close();
                 } catch (IOException e) {
                     e.printStackTrace();
                 }
             }
         }
         return Result.error("文件导入失败！");
     }

}
