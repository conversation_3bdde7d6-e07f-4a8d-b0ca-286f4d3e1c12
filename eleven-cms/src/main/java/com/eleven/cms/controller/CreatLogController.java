package com.eleven.cms.controller;

import com.alipay.api.internal.util.codec.Base64;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.entity.*;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.shanghaimobile.util.RandomUtil;
import com.eleven.cms.util.AESCodec;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* @Description: 生成日志
* @Author: jeecg-boot
* @Date:   2021-9-1 15:55:28
* @Version: V1.0
*/
@RestController
@RequestMapping("/cms")
@Slf4j
public class CreatLogController {

    @Autowired
    private ICreateLogService createLogService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IDataNotifyLogService dataNotifyLogService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private ICreateLogConfigService createLogConfigService;


    /**
     * 日志生成模板
     *
     * @param id
     * @return
     */
    @RequestMapping("/createLog/createLog/template")
    public ModelAndView template(@RequestParam(name="id",required=true) String id) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        ModelAndView modelAndView = new ModelAndView();

        modelAndView.addObject("list",createLogService.createLog(id));
        Subscribe subscribe = subscribeService.getById(id);

        //模板中的批次号
        String createTime = df.format(subscribe.getCreateTime());
        subscribe.setDeviceInfo(createTime.substring(5,7) + createTime.substring(8,10) + "8081" + IdWorker.getId());
        //运营商
        if(subscribe.getIsp().equals("1")){
            subscribe.setIsp("移动");
        }else if(subscribe.getIsp().equals("3")){
            subscribe.setIsp("联通");
        }else if(subscribe.getIsp().equals("4")){
            subscribe.setIsp("电信");
        }
        modelAndView.addObject("openTime",df.format(subscribe.getOpenTime()));
        modelAndView.addObject("channelCode",subscribe.getChannel());
        modelAndView.addObject("timestamp",dateFormat.format(subscribe.getOpenTime()));
        modelAndView.addObject("signature", IdWorker.get32UUID());
        modelAndView.addObject("loginType", "3");
        modelAndView.addObject("msisdn", subscribe.getMobile());
        modelAndView.addObject("subscribe", subscribe);
        modelAndView.setViewName("/unicom/createLog");
        return modelAndView;
    }

    /**
     * 日志查询模板
     *
     * @param id
     * @return
     */
    @RequestMapping("/createLog/searchLog/template")
    public ModelAndView searchLogTemplate(@RequestParam(name="id",required=true) String id) {
        return createLogService.searchLog(id);
    }
    /**
     * 原始日志查询模板
     *
     * @param id
     * @return
     */
    @RequestMapping("/createLog/searchFirsthandLog/template")
    public ModelAndView searchFirsthandLogTemplate(@RequestParam(name="id",required=true) String id) {
        return createLogService.searchFirsthandLog(id);
    }

    /**
     * 日志生成模板
     *
     * @param id
     * @return
     */
    @RequestMapping("/createLog/qycl/template")
    public ModelAndView qyclCreateLog(@RequestParam(name="id",required=true) String id) throws Exception {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/unicom/createLogQycl");

        DataNotifyLog dataNotifyLog = dataNotifyLogService.getById(id);

        //手机号
        modelAndView.addObject("mobile",dataNotifyLog.getMobile());
        //省份城市
        MobileRegionResult mobileRegionResult = mobileRegionService.query(dataNotifyLog.getMobile());
        if (mobileRegionResult != null) {
            modelAndView.addObject("province",mobileRegionResult.getProvince());
            modelAndView.addObject("city",mobileRegionResult.getCity());
        }
        //data
        byte[] k = "8BD50C0320F874EAE05319AD190AA34F".getBytes();
        String keydef = Base64.encodeBase64String(k);
        String password = RandomStringUtils.randomAlphanumeric(20);
        String data = AESCodec.encrypt(password, keydef);
        modelAndView.addObject("data",data);
        //创建时间
        modelAndView.addObject("createTime",df.format(new Date(dataNotifyLog.getCreateTime().getTime()-1000*12)));
        return modelAndView;
    }

    /**
     * 短信日志生成模板
     *
     * @param id
     * @return
     */
    @RequestMapping("/createLog/table/template")
    public ModelAndView createLogTable(@RequestParam(name="id",required=true) String id) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/unicom/createLogTable");
        Subscribe subscribe = subscribeService.getById(id);
        String channel = subscribe.getChannel();
        String remark = "";
        //模板中的批次号
        String createTime = df.format(subscribe.getCreateTime());
        subscribe.setDeviceInfo(createTime.substring(5,7) + createTime.substring(8,10) + "8081" + IdWorker.getId());
        //运营商
        if(subscribe.getIsp().equals("1")){
            subscribe.setIsp("移动");
        }else if(subscribe.getIsp().equals("3")){
            subscribe.setIsp("联通");
        }else if(subscribe.getIsp().equals("4")){
            subscribe.setIsp("电信");
        }
        //短信模板
        SmsModel smsModel = smsModelService.lambdaQuery().eq(SmsModel::getChannel, channel).eq(SmsModel::getServiceType, 5).one();
        if(smsModel != null){
            remark = smsModel.getSmsModel();
        }else{
                    if(channel.equals("014X04C")
                || channel.equals("014X04F")
                || channel.equals("014X04D")
                || channel.equals("014X04E")
                || channel.equals("014X04G")
                || channel.equals("014X09P")
                || channel.equals("014X09T")){
            remark = "【麦禾视频彩铃】您的验证码" + RandomUtils.randomCode() + "，该验证码5分钟内有效，请勿泄漏于他人！";
        }else if("00210Q6".equals(subscribe.getChannel())
                ||"00210Y0".equals(subscribe.getChannel())
                ||"00210XZ".equals(subscribe.getChannel())){
            remark = "【休闲集社】尊敬的用户，您本次办理咪咕音乐北岸唐唱音乐包赠送的会员权益，请到微信公众号“休闲集社”点击权益领取-登录订购的手机号领取权益。如有疑问可致电联系客服4000099971。";
        }else if("002112S".equals(subscribe.getChannel())
                || "002112O".equals(subscribe.getChannel())
                || "002112T".equals(subscribe.getChannel())){
            remark = "【爱休闲集社】尊敬的用户，您本次办理咪咕音乐音乐全曲包-七彩音乐包赠送的会员权益，请到微信公众号“爱休闲集社”点击权益领取-登录订购的手机号领取权益。如有疑问可致电联系客服4000099971。";
        }else if("00210U2".equals(subscribe.getChannel())
                || "00210PP".equals(subscribe.getChannel())){
            remark = "【白金会员】尊敬的用户，尊敬的用户，您本次办理咪咕音乐白金会员包赠送的会员权益，请到微信公众号“爱休闲集社”点击权益领取-登录订购的手机号领取权益。如有疑问可致电联系客服4000099971。";
        }else if("0021107".equals(subscribe.getChannel())){
            remark = "【七彩歌曲】尊敬的用户，您本次办理咪咕音乐七彩歌曲音乐包赠送的会员权益，请到微信公众号“权益公社\"点击我的-权益兑换-登录订购的手机号领取权益。如有疑问可致电联系客服4001897518。";
        }else if("00210VO".equals(subscribe.getChannel())){
            remark = "【七彩歌曲】尊敬的用户，您本次办理咪咕音乐七彩歌曲10元包赠送的音乐会员权益，请到公众号“休闲集社”点击会员领取-登录订购的手机号领取权益。活动期间内，续订用户每月均可领取。如有疑问可致电联系客服4000099971。";
        }else if("00210H0".equals(subscribe.getChannel())
                || "00210T7".equals(subscribe.getChannel())){
            remark = "多彩美音汇】尊敬的用户，您本次办理咪咕音乐白金会员赠送的音乐会员权益，请到微信公众号“多彩美音汇”点击会员领取-登录订购的手机号领取权益。如有疑问可致电联系客服4001331008。";
        }else if("00210W5".equals(subscribe.getChannel())
                || "002110A".equals(subscribe.getChannel())
                || "0021109".equals(subscribe.getChannel())){
            remark = "【经典音乐】尊敬的用户，您本次办理咪咕音乐经典音乐包赠送的会员权益，请到公众号“光明新媒体”点击会员领取-登录订购的手机号领取权益。如有疑问可致电联系客户4000099971。";
        }else {
            remark = "【爱休闲集社】尊敬的用户，您本次办理咪咕音乐北岸唐唱音乐包赠送的会员权益，请到微信公众号“爱休闲集社”点击权益领取-登录订购的手机号领取权益。如有疑问可致电联系客服4000099971。";
        }
        }

        subscribe.setRemark(remark);
        modelAndView.addObject("subscribe", subscribe);
        return modelAndView;
    }

    @RequestMapping("/subscribe/queryLoginLog")
    @RequiresPermissions("subscribe:queryLoginLog")
    public ModelAndView queryLoginLog() {

        ModelAndView modelAndView = new ModelAndView();

        //查询生成配置
        CreateLogConfig createLogConfig = createLogConfigService.list().get(0);
        if(createLogConfig != null){
            modelAndView.addObject("mobile", createLogConfig.getMobile());
            modelAndView.setViewName("/unicom/createLogin");
            modelAndView.addObject("openTime",createLogConfig.getOpenTime());
        }
        return modelAndView;
    }

    /**
     * 短信验证码查询模板
     *
     * @param id
     * @return
     */
    @RequestMapping("/createLog/searchSmsCodeLog/template")
    public ModelAndView searchSmsCodeLogTemplate(@RequestParam(name="id",required=true) String id) {
        return createLogService.searchSmsCodeLog(id);
    }

    /**
     * 日志查询模板
     *
     * @param id
     * @return
     */
    @RequestMapping("/createLog/searchLogV2/template")
    public ModelAndView searchLogV2Template(@RequestParam(name="id",required=true) String id) {
        return createLogService.searchLogV2(id);
    }

    @RequestMapping("/createLog/queryFirsthandLog")
    @ResponseBody
    public Map<String,Object> queryFirsthandLog(@RequestParam(name="id",required=true) String id) {
        return createLogService.queryFirsthandLog(id);
    }

    @RequestMapping("/subscribe/querySmsCode")
    @RequiresPermissions("subscribe:querySmsCode")
    public ModelAndView querySmsCode() {

        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/unicom/querySmsCode");
        CreateLogConfig createLogConfig = createLogConfigService.list().get(0);
        Subscribe subscribe = subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile,createLogConfig.getMobile())
                .eq(Subscribe::getStatus,1)
                .orderByDesc(Subscribe::getCreateTime)
                .last("limit 1")
                .one();
        if(subscribe == null){
            return modelAndView;
        }
        String channel = subscribe.getChannel();
        String remark = "";
        //模板中的批次号
        String createTime = df.format(subscribe.getCreateTime());
        subscribe.setDeviceInfo(createTime.substring(5,7) + createTime.substring(8,10) + "8081" + IdWorker.getId());
        //运营商
        if(subscribe.getIsp().equals("1")){
            subscribe.setIsp("移动");
        }else if(subscribe.getIsp().equals("3")){
            subscribe.setIsp("联通");
        }else if(subscribe.getIsp().equals("4")){
            subscribe.setIsp("电信");
        }
        //短信模板
        SmsModel smsModel = smsModelService.lambdaQuery().eq(SmsModel::getChannel, channel).eq(SmsModel::getServiceType, 5).one();
        if(smsModel != null){
            remark = smsModel.getSmsModel();
        }else{
            if(channel.equals("014X04C")
                    || channel.equals("014X04F")
                    || channel.equals("014X04D")
                    || channel.equals("014X04E")
                    || channel.equals("014X04G")
                    || channel.equals("014X09P")
                    || channel.equals("014X09T")){
                remark = "【麦禾视频彩铃】您的验证码" + RandomUtils.randomCode() + "，该验证码5分钟内有效，请勿泄漏于他人！";
            }else if("00210Q6".equals(subscribe.getChannel())
                    ||"00210Y0".equals(subscribe.getChannel())
                    ||"00210XZ".equals(subscribe.getChannel())){
                remark = "【休闲集社】尊敬的用户，您本次办理咪咕音乐北岸唐唱音乐包赠送的会员权益，请到微信公众号“休闲集社”点击权益领取-登录订购的手机号领取权益。如有疑问可致电联系客服4000099971。";
            }else if("002112S".equals(subscribe.getChannel())
                    || "002112O".equals(subscribe.getChannel())
                    || "002112T".equals(subscribe.getChannel())){
                remark = "【爱休闲集社】尊敬的用户，您本次办理咪咕音乐音乐全曲包-七彩音乐包赠送的会员权益，请到微信公众号“爱休闲集社”点击权益领取-登录订购的手机号领取权益。如有疑问可致电联系客服4000099971。";
            }else if("00210U2".equals(subscribe.getChannel())
                    || "00210PP".equals(subscribe.getChannel())){
                remark = "【白金会员】尊敬的用户，尊敬的用户，您本次办理咪咕音乐白金会员包赠送的会员权益，请到微信公众号“爱休闲集社”点击权益领取-登录订购的手机号领取权益。如有疑问可致电联系客服4000099971。";
            }else if("0021107".equals(subscribe.getChannel())){
                remark = "【七彩歌曲】尊敬的用户，您本次办理咪咕音乐七彩歌曲音乐包赠送的会员权益，请到微信公众号“权益公社\"点击我的-权益兑换-登录订购的手机号领取权益。如有疑问可致电联系客服4001897518。";
            }else if("00210VO".equals(subscribe.getChannel())){
                remark = "【七彩歌曲】尊敬的用户，您本次办理咪咕音乐七彩歌曲10元包赠送的音乐会员权益，请到公众号“休闲集社”点击会员领取-登录订购的手机号领取权益。活动期间内，续订用户每月均可领取。如有疑问可致电联系客服4000099971。";
            }else if("00210H0".equals(subscribe.getChannel())
                    || "00210T7".equals(subscribe.getChannel())){
                remark = "多彩美音汇】尊敬的用户，您本次办理咪咕音乐白金会员赠送的音乐会员权益，请到微信公众号“多彩美音汇”点击会员领取-登录订购的手机号领取权益。如有疑问可致电联系客服4001331008。";
            }else if("00210W5".equals(subscribe.getChannel())
                    || "002110A".equals(subscribe.getChannel())
                    || "0021109".equals(subscribe.getChannel())){
                remark = "【经典音乐】尊敬的用户，您本次办理咪咕音乐经典音乐包赠送的会员权益，请到公众号“光明新媒体”点击会员领取-登录订购的手机号领取权益。如有疑问可致电联系客户4000099971。";
            }else {
                remark = "【爱休闲集社】尊敬的用户，您本次办理咪咕音乐北岸唐唱音乐包赠送的会员权益，请到微信公众号“爱休闲集社”点击权益领取-登录订购的手机号领取权益。如有疑问可致电联系客服4000099971。";
            }
        }

        subscribe.setRemark(remark);
        modelAndView.addObject("subscribe", subscribe);
        return modelAndView;
    }




}
