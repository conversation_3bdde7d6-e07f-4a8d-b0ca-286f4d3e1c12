package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.config.OtherRecharge;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Strings;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.aspect.annotation.UnifyWeChatLogin;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * 订购成功，立即领取权益
 */
@Api(tags = "订购成功，立即领取权益")
@RestController
@RequestMapping("/api")
@Slf4j
@Validated
public class ReceiveController {
    @Autowired
    private IMemberService memberService;
    @Autowired
    private JunboApiProperties junboApiProperties;
    private static final Interner<String> interner = Interners.newWeakInterner();
    /**
     * 查询权益列表
     * @param serviceId
     * @return
     */
    @ApiOperation(value = "查询权益列表", notes = "查询权益列表")
    @PostMapping(value = "/rights/list")
    @ResponseBody
    public FebsResponse memberRightsList(@RequestParam(value = "serviceId", required = false, defaultValue = BizConstant.BIZ_TYPE_BJHY_PP_SERVICE_ID)String serviceId,
                                         @RequestParam(value = "channelCode", required = false, defaultValue = "")String channelCode,HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("查询权益列表(推广页)=>业务ID:{},渠道号:{},referer:{}",serviceId,channelCode,referer);
        if(StringUtils.isNotBlank(channelCode)){
            return memberService.queryRightsListByChannel(channelCode);
        }
        return memberService.queryRightsList(serviceId);
    }

    /**
     *  会员领取权益
     *
     * @return
     */
    @ApiOperation(value = "会员领取权益", notes = "会员领取权益")
    @PostMapping(value = "/member/receive/rights")
    @ResponseBody
    public FebsResponse memberReceiveRights(@RequestParam("phone") String phone,
                                            @RequestParam(value = "account", required = false, defaultValue = "")String account,
                                            @RequestParam(value = "serviceId", required = false, defaultValue = BizConstant.BIZ_TYPE_BJHY_PP_SERVICE_ID)String serviceId,
                                            @RequestParam("packName") String packName, @RequestParam("rightsId") String rightsId, HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("会员领取权益(推广页)=>手机号:{},账号:{},serviceId:{},packName:{},rightsId:{},referer:{}",phone,account,serviceId,packName,rightsId,referer);
        //根据手机号加锁
        synchronized (interner.intern(phone)){
            account= oConvertUtils.isEmpty(account)?phone:account;
            List<String> packNameList = Arrays.asList(packName.split(StringPool.COMMA));
            List<String> rightsIdList = Arrays.asList(rightsId.split(StringPool.COMMA));
            if(packNameList.size()>0 && packNameList!=null && rightsIdList.size()>0 && rightsIdList!=null && packName.contains(StringPool.COMMA) && rightsId.contains(StringPool.COMMA)){
                FebsResponse febs=null;
                for(int i=0;i<packNameList.size();i++) {
                    febs=memberService.memberReceiveRights(serviceId,packNameList.get(i),rightsIdList.get(i),phone,account,true,true);
                }
                return febs;
            }else{
                return memberService.memberReceiveRights(serviceId,packName,rightsId,phone,account,true,true);
            }
        }
    }


    @ApiOperation(value = "充值白金会员权益", notes = "充值白金会员权益")
    @PostMapping(value = "/recharge/bjhy/rights")
    @ResponseBody
    public FebsResponse rechargeRights(@RequestParam("phone") String phone,
                                       @RequestParam("channelId") String channelId,
                                       @RequestParam("submitTime") String submitTime,
                                       @RequestParam(value = "rightsId", required = false, defaultValue = "mgyybjhy")String rightsId,
                                       HttpServletRequest request){
        String sign = request.getHeader("sign");
        String referer = request.getHeader("Referer");
        log.info("充值白金会员权益,phone:{},channelId:{},submitTime:{},sign:{},referer:{}",phone,channelId,submitTime,sign,referer);
        if (Strings.isNullOrEmpty(phone) || !phone.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().code(HttpStatus.NOT_IMPLEMENTED).message("手机号错误！");
        }
        if(StringUtils.isEmpty(sign) || StringUtils.isEmpty(channelId) || StringUtils.isEmpty(submitTime) ){
            return new FebsResponse().code(HttpStatus.BAD_GATEWAY).message("参数错误！");
        }
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(channelId);
        if(otherRecharge==null){
            return new FebsResponse().code(HttpStatus.SERVICE_UNAVAILABLE).message("渠道号错误！");
        }
        String ourSign = DigestUtils.md5DigestAsHex((channelId+phone+submitTime + otherRecharge.getKey()).getBytes(StandardCharsets.UTF_8));
        if(!StringUtil.equals(sign,ourSign)){
            return new FebsResponse().code(HttpStatus.GATEWAY_TIMEOUT).message("验签失败！");
        }
        synchronized (interner.intern(phone)) {
            return memberService.rechargeBjhyRights(phone,channelId,otherRecharge.getServiceId(),otherRecharge.getName(),rightsId);
        }
    }


    /**
     * 查询渠道业务关联的权益列表
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "查询渠道业务关联的权益列表", notes = "查询渠道业务关联的权益列表")
    @PostMapping(value = "/query/rights/list")
    @ResponseBody
    public FebsResponse queryRightsListByChannel(@RequestParam(value = "channelCode", required = false, defaultValue = "")String channelCode,HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("查询渠道业务关联的权益列表=>渠道号:{},referer:{}",channelCode,referer);
        return memberService.queryRightsListByChannel(channelCode);
    }
}
