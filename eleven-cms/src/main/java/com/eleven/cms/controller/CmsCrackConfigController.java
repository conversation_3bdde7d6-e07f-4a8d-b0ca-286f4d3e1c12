package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.MiguPack;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.entity.RightsPack;
import com.eleven.cms.service.IBizTypeService;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MiguPackPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: cms_crack_config
 * @Author: jeecg-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
@Api(tags = "cms_crack_config")
@RestController
@RequestMapping("/cms/cmsCrackConfig")
@Slf4j
public class CmsCrackConfigController extends JeecgController<CmsCrackConfig, ICmsCrackConfigService> {
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private IBizTypeService bizTypeService;
    @Autowired
    private IPortCrackConfigService portCrackConfigService;


    /**
     * 分页列表查询
     *
     * @param cmsCrackConfig
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "cms_crack_config-分页列表查询")
    @ApiOperation(value = "cms_crack_config-分页列表查询", notes = "cms_crack_config-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(CmsCrackConfig cmsCrackConfig,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<CmsCrackConfig> queryWrapper = QueryGenerator.initQueryWrapper(cmsCrackConfig, req.getParameterMap());
        Page<CmsCrackConfig> page = new Page<CmsCrackConfig>(pageNo, pageSize);
        IPage<CmsCrackConfig> pageList = cmsCrackConfigService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param cmsCrackConfig
     * @return
     */
    //@AutoLog(value = "cms_crack_config-添加")
    @ApiOperation(value = "cms_crack_config-添加", notes = "cms_crack_config-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody CmsCrackConfig cmsCrackConfig) {
        cmsCrackConfigService.save(cmsCrackConfig);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param cmsCrackConfig
     * @return
     */
    //@AutoLog(value = "cms_crack_config-编辑")
    @ApiOperation(value = "cms_crack_config-编辑", notes = "cms_crack_config-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody CmsCrackConfig cmsCrackConfig) {
        cmsCrackConfigService.updateCrackConfig(cmsCrackConfig);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_crack_config-通过id删除")
    @ApiOperation(value = "cms_crack_config-通过id删除", notes = "cms_crack_config-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        cmsCrackConfigService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "cms_crack_config-批量删除")
    @ApiOperation(value = "cms_crack_config-批量删除", notes = "cms_crack_config-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.cmsCrackConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_crack_config-通过id查询")
    @ApiOperation(value = "cms_crack_config-通过id查询", notes = "cms_crack_config-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getById(id);
        if (cmsCrackConfig == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(cmsCrackConfig);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param cmsCrackConfig
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsCrackConfig cmsCrackConfig) {
        return super.exportXls(request, cmsCrackConfig, CmsCrackConfig.class, "cms_crack_config");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CmsCrackConfig.class);
    }

    /**
     * 查询渠道号列表
     *
     * @return Result<?>
     */
    @GetMapping("/query/list")
    public Result<?> queryList() {
        return Result.ok(bizTypeService.queryChannelList());
    }



    /**
     * 手动复制参数
     * @param cmsCrackConfig
     * @return
     */
    @PostMapping(value = "/copyCrackConfig")
    public Result<?> copyCrackConfig(@RequestBody CmsCrackConfig cmsCrackConfig) {
        boolean crackConfig=cmsCrackConfigService.lambdaQuery().eq(CmsCrackConfig::getChannel,cmsCrackConfig.getChannel()).count()>0;
        if(crackConfig){
            return Result.error("crack渠道号已存在！");
        }
        if(StringUtils.isNotBlank(cmsCrackConfig.getPayCode())){
            boolean config=cmsCrackConfigService.lambdaQuery().eq(CmsCrackConfig::getPayCode,cmsCrackConfig.getPayCode()).count()>0;
            if(config){
                return Result.error("PayCode参数已存在！");
            }
        }
        if(StringUtils.isNotBlank(cmsCrackConfig.getNewPayCode())){
            boolean config=cmsCrackConfigService.lambdaQuery().eq(CmsCrackConfig::getNewPayCode,cmsCrackConfig.getNewPayCode()).count()>0;
            if(config){
                return Result.error("NewPayCode参数已存在！");
            }
        }
        if(StringUtils.isNotBlank(cmsCrackConfig.getSenyueProductId())){
            boolean config=cmsCrackConfigService.lambdaQuery().eq(CmsCrackConfig::getSenyueProductId,cmsCrackConfig.getSenyueProductId()).count()>0;
            if(config){
                return Result.error("SenyueProductId参数已存在！");
            }
        }
        boolean portCrackConfig=portCrackConfigService.lambdaQuery().eq(PortCrackConfig::getChannel,cmsCrackConfig.getChannel()).count()>0;
        if(portCrackConfig){
            return Result.error("portCrack渠道号已存在！");
        }
        cmsCrackConfig.setId("");
        cmsCrackConfigService.save(cmsCrackConfig);
        CmsCrackConfig config=cmsCrackConfigService.lambdaQuery().select(CmsCrackConfig::getChannel).eq(CmsCrackConfig::getBizType,cmsCrackConfig.getBizType()).orderByAsc(CmsCrackConfig::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(config==null){
            return Result.ok("添加成功！");

        }
        PortCrackConfig portCrack=portCrackConfigService.lambdaQuery().eq(PortCrackConfig::getChannel,config.getChannel()).orderByAsc(PortCrackConfig::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(portCrack==null){
            return Result.ok("添加成功！");
        }
        portCrack.setId("");
        portCrack.setChannel(cmsCrackConfig.getChannel());
        portCrack.setLogTag(cmsCrackConfig.getLogTag());
        portCrackConfigService.save(portCrack);
        return Result.ok("添加成功！");
    }
}
