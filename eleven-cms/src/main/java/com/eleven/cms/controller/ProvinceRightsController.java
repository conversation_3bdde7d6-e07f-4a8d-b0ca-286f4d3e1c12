package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.ProvinceRights;
import com.eleven.cms.service.IProvinceRightsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 省份权益限制
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
@Api(tags="省份权益限制")
@RestController
@RequestMapping("/cms/provinceRights")
@Slf4j
public class ProvinceRightsController extends JeecgController<ProvinceRights, IProvinceRightsService> {
	@Autowired
	private IProvinceRightsService provinceRightsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param provinceRights
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "省份权益限制-分页列表查询")
	@ApiOperation(value="省份权益限制-分页列表查询", notes="省份权益限制-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ProvinceRights provinceRights,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ProvinceRights> queryWrapper = QueryGenerator.initQueryWrapper(provinceRights, req.getParameterMap());
		Page<ProvinceRights> page = new Page<ProvinceRights>(pageNo, pageSize);
		IPage<ProvinceRights> pageList = provinceRightsService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param provinceRights
	 * @return
	 */
	//@AutoLog(value = "省份权益限制-添加")
	@ApiOperation(value="省份权益限制-添加", notes="省份权益限制-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProvinceRights provinceRights) {
		provinceRightsService.save(provinceRights);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param provinceRights
	 * @return
	 */
	//@AutoLog(value = "省份权益限制-编辑")
	@ApiOperation(value="省份权益限制-编辑", notes="省份权益限制-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProvinceRights provinceRights) {
		provinceRightsService.updateById(provinceRights);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "省份权益限制-通过id删除")
	@ApiOperation(value="省份权益限制-通过id删除", notes="省份权益限制-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		provinceRightsService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "省份权益限制-批量删除")
	@ApiOperation(value="省份权益限制-批量删除", notes="省份权益限制-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.provinceRightsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "省份权益限制-通过id查询")
	@ApiOperation(value="省份权益限制-通过id查询", notes="省份权益限制-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProvinceRights provinceRights = provinceRightsService.getById(id);
		if(provinceRights==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(provinceRights);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param provinceRights
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProvinceRights provinceRights) {
        return super.exportXls(request, provinceRights, ProvinceRights.class, "省份权益限制");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProvinceRights.class);
    }

}
