package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.LianlianChargeLog;
import com.eleven.cms.service.ILianlianChargeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 联联分销充值记录
 * @Author: jeecg-boot
 * @Date:   2024-05-14
 * @Version: V1.0
 */
@Api(tags="联联分销充值记录")
@RestController
@RequestMapping("/cms/lianlianChargeLog")
@Slf4j
public class LianlianChargeLogController extends JeecgController<LianlianChargeLog, ILianlianChargeLogService> {
	@Autowired
	private ILianlianChargeLogService lianlianChargeLogService;

	/**
	 * 分页列表查询
	 *
	 * @param lianlianChargeLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "联联分销充值记录-分页列表查询")
	@ApiOperation(value="联联分销充值记录-分页列表查询", notes="联联分销充值记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(LianlianChargeLog lianlianChargeLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<LianlianChargeLog> queryWrapper = QueryGenerator.initQueryWrapper(lianlianChargeLog, req.getParameterMap());
		Page<LianlianChargeLog> page = new Page<LianlianChargeLog>(pageNo, pageSize);
		IPage<LianlianChargeLog> pageList = lianlianChargeLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param lianlianChargeLog
	 * @return
	 */
	//@AutoLog(value = "联联分销充值记录-添加")
	@ApiOperation(value="联联分销充值记录-添加", notes="联联分销充值记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody LianlianChargeLog lianlianChargeLog) {
		lianlianChargeLogService.save(lianlianChargeLog);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param lianlianChargeLog
	 * @return
	 */
	//@AutoLog(value = "联联分销充值记录-编辑")
	@ApiOperation(value="联联分销充值记录-编辑", notes="联联分销充值记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody LianlianChargeLog lianlianChargeLog) {
		lianlianChargeLogService.updateById(lianlianChargeLog);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "联联分销充值记录-通过id删除")
	@ApiOperation(value="联联分销充值记录-通过id删除", notes="联联分销充值记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		lianlianChargeLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "联联分销充值记录-批量删除")
	@ApiOperation(value="联联分销充值记录-批量删除", notes="联联分销充值记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.lianlianChargeLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "联联分销充值记录-通过id查询")
	@ApiOperation(value="联联分销充值记录-通过id查询", notes="联联分销充值记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		LianlianChargeLog lianlianChargeLog = lianlianChargeLogService.getById(id);
		if(lianlianChargeLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(lianlianChargeLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param lianlianChargeLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LianlianChargeLog lianlianChargeLog) {
        return super.exportXls(request, lianlianChargeLog, LianlianChargeLog.class, "联联分销充值记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LianlianChargeLog.class);
    }

	 /**
	  * 退款
	  * @param orderId
	  * @return
	  */
	 @PostMapping(value = "/refund")
	 @ResponseBody
	 @RequiresPermissions("lianlianOrder:refund")
	 public Result<?> wechatRefund(@RequestParam(value = "orderId", required = false, defaultValue ="")String orderId){
		 if(StringUtils.isEmpty(orderId)){
			 return Result.error("订单号不能为空");
		 }
		 return lianlianChargeLogService.wechatRefund(orderId);
	 }

}
