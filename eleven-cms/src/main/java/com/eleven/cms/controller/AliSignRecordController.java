package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alipay.api.request.AlipayUserAgreementUnsignRequest;
import com.alipay.api.response.AlipayUserAgreementExecutionplanModifyResponse;
import com.alipay.api.response.AlipayUserAgreementUnsignResponse;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eleven.cms.entity.*;
import com.eleven.cms.service.IAliSignChargingOrderService;
import com.eleven.cms.service.IAliSignRecordService;
import com.eleven.cms.service.IAlipayConfigService;
import com.eleven.cms.service.IAlipayService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_ali_sign_record
 * @Author: jeecg-boot
 * @Date:   2022-12-29
 * @Version: V1.0
 */
@Api(tags="cms_ali_sign_record")
@RestController
@RequestMapping("/cms/aliSignRecord")
@Slf4j
public class AliSignRecordController extends JeecgController<AliSignRecord, IAliSignRecordService> {
	 @Autowired
	 private IAliSignRecordService aliSignRecordService;
	 @Autowired
	 private IAlipayService alipayService;
	 @Autowired
	 private IAlipayConfigService alipayConfigService;
	 @Autowired
	 private IAliSignChargingOrderService aliSignChargingOrderService;
	/**
	 * 分页列表查询
	 *
	 * @param aliSignRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_record-分页列表查询")
	@ApiOperation(value="cms_ali_sign_record-分页列表查询", notes="cms_ali_sign_record-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AliSignRecord aliSignRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AliSignRecord> queryWrapper = QueryGenerator.initQueryWrapper(aliSignRecord, req.getParameterMap());
		Page<AliSignRecord> page = new Page<AliSignRecord>(pageNo, pageSize);
		IPage<AliSignRecord> pageList = aliSignRecordService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param aliSignRecord
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_record-添加")
	@ApiOperation(value="cms_ali_sign_record-添加", notes="cms_ali_sign_record-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AliSignRecord aliSignRecord) {
		aliSignRecordService.save(aliSignRecord);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param aliSignRecord
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_record-编辑")
	@ApiOperation(value="cms_ali_sign_record-编辑", notes="cms_ali_sign_record-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AliSignRecord aliSignRecord) {
		aliSignRecordService.updateById(aliSignRecord);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_record-通过id删除")
	@ApiOperation(value="cms_ali_sign_record-通过id删除", notes="cms_ali_sign_record-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aliSignRecordService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_record-批量删除")
	@ApiOperation(value="cms_ali_sign_record-批量删除", notes="cms_ali_sign_record-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aliSignRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_record-通过id查询")
	@ApiOperation(value="cms_ali_sign_record-通过id查询", notes="cms_ali_sign_record-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AliSignRecord aliSignRecord = aliSignRecordService.getById(id);
		if(aliSignRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aliSignRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aliSignRecord
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AliSignRecord aliSignRecord) {
        return super.exportXls(request, aliSignRecord, AliSignRecord.class, "cms_ali_sign_record");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AliSignRecord.class);
    }

	 ///**
	 // * 延期扣款
	 // * @param id
	 // * @return
	 // */
	 ////@AutoLog(value = "延期扣款")
	 //@PostMapping(value = "/delay")
	 //@ResponseBody
	 //public Result<?> delay(@RequestParam(value = "id", required = false, defaultValue ="")String id) {
		// AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getId,id).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
		// if(aliSignRecord!=null){
		//	 AlipayUserAgreementExecutionplanModifyResponse response= alipayService.alipayDelay(aliSignRecord.getAgreementNo(),aliSignRecord.getBusinessType(),null);
		//	 String remark="";
		//	 if(response.isSuccess()){
		//		 remark="延期扣款调用成功=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
		//	} else {
		//		 remark="延期扣款调用失败=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
		//	}
		//	 aliSignRecordService.lambdaUpdate()
		//			 .eq(AliSignRecord::getId, id)
		//			 .set(AliSignRecord::getRemark,remark)
		//			 .set(AliSignRecord::getAgreementNo, StringUtils.isEmpty(response.getAgreementNo())?aliSignRecord.getAgreementNo():response.getAgreementNo())
		//			 .set(AliSignRecord::getUpdateTime,new Date()).update();
		//	if(response.isSuccess()){
		//		//发起第一次扣款
		//		Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getAppId, aliSignRecord.getAppId()).one();
     //
		//		String totalAmount = alipay.getSingleAmount();
		//		String subject = alipay.getBusinessName();
		//		String outTradeNo = "AliPay" + IdWorker.getIdStr();
		//		alipayService.aliTradePay(aliSignRecord.getMobile(),
		//				aliSignRecord.getExternalAgreementNo(),
		//				response.getAgreementNo(),
		//				outTradeNo,
		//				totalAmount,
		//				subject,aliSignRecord.getAppId(),aliSignRecord.getBusinessType(),aliSignRecord.getBusinessName(),aliSignRecord.getSubChannel());
		//	}
     //
     //
		//	 return Result.ok(remark);
		// }
		// return Result.error("数据不存在");
	 //}

	 /**
	  * 解约
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "解约")
	 @PostMapping(value = "/rescind")
	 @ResponseBody
	 @RequiresPermissions("aliSignRecord:rescind")
	 public Result<?> rescind(@RequestParam(value = "id", required = false, defaultValue ="")String id) {
		 AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getId,id).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
		 if(aliSignRecord!=null){
			 AlipayUserAgreementUnsignResponse response= alipayService.alipayRescind(aliSignRecord.getAgreementNo(),aliSignRecord.getBusinessType());
			 String remark="";
			 if(response.isSuccess()){
				 remark="解约调用成功=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
			 } else {
				 remark="解约调用失败=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
			 }
			 aliSignRecordService.lambdaUpdate()
					 .eq(AliSignRecord::getId, id).set(AliSignRecord::getRemark,remark).update();
			 return Result.ok(remark);
		 }
		 return Result.error("数据不存在");
	 }

	// /**
	//  * 批量延期扣款
	//  * @return
	//  */
	// //@AutoLog(value = "批量延期扣款")
	// @PostMapping(value = "/delay/list")
	// @ResponseBody
	// public Result<?> delayList() {
	//	 List<AliSignChargingOrder> aliSignChargingOrderList=aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getOrderStatus,0).eq(AliSignChargingOrder::getRemark,"扣款日期不在签约时的允许范围之内").groupBy(AliSignChargingOrder::getMobile).orderByDesc(AliSignChargingOrder::getCreateTime).list();
	//	 aliSignChargingOrderList.forEach(item -> {
	//		 Integer payCount=aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getMobile,item.getMobile()).eq(AliSignChargingOrder::getOrderStatus,1).count();
	//		 if(payCount<=0){
	//			 AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getMobile,item.getMobile()).eq(AliSignRecord::getSignStatus,1).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
	//			 if(aliSignRecord!=null){
	//				 AlipayUserAgreementExecutionplanModifyResponse response= alipayService.alipayDelay(aliSignRecord.getAgreementNo(),aliSignRecord.getBusinessType(),null);
	//				 String remark="";
	//				 if(response.isSuccess()){
	//					 remark="延期扣款调用成功=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
	//				 } else {
	//					 remark="延期扣款调用失败=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
	//				 }
	//				 aliSignRecordService.lambdaUpdate()
	//						 .eq(AliSignRecord::getId, aliSignRecord.getId())
	//						 .set(AliSignRecord::getRemark,remark)
	//						 .set(AliSignRecord::getAgreementNo, StringUtils.isEmpty(response.getAgreementNo())?aliSignRecord.getAgreementNo():response.getAgreementNo())
	//						 .set(AliSignRecord::getUpdateTime,new Date()).update();
	//				 if(response.isSuccess()){
	//					 //发起第一次扣款
	//					 Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getAppId, aliSignRecord.getAppId()).one();
 //
	//					 String totalAmount = alipay.getSingleAmount();
	//					 String subject = alipay.getBusinessName();
	//					 String outTradeNo = "AliPay" + IdWorker.getIdStr();
	//					 alipayService.aliTradePay(aliSignRecord.getMobile(),
	//							 aliSignRecord.getExternalAgreementNo(),
	//							 response.getAgreementNo(),
	//							 outTradeNo,
	//							 totalAmount,
	//							 subject,aliSignRecord.getAppId(),aliSignRecord.getBusinessType(),aliSignRecord.getBusinessName(),aliSignRecord.getSubChannel());
	//				 }
	//			 }
	//		 }
	//	 });
	//	 return Result.error("数据不存在");
	// }
 }
