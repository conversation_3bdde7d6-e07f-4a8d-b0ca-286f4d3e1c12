package com.eleven.cms.controller.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.aiunion.entity.PayOrders;
import com.eleven.cms.aiunion.service.IPayOrdersService;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.service.pay.PayNotifyService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("miniApi/pay/douYin")
@Slf4j
public class DouYinPayController {

    @Resource
    private List<PayNotifyService> payNotifyServiceList;
    @Resource
    IPayOrdersService payOrdersService;

    @RequestMapping(value = "douYinPayResultNotify", produces = "text/plain")
    @ResponseBody
    public String notify(HttpServletRequest request) throws Exception {
        ReqInfo reqInfo = resolveReq(request);
//        verifySignature(reqInfo, "");
        String body = reqInfo.getBody();
        Map bodyMap = JSON.parseObject(body, Map.class);
        String msg = (String) bodyMap.get("msg");
        Map notityMap = JSON.parseObject(msg, Map.class);
        String outTradeNo = (String) notityMap.get("out_order_no");
        String status = (String) notityMap.get("status");
        PayOrders payOrders = payOrdersService.getPayOrdersByOrderNo(outTradeNo);
        if (Objects.isNull(payOrders)) {
            log.error("查询订单失败:{}", outTradeNo);
            JSONObject response = new JSONObject();
            response.put("err_no", 0);
            response.put("err_tips", "success");
            return response.toJSONString();
        }
        if (Objects.equals(payOrders.getPayStatus(), PayStatueEnum.PAID.getPayType())) {
            log.error("订单状态已支付:{}", outTradeNo);
            JSONObject response = new JSONObject();
            response.put("err_no", 0);
            response.put("err_tips", "success");
            return response.toJSONString();
        }

        //更新已支付状态
        if (StringUtils.equals("SUCCESS", status)) {
            payOrders.setPayStatus(PayStatueEnum.PAID.getPayType());
            payOrders.setOutTradeNo(outTradeNo);
            payOrdersService.updateById(payOrders);
        }

        for (PayNotifyService notifyService : payNotifyServiceList) {
            if (Objects.equals(notifyService.getBusinessType().getType(), payOrders.getBusinessType())) {
                notifyService.handleNotify(payOrders.getOrderNo(), notityMap);
            }
        }
        return "success";
    }

    public boolean verifySignature(ReqInfo reqInfo, String publicKey) throws Exception {
        StringBuffer buffer = new StringBuffer();
        buffer.append(reqInfo.timestamp).append("\n");
        buffer.append(reqInfo.nonce).append("\n");
        buffer.append(reqInfo.body).append("\n");
        String message = buffer.toString();
        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initVerify(string2PublicKey(publicKey)); // 注意验签时publicKey使用平台公钥而非应用公钥
        sign.update(message.getBytes(StandardCharsets.UTF_8));
        return sign.verify(Base64.getDecoder().decode(reqInfo.signature.getBytes(StandardCharsets.UTF_8)));
    }

    public ReqInfo resolveReq(HttpServletRequest req) {
        log.info("receive payNotify");
        String body = getRequestBodyParamsStr(req);
        String timestamp = req.getHeader("Byte-Timestamp");
        String nonce = req.getHeader("Byte-Nonce-Str");
        String signature = req.getHeader("Byte-Signature");
        log.info("body:%s, timestamp:%s, nonce:%s, signature:%s", body, timestamp, nonce, signature);
        ReqInfo reqInfo = new ReqInfo();
        reqInfo.body = body;
        reqInfo.timestamp = timestamp;
        reqInfo.nonce = nonce;
        reqInfo.signature = signature;
        return reqInfo;
    }

    public String getRequestBodyParamsStr(HttpServletRequest request) {
        BufferedReader br;
        String str, wholeStr = "";
        try {
            br = request.getReader();
            while ((str = br.readLine()) != null) {
                wholeStr += str;
            }
            wholeStr = wholeStr.replaceAll(" ", "");
            log.info(wholeStr);
        } catch (IOException e) {
            e.printStackTrace();
            log.error(String.valueOf(e));
        }
        return wholeStr;
    }

    public PublicKey string2PublicKey(String publicKey) throws Exception {
        byte[] decoded = Base64.getDecoder().decode(publicKey);
        return KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
    }

    @Data
    public static class ReqInfo {
        public String body;
        public String timestamp;
        public String nonce;
        public String signature;
    }
}
