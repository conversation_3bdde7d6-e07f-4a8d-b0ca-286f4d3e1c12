package com.eleven.cms.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.entity.ColumnMusicDyVo;
import com.eleven.cms.entity.ColumnMusicVo;
import org.jeecg.common.util.HttpUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.ColumnMusicDy;
import com.eleven.cms.entity.ColumnDy;
import com.eleven.cms.vo.ColumnDyPage;
import com.eleven.cms.service.IColumnDyService;
import com.eleven.cms.service.IColumnMusicDyService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 订阅包栏目
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
@Api(tags="订阅包栏目")
@RestController
@RequestMapping("/cms/columnDy")
@Slf4j
public class ColumnDyController {
	@Autowired
	private IColumnDyService columnDyService;
	@Autowired
	private IColumnMusicDyService columnMusicDyService;

	/**
	 * 分页列表查询
	 *
	 * @param columnDy
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "订阅包栏目-分页列表查询")
	@ApiOperation(value="订阅包栏目-分页列表查询", notes="订阅包栏目-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ColumnDy columnDy,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ColumnDy> queryWrapper = QueryGenerator.initQueryWrapper(columnDy, req.getParameterMap());
		Page<ColumnDy> page = new Page<ColumnDy>(pageNo, pageSize);
		IPage<ColumnDy> pageList = columnDyService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param columnDyPage
	 * @return
	 */
	//@AutoLog(value = "订阅包栏目-添加")
	@ApiOperation(value="订阅包栏目-添加", notes="订阅包栏目-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ColumnDyPage columnDyPage) {
		ColumnDy columnDy = new ColumnDy();
		BeanUtils.copyProperties(columnDyPage, columnDy);
		columnDyService.saveMain(columnDy, columnDyPage.getColumnMusicDyList());
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param columnDyPage
	 * @return
	 */
	//@AutoLog(value = "订阅包栏目-编辑")
	@ApiOperation(value="订阅包栏目-编辑", notes="订阅包栏目-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ColumnDyPage columnDyPage) {
		ColumnDy columnDy = new ColumnDy();
		BeanUtils.copyProperties(columnDyPage, columnDy);
		ColumnDy columnDyEntity = columnDyService.getById(columnDy.getId());
		if(columnDyEntity==null) {
			return Result.error("未找到对应数据");
		}
		columnDyService.updateMain(columnDy, columnDyPage.getColumnMusicDyList());
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "订阅包栏目-通过id删除")
	@ApiOperation(value="订阅包栏目-通过id删除", notes="订阅包栏目-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		columnDyService.delMain(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "订阅包栏目-批量删除")
	@ApiOperation(value="订阅包栏目-批量删除", notes="订阅包栏目-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.columnDyService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "订阅包栏目-通过id查询")
	@ApiOperation(value="订阅包栏目-通过id查询", notes="订阅包栏目-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ColumnDy columnDy = columnDyService.getById(id);
		if(columnDy==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(columnDy);

	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "订阅包栏目歌曲通过主表ID查询")
	@ApiOperation(value="订阅包栏目歌曲主表ID查询", notes="订阅包栏目歌曲-通主表ID查询")
	@GetMapping(value = "/queryColumnMusicDyByMainId")
	public Result<?> queryColumnMusicDyListByMainId(@RequestParam(name="id",required=true) String id) {
		List<ColumnMusicDy> columnMusicDyList = columnMusicDyService.selectByMainId(id);
		return Result.ok(columnMusicDyList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param columnDy
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ColumnDy columnDy) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<ColumnDy> queryWrapper = QueryGenerator.initQueryWrapper(columnDy, request.getParameterMap());
      LoginUser sysUser = HttpUtil.getCurrUser();

      //Step.2 获取导出数据
      List<ColumnDy> queryList = columnDyService.list(queryWrapper);
      // 过滤选中数据
      String selections = request.getParameter("selections");
      List<ColumnDy> columnDyList = new ArrayList<ColumnDy>();
      if(oConvertUtils.isEmpty(selections)) {
          columnDyList = queryList;
      }else {
          List<String> selectionList = Arrays.asList(selections.split(","));
          columnDyList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
      }

      // Step.3 组装pageList
      List<ColumnDyPage> pageList = new ArrayList<ColumnDyPage>();
      for (ColumnDy main : columnDyList) {
          ColumnDyPage vo = new ColumnDyPage();
          BeanUtils.copyProperties(main, vo);
          List<ColumnMusicDy> columnMusicDyList = columnMusicDyService.selectByMainId(main.getId());
          vo.setColumnMusicDyList(columnMusicDyList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "订阅包栏目列表");
      mv.addObject(NormalExcelConstants.CLASS, ColumnDyPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("订阅包栏目数据", "导出人:"+sysUser.getRealname(), "订阅包栏目"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          MultipartFile file = entity.getValue();// 获取上传文件对象
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<ColumnDyPage> list = ExcelImportUtil.importExcel(file.getInputStream(), ColumnDyPage.class, params);
              for (ColumnDyPage page : list) {
                  ColumnDy po = new ColumnDy();
                  BeanUtils.copyProperties(page, po);
                  columnDyService.saveMain(po, page.getColumnMusicDyList());
              }
              return Result.ok("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.ok("文件导入失败！");
    }
	 /**
	  * 导出歌曲excel
	  *
	  * @param request
	  * @param id
	  */
	 @RequestMapping(value = "/exportMusic")
	 public ModelAndView exportMusic(HttpServletRequest request, String id) {

		 LoginUser sysUser = HttpUtil.getCurrUser();
		 //Step.1 获取导出数据
		 List<ColumnMusicDyVo> columnMusicDyList = columnMusicDyService.selectByMainIdNew(id);

		 // Step.2 AutoPoi 导出Excel
		 ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		 mv.addObject(NormalExcelConstants.FILE_NAME, "歌曲列表");
		 mv.addObject(NormalExcelConstants.CLASS, ColumnMusicDyVo.class);
		 mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("歌曲数据", "导出人:"+sysUser.getRealname(), "歌曲"));
		 mv.addObject(NormalExcelConstants.DATA_LIST, columnMusicDyList);
		 return mv;
	 }


	 /**
	  * 通过excel导入歌曲
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @RequestMapping(value = "/importMusic", method = RequestMethod.POST)
	 public Result<?> importMusic(HttpServletRequest request, HttpServletResponse response,String id) {
		 MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		 Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		 for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			 MultipartFile file = entity.getValue();// 获取上传文件对象
			 ImportParams params = new ImportParams();
			 params.setTitleRows(2);
			 params.setHeadRows(1);
			 params.setNeedSave(true);
			 try {
				 List<ColumnMusicDyVo> list = ExcelImportUtil.importExcel(file.getInputStream(), ColumnMusicDyVo.class, params);
				 columnMusicDyService.importMusic(list,id);
				 return Result.ok("文件导入成功！数据行数:" + list.size());
			 } catch (Exception e) {
				 log.error(e.getMessage(),e);
				 return Result.error("文件导入失败:"+e.getMessage());
			 } finally {
				 try {
					 file.getInputStream().close();
				 } catch (IOException e) {
					 e.printStackTrace();
				 }
			 }
		 }
		 return Result.ok("文件导入失败！");
	 }
}
