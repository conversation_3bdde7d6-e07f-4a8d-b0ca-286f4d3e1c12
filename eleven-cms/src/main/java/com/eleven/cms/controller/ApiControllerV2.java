package com.eleven.cms.controller;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.dto.WechatOpenIdResponse;
import com.eleven.cms.service.IQyclWxpayService;
import com.eleven.cms.wallpaper.entity.MiniAppWechatUser;
import com.eleven.cms.wallpaper.service.IMiniAppWechatUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 对外的api接口
 */
@Api(tags = "api")
@RestController
@RequestMapping("/api/common")
@Slf4j
@Validated
public class ApiControllerV2 {


    @Resource
    private IQyclWxpayService qyclWxpayService;


    @Resource
    private IMiniAppWechatUserService miniAppWechatUserService;
    /**
     * 获取微信用户openId接口
     *
     * @param code
     * @return
     */
    @ApiOperation(value = "获取微信用户openId接口", notes = "获取微信用户openId接口")
    @GetMapping(value = "/wechat/openId")
    @ResponseBody
    public Result<Object> wechatOpenId(@RequestParam(value = "code", required = false, defaultValue = "") String code,
                               @RequestParam(value = "businessType") String businessType) {
        if (StringUtils.isEmpty(code)) {
            return Result.error("code不能为空");
        }
        WechatOpenIdResponse wechatOpenIdResponse = new WechatOpenIdResponse();
        try {
            String openIdResult = qyclWxpayService.getWeChatOpenId(code, businessType);
            log.info("获取微信用户openId成功:{}", JSONObject.toJSONString(openIdResult));
            JSONObject json = JSONObject.parseObject(openIdResult);
            String openid = (String) json.get("openid");
            if (StringUtils.isNotEmpty(openid)) {
                MiniAppWechatUser miniAppWechatUser = miniAppWechatUserService.lambdaQuery().eq(MiniAppWechatUser::getOpenId, openid).eq(MiniAppWechatUser::getBusinessType, businessType).last("limit 1").one();
                if (miniAppWechatUser == null) {
                    miniAppWechatUser = new MiniAppWechatUser();
                    miniAppWechatUser.setOpenId(openid);
                    miniAppWechatUser.setBusinessType(businessType);
                    miniAppWechatUserService.save(miniAppWechatUser);
                }
                wechatOpenIdResponse.setUserCode(miniAppWechatUser.getId());
                wechatOpenIdResponse.setOpenId(openid);
                return Result.ok(wechatOpenIdResponse);
            }
        } catch (Exception e) {
            log.error("获取微信用户openId失败",e);
        }
        return Result.error("获取openId失败");
    }
}

