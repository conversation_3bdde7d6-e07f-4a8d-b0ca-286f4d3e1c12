package com.eleven.cms.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.entity.ProvinceLimit;
import com.eleven.cms.service.IProvinceLimitService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 省份限制
 * @Author: jeecg-boot
 * @Date:   2021-02-22
 * @Version: V1.0
 */
@Api(tags="省份限制")
@RestController
@RequestMapping("/cms/provinceLimit")
@Slf4j
public class ProvinceLimitController extends JeecgController<ProvinceLimit, IProvinceLimitService> {
	@Autowired
	private IProvinceLimitService provinceLimitService;
	
	/**
	 * 分页列表查询
	 *
	 * @param provinceLimit
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "省份限制-分页列表查询")
	@ApiOperation(value="省份限制-分页列表查询", notes="省份限制-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ProvinceLimit provinceLimit,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ProvinceLimit> queryWrapper = QueryGenerator.initQueryWrapper(provinceLimit, req.getParameterMap());
        queryWrapper.orderByAsc("province_code");
		Page<ProvinceLimit> page = new Page<ProvinceLimit>(pageNo, pageSize);
		IPage<ProvinceLimit> pageList = provinceLimitService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param provinceLimit
	 * @return
	 */
	//@AutoLog(value = "省份限制-添加")
	@ApiOperation(value="省份限制-添加", notes="省份限制-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProvinceLimit provinceLimit) {
		provinceLimitService.save(provinceLimit);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param provinceLimit
	 * @return
	 */
	//@AutoLog(value = "省份限制-编辑")
	@ApiOperation(value="省份限制-编辑", notes="省份限制-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProvinceLimit provinceLimit) {
		provinceLimitService.updateById(provinceLimit);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "省份限制-通过id删除")
	@ApiOperation(value="省份限制-通过id删除", notes="省份限制-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		provinceLimitService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "省份限制-批量删除")
	@ApiOperation(value="省份限制-批量删除", notes="省份限制-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.provinceLimitService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "省份限制-通过id查询")
	@ApiOperation(value="省份限制-通过id查询", notes="省份限制-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProvinceLimit provinceLimit = provinceLimitService.getById(id);
		if(provinceLimit==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(provinceLimit);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param provinceLimit
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProvinceLimit provinceLimit) {
        return super.exportXls(request, provinceLimit, ProvinceLimit.class, "省份限制");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProvinceLimit.class);
    }
	 /**
	  * 通过provinceCode查询详细
	  *
	  * @param provinceCode
	  * @return
	  */
	 //@AutoLog(value = "cms_province_limit-通过provinceCode查询详细")
	 @ApiOperation(value="cms_province_limit-通过provinceCode查询详细", notes="cms_province_limit-通过provinceCode查询详细")
	 @GetMapping(value = "/queryByProvinceCode")
	 public Result<?> queryByProvinceCode(@RequestParam(name="provinceCode",required=true) String provinceCode) {
		 ProvinceLimit provinceLimit = provinceLimitService.selectByCode(provinceCode);
		 if(provinceLimit==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.ok(provinceLimit);
	 }

	 /**
	  * 通过provinceCode查询移动状态
	  *
	  * @param provinceCode
	  * @return
	  */
	 //@AutoLog(value = "cms_province_limit-通过provinceCode查询移动状态")
	 @ApiOperation(value="cms_province_limit-通过provinceCode查询移动状态", notes="cms_province_limit-通过provinceCode查询移动状态")
	 @GetMapping(value = "/queryMobileByProvinceCode")
	 public Result<?> queryMobileByProvinceCode(@RequestParam(name="provinceCode",required=true) String provinceCode) {
		 boolean flag = provinceLimitService.isAvailableMobile(provinceCode);
		 return Result.ok(flag);
	 }

	 /**
	  * 通过provinceCode查询联通状态
	  *
	  * @param provinceCode
	  * @return
	  */
	 //@AutoLog(value = "cms_province_limit-通过provinceCode查询联通状态")
	 @ApiOperation(value="cms_province_limit-通过provinceCode查询联通状态", notes="cms_province_limit-通过provinceCode查询联通状态")
	 @GetMapping(value = "/queryUnicomByProvinceCode")
	 public Result<?> queryUnicomByProvinceCode(@RequestParam(name="provinceCode",required=true) String provinceCode) {
		 boolean flag = provinceLimitService.isAvailableUnicom(provinceCode);
		 return Result.ok(flag);
	 }


	 /**
	  * 通过provinceCode查询电信状态
	  *
	  * @param provinceCode
	  * @return
	  */
	 //@AutoLog(value = "cms_province_limit-通过provinceCode查询电信状态")
	 @ApiOperation(value="cms_province_limit-通过provinceCode查询电信状态", notes="cms_province_limit-通过provinceCode查询电信状态")
	 @GetMapping(value = "/queryTelecomByProvinceCode")
	 public Result<?> queryTelecomByProvinceCode(@RequestParam(name="provinceCode",required=true) String provinceCode) {
		 boolean flag = provinceLimitService.isAvailableTelecom(provinceCode);
		 return Result.ok(flag);
	 }

}
