package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.dto.MemberRightsImport;
import com.eleven.cms.dto.MobileFeeChargeImport;
import com.eleven.cms.entity.MemberRights;
import com.eleven.cms.entity.MobileFeeChargeLog;
import com.eleven.cms.service.IMobileFeeChargeLogService;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 话费直充记录
 * @Author: jeecg-boot
 * @Date:   2022-09-28
 * @Version: V1.0
 */
@Api(tags="话费直充记录")
@RestController
@RequestMapping("/cms/mobileFeeChargeLog")
@Slf4j
public class MobileFeeChargeLogController extends JeecgController<MobileFeeChargeLog, IMobileFeeChargeLogService> {
	@Autowired
	private IMobileFeeChargeLogService mobileFeeChargeLogService;

	/**
	 * 分页列表查询
	 *
	 * @param mobileFeeChargeLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "话费直充记录-分页列表查询")
	@ApiOperation(value="话费直充记录-分页列表查询", notes="话费直充记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(MobileFeeChargeLog mobileFeeChargeLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<MobileFeeChargeLog> queryWrapper = QueryGenerator.initQueryWrapper(mobileFeeChargeLog, req.getParameterMap());
		Page<MobileFeeChargeLog> page = new Page<MobileFeeChargeLog>(pageNo, pageSize);
		IPage<MobileFeeChargeLog> pageList = mobileFeeChargeLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param mobileFeeChargeLog
	 * @return
	 */
	//@AutoLog(value = "话费直充记录-添加")
	@ApiOperation(value="话费直充记录-添加", notes="话费直充记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody MobileFeeChargeLog mobileFeeChargeLog) {
		return mobileFeeChargeLogService.feeCharge(mobileFeeChargeLog);
	}
	 /**
	  *   添加
	  *
	  * @param mobileFeeChargeLog
	  * @return
	  */
	 //@AutoLog(value = "话费直充记录-添加")
	 @ApiOperation(value="话费直充记录-添加", notes="话费直充记录-添加")
	 @PostMapping(value = "/addPlus")
	 public Result<?> addPlus(@RequestBody MobileFeeChargeLog mobileFeeChargeLog) {
		 return mobileFeeChargeLogService.feeChargePlus(mobileFeeChargeLog);
	 }
	/**
	 *  编辑
	 *
	 * @param mobileFeeChargeLog
	 * @return
	 */
	//@AutoLog(value = "话费直充记录-编辑")
	@ApiOperation(value="话费直充记录-编辑", notes="话费直充记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody MobileFeeChargeLog mobileFeeChargeLog) {
		mobileFeeChargeLogService.updateById(mobileFeeChargeLog);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "话费直充记录-通过id删除")
	@ApiOperation(value="话费直充记录-通过id删除", notes="话费直充记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		mobileFeeChargeLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "话费直充记录-批量删除")
	@ApiOperation(value="话费直充记录-批量删除", notes="话费直充记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.mobileFeeChargeLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "话费直充记录-通过id查询")
	@ApiOperation(value="话费直充记录-通过id查询", notes="话费直充记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		MobileFeeChargeLog mobileFeeChargeLog = mobileFeeChargeLogService.getById(id);
		if(mobileFeeChargeLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(mobileFeeChargeLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param mobileFeeChargeLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MobileFeeChargeLog mobileFeeChargeLog) {
        return super.exportXls(request, mobileFeeChargeLog, MobileFeeChargeLog.class, "话费直充记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MobileFeeChargeLog.class);
    }


	 /**
	  * 话费直充详细
	  * @param id
	  * @return
	  */
	 @PostMapping(value = "/detail")
	 @ResponseBody
	 public Result<?> detail(@RequestParam(value = "id", required = false, defaultValue ="")String id){
		 if(StringUtils.isEmpty(id)){
			 return Result.error("ID不能为空");
		 }
		 return mobileFeeChargeLogService.detail(id);
	 }
	/**
	 * 通过excel导入充值数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importRechargeExcel", method = RequestMethod.POST)
	public Result<?> importRechargeExcel(HttpServletRequest request, HttpServletResponse response) {
		return this.importExcel(request);
	}

	/**
	 * 通过excel导入数据
	 * @param request
	 * @return
	 */
	public Result<?> importExcel(HttpServletRequest request) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			ImportParams params = new ImportParams();
			params.setTitleRows(0);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<MobileFeeChargeImport> mobileFeeChargeLogList = ExcelImportUtil.importExcel(file.getInputStream(), MobileFeeChargeImport.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				mobileFeeChargeLogList.forEach(item -> {
					if("移动".equals(item.getIsp()) || StringUtils.isEmpty(item.getIsp())){
						MobileFeeChargeLog mobileFeeChargeLog=new MobileFeeChargeLog();
						mobileFeeChargeLog.setMobile(item.getMobile());
						mobileFeeChargeLog.setRemark("导入批量充值");
						/**渠道号*/
						mobileFeeChargeLog.setChannel("00210PP");
						/**产品编码*/
						mobileFeeChargeLog.setCouponId("YD"+item.getPrice());
						mobileFeeChargeLogService.feeChargePlus(mobileFeeChargeLog);
					}else if("联通".equals(item.getIsp())){
						MobileFeeChargeLog mobileFeeChargeLog=new MobileFeeChargeLog();
						mobileFeeChargeLog.setMobile(item.getMobile());
						mobileFeeChargeLog.setRemark("导入批量充值");
						/**渠道号*/
						mobileFeeChargeLog.setChannel("00210PP");
						/**产品编码*/
						mobileFeeChargeLog.setCouponId("LT"+item.getPrice());
						mobileFeeChargeLogService.feeCharge(mobileFeeChargeLog);
					}else if("电信".equals(item.getIsp())){
						MobileFeeChargeLog mobileFeeChargeLog=new MobileFeeChargeLog();
						mobileFeeChargeLog.setMobile(item.getMobile());
						mobileFeeChargeLog.setRemark("导入批量充值");
						/**渠道号*/
						mobileFeeChargeLog.setChannel("00210PP");
						/**产品编码*/
						mobileFeeChargeLog.setCouponId("DX"+item.getPrice());
						mobileFeeChargeLogService.feeCharge(mobileFeeChargeLog);
					}
				});

				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + mobileFeeChargeLogList.size());
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				return Result.error("文件导入失败:" + e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
	}
	/**
	 *   添加
	 *
	 * @param mobileFeeChargeLog
	 * @return
	 */
	//@AutoLog(value = "话费直充记录-添加")
	@ApiOperation(value="话费直充记录-添加", notes="话费直充记录-添加")
	@PostMapping(value = "/heBaoAddPlus")
	public Result<?> heBaoAddPlus(@RequestBody MobileFeeChargeLog mobileFeeChargeLog) {
		return mobileFeeChargeLogService.heBaoAddPlus(mobileFeeChargeLog);
	}



	/**
	 * 通过excel导入充值数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/heBaoImportRechargeExcel", method = RequestMethod.POST)
	public Result<?> heBaoimportRechargeExcel(HttpServletRequest request, HttpServletResponse response) {
		return this.importHeBaoExcel(request);
	}

	/**
	 * 通过excel导入数据
	 * @param request
	 * @return
	 */
	public Result<?> importHeBaoExcel(HttpServletRequest request) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			ImportParams params = new ImportParams();
			params.setTitleRows(0);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<MobileFeeChargeImport> mobileFeeChargeLogList = ExcelImportUtil.importExcel(file.getInputStream(), MobileFeeChargeImport.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				mobileFeeChargeLogList.forEach(item -> {
					if("移动".equals(item.getIsp()) || StringUtils.isEmpty(item.getIsp())){
						MobileFeeChargeLog mobileFeeChargeLog=new MobileFeeChargeLog();
						mobileFeeChargeLog.setMobile(item.getMobile());
						mobileFeeChargeLog.setRemark("导入批量充值");
						/**渠道号*/
						mobileFeeChargeLog.setChannel("00210PP");
						/**产品编码*/
						mobileFeeChargeLog.setCouponId("YD"+item.getPrice());
						mobileFeeChargeLogService.heBaoAddPlus(mobileFeeChargeLog);
					}else if("联通".equals(item.getIsp())){
						MobileFeeChargeLog mobileFeeChargeLog=new MobileFeeChargeLog();
						mobileFeeChargeLog.setMobile(item.getMobile());
						mobileFeeChargeLog.setRemark("导入批量充值");
						/**渠道号*/
						mobileFeeChargeLog.setChannel("00210PP");
						/**产品编码*/
						mobileFeeChargeLog.setCouponId("LT"+item.getPrice());
						mobileFeeChargeLogService.heBaoAddPlus(mobileFeeChargeLog);
					}else if("电信".equals(item.getIsp())){
						MobileFeeChargeLog mobileFeeChargeLog=new MobileFeeChargeLog();
						mobileFeeChargeLog.setMobile(item.getMobile());
						mobileFeeChargeLog.setRemark("导入批量充值");
						/**渠道号*/
						mobileFeeChargeLog.setChannel("00210PP");
						/**产品编码*/
						mobileFeeChargeLog.setCouponId("DX"+item.getPrice());
						mobileFeeChargeLogService.heBaoAddPlus(mobileFeeChargeLog);
					}
				});

				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + mobileFeeChargeLogList.size());
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				return Result.error("文件导入失败:" + e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
	}
}
