package com.eleven.cms.controller;

import com.alipay.api.response.AlipayUserAgreementUnsignResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AliSignChargingOrder;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.service.IAliSignChargingOrderService;
import com.eleven.cms.service.IAliSignRecordService;
import com.eleven.cms.service.IAlipayService;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: cms_ali_sign_charging_order
 * @Author: jeecg-boot
 * @Date:   2022-12-30
 * @Version: V1.0
 */
@Api(tags="cms_ali_sign_charging_order")
@RestController
@RequestMapping("/cms/aliSignChargingOrder")
@Slf4j
public class AliSignChargingOrderController extends JeecgController<AliSignChargingOrder, IAliSignChargingOrderService> {
	 @Autowired
	 private IAliSignChargingOrderService aliSignChargingOrderService;
	 @Autowired
	 private IAliSignRecordService aliSignRecordService;
	 @Autowired
	 private IAlipayService alipayService;
	 private static final Interner<String> interner = Interners.newWeakInterner();


	 /**
      * 分页列表查询
      *
      * @param aliSignChargingOrder
      * @param pageNo
      * @param pageSize
      * @param req
      * @return
      */
	//@AutoLog(value = "cms_ali_sign_charging_order-分页列表查询")
	@ApiOperation(value="cms_ali_sign_charging_order-分页列表查询", notes="cms_ali_sign_charging_order-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AliSignChargingOrder aliSignChargingOrder,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AliSignChargingOrder> queryWrapper = QueryGenerator.initQueryWrapper(aliSignChargingOrder, req.getParameterMap());
		Page<AliSignChargingOrder> page = new Page<AliSignChargingOrder>(pageNo, pageSize);
		IPage<AliSignChargingOrder> pageList = aliSignChargingOrderService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	 /**
	  * 分页列表查询(三方限制使用)
	  *
	  * @param aliSignChargingOrder
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "cms_ali_sign_charging_order-分页列表查询(三方限制使用)")
	 @ApiOperation(value="cms_ali_sign_charging_order-分页列表查询(三方限制使用)", notes="cms_ali_sign_charging_order-分页列表查询(三方限制使用)")
	 @GetMapping(value = "/limitList")
	 public Result<?> queryPageLimitList(AliSignChargingOrder aliSignChargingOrder,
									@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
									@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
									HttpServletRequest req) {
		 QueryWrapper<AliSignChargingOrder> queryWrapper = new QueryWrapper<>();
		 Page<AliSignChargingOrder> page = new Page<AliSignChargingOrder>(pageNo, pageSize);
		 //查询条件为空
		 if(StringUtils.isEmpty(aliSignChargingOrder.getMobile())
				 & StringUtils.isEmpty(aliSignChargingOrder.getOrderNo())
				 & StringUtils.isEmpty(aliSignChargingOrder.getTradeNo())){
			 return Result.ok(page);
		 }
		 queryWrapper.setEntity(aliSignChargingOrder);
		 queryWrapper.orderByDesc("create_time");
		 IPage<AliSignChargingOrder> pageList = aliSignChargingOrderService.page(page, queryWrapper);
		 return Result.ok(pageList);
	 }


	 /**
	 *   添加
	 *
	 * @param aliSignChargingOrder
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_charging_order-添加")
	@ApiOperation(value="cms_ali_sign_charging_order-添加", notes="cms_ali_sign_charging_order-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AliSignChargingOrder aliSignChargingOrder) {
		aliSignChargingOrderService.save(aliSignChargingOrder);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param aliSignChargingOrder
	 * @return
	 */
	@PutMapping(value = "/edit")
	@RequiresPermissions("aliSignChargingOrder:transferFee")
	public Result<?> edit(@RequestBody AliSignChargingOrder aliSignChargingOrder) {
		log.error("支付宝人工转账,订单号:{},退款备注:{}",aliSignChargingOrder.getOrderNo(),aliSignChargingOrder.getRefundRemark());
		if(StringUtils.isEmpty(aliSignChargingOrder.getOrderNo())){
			return Result.error("订单号不能为空");
		}
		if(StringUtils.isEmpty(aliSignChargingOrder.getRefundRemark())){
			return Result.error("退款备注不能为空");
		}
		synchronized (interner.intern(aliSignChargingOrder.getOrderNo())) {
			return aliSignChargingOrderService.aliPayHandMovementTransferFee(aliSignChargingOrder.getOrderNo(),aliSignChargingOrder.getRefundRemark());
		}
	}


	 /**
	  * 添加客服备注
	  * @param orderNo
	  * @param waiterRemark
	  * @return
	  */
	 @PostMapping(value = "/edit/waiterRemark")
	 @ResponseBody
	 @RequiresPermissions("aliSignChargingOrder:transferFee")
	 public Result<?> aliPaySetWaiterRemark(@RequestParam(value = "orderNo", required = false, defaultValue ="")String orderNo,
											@RequestParam(value = "waiterRemark", required = false, defaultValue ="")String waiterRemark) {
		 log.error("添加客服备注,订单号:{},客服备注:{}",orderNo,waiterRemark);
		 if(StringUtils.isEmpty(orderNo)){
			 return Result.error("订单号不能为空");
		 }
		 if(StringUtils.isEmpty(waiterRemark)){
			 return Result.error("客服备注不能为空");
		 }
		 synchronized (interner.intern(orderNo)) {
			 return aliSignChargingOrderService.aliPaySetWaiterRemark(orderNo,waiterRemark);
		 }
	 }

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_charging_order-通过id删除")
	@ApiOperation(value="cms_ali_sign_charging_order-通过id删除", notes="cms_ali_sign_charging_order-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aliSignChargingOrderService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_charging_order-批量删除")
	@ApiOperation(value="cms_ali_sign_charging_order-批量删除", notes="cms_ali_sign_charging_order-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aliSignChargingOrderService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_ali_sign_charging_order-通过id查询")
	@ApiOperation(value="cms_ali_sign_charging_order-通过id查询", notes="cms_ali_sign_charging_order-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AliSignChargingOrder aliSignChargingOrder = aliSignChargingOrderService.getById(id);
		if(aliSignChargingOrder==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aliSignChargingOrder);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aliSignChargingOrder
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AliSignChargingOrder aliSignChargingOrder) {
        return super.exportXls(request, aliSignChargingOrder, AliSignChargingOrder.class, "cms_ali_sign_charging_order");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AliSignChargingOrder.class);
    }
	 /**
	  * 支付宝退款
	  * @param outTradeNo 订单号
	  * @param refund
	  * @return
	  */
	 @ApiOperation(value = "支付宝退款", notes = "支付宝退款")
	 @PostMapping(value = "/aliPay/refund")
	 @ResponseBody
	 @RequiresPermissions("aliSignChargingOrder:refund")
	 public Result aliPayRefund(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo,@RequestParam(value = "refund", required = false, defaultValue ="")String refund){
		 if(StringUtils.isEmpty(outTradeNo)){
			 return Result.error("订单号不能为空");
		 }
		 if(StringUtils.isEmpty(refund)){
			 return Result.error("退款金额不能为空");
		 }
		 synchronized (interner.intern(outTradeNo)) {
			 return aliSignChargingOrderService.aliPayRefund(outTradeNo,refund);
		 }
	 }
	 /**
	  * 支付宝查询退款
	  * @param outTradeNo 订单号
	  * @return
	  */
	 @ApiOperation(value = "支付宝查询退款", notes = "支付宝查询退款")
	 @PostMapping(value = "/aliPay/query/refund")
	 @ResponseBody
	 public Result<?> aliPayQueryRefund(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo){
		 if(StringUtils.isEmpty(outTradeNo)){
			 return Result.error("订单号不能为空");
		 }
		 return aliSignChargingOrderService.aliPayQueryRefund(outTradeNo);
	 }
//	 /**
//	  * 支付宝退款根据手机号退款
//	  * @param mobile 订单号
//	  * @param refund
//	  * @return
//	  */
//	 @ApiOperation(value = "支付宝退款", notes = "支付宝退款")
//	 @PostMapping(value = "/aliPay/refund/mobile")
//	 @ResponseBody
//	 @RequiresPermissions("aliSignChargingOrder:refundMobile")
//	 public Result aliPayRefundByMobile(@RequestParam(value = "mobile", required = false, defaultValue ="")String mobile,@RequestParam(value = "refund", required = false, defaultValue ="")String refund){
//		 if(StringUtils.isEmpty(mobile)){
//			 return Result.error("手机号不能为空");
//		 }
//		 if(StringUtils.isEmpty(refund)){
//			 return Result.error("退款金额不能为空");
//		 }
//		 return aliSignChargingOrderService.aliPayRefundByMobile(mobile,refund);
//	 }



	 /**
	  * 解约
	  * @param outTradeNo
	  * @return
	  */
	 //@AutoLog(value = "解约")
	 @PostMapping(value = "/rescind")
	 @ResponseBody
	 @RequiresPermissions("aliSignChargingOrder:rescind")
	 public Result<?> rescind(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo) {

		 AliSignChargingOrder orderPay=aliSignChargingOrderService.lambdaQuery()
				 .eq(AliSignChargingOrder::getOrderNo, outTradeNo)
				 .in(AliSignChargingOrder::getOrderStatus, 1)
				 .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
		 if(orderPay==null){
			 return Result.error("支付订单不存在");
		 }
		 AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo,orderPay.getExternalAgreementNo()).eq(AliSignRecord::getSignStatus,1).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
		 if(aliSignRecord!=null){
			 AlipayUserAgreementUnsignResponse response= alipayService.alipayRescind(aliSignRecord.getAgreementNo(),aliSignRecord.getBusinessType());
			 String remark="";
			 if(response.isSuccess()){
				 remark="解约调用成功=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
			 } else {
				 remark="解约调用失败=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
			 }
			 aliSignRecordService.lambdaUpdate()
					 .eq(AliSignRecord::getId, aliSignRecord.getId()).set(AliSignRecord::getRemark,remark).update();
			 return Result.ok(remark);
		 }
		 return Result.error("已解约");
	 }

	 /**
	  * 支付宝转账
	  * @param outTradeNo
	  * @param refund
	  * @param payeeType
	  * @return
	  */
	 @ApiOperation(value = "支付宝转账", notes = "支付宝转账")
	 @PostMapping(value = "/aliPay/transfer/fee")
	 @ResponseBody
	 @RequiresPermissions("aliSignChargingOrder:transferFee")
	 public Result aliPayTransferFee(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo,
									 @RequestParam(value = "refund", required = false, defaultValue ="")String refund,
									 @RequestParam(value = "payeeType", required = false, defaultValue ="ALIPAY_USERID")String payeeType,
									 @RequestParam(value = "mobile", required = false, defaultValue ="")String mobile,
									 @RequestParam(value = "businessType", required = false, defaultValue ="MEMBER_TRANSFER_FEE")String businessType){
		 if(StringUtils.isEmpty(outTradeNo)){
			 return Result.error("订单号不能为空");
		 }
		 if(StringUtils.isEmpty(refund)){
			 return Result.error("退款金额不能为空");
		 }
		 synchronized (interner.intern(outTradeNo)) {
			 return aliSignChargingOrderService.aliPayTransferFee(outTradeNo,refund,payeeType,mobile,businessType);
		 }

	 }

	 /**
	  * 支付宝查询转账
	  * @param outTradeNo 订单号
	  * @return
	  */
	 @ApiOperation(value = "支付宝查询转账", notes = "支付宝查询转账")
	 @PostMapping(value = "/aliPay/query/transfer/fee")
	 @ResponseBody
	 public Result<?> aliPayQueryTransferFee(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo,
											 @RequestParam(value = "businessType", required = false, defaultValue ="MEMBER_TRANSFER_FEE")String businessType){
		 if(StringUtils.isEmpty(outTradeNo)){
			 return Result.error("订单号不能为空");
		 }
		 return aliSignChargingOrderService.aliPayQueryTransferFee(outTradeNo,businessType);
	 }
}
