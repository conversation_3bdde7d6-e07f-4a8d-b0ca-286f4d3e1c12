package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CmsAdvInfo;
import com.eleven.cms.service.ICmsAdvInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: cms_adv_info
 * @Author: jeecg-boot
 * @Date:   2022-11-21
 * @Version: V1.0
 */
@Api(tags="cms_adv_info")
@RestController
@RequestMapping("/com.eleven.cms/cmsAdvInfo")
@Slf4j
public class CmsAdvInfoController extends JeecgController<CmsAdvInfo, ICmsAdvInfoService> {
	@Autowired
	private ICmsAdvInfoService cmsAdvInfoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cmsAdvInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_adv_info-分页列表查询")
	@ApiOperation(value="cms_adv_info-分页列表查询", notes="cms_adv_info-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CmsAdvInfo cmsAdvInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CmsAdvInfo> queryWrapper = QueryGenerator.initQueryWrapper(cmsAdvInfo, req.getParameterMap());
		Page<CmsAdvInfo> page = new Page<CmsAdvInfo>(pageNo, pageSize);
		IPage<CmsAdvInfo> pageList = cmsAdvInfoService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cmsAdvInfo
	 * @return
	 */
	//@AutoLog(value = "cms_adv_info-添加")
	@ApiOperation(value="cms_adv_info-添加", notes="cms_adv_info-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CmsAdvInfo cmsAdvInfo) {
		cmsAdvInfoService.save(cmsAdvInfo);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cmsAdvInfo
	 * @return
	 */
	//@AutoLog(value = "cms_adv_info-编辑")
	@ApiOperation(value="cms_adv_info-编辑", notes="cms_adv_info-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody CmsAdvInfo cmsAdvInfo) {
		cmsAdvInfoService.updateById(cmsAdvInfo);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_adv_info-通过id删除")
	@ApiOperation(value="cms_adv_info-通过id删除", notes="cms_adv_info-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		cmsAdvInfoService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_adv_info-批量删除")
	@ApiOperation(value="cms_adv_info-批量删除", notes="cms_adv_info-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cmsAdvInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_adv_info-通过id查询")
	@ApiOperation(value="cms_adv_info-通过id查询", notes="cms_adv_info-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CmsAdvInfo cmsAdvInfo = cmsAdvInfoService.getById(id);
		if(cmsAdvInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(cmsAdvInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmsAdvInfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsAdvInfo cmsAdvInfo) {
        return super.exportXls(request, cmsAdvInfo, CmsAdvInfo.class, "cms_adv_info");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CmsAdvInfo.class);
    }

}
