package com.eleven.cms.controller;

import java.io.*;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.remote.JiangxiYidongService;
import com.eleven.cms.remote.JiangxiYidongV2Service;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.JiangxiYidongPdfResult;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.ISubscribeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 渠道订阅
 * @Author: jeecg-boot
 * @Date:   2020-09-23
 * @Version: V1.0
 */
@Api(tags="渠道订阅")
@RestController
@RequestMapping("/cms/subscribe")
@Slf4j
public class SubscribeController extends JeecgController<Subscribe, ISubscribeService> {
	@Autowired
	private ISubscribeService subscribeService;
    @Autowired
    private JiangxiYidongV2Service jiangxiYidongV2Service;
	
	/**
	 * 分页列表查询
	 *
	 * @param subscribe
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "渠道订阅-分页列表查询")
	@ApiOperation(value="渠道订阅-分页列表查询", notes="渠道订阅-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(Subscribe subscribe,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<Subscribe> queryWrapper = QueryGenerator.initQueryWrapper(subscribe, req.getParameterMap());
		Page<Subscribe> page = new Page<Subscribe>(pageNo, pageSize);
		IPage<Subscribe> pageList = subscribeService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param subscribe
	 * @return
	 */
	////@AutoLog(value = "渠道订阅-添加")
	@ApiOperation(value="渠道订阅-添加", notes="渠道订阅-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody Subscribe subscribe) {
		subscribeService.createSubscribeDbAndEs(subscribe);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param subscribe
	 * @return
	 */
	////@AutoLog(value = "渠道订阅-编辑")
	@ApiOperation(value="渠道订阅-编辑", notes="渠道订阅-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody Subscribe subscribe) {
		subscribeService.updateSubscribeDbAndEs(subscribe);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	////@AutoLog(value = "渠道订阅-通过id删除")
	@ApiOperation(value="渠道订阅-通过id删除", notes="渠道订阅-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		subscribeService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	////@AutoLog(value = "渠道订阅-批量删除")
	@ApiOperation(value="渠道订阅-批量删除", notes="渠道订阅-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.subscribeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "渠道订阅-通过id查询")
	@ApiOperation(value="渠道订阅-通过id查询", notes="渠道订阅-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		Subscribe subscribe = subscribeService.getById(id);
		if(subscribe==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(subscribe);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param subscribe
    */
    @RequestMapping(value = "/exportXls")
	@RequiresPermissions("subscribe:import")
    public ModelAndView exportXls(HttpServletRequest request, Subscribe subscribe) {
        return super.exportXls(request, subscribe, Subscribe.class, "渠道订阅",
                "mobile","isp","biz_type","province","city","channel","sub_channel","owner","copyright_id","status","result","verify_status","verify_status_daily","extra","referer","device_info","ip","open_time","create_time");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Subscribe.class);
    }

     /**
      * 下载pdf
      *
      * @param id
      * @return
      */
     @RequestMapping(value = "/downLoadPdf", method = RequestMethod.GET)
     public Result<?> importExcel(@RequestParam String id,HttpServletResponse response) throws IOException {
         Subscribe subscribe = subscribeService.getById(id);
         if (subscribe == null || !BizConstant.BIZ_TYPE_JXYD.equals(subscribe.getBizType())) {
             return Result.error("未找到对应数据");
         }
         JiangxiYidongPdfResult jiangxiYidongPdfResult = jiangxiYidongV2Service.getPdf(subscribe.getServiceId(),subscribe.getRemark());
         if(jiangxiYidongPdfResult.isOk()){
             String fileName = subscribe.getJcmccChannel() + "_" + subscribe.getMobile() + "_" + DateUtil.formatFullTime(LocalDateTime.now()) + ".pdf";
             response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
             response.setContentType("application/octet-stream");
             OutputStream outputStream = response.getOutputStream();
             IOUtils.copy(new ByteArrayInputStream(Base64.getDecoder().decode(jiangxiYidongPdfResult.getData().getResultMsg().getResult().replaceAll("\\n",""))), outputStream);
         }
         return Result.ok("OK");
     }


 }
