package com.eleven.cms.controller;

import com.eleven.cms.aivrbt.service.IAiRingVideoService;
import com.eleven.cms.config.CaixunProperties;
import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.job.SubscribeVerifyTempWorker;
import com.eleven.cms.job.SubscribeVerifyWorker;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.qycl.service.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 */
@Api(tags = "test")
@RestController
@RequestMapping("/test")
@Slf4j
public class ExecController {
    @Autowired
    SubscribeVerifyWorker subscribeVerifyWorker;
    @Autowired
    SubscribeVerifyTempWorker subscribeVerifyTempWorker;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    ISubscribeService subscribeService;

    @Autowired
    MiguApiService miguApiService;

    @Autowired
    IEsDataService esDataService;

    @Autowired
    LiantongVrbtService liantongVrbtService;

    @Autowired
    ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;

    @Autowired
    TtsService ttsService;

    @Autowired
    IQyclCompanyService qyclCompanyService;

    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;

    @Autowired
    IQyclRingService qyclRingService;

    @Autowired
    IQyclCompanyMemberService qyclCompanyMemberService;
    @Autowired
    IDataNotifyLogService dataNotifyLogService;
    @Autowired
    IVrbtZeroOrderService vrbtZeroOrderService;
    @Autowired
    private SubscribeResultNotifyService subscribeResultNotifyService;
    @Autowired
    private ICouponCodeService couponCodeService;
    @Autowired
    private SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private ICouponCodeRepeatOrderService couponCodeRepeatOrderService;
    @Autowired
    private IHetuCouponCodeService hetuCouponCodeService;
    @Autowired
    private ISwfzCouponCodeService swfzCouponCodeService;
    @Autowired
    private IDyxcCouponCodeService dyxcCouponCodeService;
    @Autowired
    private ICfdtwCouponCodeService cfdtwCouponCodeService;
    @Autowired
    private ISjcsCouponCodeService sjcsCouponCodeService;
    @Autowired
    private IAhzzCouponCodeService ahzzCouponCodeService;
    @Autowired
    private ICmsCaixunUnsubscribeService cmsCaixunUnsubscribeService;
    @Autowired
    private RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private IAiRingVideoService aiRingVideoService;
    @Autowired
    private SiChuanMobileApiService siChuanMobileApiService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    @Autowired
    CaixunProperties caixunProperties;
    @Autowired
    ElasticsearchRestTemplate restTemplate;
    @Autowired
    private ISmsModelService smsModelService;
    ////@AutoLog(value = "通用测试回调")
//    @ApiOperation(value = "通用测试回调", notes = "通用测试回调")
//    @RequestMapping(value = "/notifyLog")
//    public String notifyLog(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
//
//        log.info("通用测试回调,参数map:{},json数据:{}", requestMap, jsonNode);
//        if (jsonNode == null) {
//            return "OK";
//        }
//        String caixunChannelCode = jsonNode.at("/channelCode").asText();
//        String channelCode = caixunProperties.getChannelCodeMap().entrySet().stream().collect(Collectors.toMap(entity -> entity.getValue(), entity -> entity.getKey())).get(caixunChannelCode);
//        if (StringUtils.isBlank(channelCode)) {
//            return "OK";
//        }
//        CmsCaixunUnsubscribe cmsCaixunUnsubscribe = mapper.convertValue(jsonNode, CmsCaixunUnsubscribe.class);
//        cmsCaixunUnsubscribe.setChannelCode(channelCode);
//        cmsCaixunUnsubscribeService.save(cmsCaixunUnsubscribe);
//        return "OK";
//    }
//
//    @ApiOperation(value = "测试生成session1", notes = "测试生成session1")
//    @RequestMapping(value = "/genSession1")
//    public String genSession1(HttpServletRequest request) {
//        return request.getSession().getId();
//    }
//
//    @ApiOperation(value = "测试生成session2", notes = "测试生成session2")
//    @RequestMapping(value = "/genSession2")
//    public String genSession2(HttpServletRequest request) {
//        final HttpSession session = request.getSession();
//        session.setAttribute("key", "value");
//        return session.getId();
//    }
//
//    @RequestMapping(value = "/sendMqMsg")
//    public String sendMqMsg() {
//        for (int i = 0; i < 20; i++) {
//            final DelayedMessage delayedMessage = new DelayedMessage("id" + i, "tag" + i, "msg" + i, "extra1hour");
//            rabbitTemplate.convertAndSend(RabbitMQConfig.SUBSCRIBE_EVENT_EXCHANGE_NAME, RabbitMQConfig.SUBSCRIBE_VERIFY_DELAY_1HOUR_QUEUE_ROUTING_KEY, delayedMessage);
//            log.info("发送延迟队列消息:"+delayedMessage);
//            delayedMessage.setExtra("extra3day");
//            rabbitTemplate.convertAndSend(RabbitMQConfig.SUBSCRIBE_EVENT_EXCHANGE_NAME, RabbitMQConfig.SUBSCRIBE_VERIFY_DELAY_3DAY_QUEUE_ROUTING_KEY, delayedMessage);
//            log.info("发送延迟队列消息:"+delayedMessage);
//            delayedMessage.setExtra("extra1day");
//            rabbitTemplate.convertAndSend(RabbitMQConfig.SUBSCRIBE_EVENT_EXCHANGE_NAME, RabbitMQConfig.SUBSCRIBE_VERIFY_DELAY_1DAY_QUEUE_ROUTING_KEY, delayedMessage);
//            log.info("发送延迟队列消息:"+delayedMessage);
//        }
//        return "OK";
//    }

    //@RequestMapping(value = "/exec")
    //public Object exec(String bean, String method, String[] args) {
    //    Object t = SpringContextUtils.getBean(bean); // fetch the bean instance
    //
    //    Method[] allMethods = t.getClass().getDeclaredMethods();
    //    for (Method m : allMethods) {
    //        String mname = m.getName();
    //        if (!mname.startsWith(method)) {
    //            continue;
    //        }
    //        try {
    //            m.setAccessible(true);
    //            return m.invoke(t, args);
    //        } catch (InvocationTargetException | IllegalAccessException x) {
    //            Throwable cause = x.getCause();
    //            System.err.format("invocation of %s failed: %s%n", mname, cause.getMessage());
    //            return "执行异常";
    //        }
    //    }
    //
    //    return "未找到要执行的方法";
    //}

    /**
     * 重置前6个月需要校验的数据
     *
     * @return
     */
//    @ResponseBody
//    @RequestMapping(value = "/verify/reset")
//    public String verifyReset() {
//        int count = subscribeVerifyWorker.resetLast6MonthToVerify();
//        log.info("重置前6个月需要校验的数据{}条", count);
//        return "已更新" + count + "条";
//    }
//
//    /**
//     * 开始校验前6个月的数据
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/verify/start")
//    public String verifyStart() {
//        boolean start = subscribeVerifyWorker.start();
//        return start ? "OK" : "FAIL";
//    }
//
//    /**
//     * 停止校验前6个月的数据
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/verify/stop")
//    public String verifyStop() {
//        subscribeVerifyWorker.stop();
//        return "OK";
//    }
//
//    /**
//     * 开始临时包月校验
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/tempVerify/start")
//    public String tempVerifyStart() {
//        boolean start = subscribeVerifyTempWorker.start();
//        return start ? "OK" : "FAIL";
//    }
//
//    /**
//     * 停止临时包月校验
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/tempVerify/stop")
//    public String tempVerifyStop() {
//        subscribeVerifyTempWorker.stop();
//        return "OK";
//    }
//
//    /**
//     * 生成存量校验数据
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/verify/genReport")
//    public String verifyGenReport(@RequestParam(value = "channel", required = false, defaultValue ="")  String channel) {
//        if(StringUtils.isBlank(channel)){
//            subscribeVerifyService.genLast6MonthVerifyReport();
//        }else {
//            subscribeVerifyService.genChannelSubStatisticsAndReport(channel);
//        }
//
//        return "OK";
//    }

    ///**
    // * 手动校验
    // *
    // * @return
    // */
    //@ResponseBody
    //@RequestMapping(value = "/verify/custom")
    //public String verifyCustom() {
    //    subscribeService.lambdaQuery().select(Subscribe::getId, Subscribe::getMobile, Subscribe::getChannel)
    //            .in(Subscribe::getChannel, "014X04C", "014X04E", "014X04F")
    //            .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
    //            .eq(Subscribe::getBizType, BizConstant.BIZ_TYPE_VRBT)
    //            .eq(Subscribe::getIsp, "1")
    //            .isNull(Subscribe::getProvinceCode)
    //            .list()
    //            .forEach(sub -> {
    //                String mobile = sub.getMobile();
    //                String channel = sub.getChannel();
    //                Integer monthVerify = miguApiService.verifyMiguVrbtMonthStatus(mobile, channel, false) > 0 ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
    //                subscribeService.lambdaUpdate().eq(Subscribe::getId, sub.getId()).set(Subscribe::getProvinceCode, String.valueOf(monthVerify)).update();
    //            });
    //    return "OK";
    //}

    /**
     * 我放三方支付视频彩铃0元订购
     */
//    @ResponseBody
//    @RequestMapping(value = "/vrbtZeroOrder")
//    public String vrbtZeroOrder(@RequestParam Integer fromId,
//                                @RequestParam Integer amount,
//                                @RequestParam(value = "complainEventId", required = false, defaultValue ="5000") Integer intervalMaxMill)  {
//        vrbtZeroOrderService.vrbtZeroOrder(fromId,amount,intervalMaxMill );
//        return "OK";
//    }
//    /**
//     * 讯飞三方支付视频彩铃0元订购
//     */
//    @ResponseBody
//    @RequestMapping(value = "/vrbtZeroOrderXunfei")
//    public String vrbtZeroOrderXunfei(@RequestParam Integer fromId,
//                                @RequestParam Integer amount,
//                                @RequestParam(value = "complainEventId", required = false, defaultValue ="5000") Integer intervalMaxMill)  {
//        vrbtZeroOrderService.vrbtZeroOrderXunfei(fromId,amount,intervalMaxMill );
//        return "OK";
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/setTestMobiles/{mobiles}")
//    public Object verifyStop(@PathVariable String mobiles) {
//        redisUtil.set(CacheConstant.TEST_MOBILE_REDIS_KEY, mobiles);
//        return redisUtil.get(CacheConstant.TEST_MOBILE_REDIS_KEY);
//    }
//
//    /**
//     * 手动检测未校验数据 订购状态
//     */
//    @ResponseBody
//    @RequestMapping(value = "/manualVerify")
//    public String manualVerify(@RequestParam String dateStr) throws Exception {
//        log.info("开始校验,数据日期:{}", dateStr);
//        try {
//            LocalDate localDate = DateUtils.toLocalDate(dateStr);
//            LocalDateTime start = localDate.atTime(LocalTime.MIN);
//            LocalDateTime end = localDate.atTime(LocalTime.MAX);
//
//            subscribeService.lambdaQuery().between(Subscribe::getCreateTime, start, end)
//                    .eq(Subscribe::getStatus, 1).eq(Subscribe::getIsp, "1")
//                    .eq(Subscribe::getBizType, BizConstant.BIZ_TYPE_VRBT)
//                    .eq(Subscribe::getChannel, "00210QY")
//                    .eq(Subscribe::getVerifyStatus, -1).list()
//                    .forEach(subscribe -> {
//                        Integer result = monthVerify(subscribe);
//                        Subscribe udp = new Subscribe();
//                        udp.setId(subscribe.getId());
//                        udp.setVerifyStatus(result);
//                        subscribeService.updateSubscribeDbAndEsSync(udp);
//                    });
//
//        } catch (Exception e) {
//            throw new Exception("无效的日期");
//        }
//        log.info("结束校验,数据日期:{}", dateStr);
//        return "OK";
//    }
//
//
//    @ResponseBody
//    @RequestMapping(value = "/switchBizHnyd/{param}")
//    public Object switchBizHnyd(@PathVariable String param) {
//        //参数值为1则 关闭业务
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_BIZ_SWITCH_HNYD, param);
//        } else {
//            //打开业务
//            redisUtil.del(CacheConstant.CMS_BIZ_SWITCH_HNYD);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HNYD) ? "已成功关闭业务" : "已成功开启业务";
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/switchBizHnydQwzj/{param}")
//    public Object switchBizHnydQwzj(@PathVariable String param) {
//        //参数值为1则 关闭业务
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_BIZ_SWITCH_HNYD_QWZJ, param);
//        } else {
//            //打开业务
//            redisUtil.del(CacheConstant.CMS_BIZ_SWITCH_HNYD_QWZJ);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HNYD_QWZJ) ? "已成功关闭业务" : "已成功开启业务";
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/switchBizHnydMgb/{param}")
//    public Object switchBizHnydMgb(@PathVariable String param) {
//        //参数值为1则 关闭业务
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_BIZ_SWITCH_HNYD_MGB, param);
//        } else {
//            //打开业务
//            redisUtil.del(CacheConstant.CMS_BIZ_SWITCH_HNYD_MGB);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HNYD_MGB) ? "已成功关闭业务" : "已成功开启业务";
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/switchBizGzyd/{param}")
//    public Object switchBizGzyd(@PathVariable String param) {
//        //参数值为1则 关闭业务
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_BIZ_SWITCH_GZYD, param);
//        } else {
//            //打开业务
//            redisUtil.del(CacheConstant.CMS_BIZ_SWITCH_GZYD);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_GZYD) ? "已成功关闭业务" : "已成功开启业务";
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/switchBizHljyd/{param}")
//    public Object switchBizHljyd(@PathVariable String param) {
//        //参数值为1则 关闭业务
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_BIZ_SWITCH_HLJYD, param);
//        } else {
//            //打开业务
//            redisUtil.del(CacheConstant.CMS_BIZ_SWITCH_HLJYD);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HLJYD) ? "已成功关闭业务" : "已成功开启业务";
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/vrbtSwitchScyd/{param}")
//    public Object vrbtSwitchScyd(@PathVariable String param) {
//        //参数值为1则 打开业务切换
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_VRBT_SWITCH_SCYD, param);
//        } else {
//            //关闭切换
//            redisUtil.del(CacheConstant.CMS_VRBT_SWITCH_SCYD);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_VRBT_SWITCH_SCYD) ? "已成功开启业务切换" : "已成功关闭业务切换";
//
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/zhbSwitchScyd/{param}")
//    public Object zhbSwitchScyd(@PathVariable String param) {
//        //参数值为1则 打开业务切换
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_ZHB_SWITCH_SCYD, param);
//        } else {
//            //关闭切换
//            redisUtil.del(CacheConstant.CMS_ZHB_SWITCH_SCYD);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_ZHB_SWITCH_SCYD) ? "已成功开启业务切换" : "已成功关闭业务切换";
//
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/vrbtSwitchJsyd/{param}")
//    public Object vrbtSwitchJsyd(@PathVariable String param) {
//        //参数值为1则 打开业务切换
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_VRBT_SWITCH_JSYD, param);
//        } else {
//            //关闭切换
//            redisUtil.del(CacheConstant.CMS_VRBT_SWITCH_JSYD);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_VRBT_SWITCH_JSYD) ? "已成功开启业务切换" : "已成功关闭业务切换";
//
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/vrbtSwitchGdyd/{param}")
//    public Object vrbtSwitchGdyd(@PathVariable String param) {
//        //参数值为1则 打开业务切换
//        if (StringUtil.equals(String.valueOf(SUBSCRIBE_STATUS_SUCCESS), param)) {
//            redisUtil.set(CacheConstant.CMS_VRBT_SWITCH_GDYD, param);
//        } else {
//            //关闭切换
//            redisUtil.del(CacheConstant.CMS_VRBT_SWITCH_GDYD);
//        }
//        return redisUtil.hasKey(CacheConstant.CMS_VRBT_SWITCH_GDYD) ? "已成功开启业务切换" : "已成功关闭业务切换";
//
//    }
//
//
//
//    @ResponseBody
//    @RequestMapping(value = "/queryVrbtSwitchStatus")
//    public Object queryVrbtSwitchStatus() {
//        HashMap<String, Object> dataMap = new HashMap();
//        BizConstant.SWITCH_PROVICE_KEYS.forEach(key -> {
//            if (CacheConstant.CMS_BIZ_SWITCH_HNYD.equals(key)) {
//                dataMap.put("hnydSwitchStatus", !redisUtil.hasKey(key));
//            } else if (CacheConstant.CMS_BIZ_SWITCH_HNYD_QWZJ.equals(key)) {
//                dataMap.put("hnydSwitchQwzjStatus", !redisUtil.hasKey(key));
//            }else if (CacheConstant.CMS_BIZ_SWITCH_HNYD_MGB.equals(key)) {
//                dataMap.put("hnydSwitchMgbStatus", !redisUtil.hasKey(key));
//            }else if (CacheConstant.CMS_BIZ_SWITCH_HLJYD.equals(key)) {
//                dataMap.put("hljydSwitchStatus", !redisUtil.hasKey(key));
//            } else if (CacheConstant.CMS_VRBT_SWITCH_SCYD.equals(key)) {
//                dataMap.put("vrbtSwitchScydStatus", redisUtil.hasKey(key));
//            } else if (CacheConstant.CMS_ZHB_SWITCH_SCYD.equals(key)) {
//                dataMap.put("zhbSwitchScydStatus", redisUtil.hasKey(key));
//            } else if (CacheConstant.CMS_VRBT_SWITCH_GDYD.equals(key)) {
//                dataMap.put("vrbtSwitchGdydStatus", redisUtil.hasKey(key));
//            } else if (CacheConstant.CMS_VRBT_SWITCH_JSYD.equals(key)) {
//                dataMap.put("vrbtSwitchJsydStatus", redisUtil.hasKey(key));
//            }else if (CacheConstant.CMS_BIZ_SWITCH_GZYD.equals(key)) {
//                dataMap.put("gzydSwitchStatus", !redisUtil.hasKey(key));
//            }
//        });
//        return dataMap;
//    }
//
//
//
//    public Integer monthVerify(Subscribe subscribe) {
//        int status = -1;
//        switch (subscribe.getBizType()) {
//            case BizConstant.BIZ_TYPE_VRBT:
//                status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), subscribe.getChannel(), false) > 0 ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
//                break;
//            case BizConstant.BIZ_TYPE_CPMB:
//                final RemoteResult result = miguApiService.cpmbQuery(subscribe.getMobile(), subscribe.getChannel());
//                status = result.isCpmbMember() ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
//                break;
//        }
//        return status;
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/dianxin/setChannel/{channelCode}")
//    public Object dianxinChannelSwitch(@PathVariable String channelCode) {
//        //设置缓存有效时间到当天24时结束
//        redisUtil.set(CacheConstant.DIANXIN_CURRENT_CHANNEL_KEY, channelCode, DateUtil.getSecondByNowDiffEndOfDay());
//        return redisUtil.get(CacheConstant.DIANXIN_CURRENT_CHANNEL_KEY);
//    }
//
//
//    @ResponseBody
//    @RequestMapping(value = "/hljyd/{count}")
//    public Object setHljydVrbtCount(@PathVariable Integer count) {
//        redisUtil.set(CacheConstant.CMS_CACHE_BIZ_HLJYD_VRBT, count);
//        return redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_VRBT);
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/hljyd/dy/{count}")
//    public Object setHljydDyVrbtCount(@PathVariable Integer count) {
//        redisUtil.set(CacheConstant.CMS_CACHE_BIZ_HLJYD_DYVRBT, count);
//        return redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_DYVRBT);
//    }
//
//
//    /**
//     * 获取四川移动渠道号
//     *
//     * @return
//     */
//    @RequestMapping("/getScydChannelList")
//    @ResponseBody
//    public Object getScydChannelList() throws JsonProcessingException {
//        String vrSelectChannel = (String) redisUtil.get(CacheConstant.CMS_VR_SELECT_SCYD);
//        List<String> channelList = sichuanMobileFlowPacketProperties.getChannelList();
//        AtomicBoolean selectd = new AtomicBoolean(false);
//        channelList.add(0,"关闭");
//        List<Map<String, Object>> list = channelList.stream().map(str -> {
//            Map<String, Object> map = new HashMap<>();
//            map.put("key", str);
//            map.put("label", str);
//            if (StringUtils.equals(vrSelectChannel, str)) {
//                map.put("selected", true);
//                selectd.set(true);
//            }
//            return map;
//        }).collect(Collectors.toList());
//        if(!selectd.get()){
//            list.get(0).put("selected", true);
//        }
//        return list;
//    }
//
//    /**
//     * 获取四川移动渠道号
//     *
//     * @return
//     */
//    @RequestMapping("/vrSettingScydChannel")
//    @ResponseBody
//    public Object vrSettingScydChannel(String channel) throws JsonProcessingException {
//        if(StringUtils.equals(channel,"关闭")) {
//            redisUtil.del(CacheConstant.CMS_VR_SELECT_SCYD);
//        }else{
//            redisUtil.set(CacheConstant.CMS_VR_SELECT_SCYD, channel);
//        }
//        return "OK";
//    }
//
//
//    @ResponseBody
//    @RequestMapping(value = "/syncEsData")
//    public String syncEsData() {
//        esDataService.syncEsData();
//        return "OK";
//    }

//    @ResponseBody
//    @RequestMapping(value = "/addDelayedQueue")
//    public String addDelayedQueue() {
//        Date start = DateUtils.str2Date("2022-03-15 00:00:00", DateUtils.datetimeFormat.get());
//        Date end = DateUtils.str2Date("2022-03-17 10:20:00", DateUtils.datetimeFormat.get());
//        LocalDateTime now = LocalDateTime.now();
//        subscribeService.lambdaQuery().between(Subscribe::getCreateTime, start, end)
//                .eq(Subscribe::getStatus, 1).eq(Subscribe::getIsp, "1")
//                .eq(Subscribe::getBizType, BizConstant.BIZ_TYPE_VRBT)
//                .eq(Subscribe::getChannel, "00210QY")
//                .eq(Subscribe::getVerifyStatusDaily, -1).list()
//                .forEach(subscribe -> {
//                    long plusDays = Period.between(subscribe.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), now.toLocalDate()).getDays();
//                    plusDays = plusDays + 1;
//                    long nextDayDelayMinutesMin = Duration.between(now, now.toLocalDate().plusDays(plusDays).atTime(2, 0, 0)).toMinutes();
//                    long nextDayDelayMinutesMax = Duration.between(now, now.toLocalDate().plusDays(plusDays).atTime(7, 0, 0)).toMinutes();
//                    long nextDayDelayMinutes = ThreadLocalRandom.current().nextLong(nextDayDelayMinutesMin, nextDayDelayMinutesMax);
//                    redisDelayedQueueManager.add(DelayedMessage.builder().tag(MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY).id(subscribe.getId()).msg("包月状态延迟3天校验").extra(
//                            MESSAG_EXTRA_3_DAY).build(), nextDayDelayMinutes, TimeUnit.MINUTES);
//                });
//        return "OK";
//    }
//
//    @RequestMapping("/generateWord")
//    public String generateWord() throws IOException {
//        Configuration configuration = new Configuration();
//        configuration.setDefaultEncoding("UTF-8");
//        configuration.setClassForTemplateLoading(this.getClass(), "/templates/complaint/");
//        Template template = configuration.getTemplate("new_template.ftl");

//        UserInfo userInfo = new UserInfo();
//        userInfo.setMobile("19896680955");
//        userInfo.setD("2022-05-05 11:49:10");
//        userInfo.setE("2022-05-05 11:47:33");
//        userInfo.setF("2022-05-05 11:47:13");
//        userInfo.setG("2022-05-05 11:47:10");
//        userInfo.setH("2022-05-05 11:47:03");
//        userInfo.setI("2022-05-05 11:46:53");
//        userInfo.setJ("2022-05-05  11:46:46");
//        userInfo.setK("2022-05-05 11:46:38");
//        userInfo.setL("2022-05-05 11:46:31");
//        userInfo.setM("2022-05-05 11:46:23");
//        userInfo.setN("1536583744636993538&uid=1536583744121094146");
//        userInfo.setO("1536583745018675202&uid=1536583744121094146");
//        userInfo.setP("1536583745446494210");
//        userInfo.setQ("1536583745987559425");
//        userInfo.setR("雪程");
//        userInfo.setS("638799T3924");


//        ImportParams params = new ImportParams();
//        params.setTitleRows(0);
//        params.setHeadRows(1);
//        params.setNeedSave(true);
//        List<UserInfo> dataList = ExcelImportUtil.importExcel(new File("D:\\data.xls"), UserInfo.class, params);
//        long start = System.currentTimeMillis();
//
//        for (int i = 0; i < dataList.size(); i++) {
//            UserInfo userInfo = dataList.get(i);
//            userInfo.setImg1(imageToString(1, userInfo.getMobile(), i + 1));
//            userInfo.setImg2(imageToString(2, userInfo.getMobile(), i + 1));
//            userInfo.setImg3(imageToString(3, userInfo.getMobile(), i + 1));
//            userInfo.setImg4(imageToString(4, userInfo.getMobile(), i + 1));
//            userInfo.setImg5(imageToString(5, userInfo.getMobile(), i + 1));
//        }
//        log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
//
//        File outFile = new File("D:\\test.docx");
//        Writer out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "UTF-8"));
//        try {
//            Map<String, Object> freeMarkerMap = new HashMap<>();
//            freeMarkerMap.put("userList", dataList);
//            template.process(freeMarkerMap, out);
//        } catch (TemplateException e) {
//            e.printStackTrace();
//        } finally {
//            out.flush();
//            out.close();
//
//        }
//        return "OK";
//    }
//
//    @RequestMapping("/generateFjWord")
//    public String generateFjWord() throws IOException {
//        Configuration configuration = new Configuration();
//        configuration.setDefaultEncoding("UTF-8");
//        configuration.setClassForTemplateLoading(this.getClass(), "/templates/complaint/");
//        Template template = configuration.getTemplate("template_fj.ftl");
//
////        UserInfo userInfo = new UserInfo();
////        userInfo.setMobile("19896680955");
////        userInfo.setD("2022-05-05 11:49:10");
////        userInfo.setE("2022-05-05 11:47:33");
////        userInfo.setF("2022-05-05 11:47:13");
////        userInfo.setG("2022-05-05 11:47:10");
////        userInfo.setH("2022-05-05 11:47:03");
////        userInfo.setI("2022-05-05 11:46:53");
////        userInfo.setJ("2022-05-05  11:46:46");
////        userInfo.setK("2022-05-05 11:46:38");
////        userInfo.setL("2022-05-05 11:46:31");
////        userInfo.setM("2022-05-05 11:46:23");
////        userInfo.setN("1536583744636993538&uid=1536583744121094146");
////        userInfo.setO("1536583745018675202&uid=1536583744121094146");
////        userInfo.setP("1536583745446494210");
////        userInfo.setQ("1536583745987559425");
////        userInfo.setR("雪程");
////        userInfo.setS("638799T3924");
//
//
//        ImportParams params = new ImportParams();
//        params.setTitleRows(0);
//        params.setHeadRows(1);
//        params.setNeedSave(true);
//        List<UserInfo> dataList = ExcelImportUtil.importExcel(new File("D:\\data_fj.xlsx"), UserInfo.class, params);
//        long start = System.currentTimeMillis();
//
////        for (int i = 0; i < dataList.size(); i++) {
////            UserInfo userInfo = dataList.get(i);
////            userInfo.setImg1(imageToString(1, userInfo.getMobile(), i + 1));
////            userInfo.setImg2(imageToString(2, userInfo.getMobile(), i + 1));
////            userInfo.setImg3(imageToString(3, userInfo.getMobile(), i + 1));
////            userInfo.setImg4(imageToString(4, userInfo.getMobile(), i + 1));
////            userInfo.setImg5(imageToString(5, userInfo.getMobile(), i + 1));
////        }
////        log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
//
//        File outFile = new File("D:\\test_fj.docx");
//        Writer out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "UTF-8"));
//        try {
//            Map<String, Object> freeMarkerMap = new HashMap<>();
//            freeMarkerMap.put("userList", dataList);
//            template.process(freeMarkerMap, out);
//        } catch (TemplateException e) {
//            e.printStackTrace();
//        } finally {
//            out.flush();
//            out.close();
//            return "OK";
//        }
//    }

//    public static String imageToString(Integer imgSequence, String mobile, Integer sequence) {
//        String imgFile = "";
//        switch (imgSequence) {
//            case 1:
//                imgFile = "D:\\jietu\\img\\" + mobile + ".png";
//                break;
//            case 2:
//                imgFile = "D:\\jietu\\1-1\\" + sequence + "-1.png";
//                break;
//            case 3:
//                imgFile = "D:\\jietu\\1-1\\" + sequence + "-2.png";
//                break;
//            case 4:
//                imgFile = "D:\\jietu\\1-1\\" + sequence + "-3.png";
//                break;
//            case 5:
//                imgFile = "D:\\jietu\\1-1\\" + sequence + "-4.png";
//                break;
//        }
//        InputStream in = null;
//        byte[] data = null;
//        try {
//            log.info("图片路径:" + imgFile);
//            in = new FileInputStream(imgFile);
//            data = new byte[in.available()];
//            in.read(data);
//            in.close();
//        } catch (IOException e) {
//            log.error("文件：{}未找到", imgFile);
//        } finally {
//            if (data != null) {
//                return Base64Utils.encodeToString(data);
//            } else {
//                return "";
//            }
//        }
//
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/ltSendMsg")
//    public Object ltSendMsg() {
//        return liantongVrbtService.sendMsg("18613222501",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
//    }
//
//    /**
//     * tts测试
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/tts")
//    public String testTts(String text) {
//        return QyclConstant.RING_FILE_BASE_URL+ttsService.textToVoiceFile(text);
//    }
//
//    /**
//     * tts测试
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/ffmpeg")
//    public String testFfmpeg(String text) throws IOException, InterruptedException {
//
//        FfmpegUtils.merge("/data/audio/video.mp4",
//                "/data/audio/audio.mp3",
//                "/data/audio/java-out.mp4");
//
//        return "OK";
//    }
//
//    /**
//     * tts测试
//     *
//     * @return
//     */
//    @ResponseBody
//    @RequestMapping(value = "/ipLocation")
//    public String ipLocation(@RequestParam String ip) {
//
//        String[] ipLocation = IPUtils.ipLocation(ip);
//        return ipLocation[0]+" "+ipLocation[1]+" "+ipLocation[2];
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/qyclManualProcess")
//    public String qyclManualProcess() {
//        LocalDate localDate = DateUtils.toLocalDate("2023-02-19");
//        LocalDateTime start = localDate.atTime(LocalTime.MIN);
//        localDate = DateUtils.toLocalDate("2023-02-20");
//        LocalDateTime end = localDate.atTime(10,0,0);
//        List<QyclCompany> list = qyclCompanyService.lambdaQuery().isNull(QyclCompany::getDepartmentId).isNotNull(QyclCompany::getPayTime).ge(QyclCompany::getCreateTime, start).le(QyclCompany::getCreateTime,end).list();
//        for (QyclCompany qyclCompany : list) {
//            //创建部门(替换掉特殊字符)
//            String departmentName = qyclCompany.getTitle().replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
//            departmentName = StringUtils.substring(departmentName, 0, 19);
//            final EntVrbtResult departmentEntVrbtResult = enterpriseVrbtService.ecOperationByChannel("0", departmentName + qyclCompany.getMobile(), "",qyclCompany.getCompanyOwner(),qyclCompany.getChannel());
//            String departmentId = null;
//            if (departmentEntVrbtResult.getData() != null) {
//                departmentId = departmentEntVrbtResult.getData().at("/departmentId").asText();
//                qyclCompany.setDepartmentId(departmentId);
//                qyclCompanyService.updateById(qyclCompany);
//            }
//            if (StringUtils.isNotEmpty(departmentId)) {
//                QyclRing qyclRing = qyclRingService.lambdaQuery().eq(QyclRing::getOrderPayStatus, 1).eq(QyclRing::getMobile, qyclCompany.getMobile()).orderByDesc(QyclRing::getCreateTime).last("limit 1").one();
//                qyclRingService.submitRing(qyclRing, departmentId,qyclCompany.getChannel());
//                enterpriseVrbtService.addContentMembersByChannel(departmentId, qyclCompany.getCompanyOwner(),qyclCompany.getChannel(),qyclCompany.getMobile());
//            }
//        }
//        System.out.println(list.size());
//        return "OK";
//    }
//
//    @RequestMapping(value = "/sendOutsideCallbackMqMsg")
//    public String sendOutsideCallbackMqMsg() {
//        for (int i = 0; i < 20; i++) {
//            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage("id" + i, i, "msg" + i);
//            rabbitTemplate.convertAndSend(RabbitMQConfig.OUTSIDE_CALLBACK_QUEUE_NAME, callbackNotifyMessage);
//            log.info("发送延迟队列消息:" + callbackNotifyMessage);
//        }
//        return "OK";
//    }
//
//    @RequestMapping(value = "/sendAdReportMqMsg")
//    public String sendAdReportMqMsg() {
//        for (int i = 0; i < 20; i++) {
//            ObjectNode node = mapper.createObjectNode();
//            node.put("adId", "adId"+i);
//            node.put("subChannel", "subChannel");
//            node.put("mobile", "mobile");
//            node.put("isp", i);
//            node.put("ip", "ip");
//            node.put("userAgent", "userAgent");
//            node.put("status", i);
//            node.put("remark", "");
//            node.put("adLink", "source");
//            node.put("orderNo", "subscribeId");
//            node.put("channel", "channel");
////            rabbitTemplate.convertAndSend(RabbitMQConfig.AD_REPORT_QUEUE_NAME, node);
//            log.info("发送广告上报延迟队列消息:" + node);
//        }
//        return "OK";
//    }
//
//    @RequestMapping(value = "/qyclExceptionHandle")
//    public String qyclExceptionHandle() throws Exception {
//        File file = new File("/tmp/exception_json.txt");
//        String contents = FileUtils.readFileToString(file);
//        String[] contentArray = StringUtils.split(contents, "\r\n");
//        for (String content : contentArray) {
//            JsonNode jsonNode = mapper.readTree(content);
//            String mobile = jsonNode.at("/billNum").asText();
//            int type = jsonNode.at("/type").asInt();
//            String orderId = jsonNode.at("/orderId").asText();
//            String state = jsonNode.at("/state").asText();
//            String operSystem = jsonNode.at("/operSystem").asText();
//            String finishedTime = jsonNode.at("/finishedTime").asText();
//            String desc = jsonNode.at("/desc").asText();
//            String companyOwner = QyclConstant.getCompanyOwnerByOrderId(orderId);
//            boolean isSuccess = "000000".equals(state);
//            if (1 == type) {
//                if (isSuccess) {
//                    //开通成功后处理
//                    subscribeResultNotifyService.qyclNotifyHandleNoAdFeedback(mobile);
//                    qyclCompanyMemberService.updateQyclFunStatus(mobile, QyclConstant.QYCL_FUN_STATUS_ORDER);
//                    dataNotifyLogService.saveDataNotifyLog(mobile, "MEMBER", QyclConstant.QYCL_FUN_STATUS_ORDER, companyOwner, state, desc, operSystem, finishedTime);
//                }
//
//            }
//        }
//        return "OK";
//    }
//
//    @RequestMapping(value = "/qyclDataHandle")
//    public String qyclDataHandle() throws Exception {
//        File file = new File("/tmp/qycl-data_json.txt");
//        String contents = FileUtils.readFileToString(file);
//        String[] contentArray = StringUtils.split(contents, "\r\n");
//        for (String content : contentArray) {
//            JsonNode jsonNode = mapper.readTree(content);
//            String mobile = jsonNode.at("/billNum").asText();
//            int type = jsonNode.at("/type").asInt();
//            String orderId = jsonNode.at("/orderId").asText();
//            String state = jsonNode.at("/state").asText();
//            String operSystem = jsonNode.at("/operSystem").asText();
//            String finishedTime = jsonNode.at("/finishedTime").asText();
//            String desc = jsonNode.at("/desc").asText();
//            String companyOwner = QyclConstant.getCompanyOwnerByOrderId(orderId);
//            boolean isSuccess = "000000".equals(state);
//            if (1 == type) {
//                if (isSuccess) {
//                    //开通成功后处理
//                    subscribeResultNotifyService.qyclNotifyHandleNoAdFeedback(mobile);
//                }
//            }
//        }
//        return "OK";
//    }
//
//
//    @PostMapping("queryMiguSubMonth")
//    public void queryMiguSubMonth(HttpServletRequest request) throws InterruptedException {
//        String channelCode = request.getParameter("channelCode");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> sub=getContent(file);
//        String size = request.getParameter("size");
//        List<String> successList= Lists.newArrayList();
//        for(int i=0;i<sub.size();i++){
//            final VrbtCombinResult result =miguApiService.vrbtCombinQuery(sub.get(i).trim(),channelCode);
//            if(result!=null && result.isOK() && result.isFun() && !result.isMonth() /*&& vrbtZeroOrderService.lambdaQuery().eq(VrbtZeroOrder::getMobile,sub.get(i).trim()).count()<=0*/){
//                successList.add(sub.get(i).trim());
//                try {
//                    VrbtZeroOrder zeroOrder=new VrbtZeroOrder();
//                    zeroOrder.setMobile(sub.get(i).trim());
//                    vrbtZeroOrderService.save(zeroOrder);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                if(successList.size()>=Integer.valueOf(StringUtils.isNotBlank(size)?size:String.valueOf(successList.size()))){
//                    break;
//                }
//            }
//        }
//    }
//    public static List<String> getContent(MultipartFile file){
//        BufferedReader br = null;
//        List<String> strings = Lists.newArrayList();
//        String line = null;
//        try {
//            //根据文件路径创建缓冲输入流
//            InputStream inputStream = file.getInputStream();
//            InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
//            br = new BufferedReader(inputStreamReader);
//            String str = "";
//
//            //循环读取文件的每一行，对需要修改的行进行修改，放入缓冲对象中
//            while ((line = br.readLine()) != null) {
//                //设置正则将多余空格都转为一个空格
//                str = line;
//                strings.add(str);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }finally {
//            try {
//                br.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return strings;
//    }
//
//    public static List<String> getContents(MultipartFile file){
//        BufferedReader br = null;
//        StringBuffer sb =new StringBuffer();
//        String line = null;
//        try {
//            //根据文件路径创建缓冲输入流
//            InputStream inputStream = file.getInputStream();
//            InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
//            br = new BufferedReader(inputStreamReader);
//            String str = "";
//
//            //循环读取文件的每一行，对需要修改的行进行修改，放入缓冲对象中
//            while ((line = br.readLine()) != null) {
//                //设置正则将多余空格都转为一个空格
//                str = line;
//                sb.append(str);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }finally {
//            try {
//                br.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        List list = Arrays.asList(sb.toString().split(","));
//        return list;
//    }
//
//    @PostMapping("hetuImportCouponCode")
//    public void hetuImportCouponCode(HttpServletRequest request) throws InterruptedException {
//        String rightsId = request.getParameter("rightsId");
//        String invalidTimeStr = request.getParameter("invalidTime");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> hetuCouponCodeList=getContent(file);
//        Date time=new Date();
//        try {
//            Date invalidTime = DateUtil.stringToDate(invalidTimeStr);
//            List<CouponCode> couponCodeList= Lists.newArrayList();
//            for(String code:hetuCouponCodeList){
//                if(StringUtils.isNotBlank(code.trim())){
//                    CouponCode couponCode=new CouponCode();
//                    couponCode.setStatus(0);
//                    couponCode.setCouponCode(code.trim());
//                    couponCode.setRightsId(rightsId);
//                    couponCode.setInvalidTime(invalidTime);
//                    couponCode.setCreateTime(time);
//                    couponCode.setUpdateTime(time);
//                    couponCodeList.add(couponCode);
//                }
//            }
//            if(couponCodeList!=null && couponCodeList.size()>0){
//                couponCodeService.saveBatch(couponCodeList);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @ResponseBody
//    @RequestMapping(value = "/switchBizVrCrack/{param}")
//    public Object switchBizVrCrack(@PathVariable String param) {
//        //参数值为1则 曹伟 2 吴炯
//        redisUtil.set(CacheConstant.CMS_BIZ_SWITCH_VR_CRACK, param);
//        return SUBSCRIBE_STATUS_SUCCESS.equals(redisUtil.get(CacheConstant.CMS_BIZ_SWITCH_VR_CRACK).toString()) ? "已切换曹伟破解" : "已切换吴炯破解";
//    }
//
//    @PostMapping("couponCodeByRightsMonth")
//    public void couponCodeByRightsMonth(HttpServletRequest request) throws InterruptedException {
//        String rightsMonth = request.getParameter("rightsMonth");
//        String packName = request.getParameter("packName");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> userIdList=getContent(file);
//        try {
//            for(String userId:userIdList){
//                userId=userId.trim();
//                if(StringUtils.isNotBlank(userId)){
//                    List<JunboChargeLog> junboChargeLogList=junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getAccount,userId).eq(JunboChargeLog::getRightsMonth,rightsMonth).eq(JunboChargeLog::getPackName,packName).list();
//                    if(junboChargeLogList!=null && junboChargeLogList.size()>0){
//                        for(JunboChargeLog junboChargeLog:junboChargeLogList){
//                            CouponCode couponCode=couponCodeService.lambdaQuery().eq(CouponCode::getOrderId,junboChargeLog.getMiguOrderId()).orderByDesc(CouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//                            if(couponCode!=null){
//                                CouponCodeRepeatOrder couponCodeRepeatOrder=new CouponCodeRepeatOrder();
//                                couponCodeRepeatOrder.setCouponCode(couponCode.getCouponCode());
//                                couponCodeRepeatOrder.setOrderId(couponCode.getOrderId());
//                                couponCodeRepeatOrder.setUserId(userId);
//                                couponCodeRepeatOrder.setStatus(couponCode.getStatus());
//                                couponCodeRepeatOrder.setSendTime(couponCode.getSendTime());
//                                couponCodeRepeatOrder.setExtrInfo(couponCode.getExtrInfo());
//                                couponCodeRepeatOrder.setSendServer(couponCode.getSendServer());
//                                couponCodeRepeatOrder.setSendRole(couponCode.getSendRole());
//                                String mobile=Long.toString(Long.valueOf(userId),13);
//                                couponCodeRepeatOrder.setMobile(mobile);
//                                Subscribe subscribe=subscribeService.lambdaQuery().select(Subscribe::getId, Subscribe::getOpenTime, Subscribe::getCreateTime, Subscribe::getChannel).eq(Subscribe::getMobile,mobile).eq(Subscribe::getBizType,"HETU").eq(Subscribe::getStatus,1).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//                                if(subscribe==null){
//                                    couponCodeRepeatOrder.setOpenStatus(0);
//                                }else{
//                                    couponCodeRepeatOrder.setOpenStatus(1);
//                                    couponCodeRepeatOrder.setOpenTime(subscribe.getOpenTime()==null?subscribe.getCreateTime():subscribe.getOpenTime());
//                                }
//                                couponCodeRepeatOrderService.save(couponCodeRepeatOrder);
//                            }
//                        }
//                    }
//
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//
//
//    /**
//     * 河图导入券码
//     * @param request
//     * @throws InterruptedException
//     */
//    @PostMapping("hetuImportCode")
//    public void hetuImportCode(HttpServletRequest request) throws InterruptedException {
//        String rightsId = request.getParameter("rightsId");
//        String invalidTimeStr = request.getParameter("invalidTime");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> hetuCouponCodeList=getContent(file);
//        Date time=new Date();
//        try {
//            Date invalidTime = DateUtil.stringToDate(invalidTimeStr);
//            List<HetuCouponCode> couponCodeList= Lists.newArrayList();
//            for(String code:hetuCouponCodeList){
//                if(StringUtils.isNotBlank(code.trim())){
//                    HetuCouponCode couponCode=new HetuCouponCode();
//                    couponCode.setStatus(0);
//                    couponCode.setCouponCode(code.trim());
//                    couponCode.setRightsId(rightsId);
//                    couponCode.setInvalidTime(invalidTime);
//                    couponCode.setCreateTime(time);
//                    couponCode.setUpdateTime(time);
//                    couponCodeList.add(couponCode);
//                }
//            }
//            if(couponCodeList!=null && couponCodeList.size()>0){
//                hetuCouponCodeService.saveBatch(couponCodeList);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//    /**
//     * 思维方阵导入券码
//     * @param request
//     * @throws InterruptedException
//     */
//    @PostMapping("swfzImportCode")
//    public void swfzImportCode(HttpServletRequest request) throws InterruptedException {
//        String rightsId = request.getParameter("rightsId");
//        String invalidTimeStr = request.getParameter("invalidTime");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> hetuCouponCodeList=getContents(file);
//        Date time=new Date();
//        try {
//            Date invalidTime = DateUtil.stringToDate(invalidTimeStr);
//            List<SwfzCouponCode> couponCodeList= Lists.newArrayList();
//            for(String code:hetuCouponCodeList){
//                if(StringUtils.isNotBlank(code.trim())){
//                    boolean codeCount=swfzCouponCodeService.lambdaQuery().eq(SwfzCouponCode::getCouponCode,code.trim()).count()<=0;
//                    if(codeCount){
//                        SwfzCouponCode couponCode=new SwfzCouponCode();
//                        couponCode.setStatus(0);
//                        couponCode.setCouponCode(code.trim());
//                        couponCode.setRightsId(rightsId);
//                        couponCode.setInvalidTime(invalidTime);
//                        couponCode.setCreateTime(time);
//                        couponCode.setUpdateTime(time);
//                        couponCodeList.add(couponCode);
//                    }
//                }
//            }
//            if(couponCodeList!=null && couponCodeList.size()>0){
//                swfzCouponCodeService.saveBatch(couponCodeList);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//    /**
//     * 大鱼消除导入券码
//     * @param request
//     * @throws InterruptedException
//     */
//    @PostMapping("dyxcImportCode")
//    public void dyxcImportCode(HttpServletRequest request) throws InterruptedException {
//        String rightsId = request.getParameter("rightsId");
//        String invalidTimeStr = request.getParameter("invalidTime");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> hetuCouponCodeList=getContents(file);
//        Date time=new Date();
//        try {
//            Date invalidTime = DateUtil.stringToDate(invalidTimeStr);
//            List<DyxcCouponCode> couponCodeList= Lists.newArrayList();
//            for(String code:hetuCouponCodeList){
//                if(StringUtils.isNotBlank(code.trim())){
//                    boolean codeCount=dyxcCouponCodeService.lambdaQuery().eq(DyxcCouponCode::getCouponCode,code.trim()).count()<=0;
//                    if(codeCount){
//                        DyxcCouponCode couponCode=new DyxcCouponCode();
//                        couponCode.setStatus(0);
//                        couponCode.setCouponCode(code.trim());
//                        couponCode.setRightsId(rightsId);
//                        couponCode.setInvalidTime(invalidTime);
//                        couponCode.setCreateTime(time);
//                        couponCode.setUpdateTime(time);
//                        couponCodeList.add(couponCode);
//                    }
//                }
//            }
//            if(couponCodeList!=null && couponCodeList.size()>0){
//                dyxcCouponCodeService.saveBatch(couponCodeList);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 厨房大逃亡导入券码
//     * @param request
//     * @throws InterruptedException
//     */
//    @PostMapping("cfdtwImportCode")
//    public void cfdtwImportCode(HttpServletRequest request) throws InterruptedException {
//        String rightsId = request.getParameter("rightsId");
//        String invalidTimeStr = request.getParameter("invalidTime");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> hetuCouponCodeList=getContents(file);
//        Date time=new Date();
//        try {
//            Date invalidTime = DateUtil.stringToDate(invalidTimeStr);
//            List<CfdtwCouponCode> couponCodeList= Lists.newArrayList();
//            for(String code:hetuCouponCodeList){
//                if(StringUtils.isNotBlank(code.trim())){
//                    boolean codeCount=cfdtwCouponCodeService.lambdaQuery().eq(CfdtwCouponCode::getCouponCode,code.trim()).count()<=0;
//                    if(codeCount){
//                        CfdtwCouponCode couponCode=new CfdtwCouponCode();
//                        couponCode.setStatus(0);
//                        couponCode.setCouponCode(code.trim());
//                        couponCode.setRightsId(rightsId);
//                        couponCode.setInvalidTime(invalidTime);
//                        couponCode.setCreateTime(time);
//                        couponCode.setUpdateTime(time);
//                        couponCodeList.add(couponCode);
//                    }
//                }
//            }
//            if(couponCodeList!=null && couponCodeList.size()>0){
//                cfdtwCouponCodeService.saveBatch(couponCodeList);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    /**
//     * 生成券码
//     * @param request
//     * @throws InterruptedException
//     */
//    @PostMapping("saveCode")
//    public Result saveCode(HttpServletRequest request){
//        String channel = request.getParameter("channel");
//        String length = request.getParameter("length");
//        String edId = request.getParameter("edId");
//
//        String  content=couponCodeService.saveCode(channel,Integer.valueOf(length),edId);
//        return Result.ok(content);
//    }


//    /**
//     * 生成券码
//     * @param request
//     * @throws InterruptedException
//     */
//    @PostMapping("saveCodeTime")
//    public Result saveCodeTime(HttpServletRequest request){
//        try {
//            String channel = request.getParameter("channel");
//            String length = request.getParameter("length");
//            String start = request.getParameter("start");
//            String end = request.getParameter("end");
//            String content = couponCodeService.saveCode(channel,Integer.valueOf(length),start,end);
//            return Result.ok(content);
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        return Result.error("系统错误");
//    }
    /**
     * 水晶传说导入券码
     * @param request
     * @throws InterruptedException
     */
//    @PostMapping("sjcsImportCode")
//    public void sjcsImportCode(HttpServletRequest request) throws InterruptedException {
//        String rightsId = request.getParameter("rightsId");
//        String invalidTimeStr = request.getParameter("invalidTime");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> hetuCouponCodeList=getContent(file);
//        Date time=new Date();
//        try {
//            Date invalidTime = DateUtil.stringToDate(invalidTimeStr);
//            List<SjcsCouponCode> couponCodeList= Lists.newArrayList();
//            for(String code:hetuCouponCodeList){
//                if(StringUtils.isNotBlank(code.trim())){
//                    SjcsCouponCode couponCode=new SjcsCouponCode();
//                    couponCode.setStatus(0);
//                    couponCode.setCouponCode(code.trim());
//                    couponCode.setRightsId(rightsId);
//                    couponCode.setInvalidTime(invalidTime);
//                    couponCode.setCreateTime(time);
//                    couponCode.setUpdateTime(time);
//                    couponCodeList.add(couponCode);
//                }
//            }
//            List<SjcsCouponCode> codesList= Lists.newArrayList();
//            for(int i=0;i<couponCodeList.size();i++){
//                codesList.add(couponCodeList.get(i));
//                if (couponCodeList.size() % 1000 == 0 || i >= couponCodeList.size() - 1) {
//                    sjcsCouponCodeService.saveBatch(codesList);
//                    codesList.clear();
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    /**
//     * 暗黑主宰导入券码
//     * @param request
//     * @throws InterruptedException
//     */
//    @PostMapping("ahzzImportCode")
//    public void ahzzImportCode(HttpServletRequest request) throws InterruptedException {
//        String rightsId = request.getParameter("rightsId");
//        String invalidTimeStr = request.getParameter("invalidTime");
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        List<String> hetuCouponCodeList=getContent(file);
//        Date time=new Date();
//        try {
//            Date invalidTime = DateUtil.stringToDate(invalidTimeStr);
//            List<AhzzCouponCode> couponCodeList= Lists.newArrayList();
//            for(String code:hetuCouponCodeList){
//                if(StringUtils.isNotBlank(code.trim())){
//                    AhzzCouponCode couponCode=new AhzzCouponCode();
//                    couponCode.setStatus(0);
//                    couponCode.setCouponCode(code.trim());
//                    couponCode.setRightsId(rightsId);
//                    couponCode.setInvalidTime(invalidTime);
//                    couponCode.setCreateTime(time);
//                    couponCode.setUpdateTime(time);
//                    couponCodeList.add(couponCode);
//                }
//            }
//            List<AhzzCouponCode> codesList= Lists.newArrayList();
//            for(int i=0;i<couponCodeList.size();i++){
//                codesList.add(couponCodeList.get(i));
//                if (couponCodeList.size() % 1000 == 0 || i >= couponCodeList.size() - 1) {
//                    ahzzCouponCodeService.saveBatch(codesList);
//                    codesList.clear();
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    /**
//     * 水晶传说通过excel导入券码
//     * @param request
//     * @return
//     */
//    @RequestMapping(value = "/sjcsImportPriceExcel", method = RequestMethod.POST)
//    public Result<?> sjcsImportPriceExcel(HttpServletRequest request) {
//        return this.importExcel(request);
//    }
//    /**
//     * 通过excel导入数据
//     * @param request
//     * @return
//     */
//    public Result<?> importExcel(HttpServletRequest request) {
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//        String rightsId = request.getParameter("rightsId");
//        String invalidTimeStr = request.getParameter("invalidTime");
//        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
//        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
//            MultipartFile file = entity.getValue();// 获取上传文件对象
//            ImportParams params = new ImportParams();
//            params.setTitleRows(0);
//            params.setHeadRows(1);
//            params.setNeedSave(true);
//            try {
//                List<SjcsCouponCodeImport> list = ExcelImportUtil.importExcel(file.getInputStream(), SjcsCouponCodeImport.class, params);
//                //update-begin-author:taoyan date:20190528 for:批量插入数据
//                long start = System.currentTimeMillis();
//                Date invalidTime = DateUtil.stringToDate(invalidTimeStr);
//                Date time=new Date();
//                List<SjcsCouponCode> couponCodeList= Lists.newArrayList();
//                list.forEach(item -> {
//                    if(StringUtils.isNotBlank(item.getCouponCode().trim())){
//                        SjcsCouponCode couponCode=new SjcsCouponCode();
//                        couponCode.setStatus(0);
//                        couponCode.setCouponCode(item.getCouponCode().trim());
//                        couponCode.setRightsId(rightsId);
//                        couponCode.setInvalidTime(invalidTime);
//                        couponCode.setCreateTime(time);
//                        couponCode.setUpdateTime(time);
//                        couponCodeList.add(couponCode);
//                    }
//                });
//                List<SjcsCouponCode> codesList= Lists.newArrayList();
//                for(int i=0;i<couponCodeList.size();i++){
//                    codesList.add(couponCodeList.get(i));
//                    if (couponCodeList.size() % 1000 == 0 || i >= couponCodeList.size() - 1) {
//                        sjcsCouponCodeService.saveBatch(codesList);
//                        codesList.clear();
//                    }
//                }
//                //400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
//                //1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
//                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
//                //update-end-author:taoyan date:20190528 for:批量插入数据
//                return Result.ok("文件导入成功！数据行数：" + list.size());
//            } catch (Exception e) {
//                log.error(e.getMessage(), e);
//                return Result.error("文件导入失败:" + e.getMessage());
//            } finally {
//                try {
//                    file.getInputStream().close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//        return Result.error("文件导入失败！");
//    }


//    /**
//     * 发送特定权益
//     */
//    @PostMapping("monthRenewSubRights")
//    public Result<?> monthRenewSubRights(HttpServletRequest request) {
//        String channels = request.getParameter("channel");
//        if(StringUtils.isBlank(channels)){
//            return Result.error("渠道号错误");
//        }
//        List<String> channelList=Lists.newArrayList();
//        channelList.add(channels);
//
//        EsSubscribe esSubscribe=new EsSubscribe();
//        esSubscribe.setStatus(1);
//        esSubscribe.setVerifyStatus(1);
//        //需要查询的字段
//        String[] includes = {"id","mobile","bizType","channel","createTime"};
//        BoolQueryBuilder builder = QueryBuilders.boolQuery();
//        if(channelList!=null && !channelList.isEmpty()){
//            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
//            channelList.forEach(channel -> {
//                shouldQuery.should(QueryBuilders.termQuery("channel.keyword", channel));
//            });
//            builder.must(shouldQuery);
//        }
//        //开通状态
//        if (esSubscribe.getStatus() != null) {
//            builder.must(QueryBuilders.termQuery("status", esSubscribe.getStatus()));
//        }
//        if (esSubscribe.getVerifyStatus() != null) {
//            builder.must(QueryBuilders.termQuery("verifyStatus", esSubscribe.getVerifyStatus()));
//        }
//        if (esSubscribe.getVerifyStatusDaily() != null) {
//            builder.must(QueryBuilders.termQuery("verifyStatusDaily", esSubscribe.getVerifyStatusDaily()));
//        }
//        builder.must(QueryBuilders.rangeQuery("createTime").lt(DateUtil.localDateTimeToDate(DateUtil.getFirstDayOfMonthWithMinTime()).getTime()));
//        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
//                withSourceFilter(new SourceFilter() {
//                    @Override
//                    public String[] getIncludes() {
//                        return includes;
//                    }
//
//                    @Override
//                    public String[] getExcludes() {
//                        return new String[0];
//                    }
//                }).
//                withQuery(builder).
//                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
//                build();
//        nativeSearchQuery.setTrackTotalHits(true);
//        nativeSearchQuery.setMaxResults(500000);
//
//        SearchHits<EsSubscribe> searchHits = restTemplate.search(nativeSearchQuery, EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
//        if(searchHits==null || searchHits.isEmpty()){
//            log.info("ES查询数据不存在=>渠道号:{}",channelList);
//            return Result.error("ES查询数据不存在");
//        }
//        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
//        if(esSubscribeList==null || esSubscribeList.size()<=0){
//            log.info("发送特定权益-每月续订es数据查询失败");
//            return Result.error("每月续订es数据查询失败");
//        }
//        for (EsSubscribe esSub : esSubscribeList) {
//            smsModelService.monthRenewSubSmsMessage(esSub.getChannel(),esSub.getMobile(),esSub.getId(),esSub.getBizType());
//        }
//        return Result.ok();
//    }
//
//
//    /**
//     * 删除游戏缓存
//     * @param request
//     * @return
//     */
//    @RequestMapping(value = "/delRedisGame", method = RequestMethod.POST)
//    public Result<?> delRedisGame(HttpServletRequest request) {
//        String rightsId = request.getParameter("rightsId");
//        if(StringUtils.isBlank(rightsId)){
//            return Result.error("rightsId不能为空");
//        }
//        final String key = "coupon:huyu:"+rightsId;
//        redisUtil.del(key);
//        return Result.ok();
//    }
//
//    @GetMapping("/test/report")
//    public void testReport(String mobile, String channelCode) {
//        rabbitMQMsgSender.sendMiguRingFtpUploadMessage(MiguRingFtpUploadMessage.builder().id(IdWorker.getIdStr()).mobile(mobile).tag(channelCode).build());
//    }
//
//    @GetMapping("/test/addAiRingVideo")
//    public Result addAiRingVideo(String columnId, String productIds) {
//        List<String> data = Arrays.asList(productIds.split(","));
//        List<AiRingVideo> result = new ArrayList<>();
//        data.forEach(e -> {
//            VrbtProduct r = miguApiService.fetchVrbtProduct(e);
//
//            AiRingVideo aiRingVideo = new AiRingVideo();
//            aiRingVideo.setCopyrightId(r.getCopyrightId());
//            aiRingVideo.setVrbtProductId(r.getVrbtProductId());
//            aiRingVideo.setRingName(r.getMusicName().replace("AI一语成片-", ""));
//            aiRingVideo.setRingPicUrl(r.getVrbtImg());
//            aiRingVideo.setRingUrl(r.getVrbtVideo());
//            aiRingVideo.setColumnId(columnId);
//            result.add(aiRingVideo);
//        });
//        aiRingVideoService.saveBatch(result);
//        return Result.ok();
//    }
//
//
//    @RequestMapping(value = "/test/queryOrder", method = RequestMethod.POST)
//    public SichuanMobileQueryOrderResult queryOrder(HttpServletRequest request) {
//        String orderNo = request.getParameter("orderNo");
//        String channelCode = request.getParameter("channelCode");
//        String phone = request.getParameter("phone");
//       return siChuanMobileApiService.queryOrder(orderNo,channelCode,phone);
//    }
}
