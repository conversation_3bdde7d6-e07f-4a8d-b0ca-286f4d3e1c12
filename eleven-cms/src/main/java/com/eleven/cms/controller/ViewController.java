package com.eleven.cms.controller;

import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.remote.BizUnsubscribeService;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.VrbtCombinResult;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 *
 */
@Api(tags = "通用视图跳转")
@Controller
@Slf4j
public class ViewController {

    @Autowired
    private BizUnsubscribeService bizUnsubscribeService;

    private final ObjectMapper objectMapper = new ObjectMapper().configure(JsonGenerator.Feature.WRITE_NUMBERS_AS_STRINGS, true);

    private final Map<String,ImmutablePair<String,String>> chConfigMap = ImmutableMap.of("DYB",new ImmutablePair<>(MiguApiService.CH_DYB_ULOGIN, "698039035100000014"),
            "QDB",new ImmutablePair<>(MiguApiService.CH_QDB_ULOGIN, "698039042105792434")) ;

    @Autowired
    MiguApiService miguApiService;
    public static final String LOG_TAG_IVR = "IVR业务退订";

    /**
     * 联通包月产品退订页面
     *
     * @param modelAndView
     * @return
     */
    @RequestMapping("/unicom/unsub")
    public ModelAndView unicomUnSub(ModelAndView modelAndView) {
        modelAndView.setViewName("/unicom/unsub");

        return modelAndView;
    }

    /**
     * 电信包月产品退订页面
     *
     * @param modelAndView
     * @return
     */
    @RequestMapping("/telecom/unsub")
    public ModelAndView telecomUnSub(ModelAndView modelAndView) {
        modelAndView.setViewName("/telecom/unsub");
        return modelAndView;
    }


    /**
     * 天翼空间包月产品退订页面
     *
     * @param modelAndView
     * @return
     */
    @RequestMapping("/telecom/tianyiUnsub")
    public ModelAndView tianyiUnSub(ModelAndView modelAndView) {
        modelAndView.setViewName("/telecom/tianyiUnsub");
        return modelAndView;
    }

    /**
     * 域名配置页面
     *
     * @param modelAndView
     * @return
     */
    @RequestMapping("/domain/config")
    public ModelAndView domainConfig(ModelAndView modelAndView) {
        modelAndView.setViewName("/domain/config");
        return modelAndView;
    }



    /**
     * 发送联通退订网址短信[FOR IVR]
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/unicom/sendUnsubSms")
    @ResponseBody
    public String sendUnicomUnsubNotice(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        return objectMapper.writeValueAsString(bizUnsubscribeService.sendUnicomUnsubNotice(callNo));
    }

    /**
     * 电信视频彩铃退订[FOR IVR]   40061007  200
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/ctccCancelVrbtPack")
    @ResponseBody
    public String ctccCancelVrbtPack(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-电信视频彩铃退订,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.ctccCancelVrbtPack(callNo));
    }

    /**
     * 移动视频彩铃退订,含开放平台的订阅/渠道,以及彩铃中心的订阅[FOR IVR]  40061007  750
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/cmccCancelVrbtPack")
    @ResponseBody
    public String cmccCancelVrbtPack(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-移动视频彩铃退订,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.cmccCancelVrbtPack(callNo));
    }

    /**
     * 联通,电信,移动 视频彩铃包月公用退订入口    40061007  750
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/commonCancelVrbtPack")
    @ResponseBody
    public String commonCancelVrbtPack(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-全网视频彩铃退订,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.commonCancelVrbtPack(callNo));
    }


    /**
     * 联通,电信,移动 视频彩铃包月公用退订入口  999  1500
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/allBizCancel")
    @ResponseBody
    public String allBizCancel(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-全网所有业务退订,手机号:{}", LOG_TAG_IVR,  callNo);
        //退订最近90天的
        return objectMapper.writeValueAsString(bizUnsubscribeService.unsubscribeRecent90Days(callNo, BizConstant.BIZ_UNSUBSCRIBE_REQUEST_WAY_IVR));
    }


    /**
     * 咪咕音乐业务退订(北岸唐唱+白金会员退订+藕粉咪咕同享会联合会员)[FOR IVR]  40061007  750
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/cmccCancelCpmb")
    @ResponseBody
    public String cmccCancelMiguMusicBiz(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-咪咕音乐渠道业务退订,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.cmccCancelMiguMusicBiz(callNo));
    }

    /**
     * 阿里云协议签约解除  三方支付会员业务退订  853515  2000
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/memberUnsign")
    @ResponseBody
    public String alipayAgreementUnsign(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-三方支付会员业务退订,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.alipayAgreementUnsign(callNo));
    }


    /**
     * 咪咕视频彩铃包月订购
     * @param ch 渠道路径参数,只能为DYB/QDB
     * @param miguToken 统一认证token
     * @param callbackUrl 回调地址
     * @return
     */
    @RequestMapping("/migu/vrbtMonthOrder/{ch}")
    public String vrbtMonthOrder(@PathVariable(value = "ch") String ch,
                                       @RequestParam("miguToken") String miguToken,
                                       @RequestParam("callbackUrl") String callbackUrl,
                                       Model model){
        final String view = "/migu/vrbtMonthOrder";
        String callbackRebuild = callbackUrl;
        if(StringUtils.contains(callbackUrl,"http")){
            try {
                callbackRebuild = URLDecoder.decode(callbackUrl, StandardCharsets.UTF_8.toString());
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        model.addAttribute("callbackUrl",callbackRebuild);

        //根据渠道号做开通
        ImmutablePair<String, String> channel = chConfigMap.get(StringUtils.upperCase(ch));
        if(null==channel){
            model.addAttribute("vrbtCombinResult",VrbtCombinResult.fail("地址或渠道参数错误"));
            return view;
        }
        String channelCode = channel.getLeft();
        RemoteResult loginResult = miguApiService.miguULogin(miguToken, channelCode);
        if(!loginResult.isOK()){
            model.addAttribute("vrbtCombinResult",VrbtCombinResult.fail("登录失败,无效token"));
            return view;
        }
        String token = loginResult.getToken();
        VrbtCombinResult vrbtCombinResult = miguApiService.vrbtFunAndMonthStatusQuery(token, channelCode);
        if(!vrbtCombinResult.isOK()){
            model.addAttribute("vrbtCombinResult",VrbtCombinResult.fail("网络错误,请稍后再试"));
            return view;
        }
        vrbtCombinResult.setToken(token);
        vrbtCombinResult.setChannelCode(channelCode);
        vrbtCombinResult.setServiceId(channel.getRight());

        model.addAttribute("vrbtCombinResult",vrbtCombinResult);
        return view;
    }

    /**
     * 沃音乐订购查询页面
     * @param modelAndView
     * @return
     */
    @RequestMapping("/womusic/subscribe/query")
    public ModelAndView womusicSubscribe(ModelAndView modelAndView) {
        modelAndView.setViewName("/womusic/subscribe");
        return modelAndView;
    }

    /**
     * 阿里云协议签约解除  三方支付会员业务查询订购和解约信息  853515  2000
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/querySignAndRefund")
    @ResponseBody
    public String alipayQueryUnsignAndRefund(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-三方支付会员业务查询订购和解约信息,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.alipayQuerySignAndRefund(callNo,BizConstant.BIZ_TYPE_MEMBER_ALIPAY));
    }

    /**
     * 阿里云协议签约解除  三方支付会员业务解约并退款  853515  2000
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/unsignAndRefund")
    @ResponseBody
    public String alipayUnsignAndRefund(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-三方支付会员业务解约并退款,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.alipayUnSignAndRefund(callNo,BizConstant.BIZ_TYPE_MEMBER_ALIPAY));
    }


    /**
     * 阿里云协议签约解除  三方支付移动视频彩铃查询订购和解约信息  853515  2000
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/vrbt/querySignAndRefund")
    @ResponseBody
    public String alipayVrbtQueryUnsignAndRefund(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-三方支付视频彩铃查询订购和解约信息,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.alipayQuerySignAndRefund(callNo,null));
    }

    /**
     * 阿里云协议签约解除  三方支付移动视频彩铃解约并退款  853515  2000
     *
     * @param callNo
     *         手机号
     * @return
     */
    @RequestMapping("/ivr/vrbt/unsignAndRefund")
    @ResponseBody
    public String alipayVrbtUnsignAndRefund(@RequestParam("callNo") String callNo) throws JsonProcessingException {
        log.info("{}-三方支付视频彩铃解约并退款,手机号:{}", LOG_TAG_IVR,  callNo);
        return objectMapper.writeValueAsString(bizUnsubscribeService.alipayUnSignAndRefund(callNo,null));
    }





}
