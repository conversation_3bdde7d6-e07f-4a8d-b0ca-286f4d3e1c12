package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.KeyValueConfig;
import com.eleven.cms.service.IKeyValueConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
* @Description: cms_key_value_config
* @Author: jeecg-boot
* @Date:   2023-04-24
* @Version: V1.0
*/
@Api(tags="cms_key_value_config")
@RestController
@RequestMapping("/cms/keyValueConfig")
@Slf4j
public class KeyValueConfigController extends JeecgController<KeyValueConfig, IKeyValueConfigService> {
   @Autowired
   private IKeyValueConfigService keyValueConfigService;
   
   /**
    * 分页列表查询
    *
    * @param keyValueConfig
    * @param pageNo
    * @param pageSize
    * @param req
    * @return
    */
   //@AutoLog(value = "cms_key_value_config-分页列表查询")
   @ApiOperation(value="cms_key_value_config-分页列表查询", notes="cms_key_value_config-分页列表查询")
   @GetMapping(value = "/list")
   public Result<?> queryPageList(KeyValueConfig keyValueConfig,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  HttpServletRequest req) {
       QueryWrapper<KeyValueConfig> queryWrapper = QueryGenerator.initQueryWrapper(keyValueConfig, req.getParameterMap());
       Page<KeyValueConfig> page = new Page<KeyValueConfig>(pageNo, pageSize);
       IPage<KeyValueConfig> pageList = keyValueConfigService.page(page, queryWrapper);
       return Result.ok(pageList);
   }
   
   /**
    *   添加
    *
    * @param keyValueConfig
    * @return
    */
   //@AutoLog(value = "cms_key_value_config-添加")
   @ApiOperation(value="cms_key_value_config-添加", notes="cms_key_value_config-添加")
   @PostMapping(value = "/add")
   public Result<?> add(@RequestBody KeyValueConfig keyValueConfig) {
       keyValueConfigService.save(keyValueConfig);
       return Result.ok("添加成功！");
   }
   
   /**
    *  编辑
    *
    * @param keyValueConfig
    * @return
    */
   //@AutoLog(value = "cms_key_value_config-编辑")
   @ApiOperation(value="cms_key_value_config-编辑", notes="cms_key_value_config-编辑")
   @PutMapping(value = "/edit")
   public Result<?> edit(@RequestBody KeyValueConfig keyValueConfig) {
       keyValueConfigService.updateById(keyValueConfig);
       return Result.ok("编辑成功!");
   }
   
   /**
    *   通过id删除
    *
    * @param id
    * @return
    */
   //@AutoLog(value = "cms_key_value_config-通过id删除")
   @ApiOperation(value="cms_key_value_config-通过id删除", notes="cms_key_value_config-通过id删除")
   @DeleteMapping(value = "/delete")
   public Result<?> delete(@RequestParam(name="id",required=true) String id) {
       keyValueConfigService.removeById(id);
       return Result.ok("删除成功!");
   }
   
   /**
    *  批量删除
    *
    * @param ids
    * @return
    */
   //@AutoLog(value = "cms_key_value_config-批量删除")
   @ApiOperation(value="cms_key_value_config-批量删除", notes="cms_key_value_config-批量删除")
   @DeleteMapping(value = "/deleteBatch")
   public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
       this.keyValueConfigService.removeByIds(Arrays.asList(ids.split(",")));
       return Result.ok("批量删除成功!");
   }

   /**
   * 导出excel
   *
   * @param request
   * @param keyValueConfig
   */
   @RequestMapping(value = "/exportXls")
   public ModelAndView exportXls(HttpServletRequest request, KeyValueConfig keyValueConfig) {
       return super.exportXls(request, keyValueConfig, KeyValueConfig.class, "cms_key_value_config");
   }

   /**
     * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
   @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
   public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
       return super.importExcel(request, response, KeyValueConfig.class);
   }

}
