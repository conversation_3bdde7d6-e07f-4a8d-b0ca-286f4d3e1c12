package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.OrderVrbt;
import com.eleven.cms.service.IOrderVrbtService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_order_vrbt
 * @Author: jeecg-boot
 * @Date:   2020-11-16
 * @Version: V1.0
 */
@Api(tags="cms_order_vrbt")
@RestController
@RequestMapping("/cms/orderVrbt")
@Slf4j
public class OrderVrbtController extends JeecgController<OrderVrbt, IOrderVrbtService> {
	@Autowired
	private IOrderVrbtService orderVrbtService;
	
	/**
	 * 分页列表查询
	 *
	 * @param orderVrbt
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_order_vrbt-分页列表查询")
	@ApiOperation(value="cms_order_vrbt-分页列表查询", notes="cms_order_vrbt-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(OrderVrbt orderVrbt,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<OrderVrbt> queryWrapper = QueryGenerator.initQueryWrapper(orderVrbt, req.getParameterMap());
		Page<OrderVrbt> page = new Page<OrderVrbt>(pageNo, pageSize);
		IPage<OrderVrbt> pageList = orderVrbtService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param orderVrbt
	 * @return
	 */
	//@AutoLog(value = "cms_order_vrbt-添加")
	@ApiOperation(value="cms_order_vrbt-添加", notes="cms_order_vrbt-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody OrderVrbt orderVrbt) {
		orderVrbtService.save(orderVrbt);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param orderVrbt
	 * @return
	 */
	//@AutoLog(value = "cms_order_vrbt-编辑")
	@ApiOperation(value="cms_order_vrbt-编辑", notes="cms_order_vrbt-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody OrderVrbt orderVrbt) {
		orderVrbtService.updateById(orderVrbt);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_order_vrbt-通过id删除")
	@ApiOperation(value="cms_order_vrbt-通过id删除", notes="cms_order_vrbt-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		orderVrbtService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_order_vrbt-批量删除")
	@ApiOperation(value="cms_order_vrbt-批量删除", notes="cms_order_vrbt-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.orderVrbtService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_order_vrbt-通过id查询")
	@ApiOperation(value="cms_order_vrbt-通过id查询", notes="cms_order_vrbt-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		OrderVrbt orderVrbt = orderVrbtService.getById(id);
		if(orderVrbt==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(orderVrbt);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param orderVrbt
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrderVrbt orderVrbt) {
        return super.exportXls(request, orderVrbt, OrderVrbt.class, "cms_order_vrbt");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OrderVrbt.class);
    }

}
