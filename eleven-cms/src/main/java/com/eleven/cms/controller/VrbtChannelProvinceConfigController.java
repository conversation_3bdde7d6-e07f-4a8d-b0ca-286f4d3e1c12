package com.eleven.cms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.ProvinceTree;
import com.eleven.cms.entity.VrbtChannelProvinceConfig;
import com.eleven.cms.entity.VrbtProvinceSwitchConfig;
import com.eleven.cms.service.IProvinceTreeService;
import com.eleven.cms.service.IVrbtChannelProvinceConfigService;
import com.eleven.cms.service.IVrbtProvinceSwitchConfigService;
import com.eleven.cms.util.BizConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: cms_vrbt_province_switch_config
 * @Author: jeecg-boot
 * @Date: 2021-08-16
 * @Version: V1.0
 */
@Api(tags = "cms_vrbt_chanel_province_config")
@RestController
@RequestMapping("/cms/vrbtChannelProvinceConfig")
@Slf4j
public class VrbtChannelProvinceConfigController extends JeecgController<VrbtChannelProvinceConfig, IVrbtChannelProvinceConfigService> {
    @Autowired
    private IVrbtChannelProvinceConfigService vrbtChannelProvinceConfigService;
    @Autowired
    private IProvinceTreeService provinceTreeService;

    /**
     * 分页列表查询
     *
     * @param vrbtChannelProvinceConfig
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "cms_vrbt_chanel_province_config-分页列表查询")
    @ApiOperation(value = "cms_vrbt_chanel_province_config-分页列表查询", notes = "cms_vrbt_chanel_province_config-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(VrbtChannelProvinceConfig vrbtChannelProvinceConfig,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<VrbtChannelProvinceConfig> queryWrapper = QueryGenerator.initQueryWrapper(vrbtChannelProvinceConfig, req.getParameterMap());
        Page<VrbtChannelProvinceConfig> page = new Page<VrbtChannelProvinceConfig>(pageNo, pageSize);
        IPage<VrbtChannelProvinceConfig> pageList = vrbtChannelProvinceConfigService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param vrbtChannelProvinceConfig
     * @return
     */
    //@AutoLog(value = "cms_vrbt_chanel_province_config-添加")
    @ApiOperation(value = "cms_vrbt_chanel_province_config-添加", notes = "cms_vrbt_chanel_province_config-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody VrbtChannelProvinceConfig vrbtChannelProvinceConfig) {
        vrbtChannelProvinceConfigService.save(vrbtChannelProvinceConfig);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param vrbtChannelProvinceConfig
     * @return
     */
    //@AutoLog(value = "cms_vrbt_chanel_province_config-编辑")
    @ApiOperation(value = "cms_vrbt_chanel_province_config-编辑", notes = "cms_vrbt_chanel_province_config-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody VrbtChannelProvinceConfig vrbtChannelProvinceConfig) {
        vrbtChannelProvinceConfigService.updateById(vrbtChannelProvinceConfig);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_vrbt_chanel_province_config-通过id删除")
    @ApiOperation(value = "cms_vrbt_chanel_province_config-通过id删除", notes = "cms_vrbt_chanel_province_config-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        vrbtChannelProvinceConfigService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "cms_vrbt_chanel_province_config-批量删除")
    @ApiOperation(value = "cms_vrbt_chanel_province_config-批量删除", notes = "cms_vrbt_chanel_province_config-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.vrbtChannelProvinceConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_vrbt_chanel_province_config-通过id查询")
    @ApiOperation(value = "cms_vrbt_chanel_province_config-通过id查询", notes = "cms_vrbt_chanel_province_config-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        VrbtChannelProvinceConfig vrbtChannelProvinceConfig = vrbtChannelProvinceConfigService.getById(id);
        if (vrbtChannelProvinceConfig == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(vrbtChannelProvinceConfig);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param vrbtChannelProvinceConfig
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, VrbtChannelProvinceConfig vrbtChannelProvinceConfig) {
        return super.exportXls(request, vrbtChannelProvinceConfig, VrbtChannelProvinceConfig.class, "cms_vrbt_province_switch_config");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, VrbtChannelProvinceConfig.class);
    }

    /**
     * 保存关联关系
     */
    @PostMapping(value = "/saveRelate")
    public Result<?> saveDatarule(@RequestBody JSONObject jsonObject) {
        try {
            Map<String, Object> map = new HashMap<>();
            String[] provinceList = jsonObject.getString("provinceList").split(",");
            List<String> asList = Arrays.asList(provinceList);
            if (asList.size() == 1 && asList.contains("")) {
                asList = new ArrayList<String>();
            }
            String id = jsonObject.getString("roleId");
            List<ProvinceTree> provinceTrees = provinceTreeService.list();
            for (ProvinceTree province : provinceTrees) {
                map.put(province.getProvinceName(), false);
            }
            for (String province : asList) {
                map.put(province, true);
            }
            VrbtChannelProvinceConfig vrbtChannelProvinceConfig = vrbtChannelProvinceConfigService.getById(id);
            String json = JSON.toJSONString(map);
            vrbtChannelProvinceConfig.setProvinceJson(json);
            vrbtChannelProvinceConfigService.updateById(vrbtChannelProvinceConfig);
        } catch (Exception e) {
            log.error("saveDatarule()发生异常：" + e.getMessage(), e);
            return Result.error("保存失败");
        }
        return Result.ok("保存成功!");
    }

    /**
     * 查询关联关系
     */
    @GetMapping(value = "/findRelate/{roleId}")
    public Result<List<String>> loadDatarule(@PathVariable("roleId") String roleId) {
        List<String> list = new ArrayList<>();
        Result<List<String>> result = new Result<>();
        try {
            VrbtChannelProvinceConfig vrbtChannelProvinceConfig = vrbtChannelProvinceConfigService.getById(roleId);
            String provinceJson = vrbtChannelProvinceConfig.getProvinceJson();
            Map<String, Object> jsonMap = JSONObject.parseObject(provinceJson);
            List<String> provinceList =  jsonMap.entrySet().stream().filter(e -> (boolean) e.getValue()).map(Map.Entry::getKey).collect(Collectors.toList());
            list = provinceTreeService.lambdaQuery().in(ProvinceTree::getProvinceName, provinceList).list().stream().map(ProvinceTree::getId).collect(Collectors.toList());
            result.setResult(list);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }
}
