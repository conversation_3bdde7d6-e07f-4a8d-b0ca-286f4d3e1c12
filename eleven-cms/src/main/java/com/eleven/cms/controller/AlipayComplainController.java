package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AlipayComplain;
import com.eleven.cms.remote.AliComplainService;
import com.eleven.cms.service.IAlipayComplainService;
import com.eleven.cms.util.BizConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 支付宝投诉明细
 * @Author: jeecg-boot
 * @Date:   2023-04-04
 * @Version: V1.0
 */
@Api(tags="支付宝投诉明细")
@RestController
@RequestMapping("/cms/alipayComplain")
@Slf4j
public class AlipayComplainController extends JeecgController<AlipayComplain, IAlipayComplainService> {
	 @Autowired
	 private IAlipayComplainService alipayComplainService;
	 @Autowired
	 private AliComplainService aliComplainService;
	 @Autowired
	 private RedisUtil redisUtil;
	 private static final String ALIPAY_SOLVE_APPID_CACHE_REDIS_KEY = "solve::appid::cache::warn:";

	/**
	 * 分页列表查询
	 *
	 * @param alipayComplain
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "支付宝投诉明细-分页列表查询")
	@ApiOperation(value="支付宝投诉明细-分页列表查询", notes="支付宝投诉明细-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AlipayComplain alipayComplain,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AlipayComplain> queryWrapper = QueryGenerator.initQueryWrapper(alipayComplain, req.getParameterMap());
		Page<AlipayComplain> page = new Page<AlipayComplain>(pageNo, pageSize);
		IPage<AlipayComplain> pageList = alipayComplainService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param alipayComplain
	 * @return
	 */
	//@AutoLog(value = "支付宝投诉明细-添加")
	@ApiOperation(value="支付宝投诉明细-添加", notes="支付宝投诉明细-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AlipayComplain alipayComplain) {
		alipayComplainService.save(alipayComplain);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param alipayComplain
	 * @return
	 */
	//@AutoLog(value = "支付宝投诉明细-编辑")
	@ApiOperation(value="支付宝投诉明细-编辑", notes="支付宝投诉明细-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AlipayComplain alipayComplain) {
		alipayComplainService.updateById(alipayComplain);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "支付宝投诉明细-通过id删除")
	@ApiOperation(value="支付宝投诉明细-通过id删除", notes="支付宝投诉明细-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		alipayComplainService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "支付宝投诉明细-批量删除")
	@ApiOperation(value="支付宝投诉明细-批量删除", notes="支付宝投诉明细-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.alipayComplainService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "支付宝投诉明细-通过id查询")
	@ApiOperation(value="支付宝投诉明细-通过id查询", notes="支付宝投诉明细-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AlipayComplain alipayComplain = alipayComplainService.getById(id);
		if(alipayComplain==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(alipayComplain);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param alipayComplain
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AlipayComplain alipayComplain) {
        return super.exportXls(request, alipayComplain, AlipayComplain.class, "支付宝投诉明细");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AlipayComplain.class);
    }

	 /**
	  * 支付宝查询投诉详细
	  * @param complainEventId
	  * @param appId
	  * @return
	  */
	 @ApiOperation(value = "支付宝查询投诉详细", notes = "支付宝查询投诉详细")
	 @PostMapping(value = "/aliPay/complain")
	 @ResponseBody
	 public Result<?> aliPayComplain(@RequestParam(value = "complainEventId", required = false, defaultValue ="")String complainEventId,@RequestParam(value = "appId", required = false, defaultValue ="")String appId){
		 if(StringUtils.isEmpty(complainEventId)){
			 return Result.error("订单号不能为空");
		 }
		 if(StringUtils.isEmpty(appId)){
			 return Result.error("商户号不能为空");
		 }
		 return aliComplainService.alipayQueryComplainDetail(complainEventId,appId);
	 }
	 /**
	  * 支付宝appId缓存
	  * @param appId
	  * @return
	  */
	 @ApiOperation(value = "支付宝appId缓存", notes = "支付宝appId缓存")
	 @PostMapping(value = "/aliPay/solve/appIdCache")
	 @ResponseBody
	 public Result<?> aliPaySolveAppIdCache(@RequestParam(value = "appId", required = false, defaultValue ="")String appId){
		 if(StringUtils.isEmpty(appId)){
			 return Result.error("商户号不能为空");
		 }
		 if(redisUtil.hasKey(ALIPAY_SOLVE_APPID_CACHE_REDIS_KEY)){
			 redisUtil.del(ALIPAY_SOLVE_APPID_CACHE_REDIS_KEY);
		 }
		 redisUtil.set(ALIPAY_SOLVE_APPID_CACHE_REDIS_KEY,appId);
		 return Result.ok();
	 }
	 /**
	  * 支付宝投诉上传图片
	  * @param file
	  * @return
	  */
	 @ApiOperation(value = "支付宝投诉上传图片", notes = "支付宝投诉上传图片")
	 @ResponseBody
	 @RequestMapping("/upload")
	 public Result<?> alipayUploadImg(MultipartFile file){
		 if(file==null) {
			 return Result.error("图片不能为空");
		 }
		 String appId=(String)redisUtil.get(ALIPAY_SOLVE_APPID_CACHE_REDIS_KEY);
		 return aliComplainService.alipayUploadImg(file,appId);
	 }




	 /**
	  * 商家处理交易投诉
	  * @param complainEventId 投诉号
	  * @param feedbackImages 商家处理投诉时反馈凭证的图片id，多个逗号隔开（图片id可以通过"商户上传处理图片"接口获取）
	  * @param feedbackContent 投诉处理描述 200个字符
	  * @param feedbackCode 反馈类目ID 00:使用体验保障金退款； 02:通过其他方式退款; 03:已发货; 04:其他; 05:已完成售后服务; 06:非我方责任范围；
	  * @param operator 处理投诉人
	  * @return
	  */

	 @ApiOperation(value = "商家处理交易投诉", notes = "商家处理交易投诉")
	 @PostMapping(value = "/aliPay/solve/complain")
	 @ResponseBody
	 public Result<?> alipaySolveComplain(@RequestParam(value = "complainEventId", required = false, defaultValue ="")String complainEventId,
									 @RequestParam(value = "feedbackImages", required = false, defaultValue ="")String feedbackImages,
									 @RequestParam(value = "feedbackContent", required = false, defaultValue ="")String feedbackContent,
									 @RequestParam(value = "feedbackCode", required = false, defaultValue ="")String feedbackCode,
									 @RequestParam(value = "operator", required = false, defaultValue ="")String operator){
	 	log.info("商家处理交易投诉,投诉订单号:{},图片ID:{},详细:{},类型:{},处理人:{}",complainEventId,feedbackImages,feedbackContent,feedbackCode,operator);
	 	 if(StringUtils.isEmpty(complainEventId)){
			 return Result.error("订单号不能为空");
		 }
		 return alipayComplainService.alipaySolveComplain( complainEventId, feedbackImages, feedbackContent, feedbackCode, operator);
	 }



//
//
//	 /**
//	  * 商家留言回复
//	  * @param complainEventId 投诉号
//	  * @param feedbackImages 商家处理投诉时反馈凭证的图片id，多个逗号隔开（图片id可以通过"商户上传处理图片"接口获取）
//	  * @param feedbackContent 投诉处理描述 200个字符
//	  * @return
//	  */
//
//	 @ApiOperation(value = "商家留言回复", notes = "商家留言回复")
//	 @PostMapping(value = "/aliPay/reply/complain")
//	 @ResponseBody
//	 public Result<?> alipayReplyComplain(@RequestParam(value = "complainEventId", required = false, defaultValue ="")String complainEventId,
//										  @RequestParam(value = "feedbackImages", required = false, defaultValue ="")String feedbackImages,
//										  @RequestParam(value = "feedbackContent", required = false, defaultValue ="")String feedbackContent){
//		 if(StringUtils.isEmpty(complainEventId)){
//			 return Result.error("订单号不能为空");
//		 }
//		 return alipayComplainService.alipayReplyComplain( complainEventId, feedbackImages, feedbackContent);
//	 }
//
//	 /**
//	  * 商家补充凭证
//	  * @param complainEventId 投诉号
//	  * @param feedbackImages 商家处理投诉时反馈凭证的图片id，多个逗号隔开（图片id可以通过"商户上传处理图片"接口获取）
//	  * @param feedbackContent 投诉处理描述 200个字符
//	  * @return
//	  */
//
//	 @ApiOperation(value = "商家补充凭证", notes = "商家补充凭证")
//	 @PostMapping(value = "/aliPay/supplement/complain")
//	 @ResponseBody
//	 public Result<?> alipaySupplementComplain(@RequestParam(value = "complainEventId", required = false, defaultValue ="")String complainEventId,
//										  @RequestParam(value = "feedbackImages", required = false, defaultValue ="")String feedbackImages,
//										  @RequestParam(value = "feedbackContent", required = false, defaultValue ="")String feedbackContent){
//		 if(StringUtils.isEmpty(complainEventId)){
//			 return Result.error("订单号不能为空");
//		 }
//		 return alipayComplainService.alipaySupplementComplain( complainEventId, feedbackImages, feedbackContent);
//	 }
 }
