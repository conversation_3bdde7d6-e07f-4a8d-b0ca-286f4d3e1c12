package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.MusicCollect;
import com.eleven.cms.service.IMusicCollectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 歌曲收藏
 * @Author: jeecg-boot
 * @Date:   2023-11-21
 * @Version: V1.0
 */
@Api(tags="歌曲收藏")
@RestController
@RequestMapping("/cms/musicCollect")
@Slf4j
public class MusicCollectController extends JeecgController<MusicCollect, IMusicCollectService> {
	@Autowired
	private IMusicCollectService musicCollectService;

	/**
	 * 分页列表查询
	 *
	 * @param musicCollect
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "歌曲收藏-分页列表查询")
	@ApiOperation(value="歌曲收藏-分页列表查询", notes="歌曲收藏-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(MusicCollect musicCollect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<MusicCollect> queryWrapper = QueryGenerator.initQueryWrapper(musicCollect, req.getParameterMap());
		Page<MusicCollect> page = new Page<MusicCollect>(pageNo, pageSize);
		IPage<MusicCollect> pageList = musicCollectService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param musicCollect
	 * @return
	 */
	//@AutoLog(value = "歌曲收藏-添加")
	@ApiOperation(value="歌曲收藏-添加", notes="歌曲收藏-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody MusicCollect musicCollect) {
		musicCollectService.save(musicCollect);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param musicCollect
	 * @return
	 */
	//@AutoLog(value = "歌曲收藏-编辑")
	@ApiOperation(value="歌曲收藏-编辑", notes="歌曲收藏-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody MusicCollect musicCollect) {
		musicCollectService.updateById(musicCollect);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "歌曲收藏-通过id删除")
	@ApiOperation(value="歌曲收藏-通过id删除", notes="歌曲收藏-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		musicCollectService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "歌曲收藏-批量删除")
	@ApiOperation(value="歌曲收藏-批量删除", notes="歌曲收藏-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.musicCollectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "歌曲收藏-通过id查询")
	@ApiOperation(value="歌曲收藏-通过id查询", notes="歌曲收藏-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		MusicCollect musicCollect = musicCollectService.getById(id);
		if(musicCollect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(musicCollect);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param musicCollect
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MusicCollect musicCollect) {
        return super.exportXls(request, musicCollect, MusicCollect.class, "歌曲收藏");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MusicCollect.class);
    }

}
