package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.VrbtAppUserPoints;
import com.eleven.cms.service.IVrbtAppUserPointsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 视频彩铃app用户积分
 * @Author: jeecg-boot
 * @Date:   2024-04-16
 * @Version: V1.0
 */
@Api(tags="视频彩铃app用户积分")
@RestController
@RequestMapping("/cms/vrbtAppUserPoints")
@Slf4j
public class VrbtAppUserPointsController extends JeecgController<VrbtAppUserPoints, IVrbtAppUserPointsService> {
	@Autowired
	private IVrbtAppUserPointsService vrbtAppUserPointsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param vrbtAppUserPoints
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	////@AutoLog(value = "视频彩铃app用户积分-分页列表查询")
	@ApiOperation(value="视频彩铃app用户积分-分页列表查询", notes="视频彩铃app用户积分-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(VrbtAppUserPoints vrbtAppUserPoints,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<VrbtAppUserPoints> queryWrapper = QueryGenerator.initQueryWrapper(vrbtAppUserPoints, req.getParameterMap());
		Page<VrbtAppUserPoints> page = new Page<VrbtAppUserPoints>(pageNo, pageSize);
		IPage<VrbtAppUserPoints> pageList = vrbtAppUserPointsService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param vrbtAppUserPoints
	 * @return
	 */
	//@AutoLog(value = "视频彩铃app用户积分-添加")
	@ApiOperation(value="视频彩铃app用户积分-添加", notes="视频彩铃app用户积分-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody VrbtAppUserPoints vrbtAppUserPoints) {
		vrbtAppUserPointsService.save(vrbtAppUserPoints);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param vrbtAppUserPoints
	 * @return
	 */
	//@AutoLog(value = "视频彩铃app用户积分-编辑")
	@ApiOperation(value="视频彩铃app用户积分-编辑", notes="视频彩铃app用户积分-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody VrbtAppUserPoints vrbtAppUserPoints) {
		vrbtAppUserPointsService.updateById(vrbtAppUserPoints);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "视频彩铃app用户积分-通过id删除")
	@ApiOperation(value="视频彩铃app用户积分-通过id删除", notes="视频彩铃app用户积分-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		vrbtAppUserPointsService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "视频彩铃app用户积分-批量删除")
	@ApiOperation(value="视频彩铃app用户积分-批量删除", notes="视频彩铃app用户积分-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.vrbtAppUserPointsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "视频彩铃app用户积分-通过id查询")
	@ApiOperation(value="视频彩铃app用户积分-通过id查询", notes="视频彩铃app用户积分-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		VrbtAppUserPoints vrbtAppUserPoints = vrbtAppUserPointsService.getById(id);
		if(vrbtAppUserPoints==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(vrbtAppUserPoints);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param vrbtAppUserPoints
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, VrbtAppUserPoints vrbtAppUserPoints) {
        return super.exportXls(request, vrbtAppUserPoints, VrbtAppUserPoints.class, "视频彩铃app用户积分");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, VrbtAppUserPoints.class);
    }

}
