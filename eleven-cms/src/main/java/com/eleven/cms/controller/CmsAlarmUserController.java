package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CmsAlarmUser;
import com.eleven.cms.service.ICmsAlarmUserService;
import com.eleven.cms.vo.AlarmUserListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: cms_alarm_user
 * @Author: jeecg-boot
 * @Date:   2024-09-04
 * @Version: V1.0
 */
@Api(tags="cms_alarm_user")
@RestController
@RequestMapping("/cms/cmsAlarmUser")
@Slf4j
public class CmsAlarmUserController extends JeecgController<CmsAlarmUser, ICmsAlarmUserService> {
	@Autowired
	private ICmsAlarmUserService cmsAlarmUserService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cmsAlarmUser
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user-分页列表查询")
	@ApiOperation(value="cms_alarm_user-分页列表查询", notes="cms_alarm_user-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CmsAlarmUser cmsAlarmUser,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CmsAlarmUser> queryWrapper = QueryGenerator.initQueryWrapper(cmsAlarmUser, req.getParameterMap());
		Page<CmsAlarmUser> page = new Page<CmsAlarmUser>(pageNo, pageSize);
		IPage<CmsAlarmUser> pageList = cmsAlarmUserService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cmsAlarmUser
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user-添加")
	@ApiOperation(value="cms_alarm_user-添加", notes="cms_alarm_user-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody @Validated CmsAlarmUser cmsAlarmUser) {
		cmsAlarmUserService.save(cmsAlarmUser);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cmsAlarmUser
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user-编辑")
	@ApiOperation(value="cms_alarm_user-编辑", notes="cms_alarm_user-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody @Validated CmsAlarmUser cmsAlarmUser) {
		cmsAlarmUserService.updateById(cmsAlarmUser);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user-通过id删除")
	@ApiOperation(value="cms_alarm_user-通过id删除", notes="cms_alarm_user-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		cmsAlarmUserService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user-批量删除")
	@ApiOperation(value="cms_alarm_user-批量删除", notes="cms_alarm_user-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cmsAlarmUserService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user-通过id查询")
	@ApiOperation(value="cms_alarm_user-通过id查询", notes="cms_alarm_user-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CmsAlarmUser cmsAlarmUser = cmsAlarmUserService.getById(id);
		if(cmsAlarmUser==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(cmsAlarmUser);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmsAlarmUser
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsAlarmUser cmsAlarmUser) {
        return super.exportXls(request, cmsAlarmUser, CmsAlarmUser.class, "cms_alarm_user");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CmsAlarmUser.class);
    }

	 /**
	  * 查询告警人列表
	  *
	  * @return Result<?>
	  */
	 @GetMapping("/query/list")
	 public Result<?> queryList() {
		 List<CmsAlarmUser> list = cmsAlarmUserService.list(new LambdaQueryWrapper<CmsAlarmUser>()
				 .select(CmsAlarmUser::getId, CmsAlarmUser::getRealName));

		 List<AlarmUserListVO> data = new ArrayList<>();
		 for (CmsAlarmUser cmsAlarmUser : list) {
			 AlarmUserListVO alarmUserListVO = new AlarmUserListVO();
			 alarmUserListVO.setId(cmsAlarmUser.getId());
			 alarmUserListVO.setRealName(cmsAlarmUser.getRealName());
			 data.add(alarmUserListVO);
		 }
		 return Result.ok(data);
	 }
}
