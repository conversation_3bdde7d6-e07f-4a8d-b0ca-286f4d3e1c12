package com.eleven.cms.controller;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.dto.MemberRightsImport;
import com.eleven.cms.dto.RightsProductImport;
import com.eleven.cms.entity.MemberRights;
import com.eleven.cms.entity.RightsPack;
import com.eleven.cms.service.IMemberRightsService;
import com.eleven.cms.service.IRightsPackService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 视听会员权益
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Api(tags="视听会员权益")
@RestController
@RequestMapping("/cms/memberRights")
@Slf4j
public class MemberRightsController extends JeecgController<MemberRights, IMemberRightsService> {
	@Autowired
	private IMemberRightsService memberRightsService;
	@Autowired
	private IRightsPackService rightsPackService;
	/**
	 * 分页列表查询
	 *
	 * @param memberRights
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "视听会员权益-分页列表查询")
	@ApiOperation(value="视听会员权益-分页列表查询", notes="视听会员权益-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(MemberRights memberRights,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<MemberRights> queryWrapper = QueryGenerator.initQueryWrapper(memberRights, req.getParameterMap());
		Page<MemberRights> page = new Page<MemberRights>(pageNo, pageSize);
		IPage<MemberRights> pageList = memberRightsService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param memberRights
	 * @return
	 */
	//@AutoLog(value = "视听会员权益-添加")
	@ApiOperation(value="视听会员权益-添加", notes="视听会员权益-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody MemberRights memberRights) {
		memberRightsService.save(memberRights);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param memberRights
	 * @return
	 */
	//@AutoLog(value = "视听会员权益-编辑")
	@ApiOperation(value="视听会员权益-编辑", notes="视听会员权益-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody MemberRights memberRights) {
		memberRightsService.updateById(memberRights);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "视听会员权益-通过id删除")
	@ApiOperation(value="视听会员权益-通过id删除", notes="视听会员权益-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		memberRightsService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "视听会员权益-批量删除")
	@ApiOperation(value="视听会员权益-批量删除", notes="视听会员权益-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.memberRightsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "视听会员权益-通过id查询")
	@ApiOperation(value="视听会员权益-通过id查询", notes="视听会员权益-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		MemberRights memberRights = memberRightsService.getById(id);
		if(memberRights==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(memberRights);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param memberRights
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MemberRights memberRights) {
        return super.exportXls(request, memberRights, MemberRights.class, "视听会员权益");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return this.importExcels(request);
    }
	/**
	 * 根据业务主键查询视听会员权益
	 *
	 * @param packId
	 * @return
	 */
	@RequestMapping(value = "/queryRightsByPackId", method = RequestMethod.GET)
	public Result<List<MemberRights>> queryRightsByPackId(@RequestParam(name = "packId", required = true) String packId, @RequestParam(name="rightsName",required=false) String rightsName) {
		Result<List<MemberRights>> result = new Result<>();
		List<MemberRights> memberRights = memberRightsService.queryRightsList();
		result.setSuccess(true);
		result.setResult(memberRights);
		return result;
	}

	/**
	 * 通过excel导入价格数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importPriceExcel", method = RequestMethod.POST)
	public Result<?> importPriceExcel(HttpServletRequest request, HttpServletResponse response) {
		return this.importExcel(request);
	}

	/**
	 * 通过excel导入数据
	 * @param request
	 * @return
	 */
	public Result<?> importExcel(HttpServletRequest request) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			ImportParams params = new ImportParams();
			params.setTitleRows(0);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<MemberRightsImport> list = ExcelImportUtil.importExcel(file.getInputStream(), MemberRightsImport.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				List<MemberRights> memberRightsList= Lists.newArrayList();
				list.forEach(item -> {
					MemberRights memberRights=memberRightsService.lambdaQuery().eq(MemberRights::getCouponId,item.getCouponId()).orderByDesc(MemberRights::getCreateTime).last("limit 1").one();
					if(memberRights!=null){
						Double totalFee=Double.valueOf(item.getProductPrice());
						String productPrice= BigDecimal.valueOf(totalFee).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
						if(productPrice.contains(".")){
							productPrice= String.format("%.0f",Double.valueOf(productPrice));
						}
						memberRights.setProductPrice(Integer.valueOf(productPrice));
						memberRights.setRightsName(item.getRightsName());
						memberRightsList.add(memberRights);
					}
				});
				if(memberRightsList!=null && memberRightsList.size()>0){
					memberRightsService.updateMemberRightsList(memberRightsList);
				}
				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				return Result.error("文件导入失败:" + e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
	}


	/**
	 * 查询产品
	 * @param rights
	 * @return
	 */
	@RequestMapping(value = "/queryShopByProductId", method = RequestMethod.GET)
	public Result<List<MemberRights>> queryShopByProductId(MemberRights rights) {
		Result<List<MemberRights>> result = new Result<>();
		List<MemberRights> memberRights =memberRightsService.queryShopByProductId(rights);
		result.setSuccess(true);
		result.setResult(memberRights);
		return result;
	}



	/**
	 * 通过excel导入数据
	 * @param request
	 * @return
	 */
	public Result<?> importExcels(HttpServletRequest request) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			ImportParams params = new ImportParams();
			params.setTitleRows(0);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<RightsProductImport> list = ExcelImportUtil.importExcel(file.getInputStream(), RightsProductImport.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				List<MemberRights> memberRightsList= Lists.newArrayList();
				list.forEach(item -> {
					boolean isProduct=memberRightsService.lambdaQuery().eq(MemberRights::getCouponId,item.getCouponId()).count()<=0;
					if(isProduct){
						MemberRights memberRights=new MemberRights();
						/**充值couponId*/
						memberRights.setCouponId(item.getCouponId());
						/**权益名称*/
						memberRights.setRightsName(item.getRightsName());
						/**产品价格（单位：分）*/
						Double totalFeeProductPrice=Double.valueOf(item.getProductPrice());
						String productPrice= BigDecimal.valueOf(totalFeeProductPrice).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
						if(productPrice.contains(".")){
							productPrice= String.format("%.0f",Double.valueOf(productPrice));
						}
						memberRights.setProductPrice(Integer.valueOf(productPrice));
						memberRights.setRightsId(item.getCouponId()+""+productPrice);
						/**是否输入账号:0=否,1=是*/
						memberRights.setIsAccount(0);
						/**充值方式:0=直充,1=券码*/
						memberRights.setRechargeState(0);
						/**产品原价（单位：分）*/
						Double totalFeeOriginalPrice=Double.valueOf(item.getOriginalPrice());
						String originalPrice= BigDecimal.valueOf(totalFeeOriginalPrice).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
						if(originalPrice.contains(".")){
							originalPrice= String.format("%.0f",Double.valueOf(originalPrice));
						}
						memberRights.setOriginalPrice(Integer.valueOf(originalPrice));
						/**权益开关:0=关,1=开*/
						memberRights.setRightsSwitchs(1);
						memberRights.setCompanyOwner("JUNBO");
						memberRightsList.add(memberRights);
					}
				});
				if(memberRightsList!=null && memberRightsList.size()>0){
					memberRightsService.addMemberRightsList(memberRightsList);
				}
				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				return Result.error("文件导入失败:" + e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
	}
}
