package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.entity.CouponCode;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.util.BizConstant;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.SmsModel;
import com.eleven.cms.service.ISmsModelService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 业务短信模板
 * @Author: jeecg-boot
 * @Date:   2022-09-23
 * @Version: V1.0
 */
@Api(tags="业务短信模板")
@RestController
@RequestMapping("/cms/smsModel")
@Slf4j
public class SmsModelController extends JeecgController<SmsModel, ISmsModelService> {
	@Autowired
	private ISmsModelService smsModelService;
	 @Autowired
	 private IMemberService memberService;
	/**
	 * 分页列表查询
	 *
	 * @param smsModel
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "业务短信模板-分页列表查询")
	@ApiOperation(value="业务短信模板-分页列表查询", notes="业务短信模板-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SmsModel smsModel,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SmsModel> queryWrapper = QueryGenerator.initQueryWrapper(smsModel, req.getParameterMap());
		Page<SmsModel> page = new Page<SmsModel>(pageNo, pageSize);
		IPage<SmsModel> pageList = smsModelService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param smsModel
	 * @return
	 */
	//@AutoLog(value = "业务短信模板-添加")
	@ApiOperation(value="业务短信模板-添加", notes="业务短信模板-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody SmsModel smsModel) {
		smsModelService.save(smsModel);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param smsModel
	 * @return
	 */
	//@AutoLog(value = "业务短信模板-编辑")
	@ApiOperation(value="业务短信模板-编辑", notes="业务短信模板-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody SmsModel smsModel) {
		smsModelService.updateById(smsModel);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "业务短信模板-通过id删除")
	@ApiOperation(value="业务短信模板-通过id删除", notes="业务短信模板-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		smsModelService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "业务短信模板-批量删除")
	@ApiOperation(value="业务短信模板-批量删除", notes="业务短信模板-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.smsModelService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "业务短信模板-通过id查询")
	@ApiOperation(value="业务短信模板-通过id查询", notes="业务短信模板-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SmsModel smsModel = smsModelService.getById(id);
		if(smsModel==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(smsModel);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param smsModel
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SmsModel smsModel) {
        return super.exportXls(request, smsModel, SmsModel.class, "业务短信模板");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SmsModel.class);
    }

	 /**
	  * 续订权益领取提醒短信
	  * @param smsModel
	  * @return
	  */
	 //@AutoLog(value = "续订权益领取提醒短信")
	 @ApiOperation(value="续订权益领取提醒短信", notes="续订权益领取提醒短信")
	 @PostMapping(value = "/renew/rights/receive/sms")
	 public Result<?> renewRightsReceiveSms(@RequestBody SmsModel smsModel){
		 if(StringUtils.isEmpty(smsModel.getChannel())){
			 return Result.error("渠道号不能为空");
		 }
		 return memberService.renewRightsReceiveSms(smsModel.getChannel(), BizConstant.BUSINESS_TYPE_RENEW);
	 }
}
