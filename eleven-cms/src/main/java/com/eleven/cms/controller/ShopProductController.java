package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.ShopProduct;
import com.eleven.cms.service.IShopProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 商城产品列表
 * @Author: jeecg-boot
 * @Date:   2023-09-19
 * @Version: V1.0
 */
@Api(tags="商城产品列表")
@RestController
@RequestMapping("/cms/shopProduct")
@Slf4j
public class ShopProductController extends JeecgController<ShopProduct, IShopProductService> {
	@Autowired
	private IShopProductService shopProductService;

	/**
	 * 分页列表查询
	 *
	 * @param shopProduct
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "商城产品列表-分页列表查询")
	@ApiOperation(value="商城产品列表-分页列表查询", notes="商城产品列表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ShopProduct shopProduct,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ShopProduct> queryWrapper = QueryGenerator.initQueryWrapper(shopProduct, req.getParameterMap());
		Page<ShopProduct> page = new Page<ShopProduct>(pageNo, pageSize);
		IPage<ShopProduct> pageList = shopProductService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param shopProduct
	 * @return
	 */
	//@AutoLog(value = "商城产品列表-添加")
	@ApiOperation(value="商城产品列表-添加", notes="商城产品列表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ShopProduct shopProduct) {
		shopProductService.save(shopProduct);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param shopProduct
	 * @return
	 */
	//@AutoLog(value = "商城产品列表-编辑")
	@ApiOperation(value="商城产品列表-编辑", notes="商城产品列表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ShopProduct shopProduct) {
		shopProductService.updateById(shopProduct);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "商城产品列表-通过id删除")
	@ApiOperation(value="商城产品列表-通过id删除", notes="商城产品列表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		shopProductService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "商城产品列表-批量删除")
	@ApiOperation(value="商城产品列表-批量删除", notes="商城产品列表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.shopProductService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "商城产品列表-通过id查询")
	@ApiOperation(value="商城产品列表-通过id查询", notes="商城产品列表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ShopProduct shopProduct = shopProductService.getById(id);
		if(shopProduct==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(shopProduct);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param shopProduct
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ShopProduct shopProduct) {
        return super.exportXls(request, shopProduct, ShopProduct.class, "商城产品列表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ShopProduct.class);
    }

}
