package com.eleven.cms.controller;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.entity.ProvinceTree;
import com.eleven.cms.service.IProvinceTreeService;
import com.eleven.cms.service.IVrbtChannelProvinceConfigService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.entity.VrbtProvinceSwitchConfig;
import com.eleven.cms.service.IVrbtProvinceSwitchConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_vrbt_province_switch_config
 * @Author: jeecg-boot
 * @Date:   2021-08-16
 * @Version: V1.0
 */
@Api(tags="cms_vrbt_province_switch_config")
@RestController
@RequestMapping("/cms/vrbtProvinceSwitchConfig")
@Slf4j
public class VrbtProvinceSwitchConfigController extends JeecgController<VrbtProvinceSwitchConfig, IVrbtProvinceSwitchConfigService> {
	@Autowired
	private IVrbtProvinceSwitchConfigService vrbtProvinceSwitchConfigService;
	@Autowired
	private IProvinceTreeService provinceTreeService;
	/**
	 * 分页列表查询
	 *
	 * @param vrbtProvinceSwitchConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_province_switch_config-分页列表查询")
	@ApiOperation(value="cms_vrbt_province_switch_config-分页列表查询", notes="cms_vrbt_province_switch_config-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(VrbtProvinceSwitchConfig vrbtProvinceSwitchConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<VrbtProvinceSwitchConfig> queryWrapper = QueryGenerator.initQueryWrapper(vrbtProvinceSwitchConfig, req.getParameterMap());
		Page<VrbtProvinceSwitchConfig> page = new Page<VrbtProvinceSwitchConfig>(pageNo, pageSize);
		IPage<VrbtProvinceSwitchConfig> pageList = vrbtProvinceSwitchConfigService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param vrbtProvinceSwitchConfig
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_province_switch_config-添加")
	@ApiOperation(value="cms_vrbt_province_switch_config-添加", notes="cms_vrbt_province_switch_config-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody VrbtProvinceSwitchConfig vrbtProvinceSwitchConfig) {
		vrbtProvinceSwitchConfigService.save(vrbtProvinceSwitchConfig);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param vrbtProvinceSwitchConfig
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_province_switch_config-编辑")
	@ApiOperation(value="cms_vrbt_province_switch_config-编辑", notes="cms_vrbt_province_switch_config-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody VrbtProvinceSwitchConfig vrbtProvinceSwitchConfig) {
		vrbtProvinceSwitchConfigService.updateById(vrbtProvinceSwitchConfig);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_province_switch_config-通过id删除")
	@ApiOperation(value="cms_vrbt_province_switch_config-通过id删除", notes="cms_vrbt_province_switch_config-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		vrbtProvinceSwitchConfigService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_province_switch_config-批量删除")
	@ApiOperation(value="cms_vrbt_province_switch_config-批量删除", notes="cms_vrbt_province_switch_config-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.vrbtProvinceSwitchConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_province_switch_config-通过id查询")
	@ApiOperation(value="cms_vrbt_province_switch_config-通过id查询", notes="cms_vrbt_province_switch_config-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		VrbtProvinceSwitchConfig vrbtProvinceSwitchConfig = vrbtProvinceSwitchConfigService.getById(id);
		if(vrbtProvinceSwitchConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(vrbtProvinceSwitchConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param vrbtProvinceSwitchConfig
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, VrbtProvinceSwitchConfig vrbtProvinceSwitchConfig) {
        return super.exportXls(request, vrbtProvinceSwitchConfig, VrbtProvinceSwitchConfig.class, "cms_vrbt_province_switch_config");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, VrbtProvinceSwitchConfig.class);
    }
	 /**
	  * 保存关联关系
	  */
	 @PostMapping(value = "/saveRelate")
	 public Result<?> saveDatarule(@RequestBody JSONObject jsonObject) {
		 try {
			 Map<String, Object> map = new HashMap<>();
			 String[] provinceList = jsonObject.getString("provinceList").split(",");
			 List<String> asList = Arrays.asList(provinceList);
			 if(asList.size() == 1 && asList.contains("")){
			 	asList = new ArrayList<String>();
			 }
			 String id = jsonObject.getString("roleId");
			 List<ProvinceTree> provinceTrees = provinceTreeService.list();
			 for (ProvinceTree province : provinceTrees) {
				 map.put(province.getProvinceName(),false);
			 }
			 for (String province : asList) {
				 map.put(province,true);
			 }
			 VrbtProvinceSwitchConfig vrbtProvinceSwitchConfig = vrbtProvinceSwitchConfigService.getById(id);
			 String json = JSON.toJSONString(map);
			 vrbtProvinceSwitchConfig.setProvinceJson(json);
			 vrbtProvinceSwitchConfigService.updateById(vrbtProvinceSwitchConfig);
		 } catch (Exception e) {
			 log.error("saveDatarule()发生异常：" + e.getMessage(),e);
			 return Result.error("保存失败");
		 }
		 return Result.ok("保存成功!");
	 }

	 /**
	  * 查询关联关系
	  */
	 @GetMapping(value = "/findRelate/{roleId}")
	 public Result<List<String>> loadDatarule(@PathVariable("roleId") String roleId) {
		 ArrayList<String> list = new ArrayList<>();
		 Result<List<String>> result = new Result<>();
		 try {
			 VrbtProvinceSwitchConfig vrbtProvinceSwitchConfig = vrbtProvinceSwitchConfigService.getById(roleId);
			 String json = vrbtProvinceSwitchConfig.getProvinceJson();
			 Map<String, Object> jsonMap = JSONObject.parseObject(json);
			 for(Map.Entry<String, Object> entry:jsonMap.entrySet()){
				 if((Boolean) entry.getValue() == true) {
					 ProvinceTree provinceTree = provinceTreeService.
							 lambdaQuery().
							 eq(ProvinceTree::getProvinceName, entry.getKey()).
							 one();
					 list.add(provinceTree.getId());
				 }
			 }
			 result.setResult(list);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 log.error(e.getMessage(), e);
		 }
		 return result;
	 }
}
