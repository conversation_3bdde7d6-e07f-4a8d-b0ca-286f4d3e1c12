package com.eleven.cms.controller;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.config.*;
import com.eleven.cms.dto.DypayNotifyMsg;
import com.eleven.cms.dto.MiniApi;
import com.eleven.cms.entity.*;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.WeChatLogin;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.ColumnClassEnum;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Api(tags = "miniApi")
@RestController
@RequestMapping("/miniApi")
@Slf4j
@Validated
public class MiniApiController {

    @Autowired
    private IMusicService musicService;

    @Autowired
    private IMusicFavouriteService musicFavouriteService;

    @Autowired
    private IColumnService columnService;

    private OkHttpClient client;

    private ObjectMapper mapper;

    @Autowired
    private MiniApiProperties miniApiProperties;

    @Autowired
    DouyinAppService douyinAppService;

    @Autowired
    private MiniApiWXPayProperties miniApiWXPayProperties;

    @Autowired
    private IMiniApiWxpayLogService miniApiWxpayLogService;
    @Autowired
    private IMemberService memberService;

    public static final okhttp3.MediaType JSON
            = okhttp3.MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     *  歌曲播放数,点赞数，收藏数更新
     *
     * @param music
     * @return
     */
    //@AutoLog(value = "歌曲-歌曲播放数，点赞数，收藏数更新")
    @ApiOperation(value="歌曲-歌曲播放数，点赞数，收藏数更新", notes="歌曲-歌曲播放数，点赞数，收藏数更新")
    @PostMapping(value = "/clickCount")
    public Result<?> clickCount(@RequestBody Music music) {
        musicService.clickCount(music);
        return Result.ok("更新成功!");
    }

    /**
     * 匹配歌曲
     *
     * @param mobile 手机号
     * @return
     */
    //@AutoLog(value = "匹配歌曲")
    @ApiOperation(value="匹配歌曲", notes="匹配歌曲")
    @GetMapping(value = "/matching")
    public Result<?> matching(@RequestParam(name="mobile",required=true) String mobile) {
        List<ColumnDetail> list = musicFavouriteService.matching(mobile);
        return Result.ok(list);
    }

    /**
     *   歌曲收藏-添加
     *
     * @param musicFavourite
     * @return
     */
    //@AutoLog(value = "歌曲收藏")
    @ApiOperation(value="歌曲收藏", notes="歌曲收藏")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MusicFavourite musicFavourite) {
        musicFavouriteService.save(musicFavourite);
        return Result.ok("添加成功！");
    }

    /**
     *   歌曲取消收藏-删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "歌曲取消收藏-删除")
    @ApiOperation(value="歌曲取消收藏-删除", notes="歌曲取消收藏-删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        musicFavouriteService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 查询收藏记录
     *
     * @param mobile 手机号
     * @return
     */
    //@AutoLog(value = "查询收藏记录")
    @ApiOperation(value="查询收藏记录", notes="查询收藏记录")
    @GetMapping(value = "/findByMobile")
    public Result<?> findByMobile(@RequestParam(name="mobile",required=true) String mobile) {
        List<MusicFavouriteVo> list = musicFavouriteService.findByMobile(mobile);
        return Result.ok(list);
    }
    /**
     * 主题广场
     *
     * @return
     */
    //@AutoLog(value = "主题广场")
    @ApiOperation(value="主题广场", notes="主题广场")
    @GetMapping(value = "/topic")
    public Result<?> topic() {
        List<ColumnDetail> list = columnService.topic();
        return Result.ok(list);
    }

    /**
     * 小程序获取会话密匙
     *
     * @return
     */
    //@AutoLog(value = "小程序获取会话密匙")
    @ApiOperation(value="小程序获取会话密匙", notes="小程序获取会话密匙")
    @PostMapping(value = "/sessionKey")
    public Result<?> getSessionKey(@RequestBody MiniApi miniApi) {


        //请求body示例：{
        //  "appid": "ttabc****",
        //  "secret": "d428**************7",
        //  "anonymous_code": "",
        //  "code": "iOyVA5hc*******"
        //}
        String content = null;
        Map<String, String> map = new HashMap<>();
        map.put("appid",miniApiProperties.getAppid());
        map.put("secret",miniApiProperties.getSecret());
        map.put("code",miniApi.getCode());
        map.put("anonymous_code",miniApi.getAnonymousCode());
        try {
            okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
            HttpUrl httpUrl = HttpUrl.parse("https://developer.toutiao.com/api/apps/v2/jscode2session")
                    .newBuilder()
                    .build();
            log.info("小程序获取会话密匙请求：" + httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                    .post(body)
                    .build();

            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                content = response.body().string();
                log.info("小程序获取会话密匙响应：" + content.toString());
            }
        } catch (Exception e) {
            log.warn("小程序获取会话密匙失败，异常：" + e);
            return Result.error(e.toString());
        }
        return Result.ok(content);
    }

    /**
     * 权益中心小程序获取密匙
     *
     * @return
     */
    //@AutoLog(value = "权益中心小程序获取密匙")
    @ApiOperation(value="权益中心小程序获取密匙", notes="权益中心小程序获取密匙")
    @PostMapping(value = "/sessionKeyForRights")
    public Result<?> getSessionKeyForRights(@RequestBody MiniApi miniApi) {


        //请求body示例：{
        //  "appid": "ttabc****",
        //  "secret": "d428**************7",
        //  "anonymous_code": "",
        //  "code": "iOyVA5hc*******"
        //}
        String content = null;
        Map<String, String> map = new HashMap<>();
        map.put("appid",miniApiProperties.getAppidForRights());
        map.put("secret",miniApiProperties.getSecretForRights());
        map.put("code",miniApi.getCode());
        map.put("anonymous_code",miniApi.getAnonymousCode());
        try {
            okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
            HttpUrl httpUrl = HttpUrl.parse("https://developer.toutiao.com/api/apps/v2/jscode2session")
                    .newBuilder()
                    .build();
            log.info("权益中心小程序获取密匙：" + httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                    .post(body)
                    .build();

            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                content = response.body().string();
                log.info("权益中心小程序获取密匙：" + content.toString());
            }
        } catch (Exception e) {
            log.warn("权益中心小程序获取密匙，异常：" + e);
            return Result.error(e.toString());
        }
        return Result.ok(content);
    }

    @ApiOperation(value = "解密权益中心小程序手机号", notes = "解密权益中心小程序手机号")
    @PostMapping(value = "/phoneNumberForRights")
    @ResponseBody
    public FebsResponse phoneNumberForRights(@RequestParam("data") String data,@RequestParam("code") String code,@RequestParam("iv") String iv) {
        return douyinAppService.phoneNumberForRights(data, code, iv);
    }

    @ApiOperation(value = "抖音小程序微信支付", notes = "抖音小程序微信支付")
    @PostMapping(value = "/ttAppletPay")
    @ResponseBody
    public JSONObject ttAppletPay(@RequestParam("couponId") String couponId,
                                  @RequestParam("mobile") String mobile,
                                  String account) {
        JSONObject returnJson = new JSONObject();
        String subject = "";
        String subjectBody = "";
        double price = 0.0;

        //根据产品id查询产品信息
        FebsResponse febsResponse = memberService.douyinQueryRightsProductPrice(mobile, couponId);
        try {
            JsonNode tree = mapper.readTree(febsResponse.get("data").toString());
            subject = tree.at("/rightsName").asText();
            subjectBody = tree.at("/productPrice").asText();
            price = Double.parseDouble(tree.at("/productPrice").asText());
            if(StringUtils.isBlank(subject) || price == 0.0){
                returnJson.put("error_info","查询权益产品信息出错,data:" + tree.toString());
                return returnJson;
            }
        } catch (Exception e) {
            log.error("抖音小程序微信支付:查询产品信息出错，",e);
            returnJson.put("error_info","抖音小程序微信支付:查询产品信息出错:" + e.getMessage());
            return returnJson;
        }
        String appid = miniApiWXPayProperties.getAppid();
        String notifyUrl = miniApiWXPayProperties.getNotifyUrl();
        String ttAppletPayUrl = miniApiWXPayProperties.getTtAppletPayUrl();
        String outTradeNo = UUIDGenerator.generate();
        MiniApiWxpayLog miniApiWxpayLog = new MiniApiWxpayLog();
        try {
            //加签验签的参数需要排序
            Map<String, Object> params = new TreeMap<String, Object>();
            //小程序APPID
            params.put("app_id",appid);
            //开发者侧的订单号。需保证同一小程序下不可重复
            params.put("out_order_no", outTradeNo);
            //支付价格。单位为[分]，取值范围：[1,10000000000]  100元 = 100*100 分
            params.put("total_amount", (int) price);
            //商品描述。
            params.put("subject", subject);
            //商品详情
            params.put("body", subjectBody);
            //订单过期时间(秒) 5min-2day
            params.put("valid_time", 1800);
            //通知地址
            params.put("notify_url", notifyUrl);
            //签名，详见https://microapp.bytedance.com/docs/zh-CN/mini-app/develop/server/ecpay/TE
            String sign = TTPayUtil.getSign(params);
            params.put("sign", sign);

            //保存订单数据
            miniApiWxpayLog.setAppid(appid);
            miniApiWxpayLog.setOutTradeNo(outTradeNo);
            miniApiWxpayLog.setTotalAmount(price + "");
            miniApiWxpayLog.setSubject(subject);
            miniApiWxpayLog.setBody(subjectBody);
            miniApiWxpayLog.setSign(sign);
            miniApiWxpayLog.setMobile(mobile);
            miniApiWxpayLog.setCouponId(couponId);
            miniApiWxpayLog.setAccount(account);

            //预下单接口
            try {
                okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(params));
                HttpUrl httpUrl = HttpUrl.parse(ttAppletPayUrl)
                        .newBuilder()
                        .build();
                Request request = new Request.Builder().url(httpUrl)
                        .post(body)
                        .build();
                log.info("抖音小程序微信支付请求:{},请求参数:{}", request.toString(),params);
                try (Response response = client.newCall(request)
                        .execute()) {
                    int code = response.code();
                    if (code != 200) {
                        log.info("抖音小程序微信支付报错:", response);
                        throw new IOException("Unexpected code " + response);
                    }
                    String result = response.body().string();
                    log.info("抖音小程序微信支付响应:" + result);
                    if (!"".equals(result)) {
                        JSONObject jsonObject = JSONObject.parseObject(result);
                        String err_no = jsonObject.getString("err_no");
                        if (null != err_no && "0".equals(err_no)) {
                            JSONObject data = jsonObject.getJSONObject("data");
                            String orderId = data.getString("order_id");
                            String orderToken = data.getString("order_token");
                            miniApiWxpayLog.setOrderId(orderId);
                            miniApiWxpayLog.setOrderToken(orderToken);
                            if (null != orderId && null != orderToken) {
                                //前端使用此处返回的data来调起付款收银台
                                returnJson.put("pay_json",data);
                            } else {
                                returnJson.put("error_info","支付参数为空");
                            }
                        } else {
                            returnJson.put("error_info","参数错误[" + err_no + "]");
                        }
                    } else {
                        returnJson.put("error_info","支付订单创建失败");
                    }
                }
            } catch (Exception e) {
                log.info("抖音小程序微信支付报错:", e);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("抖音小程序微信支付异常：{}", e);
            returnJson.put("error_info","抖音小程序微信支付异常");
        }
        miniApiWxpayLogService.save(miniApiWxpayLog);
        return returnJson ;
    }
    /**
     * 抖音小程序担保支付回调
     *  抖音小程序担保付回调地址:
     * https://crbt.cdyrjygs.com/cms-vrbt/miniApi/payment/callback
     * 抖音小程序担保付token:
     * 3r17nt7v9tzmq331
     * @return
     */
    @RequestMapping("/payment/callback")
    @ResponseBody
    public String paymentCallback(/*@RequestParam Map<String,String> allRequestParams,*//*HttpEntity<String> httpEntity, */HttpServletRequest request) {
        //log.info("抖音小程序担保支付回调,参数:{}", allRequestParams);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String raw = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("抖音小程序担保支付回调,raw:{}", raw);
            Pair<Boolean,DypayNotifyMsg> result =  miniApiWxpayLogService.parsePaymentCallback(raw);

            if(result.getFirst()){
                //充值并记录
                final DypayNotifyMsg notifyMsg = result.getSecond();
                miniApiWxpayLogService.douyinRightsRecharge(notifyMsg);
            }else {
                //如果验签失败,就打告警日志
                log.error("抖音小程序担保支付回调,验签失败,raw:{}", raw);
            }

            return "{\"err_no\": 0, \"err_tips\": \"success\"}";

        } catch (Exception e) {
            log.warn("抖音小程序担保支付回调异常", e);
            return "{\"err_no\": 999, \"err_tips\": \"success\"}";
        }
    }



    public static void main(String[] args) throws JsonProcessingException {
        String payCallbackData = "{\"msg\":\"{\\\"appid\\\":\\\"tt0323759e9c1da7ed01\\\",\\\"cp_orderno\\\":\\\"4028823f80f4ec9d0180f9d969900024\\\",\\\"cp_extra\\\":\\\"\\\",\\\"way\\\":\\\"1\\\",\\\"channel_no\\\":\\\"4351101096202205258333691623\\\",\\\"channel_gateway_no\\\":\\\"\\\",\\\"payment_order_no\\\":\\\"PCP2022052514133439008384033031\\\",\\\"out_channel_order_no\\\":\\\"4351101096202205258333691623\\\",\\\"total_amount\\\":1,\\\"status\\\":\\\"SUCCESS\\\",\\\"seller_uid\\\":\\\"70989217136054458960\\\",\\\"extra\\\":\\\"\\\",\\\"item_id\\\":\\\"\\\",\\\"paid_at\\\":1653459225,\\\"message\\\":\\\"\\\",\\\"order_id\\\":\\\"7101553088682543375\\\"}\",\"msg_signature\":\"24e6d975690ee09eeebb2fed357899bad31df8f5\",\"nonce\":\"3051\",\"timestamp\":\"1653459225\",\"type\":\"payment\"}";
        final ObjectMapper om = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        final JsonNode tree = om.readTree(payCallbackData);
        String  msgSignature = tree.at("/msg_signature").asText();
        String msg = tree.at("/msg").textValue();
        System.out.println("msg = " + msg);
        final DypayNotifyMsg douyinPayNotify = om.readValue(msg, DypayNotifyMsg.class);
        System.out.println("douyinPayNotify = " + douyinPayNotify);
        System.out.println("msgSignature = " + msgSignature);
        String token = "3r17nt7v9tzmq331";
        List<String> sortedString = new ArrayList<>();
        sortedString.add(token);
        tree.fields().forEachRemaining(entry -> {
            String feildName = entry.getKey();
            if(!"msg_signature".equals(feildName) && !"type".equals(feildName)){
                String feildValue = entry.getValue().textValue();
                sortedString.add(feildValue);
            }
        });
        final String concat = sortedString.stream().sorted().collect(Collectors.joining(""));
        System.out.println("concat = " + concat);
        final String sign = DigestUtils.sha1Hex(concat.getBytes(StandardCharsets.UTF_8));
        System.out.println("sign = " + sign);

    }

}
