package com.eleven.cms.controller;

import com.eleven.cms.dto.BusinessChargeDto;
import com.eleven.cms.dto.RightsDto;
import com.eleven.cms.service.IBusinessChargeService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.util.DateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.List;

@Api(tags="业务充值统计")
@RestController
@RequestMapping("/cms/junboChargeLog")
@Slf4j
public class BusinessChargeController extends JeecgController<BusinessChargeDto, IBusinessChargeService> {
    public static ObjectMapper mapper = new ObjectMapper();
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    /**
     * 业务充值统计
     * @param dto
     * @return
     */
    @ApiOperation(value="业务充值统计-分页列表查询", notes="业务充值统计-分页列表查询")
    @GetMapping(value = "/businessList")
    public Result<List<BusinessChargeDto>> queryPageBusinessList(BusinessChargeDto dto) {
        Result<List<BusinessChargeDto>> result = new Result<>();
        try {
            if(StringUtils.isNotBlank(dto.getCreateTime_begin())){
                dto.setCreateTimeBegin(DateUtil.stringToDate(DateUtil.handleSqlDateStartTime(dto.getCreateTime_begin())));
            }else{
                dto.setCreateTimeBegin(DateUtil.localDateTimeToDate(DateUtil.dayMinTime(LocalDate.now())));
            }
            if(StringUtils.isNotBlank(dto.getCreateTime_end())){
                dto.setCreateTimeEnd(DateUtil.stringToDate(DateUtil.handleSqlDateEndTime(dto.getCreateTime_end())));
            }else{
                dto.setCreateTimeEnd(DateUtil.localDateTimeToDate(DateUtil.getLastDayOfMonthWithMaxTime()));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<BusinessChargeDto> pageList = junboChargeLogService.pageBusinessList(dto);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 导出excel
     * @param dto
     * @return
     */
    @RequestMapping(value = "/downXlsx")
    public ModelAndView downXlsx(BusinessChargeDto dto) {
        try {
            if(StringUtils.isNotBlank(dto.getCreateTime_begin())){
                dto.setCreateTimeBegin(DateUtil.stringToDate(DateUtil.handleSqlDateStartTime(dto.getCreateTime_begin())));
            }else{
                dto.setCreateTimeBegin(DateUtil.localDateTimeToDate(DateUtil.dayMinTime(LocalDate.now())));
            }
            if(StringUtils.isNotBlank(dto.getCreateTime_end())){
                dto.setCreateTimeEnd(DateUtil.stringToDate(DateUtil.handleSqlDateEndTime(dto.getCreateTime_end())));
            }else{
                dto.setCreateTimeEnd(DateUtil.localDateTimeToDate(DateUtil.getLastDayOfMonthWithMaxTime()));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<BusinessChargeDto> pageList = junboChargeLogService.pageBusinessList(dto);
        return super.downXlsx(BusinessChargeDto.class, "业务充值统计",pageList);

    }

    /**
     * 业务列表查询
     * @return
     */
    @RequestMapping(value = "/getServiceList", method = RequestMethod.GET)
    public Result<List<RightsDto>> queryServiceList() {
        Result<List<RightsDto>> result = new Result<>();
        List<RightsDto> list = junboChargeLogService.queryServiceList();
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }
    /**
     * 业务关联权益查询
     * @param dto
     * @return
     */
    @RequestMapping(value = "/queryRightsList", method = RequestMethod.GET)
    public Result<List<RightsDto>> queryRightsList(RightsDto dto) {
        Result<List<RightsDto>> result = new Result<>();
        List<RightsDto> list = junboChargeLogService.queryRightsList(dto);
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }
//    /**
//     * 业务列表查询
//     * @return
//     */
//    @RequestMapping(value = "/getServiceListVrbt", method = RequestMethod.GET)
//    public Result<List<RightsDto>> getServiceListVrbt() {
//        Result<List<RightsDto>> result = new Result<>();
//        List<RightsDto> list = junboChargeLogService.queryServiceListVrbt();
//        result.setResult(list);
//        result.setSuccess(true);
//        return result;
//    }
//
//    /**
//     * 业务关联权益查询
//     * @param dto
//     * @return
//     */
//    @RequestMapping(value = "/queryRightsListVrbt", method = RequestMethod.GET)
//    public Result<List<RightsDto>> queryRightsListVrbt(RightsDto dto) {
//        Result<List<RightsDto>> result = new Result<>();
//        List<RightsDto> list = junboChargeLogService.getRightsListVrbt(dto);
//        result.setResult(list);
//        result.setSuccess(true);
//        return result;
//    }
}
