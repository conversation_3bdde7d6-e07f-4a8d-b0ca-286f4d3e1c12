package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.LiantongRing;
import com.eleven.cms.service.ILiantongRingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 联通彩铃diy
 * @Author: jeecg-boot
 * @Date:   2023-08-22
 * @Version: V1.0
 */
@Api(tags="联通彩铃diy")
@RestController
@RequestMapping("/cms/liantongRing")
@Slf4j
public class LiantongRingController extends JeecgController<LiantongRing, ILiantongRingService> {
	@Autowired
	private ILiantongRingService liantongRingService;

	/**
	 * 分页列表查询
	 *
	 * @param liantongRing
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "联通彩铃diy-分页列表查询")
	@ApiOperation(value="联通彩铃diy-分页列表查询", notes="联通彩铃diy-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(LiantongRing liantongRing,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<LiantongRing> queryWrapper = QueryGenerator.initQueryWrapper(liantongRing, req.getParameterMap());
		Page<LiantongRing> page = new Page<LiantongRing>(pageNo, pageSize);
		IPage<LiantongRing> pageList = liantongRingService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param liantongRing
	 * @return
	 */
	//@AutoLog(value = "联通彩铃diy-添加")
	@ApiOperation(value="联通彩铃diy-添加", notes="联通彩铃diy-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody LiantongRing liantongRing) {
		liantongRingService.save(liantongRing);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param liantongRing
	 * @return
	 */
	//@AutoLog(value = "联通彩铃diy-编辑")
	@ApiOperation(value="联通彩铃diy-编辑", notes="联通彩铃diy-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody LiantongRing liantongRing) {
		liantongRingService.updateById(liantongRing);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "联通彩铃diy-通过id删除")
	@ApiOperation(value="联通彩铃diy-通过id删除", notes="联通彩铃diy-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		liantongRingService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "联通彩铃diy-批量删除")
	@ApiOperation(value="联通彩铃diy-批量删除", notes="联通彩铃diy-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.liantongRingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "联通彩铃diy-通过id查询")
	@ApiOperation(value="联通彩铃diy-通过id查询", notes="联通彩铃diy-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		LiantongRing liantongRing = liantongRingService.getById(id);
		if(liantongRing==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(liantongRing);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param liantongRing
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LiantongRing liantongRing) {
        return super.exportXls(request, liantongRing, LiantongRing.class, "联通彩铃diy");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LiantongRing.class);
    }

}
