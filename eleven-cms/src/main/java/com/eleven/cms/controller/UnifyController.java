package com.eleven.cms.controller;

import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.ISmsValidateService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.TokenUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.Product;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Strings;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.UnifyWeChatLogin;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 统一领取权益功能
 */
@Api(tags = "统一领取权益功能")
@RestController
@RequestMapping("/api/unify")
@Slf4j
@Validated
public class UnifyController {
    public static ObjectMapper mapper = new ObjectMapper();
    @Autowired
    private IMemberService memberService;
    @Autowired
    private ISmsValidateService smsValidateService;

    @Autowired
    private RedisUtil redisUtil;
    private static final Interner<String> interner = Interners.newWeakInterner();
    /**
     * 会员登录
     * @param mobile
     * @param captcha
     * @return
     */
    @ApiOperation(value = "公众号会员登录", notes = "公众号会员登录")
    @PostMapping(value = "/member/login")
    @ResponseBody
    public FebsResponse memberLogin(@RequestParam("mobile") String mobile,
                                    @RequestParam("captcha")String captcha,
                                    @RequestParam(name = "dev", required = false, defaultValue = "") String dev,
                                    @RequestParam(name = "channelCode", required = false, defaultValue = BizConstant.BIZ_QYLI_ALL_CHANNEL_CODE) String channelCode,
                                    @RequestParam(name = "serviceId", required = false, defaultValue = "") String serviceId,HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("公众号会员登录=>mobile:{},captcha:{},referer:{}",mobile,captcha,referer);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return new FebsResponse().fail().message("验证码错误");
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            return new FebsResponse().fail().message(e.getMessage());
        }
        if(StringUtils.isNotEmpty(dev)){
            return memberService.unifyTestLogin(mobile,channelCode);
        }else{
            return memberService.unifyLogin(mobile,channelCode,serviceId);
        }
    }

    /**
     * 查询是否会员
     * @return
     */
    @ApiOperation(value = "是否订购会员", notes = "是否订购会员")
    @PostMapping(value = "/check/member")
    @ResponseBody
    @UnifyWeChatLogin(menu = "是否订购会员")
    public FebsResponse checkMember(@RequestParam(name = "channelCode", required = false, defaultValue = BizConstant.BIZ_QYLI_ALL_CHANNEL_CODE) String channelCode,HttpServletRequest request) {
        ObjectNode objectNode=getToken(request);
        String spcltoken = objectNode.get("spcltoken").asText();
        String bdwpToken = objectNode.get("bdwpToken").asText();
        String unifyToken=request.getHeader("token");
        String mobile =(String) redisUtil.get(spcltoken);
        String referer = request.getHeader("Referer");
        log.info("是否订购会员=>mobile:{},referer:{}",mobile,referer);
        return memberService.unifyIsMember(unifyToken,spcltoken,bdwpToken,channelCode);
    }

    /**
     *
     * 查询权益列表
     * @param serviceId
     * @return
     */
    @ApiOperation(value = "查询权益列表", notes = "查询权益列表")
    @PostMapping(value = "/query/rights/list")
    @ResponseBody
    @UnifyWeChatLogin(menu = "查询权益列表")
    public FebsResponse queryRightsList(@RequestParam("serviceId") String serviceId,HttpServletRequest request) {
        ObjectNode objectNode=getToken(request);
        String spcltoken = objectNode.get("spcltoken").asText();
        String bdwpToken = objectNode.get("bdwpToken").asText();
        String mobile =(String) redisUtil.get(spcltoken);
        String referer = request.getHeader("Referer");
        log.info("查询权益列表=>mobile:{},serviceId:{},referer:{}",mobile,serviceId,referer);
        return memberService.unifyQueryRightsList(spcltoken,bdwpToken,serviceId);
    }
    /**
     * 会员领取权益
     * @param serviceId
     * @param rightsId
     * @param packName
     * @param request
     * @return
     */
    @ApiOperation(value = "会员领取权益", notes = "会员领取权益")
    @PostMapping(value = "/member/receive/rights")
    @ResponseBody
    @UnifyWeChatLogin(menu = "会员领取权益")
    public FebsResponse memberReceiveRights(@RequestParam("serviceId") String serviceId, @RequestParam("rightsId") String rightsId,
                                            @RequestParam("packName") String packName,@RequestParam(value = "account", required = false, defaultValue = "")String account, @RequestParam(name = "dev", required = false, defaultValue = "") String dev, HttpServletRequest request) {
        ObjectNode objectNode=getToken(request);
        String spcltoken = objectNode.get("spcltoken").asText();
        String bdwpToken = objectNode.get("bdwpToken").asText();
        String mobile =(String) redisUtil.get(spcltoken);
        String referer = request.getHeader("Referer");
        log.info("会员领取权益(公众号)=>mobile:{},account:{},serviceId:{},packName:{},rightsId:{},referer:{}",mobile,account,serviceId,packName,rightsId,referer);
        synchronized (interner.intern(mobile)) {
            if(StringUtils.isNotEmpty(dev)){
                return memberService.unifyMemberReceiveRightsTest(spcltoken,bdwpToken,serviceId, rightsId, packName,account);
            }else{
                return memberService.unifyMemberReceiveRights(spcltoken,bdwpToken,serviceId, rightsId, packName,account);
            }
        }
    }

    /**
     * 查询充值列表
     * @param request
     * @return
     */
    @ApiOperation(value = "公众号查询充值列表", notes = "公众号查询充值列表")
    @PostMapping(value = "/query/charge/list")
    @ResponseBody
    @UnifyWeChatLogin(menu = "公众号查询充值列表")
    public FebsResponse queryChargeList(@RequestParam(value = "serviceId", required = false, defaultValue = "") String serviceId,
                                        @RequestParam(value = "rightsMonth", required = false, defaultValue = "")String rightsMonth,@RequestParam(value = "sourse", required = false, defaultValue =BizConstant.SOURSE_PUBLIC)String sourse,HttpServletRequest request) {
        ObjectNode objectNode=getToken(request);
        String spcltoken = objectNode.get("spcltoken").asText();
        String bdwpToken = objectNode.get("bdwpToken").asText();
        String mobile =(String) redisUtil.get(spcltoken);
        String referer = request.getHeader("Referer");
        log.info("查询充值列表(公众号)=>mobile:{},serviceId:{},rightsMonth:{},sourse:{},referer:{}",mobile,serviceId,rightsMonth,sourse,referer);
        return memberService.unifyQueryChargeList(spcltoken,bdwpToken,serviceId,rightsMonth,sourse);
    }


    private ObjectNode getToken(HttpServletRequest request) {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode objectNode= null;
        try {
            objectNode = objectMapper.readValue(redisUtil.get(request.getHeader("token")).toString(), ObjectNode.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return objectNode;
    }

    /**
     * 领取百度网盘权益
     * @return
     */
    @ApiOperation(value = "公众号领取百度网盘", notes = "公众号领取百度网盘")
    @PostMapping(value = "/receive/bdwp")
    @ResponseBody
    @UnifyWeChatLogin(menu = "公众号领取百度网盘")
    public FebsResponse receiveBdwp(HttpServletRequest request) {
        ObjectNode objectNode=getToken(request);
        String bdwpToken = objectNode.get("bdwpToken").asText();
        String spcltoken = objectNode.get("spcltoken").asText();
        String mobile =(String) redisUtil.get(spcltoken);
        String referer = request.getHeader("Referer");
        log.info("领取百度网盘(公众号)=>mobile:{},referer:{}",mobile,referer);
        return memberService.unifyReceiveBdwp(bdwpToken);
    }


    /**
     * 发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getCaptcha")
    @ResponseBody
    public Result<?> getCaptcha(@RequestParam("mobile") String mobile,
                                @RequestParam(name = "channelCode", required = false, defaultValue = BizConstant.BIZ_QYLI_ALL_CHANNEL_CODE) String channelCode,
                                @RequestParam(name = "serviceId", required = false, defaultValue ="") String serviceId,HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("发送短信验证码=>mobile:{},referer:{}",mobile,referer);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }

        boolean result = smsValidateService.createUnify(mobile,channelCode,serviceId);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }
        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }


    /**
     * 查询产品和产品订阅列表
     * @param request
     * @return
     */
    @ApiOperation(value = "查询产品和产品订阅列表", notes = "查询产品和产品订阅列表")
    @PostMapping(value = "/query/product/subscribe/list")
    @ResponseBody
    @UnifyWeChatLogin(menu = "查询产品和产品订阅列表")
    public FebsResponse queryProductAndSubscribe(HttpServletRequest request) {
        ObjectNode objectNode=getToken(request);
        String spcltoken = objectNode.get("spcltoken").asText();
        String bdwpToken = objectNode.get("bdwpToken").asText();

        String mobile =(String) redisUtil.get(spcltoken);
        //是否开启抖音小程序测试开关 0 关闭 1 开启
        Object switchs=redisUtil.get(CacheConstant.RIGHT_DOUYIN_TEST);
        String referer = request.getHeader("Referer");
        log.info("查询产品和产品订阅列表=>mobile:{},switchs:{},referer:{}",mobile,switchs,referer);
        if(switchs != null && StringUtils.equals(switchs.toString(),BizConstant.DOUYIN_TEST_SWITCHS_OPEN)){
            String json="[{\"isReceiveRights\":0,\"receiveRightsMsg\":\"会员权益已领取\",\"titleName\":\"炫酷来电\",\"productImg\":\"https://static.cdyrjygs.com/images/member/kxld.png\",\"serviceId\":\"698039035100000014\"},{\"isReceiveRights\":1,\"receiveRightsMsg\":\"会员权益已领取\",\"titleName\":\"白金会员\",\"productImg\":\"https://static.cdyrjygs.com/images/member/yysxt.png\",\"serviceId\":\"698039020105522717\"}]";
            try{
                List<Product> list= mapper.readValue(json, List.class);
                return new FebsResponse().success().data(list);
            }catch (JsonProcessingException e) {
                log.error("查询产品和产品订阅列表,Json异常信息=>mobile:{},msg:{}",mobile,e.getMessage(),e);
            }
        }
        return memberService.unifyQueryProductAndSubscribe(spcltoken,bdwpToken);
    }
    /**
     * 抖音会员登录
     * @param mobile
     * @param captcha
     * @return
     */
    @ApiOperation(value = "抖音登录视听会员", notes = "抖音登录视听会员")
    @PostMapping(value = "/douyin/member/login")
    @ResponseBody
    public FebsResponse douyinMemberLogin(@RequestParam("mobile") String mobile, @RequestParam("captcha")String captcha,
                                          @RequestParam(name = "channelCode", required = false, defaultValue = BizConstant.BIZ_QYLI_ALL_CHANNEL_CODE) String channelCode,HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("登录视听会员(抖音)=>mobile:{},captcha:{},referer:{}",mobile,captcha,referer);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        //测试手机，不校验验证码
        if(StringUtils.equalsAny("18482155682",mobile)){
            return memberService.douyinMemberLogin(mobile,channelCode);
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return new FebsResponse().fail().message("验证码错误");
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            return new FebsResponse().fail().message(e.getMessage());
        }
        return memberService.douyinMemberLogin(mobile,channelCode);
    }

    /**
     * 业务缓存开关 0 关闭 1 开启
     * @param switchs
     * @return
     */
    @ApiOperation(value = "业务缓存开关", notes = "业务缓存开关")
    @PostMapping(value = "/douyin/test/switchs")
    @ResponseBody
    public FebsResponse businessCacheSwitchs(@RequestParam(name = "key", required = false, defaultValue =CacheConstant.RIGHT_DOUYIN_TEST) String key,
                                             @RequestParam(name = "switchs", required = false, defaultValue =BizConstant.DOUYIN_TEST_SWITCHS_CLOSE) String switchs,HttpServletRequest request) {
        redisUtil.set(key,switchs,TokenUtil.USER_LOGIN_TIME);
        String referer = request.getHeader("Referer");
        log.info("业务缓存开关=>key:{},switchs:{},referer:{}",key,switchs,referer);
        return new FebsResponse().success();
    }
    /**
     * 抖音小程序会员领取权益
     * @param serviceId
     * @param rightsId
     * @param packName
     * @param request
     * @return
     */
    @ApiOperation(value = "抖音领取视听会员", notes = "抖音领取视听会员")
    @PostMapping(value = "/douyin/member/receive/rights")
    @ResponseBody
    @UnifyWeChatLogin(menu = "抖音领取视听会员")
    public FebsResponse douyinMemberReceiveRights(@RequestParam("serviceId") String serviceId,
                                                  @RequestParam("rightsId") String rightsId, @RequestParam("packName") String packName,
                                                  @RequestParam(value = "account", required = false, defaultValue = "")String account, HttpServletRequest request) {
        Object switchs=redisUtil.get(CacheConstant.RIGHT_DOUYIN_TEST);
        if(switchs != null && StringUtils.equals(switchs.toString(),BizConstant.DOUYIN_TEST_SWITCHS_OPEN)){
            return new FebsResponse().success("领取成功！");
        }
        ObjectNode objectNode=getToken(request);
        String spcltoken = objectNode.get("spcltoken").asText();
        String bdwpToken = objectNode.get("bdwpToken").asText();
        String mobile =(String) redisUtil.get(spcltoken);
        String referer = request.getHeader("Referer");
        log.info("领取视听会员(抖音)=>mobile:{},account:{},serviceId:{},packName:{},rightsId:{},referer:{}",mobile,account,serviceId,packName,rightsId,referer);
        synchronized (interner.intern(mobile)) {
            return memberService.unifyMemberReceiveRights(spcltoken,bdwpToken,serviceId, rightsId, packName,account);
        }
    }
    /**
     * 抖音小程序领取百度网盘权益
     * @return
     */
    @ApiOperation(value = "抖音领取百度网盘", notes = "抖音领取百度网盘")
    @PostMapping(value = "/douyin/receive/bdwp")
    @ResponseBody
    @UnifyWeChatLogin(menu = "抖音领取百度网盘")
    public FebsResponse douyinReceiveBdwp(HttpServletRequest request) {
        Object switchs=redisUtil.get(CacheConstant.RIGHT_DOUYIN_TEST);
        if(switchs != null && StringUtils.equals(switchs.toString(),BizConstant.DOUYIN_TEST_SWITCHS_OPEN)){
            log.info("领取百度网盘(抖音),是否开启抖音小程序测试开关=>switchs:{}",switchs);
            return new FebsResponse().success("领取成功！");
        }
        ObjectNode objectNode=getToken(request);
        String spcltoken = objectNode.get("spcltoken").asText();
        String bdwpToken = objectNode.get("bdwpToken").asText();
        String mobile =(String) redisUtil.get(spcltoken);
        String referer = request.getHeader("Referer");
        log.info("领取百度网盘(抖音)=>mobile:{},referer:{}",mobile,referer);
        return memberService.unifyReceiveBdwp(bdwpToken);
    }
    /**
     * 抖音小程序查询充值列表
     * @param request
     * @return
     */
    @ApiOperation(value = "抖音查询充值列表", notes = "抖音查询充值列表")
    @PostMapping(value = "/douyin/query/charge/list")
    @ResponseBody
    @UnifyWeChatLogin(menu = "抖音查询充值列表")
    public FebsResponse douyinqueryChargeList(@RequestParam(value = "serviceId", required = false, defaultValue = "") String serviceId,@RequestParam(value = "rightsMonth", required = false, defaultValue = "")String rightsMonth,@RequestParam(value = "sourse", required = false, defaultValue =BizConstant.SOURSE_PUBLIC)String sourse,HttpServletRequest request) {
        ObjectNode objectNode=getToken(request);
        String spcltoken = objectNode.get("spcltoken").asText();
        String bdwpToken = objectNode.get("bdwpToken").asText();
        String mobile =(String) redisUtil.get(spcltoken);
        String referer = request.getHeader("Referer");
        log.info("查询充值列表(抖音)=>mobile:{},serviceId:{},rightsMonth:{},sourse:{},referer:{}",mobile,serviceId,rightsMonth,sourse,referer);
        //是否开启抖音小程序测试开关 0 关闭 1 开启
        Object switchs=redisUtil.get(CacheConstant.RIGHT_DOUYIN_TEST);
        if(switchs != null && StringUtils.equals(switchs.toString(),BizConstant.DOUYIN_TEST_SWITCHS_OPEN)){
            String json="[{\"account\": \"***********\",\"mobile\": \"***********\",\"rightsMonth\": \"202106\",\"status\": 2,\"couponName\": \"腾讯视频周卡\",\"createTime\": \"2021-06-15 15:11:41\"},{\"account\": \"***********\",\"mobile\": \"***********\",\"rightsMonth\": \"202109\",\"status\": 1,\"couponName\": \"美团5元代金券\",\"createTime\": \"2021-09-15 10:20:00\"}]";
            try{
                List<JunboChargeLog> list= mapper.readValue(json, List.class);
                return new FebsResponse().success().data(list);
            }catch (JsonProcessingException e) {
                log.error("查询充值列表(抖音),Json异常信息=>mobile:{},msg:{}",mobile,e.getMessage(),e);
            }
        }
        return memberService.unifyQueryChargeList(spcltoken,bdwpToken,serviceId,rightsMonth,sourse);
    }


    /**
     * 是否开启pptv权益短信发送开关 0 关闭 1 开启
     * @param switchs
     * @return
     */
    @ApiOperation(value = "是否开启pptv权益短信发送开关", notes = "是否开启pptv权益短信发送开关")
    @PostMapping(value = "/pptv/test/switchs")
    @ResponseBody
    public FebsResponse pptvTestSwitchs(@RequestParam(name = "switchs", required = false, defaultValue =BizConstant.DOUYIN_TEST_SWITCHS_CLOSE) String switchs,
                                        @RequestParam(name = "channelCode", required = false, defaultValue ="") String channelCode,HttpServletRequest request) {
        redisUtil.set(CacheConstant.RIGHT_PPTV_TEST+":"+channelCode,switchs, TokenUtil.USER_LOGIN_TIME);
        String referer = request.getHeader("Referer");
        log.info("是否开启pptv权益短信发送开关=>switchs:{},referer:{}",switchs,referer);
        return new FebsResponse().success();
    }

}
