package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.ShopProductRights;
import com.eleven.cms.service.IShopProductRightsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 商城产品价格关联
 * @Author: jeecg-boot
 * @Date:   2023-09-19
 * @Version: V1.0
 */
@Api(tags="商城产品价格关联")
@RestController
@RequestMapping("/cms/shopProductRights")
@Slf4j
public class ShopProductRightsController extends JeecgController<ShopProductRights, IShopProductRightsService> {
	@Autowired
	private IShopProductRightsService shopProductRightsService;

	/**
	 * 分页列表查询
	 *
	 * @param shopProductRights
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "商城产品价格关联-分页列表查询")
	@ApiOperation(value="商城产品价格关联-分页列表查询", notes="商城产品价格关联-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ShopProductRights shopProductRights,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ShopProductRights> queryWrapper = QueryGenerator.initQueryWrapper(shopProductRights, req.getParameterMap());
		Page<ShopProductRights> page = new Page<ShopProductRights>(pageNo, pageSize);
		IPage<ShopProductRights> pageList = shopProductRightsService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param shopProductRights
	 * @return
	 */
	//@AutoLog(value = "商城产品价格关联-添加")
	@ApiOperation(value="商城产品价格关联-添加", notes="商城产品价格关联-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ShopProductRights shopProductRights) {
		shopProductRightsService.save(shopProductRights);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param shopProductRights
	 * @return
	 */
	//@AutoLog(value = "商城产品价格关联-编辑")
	@ApiOperation(value="商城产品价格关联-编辑", notes="商城产品价格关联-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ShopProductRights shopProductRights) {
		shopProductRightsService.updateById(shopProductRights);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "商城产品价格关联-通过id删除")
	@ApiOperation(value="商城产品价格关联-通过id删除", notes="商城产品价格关联-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		shopProductRightsService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "商城产品价格关联-批量删除")
	@ApiOperation(value="商城产品价格关联-批量删除", notes="商城产品价格关联-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.shopProductRightsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "商城产品价格关联-通过id查询")
	@ApiOperation(value="商城产品价格关联-通过id查询", notes="商城产品价格关联-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ShopProductRights shopProductRights = shopProductRightsService.getById(id);
		if(shopProductRights==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(shopProductRights);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param shopProductRights
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ShopProductRights shopProductRights) {
        return super.exportXls(request, shopProductRights, ShopProductRights.class, "商城产品价格关联");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ShopProductRights.class);
    }

}
