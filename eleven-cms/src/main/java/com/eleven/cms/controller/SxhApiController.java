package com.eleven.cms.controller;

import com.eleven.cms.entity.YinglouRing;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IYinglouRingService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Date;
import java.util.Map;


/**
 *
 */
@Api(tags = "sxh_api")
@RestController
@RequestMapping("/api")
@Slf4j
public class SxhApiController {
    //private static final Interner<String> interner = Interners.newWeakInterner();

    @Autowired
    MiguApiService miguApiService;

    @Autowired
    private IYinglouRingService yinglouRingService;
    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }


    /**
     * 视宣号查询商户ID
     *
     * @param mobile
     *         手机号
     * @param channelCode
     *         渠道号
     * @return
     */
    @ApiOperation(value = "视宣号查询商户ID", notes = "视宣号查询商户ID")
    @PostMapping(value = "/sxhGetCircleId")
    public RemoteResult sxhGetCircleId(String mobile, String channelCode) {

        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }
        return miguApiService.sxhGetCircleId(mobile, channelCode);
    }

    /**
     * 视宣号铃音设置
     *
     * @param mobile
     *         手机号
     * @param channelCode
     *         渠道号
     * @param ringId
     *         视宣号铃音id
     * @return
     */
    @ApiOperation(value = "视宣号铃音设置", notes = "视宣号铃音设置")
    @PostMapping(value = "/sxhRingSet")
    public RemoteResult sxhRingSet(String mobile, String channelCode, String ringId) {

        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }
        return miguApiService.sxhRingSet(mobile, channelCode, ringId);
    }

    /**
     * 视宣号铃音上传
     *
     * @param mobile
     *         手机号
     * @param circleId
     *         商户id
     * @param channelCode
     *         渠道号
     * @param file
     *         铃音文件
     * @return
     */
    @ApiOperation(value = "视宣号铃音上传", notes = "视宣号铃音上传")
    @PostMapping(value = "/sxhRingUpload")
    public RemoteResult sxhRingUpload(String mobile, String circleId, String channelCode, MultipartFile file) throws IOException {

        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }
        if (file.isEmpty()) {
            return RemoteResult.fail("请上传文件");
        }
        String filename = file.getOriginalFilename();
        final String extension = FilenameUtils.getExtension(filename);
        //验证文件扩展名
        if (StringUtils.isEmpty(filename) || !StringUtils.equalsAnyIgnoreCase(extension, "mp4","avi","mov","wmv","mpeg","mpg","vob")) {
            return RemoteResult.fail("上传的文件格式错误,只支持视频上传");
        }

        final double fileSizeMB = file.getSize() * 0.00000095367432;
        if(fileSizeMB > 80){
            return RemoteResult.fail("文件大小不能超过80M");
        }

        return miguApiService.sxhRingUpload(mobile, circleId, channelCode,
                OkHttpClientUtils.createInputStreamRequestBody(file.getInputStream()));
    }


    /**
     * 2.3.1.4.3 融媒业务咪咕三方支付订购结果通知接口
     * 接口描述
     * 实现通知合作方融媒业务咪咕三方支付订购结果的服务
     * 字段 必选 类型 说明
     * resCode true String 结果码，000000代表成功
     * resMsg true String 结果描述
     * sign true String 签名
     * transactionId true String 交易流水号，唯一
     * status true String 支付结果码， 0：支付成功 1：支付失败 2：等待支付（异步支付待确认） 3：还款成功但额度恢复失败 4：还款成功但支付失败 01：支付成功但彩铃包月订购失败
     * statusInfo true String 支付结果描述
     * realPayAmount false String 实付金额，单位：分
     * payDate false String 支付时间，格式：YYYYMMDDHHMMSS
     * extendAttr false String 合作伙伴透传字段
     * payWay false String 支付途径： 1、一网通 2、签约支付 3、和包 4、支付宝 5、财付通 6、微信 7、对公支付 9、网银支付 10、银联信用 11、咪咕币 12、话费支付 13、充值卡 14、扫描支付 15、条码支付 16、苹果支付
     *
     */
    //@ApiOperation(value = "视宣号三方支付订购结果回执", notes = "视宣号三方支付订购结果回执")
    //@RequestMapping(value = "/sxh/notify/payOrder")
    //public String miguNotifyringUploadResult(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
    //    try {
    //        log.info("视宣号三方支付订购结果回执,参数map:{},json数据:{}", requestMap, jsonNode);
    //        //qyclRingService.submitRingNotify(jsonNode);
    //    } catch (Exception e) {
    //        log.info("视宣号三方支付订购结果回执,参数map:{},json数据:{},异常!", requestMap, jsonNode, e);
    //        //return "FAIL";
    //    }
    //
    //    return "OK";
    //}

    /**
     * *******.11 视宣号成员状态回执
     * 接口描述：该接口由接入平台实现，用于接收视宣号平台的成员状态回执；视宣号平台将成员订购或者退订的最终状态回执给接入平台。回执地址需开通IP白名单。
     *
     * 字段名 类型 是否必填 字段含义
     * circleId String 必填 商户ID
     * memberMobile String 必填 成员号码
     * status String 必填 状态 01订购成功    02退订成功 03订购失败 04退订失败
     * remark String 必填 描述
     *
     */
    @ApiOperation(value = "视宣号成员状态回执", notes = "视宣号成员状态回执")
    @RequestMapping(value = "/sxh/notify/member")
    public String sxhNotifyringUpload(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            log.info("视宣号成员状态回执,参数map:{},json数据:{}", requestMap, jsonNode);
            //qyclRingService.submitRingNotify(jsonNode);
        } catch (Exception e) {
            log.info("视宣号成员状态回执,参数map:{},json数据:{},异常!", requestMap, jsonNode, e);
            //return "FAIL";
        }

        return "OK";
    }

    /**
     * *******.12 视宣号铃音回执
     * 接口描述：该接口用于铃音激活状态的回执，若激活成功则可进行设置，若激活失败则不可进行设置。回执地址需开通IP白名单。
     * 字段名 类型 是否必须 字段含义
     * transactionId String 是 铃音标识
     * ringId String 否 铃音ID
     * ringName String 是 铃音名称
     * ringUrl String 否 铃音路径（可以预览，审核通过后提供）
     * createTime String 否 铃音创建时间yyyy-MM-dd HH:mm:ss
     * status String 是 激活状态(激活失败0，激活成功1)
     * remark String 是 备注（失败原因）
     */
    @ApiOperation(value = "视宣号铃音回执", notes = "视宣号铃音回执")
    @RequestMapping(value = "/sxh/notify/ring")
    public String sxhNotifyringActive(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            log.info("视宣号铃音回执,参数map:{},json数据:{}", requestMap, jsonNode);
            String transactionId=jsonNode.at("/transactionId").asText("");
            String ringId=jsonNode.at("/ringId").asText("");
            String status=jsonNode.at("/status").asText("");
            String remark=jsonNode.at("/remark").asText("");
            String ringUrl=jsonNode.at("/ringUrl").asText("");
            YinglouRing yinglouRing=yinglouRingService.lambdaQuery().select(YinglouRing::getId,YinglouRing::getMobile,YinglouRing::getChannel).eq(YinglouRing::getTransactionId,transactionId).orderByDesc(YinglouRing::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(yinglouRing==null){
                log.info("影楼视宣号铃音回执-铃音不存在-jsonNode:{}",jsonNode);
                return "OK";
            }
            yinglouRingService.lambdaUpdate().eq(YinglouRing::getId,yinglouRing.getId())
                    .set(YinglouRing::getRingId,ringId)
                    .set(YinglouRing::getStatus,status)
                    .set(YinglouRing::getRingUrl,ringUrl)
                    .set(YinglouRing::getCallbackMsg,remark)
                    .set(YinglouRing::getUpdateTime,new Date()).update();
            //激活成功设置默认铃音
            if("1".equals(status)){
                miguApiService.sxhRingSet(yinglouRing.getMobile(),yinglouRing.getChannel(),ringId);
            }
        } catch (Exception e) {
            log.info("视宣号铃音回执,参数map:{},json数据:{},异常!", requestMap, jsonNode, e);
            //return "FAIL";
        }

        return "OK";
    }


}
