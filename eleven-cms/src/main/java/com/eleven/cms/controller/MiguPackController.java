package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.*;
import com.eleven.cms.service.IMiguPackService;
import com.eleven.cms.service.IRightsPackService;
import com.eleven.cms.vo.MiguPackPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

 /**
 * @Description: 咪咕业务包
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Api(tags="咪咕业务包")
@RestController
@RequestMapping("/cms/miguPack")
@Slf4j
public class MiguPackController {
	@Autowired
	private IMiguPackService miguPackService;
	@Autowired
	private IRightsPackService rightsPackService;

	/**
	 * 分页列表查询
	 *
	 * @param miguPack
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "咪咕业务包-分页列表查询")
	@ApiOperation(value="咪咕业务包-分页列表查询", notes="咪咕业务包-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(MiguPack miguPack,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<MiguPack> queryWrapper = QueryGenerator.initQueryWrapper(miguPack, req.getParameterMap());
		Page<MiguPack> page = new Page<MiguPack>(pageNo, pageSize);
		IPage<MiguPack> pageList = miguPackService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param miguPackPage
	 * @return
	 */
	//@AutoLog(value = "咪咕业务包-添加")
	@ApiOperation(value="咪咕业务包-添加", notes="咪咕业务包-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody MiguPackPage miguPackPage) {
		MiguPack miguPack = new MiguPack();
		BeanUtils.copyProperties(miguPackPage, miguPack);
		if(miguPackService.lambdaQuery().eq(MiguPack::getPackName,miguPack.getPackName()).count()>0){
			return Result.error("权益包名已存在！");
		};
		miguPackService.saveMain(miguPack, miguPackPage.getRightsPackList());
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param miguPackPage
	 * @return
	 */
	//@AutoLog(value = "咪咕业务包-编辑")
	@ApiOperation(value="咪咕业务包-编辑", notes="咪咕业务包-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody MiguPackPage miguPackPage) {
		MiguPack miguPack = new MiguPack();
		BeanUtils.copyProperties(miguPackPage, miguPack);
		MiguPack miguPackEntity = miguPackService.getById(miguPack.getId());
		if(miguPackEntity==null) {
			return Result.error("未找到对应数据");
		}
		miguPackService.updateMain(miguPack, miguPackPage.getRightsPackList());
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "咪咕业务包-通过id删除")
	@ApiOperation(value="咪咕业务包-通过id删除", notes="咪咕业务包-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		miguPackService.delMain(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "咪咕业务包-批量删除")
	@ApiOperation(value="咪咕业务包-批量删除", notes="咪咕业务包-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.miguPackService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "咪咕业务包-通过id查询")
	@ApiOperation(value="咪咕业务包-通过id查询", notes="咪咕业务包-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		MiguPack miguPack = miguPackService.getById(id);
		if(miguPack==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(miguPack);

	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "会员权益业务关联通过主表ID查询")
	@ApiOperation(value="会员权益业务关联主表ID查询", notes="会员权益业务关联-通主表ID查询")
	@GetMapping(value = "/queryRightsPackByMainId")
	public Result<?> queryRightsPackListByMainId(@RequestParam(name="id",required=true) String id) {
		List<RightsPack> rightsPackList = rightsPackService.selectByMainId(id);
		return Result.ok(rightsPackList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param miguPack
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiguPack miguPack) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<MiguPack> queryWrapper = QueryGenerator.initQueryWrapper(miguPack, request.getParameterMap());
      LoginUser sysUser = HttpUtil.getCurrUser();

      //Step.2 获取导出数据
      List<MiguPack> queryList = miguPackService.list(queryWrapper);
      // 过滤选中数据
      String selections = request.getParameter("selections");
      List<MiguPack> miguPackList = new ArrayList<MiguPack>();
      if(oConvertUtils.isEmpty(selections)) {
          miguPackList = queryList;
      }else {
          List<String> selectionList = Arrays.asList(selections.split(","));
          miguPackList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
      }

      // Step.3 组装pageList
      List<MiguPackPage> pageList = new ArrayList<MiguPackPage>();
      for (MiguPack main : miguPackList) {
          MiguPackPage vo = new MiguPackPage();
          BeanUtils.copyProperties(main, vo);
          List<RightsPack> rightsPackList = rightsPackService.selectByMainId(main.getId());
          vo.setRightsPackList(rightsPackList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "咪咕业务包列表");
      mv.addObject(NormalExcelConstants.CLASS, MiguPackPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("咪咕业务包数据", "导出人:"+sysUser.getRealname(), "咪咕业务包"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          MultipartFile file = entity.getValue();// 获取上传文件对象
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<MiguPackPage> list = ExcelImportUtil.importExcel(file.getInputStream(), MiguPackPage.class, params);
              for (MiguPackPage page : list) {
                  MiguPack po = new MiguPack();
                  BeanUtils.copyProperties(page, po);
                  miguPackService.saveMain(po, page.getRightsPackList());
              }
              return Result.ok("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.ok("文件导入失败！");
    }

	 /**
	  * 分页列表查询
	  * @param rightsPack
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "会员权益业务关联-权益列表查询")
	 @ApiOperation(value = "会员权益业务关联-权益列表查询", notes = "会员权益业务关联-权益列表查询")
	 @GetMapping(value = "/rightsPackList")
	 public Result<IPage<RightsPack>> queryRightsPackPageList(RightsPack rightsPack,
													 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
													 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
													 HttpServletRequest req) {
		 Result<IPage<RightsPack>> result = new Result<IPage<RightsPack>>();
		 Page<RightsPack> page = new Page<RightsPack>(pageNo, pageSize);
		 IPage<RightsPack> pageList = rightsPackService.queryRightsPackPageList(page,rightsPack);
		 result.setSuccess(true);
		 result.setResult(pageList);
		 return result;
	 }

	 /**
	  * 查询业务包
	  * @param pack
	  * @return
	  */
	 @RequestMapping(value = "/queryPackByBusinessId", method = RequestMethod.GET)
	 public Result<List<MiguPack>> queryPackByBusinessId(MiguPack pack) {
		 Result<List<MiguPack>> result = new Result<>();
		 List<MiguPack> miguPack =miguPackService.queryPackByBusinessId(pack);
		 result.setSuccess(true);
		 result.setResult(miguPack);
		 return result;
	 }


	 /**
	  * 手动复制权益
	  * @param miguPackPage
	  * @return
	  */
	 @PostMapping(value = "/copyRights")
	 public Result<?> copyRights(@RequestBody MiguPackPage miguPackPage) {
		 MiguPack miguPack = new MiguPack();
		 BeanUtils.copyProperties(miguPackPage, miguPack);
		 if(miguPackService.lambdaQuery().eq(MiguPack::getPackName,miguPack.getPackName()).count()>0){
			 return Result.error("权益包名已存在！");
		 };
		 List<RightsPack> rightsPackList=rightsPackService.lambdaQuery().eq(RightsPack::getPackId,miguPack.getId()).orderByDesc(RightsPack::getPriority).list();
		 if(rightsPackList.isEmpty()){
			 return Result.error("权益代码不存在！");
		 }
		 rightsPackList.forEach(rightsPack->{
			 rightsPack.setId(null);
		 });
		 miguPackPage.setRightsPackList(rightsPackList);
		 miguPack.setId(null);
		 miguPackService.saveMain(miguPack, miguPackPage.getRightsPackList());
		 return Result.ok("添加成功！");
	 }
}
