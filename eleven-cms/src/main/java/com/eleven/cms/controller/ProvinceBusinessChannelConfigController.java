package com.eleven.cms.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.util.HttpUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.ProvinceBusinessChannelInfo;
import com.eleven.cms.entity.ProvinceBusinessChannelConfig;
import com.eleven.cms.vo.ProvinceBusinessChannelConfigPage;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.IProvinceBusinessChannelInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_province_business_channel_config
 * @Author: jeecg-boot
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@Api(tags="cms_province_business_channel_config")
@RestController
@RequestMapping("/com.eleven.cms/provinceBusinessChannelConfig")
@Slf4j
public class ProvinceBusinessChannelConfigController {
	@Autowired
	private IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
	@Autowired
	private IProvinceBusinessChannelInfoService provinceBusinessChannelInfoService;

	/**
	 * 分页列表查询
	 *
	 * @param provinceBusinessChannelConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_province_business_channel_config-分页列表查询")
	@ApiOperation(value="cms_province_business_channel_config-分页列表查询", notes="cms_province_business_channel_config-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ProvinceBusinessChannelConfig provinceBusinessChannelConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ProvinceBusinessChannelConfig> queryWrapper = QueryGenerator.initQueryWrapper(provinceBusinessChannelConfig, req.getParameterMap());
		Page<ProvinceBusinessChannelConfig> page = new Page<ProvinceBusinessChannelConfig>(pageNo, pageSize);
		IPage<ProvinceBusinessChannelConfig> pageList = provinceBusinessChannelConfigService.page(page, queryWrapper);
        List<ProvinceBusinessChannelConfig> collect = pageList.getRecords().stream().map(obj -> {
            List<ProvinceBusinessChannelInfo> provinceBusinessChannelInfoList = provinceBusinessChannelInfoService.selectByMainId(obj.getId());
            obj.setProvince(provinceBusinessChannelInfoList.stream().filter(o -> o.isEnable()).map(o -> o.getProvince()).collect(Collectors.joining("、")));
            return obj;
        }).collect(Collectors.toList());
        pageList.setRecords(collect);
        return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param provinceBusinessChannelConfigPage
	 * @return
	 */
	//@AutoLog(value = "cms_province_business_channel_config-添加")
	@ApiOperation(value="cms_province_business_channel_config-添加", notes="cms_province_business_channel_config-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProvinceBusinessChannelConfigPage provinceBusinessChannelConfigPage) {
		ProvinceBusinessChannelConfig provinceBusinessChannelConfig = new ProvinceBusinessChannelConfig();
		BeanUtils.copyProperties(provinceBusinessChannelConfigPage, provinceBusinessChannelConfig);

		provinceBusinessChannelConfigService.saveMain(provinceBusinessChannelConfig, provinceBusinessChannelConfigPage.getProvinceBusinessChannelInfoList());
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param provinceBusinessChannelConfigPage
	 * @return
	 */
	@AutoLog(value = "cms_province_business_channel_config-编辑")
	@ApiOperation(value="cms_province_business_channel_config-编辑", notes="cms_province_business_channel_config-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProvinceBusinessChannelConfigPage provinceBusinessChannelConfigPage) {
		ProvinceBusinessChannelConfig provinceBusinessChannelConfig = new ProvinceBusinessChannelConfig();
		BeanUtils.copyProperties(provinceBusinessChannelConfigPage, provinceBusinessChannelConfig);
		ProvinceBusinessChannelConfig provinceBusinessChannelConfigEntity = provinceBusinessChannelConfigService.getById(provinceBusinessChannelConfig.getId());
		if(provinceBusinessChannelConfigEntity==null) {
			return Result.error("未找到对应数据");
		}
		if(provinceBusinessChannelConfigPage.getProvinceBusinessChannelInfoList().isEmpty()) {
			return Result.error("请刷新后重试！");
		}
		provinceBusinessChannelConfigService.updateMain(provinceBusinessChannelConfig, provinceBusinessChannelConfigPage.getProvinceBusinessChannelInfoList());
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_province_business_channel_config-通过id删除")
	@ApiOperation(value="cms_province_business_channel_config-通过id删除", notes="cms_province_business_channel_config-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		provinceBusinessChannelConfigService.delMain(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_province_business_channel_config-批量删除")
	@ApiOperation(value="cms_province_business_channel_config-批量删除", notes="cms_province_business_channel_config-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.provinceBusinessChannelConfigService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_province_business_channel_config-通过id查询")
	@ApiOperation(value="cms_province_business_channel_config-通过id查询", notes="cms_province_business_channel_config-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProvinceBusinessChannelConfig provinceBusinessChannelConfig = provinceBusinessChannelConfigService.getById(id);
		if(provinceBusinessChannelConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(provinceBusinessChannelConfig);

	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_province_business_channel_info通过主表ID查询")
	@ApiOperation(value="cms_province_business_channel_info主表ID查询", notes="cms_province_business_channel_info-通主表ID查询")
	@GetMapping(value = "/queryProvinceBusinessChannelInfoByMainId")
	public Result<?> queryProvinceBusinessChannelInfoListByMainId(@RequestParam(name="id",required=true) String id) {
		List<ProvinceBusinessChannelInfo> provinceBusinessChannelInfoList = provinceBusinessChannelInfoService.selectByMainId(id);
		return Result.ok(provinceBusinessChannelInfoList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param provinceBusinessChannelConfig
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProvinceBusinessChannelConfig provinceBusinessChannelConfig) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<ProvinceBusinessChannelConfig> queryWrapper = QueryGenerator.initQueryWrapper(provinceBusinessChannelConfig, request.getParameterMap());
      LoginUser sysUser = HttpUtil.getCurrUser();

      //Step.2 获取导出数据
      List<ProvinceBusinessChannelConfig> queryList = provinceBusinessChannelConfigService.list(queryWrapper);
      // 过滤选中数据
      String selections = request.getParameter("selections");
      List<ProvinceBusinessChannelConfig> provinceBusinessChannelConfigList = new ArrayList<ProvinceBusinessChannelConfig>();
      if(oConvertUtils.isEmpty(selections)) {
          provinceBusinessChannelConfigList = queryList;
      }else {
          List<String> selectionList = Arrays.asList(selections.split(","));
          provinceBusinessChannelConfigList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
      }

      // Step.3 组装pageList
      List<ProvinceBusinessChannelConfigPage> pageList = new ArrayList<ProvinceBusinessChannelConfigPage>();
      for (ProvinceBusinessChannelConfig main : provinceBusinessChannelConfigList) {
          ProvinceBusinessChannelConfigPage vo = new ProvinceBusinessChannelConfigPage();
          BeanUtils.copyProperties(main, vo);
          List<ProvinceBusinessChannelInfo> provinceBusinessChannelInfoList = provinceBusinessChannelInfoService.selectByMainId(main.getId());
          vo.setProvinceBusinessChannelInfoList(provinceBusinessChannelInfoList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "cms_province_business_channel_config列表");
      mv.addObject(NormalExcelConstants.CLASS, ProvinceBusinessChannelConfigPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("cms_province_business_channel_config数据", "导出人:"+sysUser.getRealname(), "cms_province_business_channel_config"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          MultipartFile file = entity.getValue();// 获取上传文件对象
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<ProvinceBusinessChannelConfigPage> list = ExcelImportUtil.importExcel(file.getInputStream(), ProvinceBusinessChannelConfigPage.class, params);
              for (ProvinceBusinessChannelConfigPage page : list) {
                  ProvinceBusinessChannelConfig po = new ProvinceBusinessChannelConfig();
                  BeanUtils.copyProperties(page, po);
                  provinceBusinessChannelConfigService.saveMain(po, page.getProvinceBusinessChannelInfoList());
              }
              return Result.ok("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.ok("文件导入失败！");
    }

}
