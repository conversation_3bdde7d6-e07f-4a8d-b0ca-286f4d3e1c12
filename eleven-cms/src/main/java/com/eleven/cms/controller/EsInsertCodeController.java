package com.eleven.cms.controller;

import com.eleven.cms.es.entity.EsInsertCode;
import com.eleven.cms.queue.RabbitMQMsgSender;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @datetime 2024/12/13 10:51
 */
@RestController
@RequestMapping("/insert-code")
@RequiredArgsConstructor
public class EsInsertCodeController {

    private final RabbitMQMsgSender rabbitMQMsgSender;

    @PostMapping("/add")
    public void addInsertCode(@RequestBody EsInsertCode esInsertCode) {
        rabbitMQMsgSender.sendInsertCodeAddMessage(esInsertCode);
    }
}
