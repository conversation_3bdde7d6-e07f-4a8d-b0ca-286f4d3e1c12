package com.eleven.cms.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.AdSiteBusinessConfig;
import com.eleven.cms.service.IAdSiteBusinessConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_ad_site_business_config
 * @Author: jeecg-boot
 * @Date:   2023-04-17
 * @Version: V1.0
 */
@Api(tags="cms_ad_site_business_config")
@RestController
@RequestMapping("/cms/adSiteBusinessConfig")
@Slf4j
public class AdSiteBusinessConfigController extends JeecgController<AdSiteBusinessConfig, IAdSiteBusinessConfigService> {
	@Autowired
	private IAdSiteBusinessConfigService adSiteBusinessConfigService;
	
	/**
	 * 分页列表查询
	 *
	 * @param adSiteBusinessConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_ad_site_business_config-分页列表查询")
	@ApiOperation(value="cms_ad_site_business_config-分页列表查询", notes="cms_ad_site_business_config-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AdSiteBusinessConfig adSiteBusinessConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AdSiteBusinessConfig> queryWrapper = QueryGenerator.initQueryWrapper(adSiteBusinessConfig, req.getParameterMap());
		Page<AdSiteBusinessConfig> page = new Page<AdSiteBusinessConfig>(pageNo, pageSize);
		IPage<AdSiteBusinessConfig> pageList = adSiteBusinessConfigService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param adSiteBusinessConfig
	 * @return
	 */
	//@AutoLog(value = "cms_ad_site_business_config-添加")
	@ApiOperation(value="cms_ad_site_business_config-添加", notes="cms_ad_site_business_config-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AdSiteBusinessConfig adSiteBusinessConfig) {
		adSiteBusinessConfig.setAdSite(adSiteBusinessConfig.getAdSite() == null ? "" : adSiteBusinessConfig.getAdSite());
		adSiteBusinessConfigService.save(adSiteBusinessConfig);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param adSiteBusinessConfig
	 * @return
	 */
	//@AutoLog(value = "cms_ad_site_business_config-编辑")
	@ApiOperation(value="cms_ad_site_business_config-编辑", notes="cms_ad_site_business_config-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AdSiteBusinessConfig adSiteBusinessConfig) {
		adSiteBusinessConfigService.updateById(adSiteBusinessConfig);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_ad_site_business_config-通过id删除")
	@ApiOperation(value="cms_ad_site_business_config-通过id删除", notes="cms_ad_site_business_config-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		adSiteBusinessConfigService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_ad_site_business_config-批量删除")
	@ApiOperation(value="cms_ad_site_business_config-批量删除", notes="cms_ad_site_business_config-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.adSiteBusinessConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_ad_site_business_config-通过id查询")
	@ApiOperation(value="cms_ad_site_business_config-通过id查询", notes="cms_ad_site_business_config-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AdSiteBusinessConfig adSiteBusinessConfig = adSiteBusinessConfigService.getById(id);
		if(adSiteBusinessConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(adSiteBusinessConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param adSiteBusinessConfig
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AdSiteBusinessConfig adSiteBusinessConfig) {
        return super.exportXls(request, adSiteBusinessConfig, AdSiteBusinessConfig.class, "cms_ad_site_business_config");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		List<AdSiteBusinessConfig> saveList = new ArrayList<>();
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			ImportParams params = new ImportParams();
			params.setTitleRows(2);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<AdSiteBusinessConfig> list = ExcelImportUtil.importExcel(file.getInputStream(), AdSiteBusinessConfig.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				for (AdSiteBusinessConfig adSiteBusinessConfig : list) {
					if(saveList.contains(adSiteBusinessConfig)){
						continue;
					}
					AdSiteBusinessConfig dto = adSiteBusinessConfigService.lambdaQuery()
							.eq(AdSiteBusinessConfig::getAdSite, adSiteBusinessConfig.getAdSite())
							.eq(AdSiteBusinessConfig::getChannel, adSiteBusinessConfig.getChannel())
							.one();
					if(dto == null){
						saveList.add(adSiteBusinessConfig);
					}
				}
				adSiteBusinessConfigService.saveBatch(saveList);
				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				return Result.error("文件导入失败:" + e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					log.info("文件导入失败：",e);
				}
			}
		}
		return Result.error("文件导入失败！");
    }

}
