package com.eleven.cms.controller;

import cn.hutool.core.util.StrUtil;
import com.eleven.cms.config.DianxinVrbtProperties;
import com.eleven.cms.dto.YCJXADLDOrderResultNotifyDTO;
import com.eleven.cms.entity.*;
import com.eleven.cms.es.entity.EsQywxAction;
import com.eleven.cms.es.repository.EsSubscribeRepository;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.SubscribeResultNotifyService;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.HaiYiMediaNotifyService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DianxinAuthUtils;
import com.eleven.cms.util.LiantongAuthUtils;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.util.wxhk.WXBizMsgCrypt;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.RightsPackDto;
import com.eleven.cms.vo.TianyiResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.*;

/**
 * <AUTHOR>
 */
@Api(tags = "notify_api")
@RestController
@RequestMapping("/api")
@Slf4j
public class NotifyController {
    @Autowired
    private EsSubscribeRepository esSubscribeRepository;

    @Autowired
    private SubscribeResultNotifyService subscribeResultNotifyService;
    @Autowired
    private ILiantongRingService liantongRingService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private IMiguAppLoginRecordService miguAppLoginRecordService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private IRightsPackService rightsPackService;
    @Autowired
    private MobileRegionService mobileRegionService;
    @Autowired
    private DianxinVrbtProperties dianxinVrbtProperties;
    @Autowired
    private HaiYiMediaNotifyService haiYiMediaNotifyService;
    @Autowired
    private IQywxService qywxService;
    @Autowired
    private IEsDataService esDataService;
    @Autowired
    private ICmsNotifyXwfcsService iCmsNotifyXwfcsService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IChannelService channelService;

    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    public static XmlMapper xmlMapper = (XmlMapper) new XmlMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * 破解开通结果同步 (新老pj共用)
     *
     * @return
     */
    @RequestMapping(value = "/billingResultSync")
    @ResponseBody
    public String billingResultSync(
            //手机IMSI信息
            @RequestParam(name = "imsi", required = false) String imsi,
            //手机号码
            @RequestParam(name = "phoneNumber", required = false) String phoneNumber,
            //交易id，与计费接口响应报文：transId对应
            @RequestParam(name = "transId", required = false) String transId,
            //处理时间
            @RequestParam(name = "processTime", required = false) String processTime,
            //错误代码：0000表示计费成功
            @RequestParam(name = "resCode", required = false) String resCode,
            //错误描述
            @RequestParam(name = "resMsg", required = false) String resMsg,
            //计费接口请求：paycode对应
            @RequestParam(name = "paycode", required = false) String paycode,
            //计费接口请求：cpid对应
            @RequestParam(name = "cpid", required = false) String cpid,
            //计费接口请求：cpparm对应
            @RequestParam(name = "cpparm", required = false) String cpparm,
            //移动订单id
            @RequestParam(name = "ydOrderId", required = false) String ydOrderId,
            //计费接口请求：fee对应
            @RequestParam(name = "fee", required = false) String fee,
            //1：音乐
            @RequestParam(name = "type", required = false) String type
    ) {
        log.info("计费开通结果同步:imsi={},phoneNumber={},transId={},processTime={},resCode={},resMsg={},paycode={},cpid={},cpparm={},ydOrder={},fee={},type={}",
                imsi, phoneNumber, transId, processTime, resCode, resMsg, paycode, cpid, cpparm, ydOrderId, fee, type);
        try {
            Date bizTime = new Date();
            if (processTime != null && processTime.length() == 19) {
                bizTime = DateUtils.str2Date(processTime, DateUtils.datetimeFormat.get());
            }
            subscribeResultNotifyService.receiveBillingResult(phoneNumber, transId, resCode, resMsg, bizTime, ydOrderId);
        } catch (Exception e) {
            log.error("计费开通结果同步异常", e);
        }
        return "ok";
    }

    /**
     * 破解开通结果同步 (wujiong)
     * code=0 成功 data为订单号
     *
     * @return
     */
    ////@AutoLog(value = "彩铃中心订阅包回调通知")
    @ApiOperation(value = "开通结果同步 (wujiong)", notes = "开通结果同步 (wujiong)")
    @GetMapping(value = "/centrality/callback")
    public String centralityCallback(@RequestParam("orderid") String orderId,
                                     @RequestParam("fee") String fee,
                                     @RequestParam("tel") String tel,
                                     @RequestParam("time") String time,
                                     @RequestParam("channel") String channel,
                                     @RequestParam(value = "province", required = false) String province,
                                     @RequestParam(value = "key", required = false) String key,
                                     @RequestParam(value = "cpparam", required = false) String cpparam) {
        log.info("计费开通结果同步wujiong,orderid:{},fee:{},tel:{},time:{},channel:{},province:{},key:{}", orderId, fee, tel, time, channel, province, key);
        //        subscribeService.receiveLiantongJunboNotify(data,code,msg);
        subscribeResultNotifyService.receiveWujiongNotify(orderId, tel, time);
        return "success";
    }

    /**
     * 我方pj接收即时开通结果
     *
     * @return
     */
    @PostMapping("/subscribeResultNotify")
    @ResponseBody
    public String subscribeResultNotify(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String raw = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("即时开通结果接收,raw:{}", raw);
            JsonNode tree = mapper.readTree(raw);
            String id = tree.at("/originId").asText(tree.at("/id").asText());
            String mobile = tree.at("/mobile").asText();
            int state = tree.at("/state").asInt();
            String result = tree.at("/result").asText();
            String extra = tree.at("/extra").asText(null);

            subscribeResultNotifyService.receiveSubscribeResult(id, mobile, state, result, extra);

            return "OK";

        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR";
        }
    }


    /**
     * 渠道订阅回调
     *
     * @return
     */
    @ApiOperation(value = "渠道订阅回调", notes = "渠道订阅回调")
    @PostMapping("/subCallback")
    public Result<?> channelSubscribeCallback(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {

        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        Map<String, String> extraMap = new HashMap<>();
        String app = request.getHeader("x-requested-with");
        String extraFromVrbtAttribute = request.getHeader("x-requested-fattr");

        if (StrUtil.isNotBlank(extraFromVrbtAttribute)) {
            extraMap.put("x-requested-fattr", extraFromVrbtAttribute);
        }
        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("渠道订阅回调=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonData, userAgent);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            subscribe.setUserAgent(userAgent);
            //referer字段存储广告app的包名
            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());
            return subscribeResultNotifyService.receiveOrderCallback(subscribe, extraMap);

        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("请求参数错误");
        }
    }

    /**
     * 渠道订阅回调
     *
     * @return
     */
    @ApiOperation(value = "渠道订阅回调", notes = "渠道订阅回调")
    @PostMapping("/aiVrbtMiniAppSubCallback")
    public Result<?> channelAiVrbtMiniAppSubCallback(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {

        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        Map<String, String> extraMap = new HashMap<>();
        String app = request.getHeader("x-requested-with");
        String extraFromVrbtAttribute = request.getHeader("x-requested-fattr");

        if (StrUtil.isNotBlank(extraFromVrbtAttribute)) {
            extraMap.put("x-requested-fattr", extraFromVrbtAttribute);
        }
        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("ai彩铃小程序渠道订阅回调=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonData, userAgent);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            subscribe.setUserAgent(userAgent);
            //referer字段存储广告app的包名
            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());
            return subscribeResultNotifyService.receiveAiVrbtMiniAppSubCallback(subscribe, extraMap);

        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("请求参数错误");
        }
    }

    /**
     * app的渠道订阅回调
     *
     * @return
     */
    @ApiOperation(value = "渠道订阅回调", notes = "渠道订阅回调")
    @PostMapping("/aiVrbtAppSubCallback")
    public Result<?> channelAiVrbtAppSubCallback(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {

        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        Map<String, String> extraMap = new HashMap<>();
        String app = request.getHeader("x-requested-with");
        String extraFromVrbtAttribute = request.getHeader("x-requested-fattr");

        if (StrUtil.isNotBlank(extraFromVrbtAttribute)) {
            extraMap.put("x-requested-fattr", extraFromVrbtAttribute);
        }
        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("ai彩铃app渠道订阅回调=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonData, userAgent);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            subscribe.setUserAgent(userAgent);
            //referer字段存储广告app的包名
            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());
            return subscribeResultNotifyService.receiveAiVrbtAppSubCallback(subscribe, extraMap);

        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("请求参数错误");
        }
    }
    /**
     * 渠道订阅回调
     *
     * @return
     */
    @ApiOperation(value = "白金会员渠道订阅回调", notes = "白金会员渠道订阅回调")
    @PostMapping("/bjhySubCallback")
    public Result<?> bjhyChannelSubscribeCallback(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {

        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }

        String ipAddr = IPUtils.getIpAddr(request);
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {

            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("渠道订阅回调请求=>{}", jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            subscribe.setReferer(finger);
            subscribe.setIp(ipAddr);
            return subscribeResultNotifyService.receiveBjhyOrderCallback(subscribe);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("请求参数错误");
        }
    }

    /**
     * 渠道订阅回调
     *
     * @return
     */
    @ApiOperation(value = "抖音小程序视频彩铃回调", notes = "抖音小程序视频彩铃回调")
    @PostMapping("/douyinVrbtCallbak")
    public Result<?> douyinVrbtCallbak(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {

        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }

        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {

            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);                                               3
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("渠道订阅回调请求=>{}", jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            subscribe.setReferer(finger);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());

            return subscribeResultNotifyService.receiveDouyinVrbtCallback(subscribe);

        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("请求参数错误");
        }
    }

    /**
     * 外部数据回传接口
     *
     * @return
     */
    @PostMapping("/outside/notify")
    public Result outsideNotify(@RequestParam("mobile") String mobile,
                                @RequestParam("channel") String channel,
                                @RequestParam("subChannel") String subChannel,
                                @RequestParam("resCode") String resCode,
                                @RequestParam("resMsg") String resMsg) {
        log.info("第三方渠道订阅回调,mobile:{},channel:{},subChannel:{},resCode:{},resMsg:{}", mobile, channel, subChannel, resCode, resMsg);
        if (org.apache.commons.lang3.StringUtils.isBlank(mobile) || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("无效的手机号");
        }
        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setChannel(channel);
        subscribe.setSubChannel(subChannel);
        subscribe.setStatus("000000".equals(resCode) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
        subscribe.setResult("计费开通结果=>" + resCode + ":" + resMsg);
        return subscribeResultNotifyService.receiveOutsideNotify(subscribe);
    }


    /**
     * 破解开通结果同步 (森越pj)
     *
     * @return
     */
    @RequestMapping(value = "/sy/billingResultSync")
    @ResponseBody
    public String senyueBillingResultSync(
            //phone	是	String	手机号
            @RequestParam(name = "phone", required = false) String phone,
            //outTradeNo	是	String	外部订单号
            @RequestParam(name = "outTradeNo", required = false) String outTradeNo,
            //linkId	是	String	平台流水号
            @RequestParam(name = "linkId", required = false) String linkId,
            //orderStatus	是	Integer	订购状态(0代表成功)
            @RequestParam(name = "orderStatus", required = false) Integer orderStatus,
            //resultCode	是	String	返回码
            @RequestParam(name = "resultCode", required = false) String resultCode,
            //resultMsg	是	String	返回说明
            @RequestParam(name = "resultMsg", required = false) String resultMsg,
            //errorCode	是	String	基地错误码(resultCode=E0005时有值)
            @RequestParam(name = "errorCode", required = false) String errorCode,
            //orderTime	是	String	订购时间(格式:yyyyMMddHHmmss)
            @RequestParam(name = "orderTime", required = false) String orderTime,
            //productId	是	String	产品编号
            @RequestParam(name = "productId", required = false) String productId,
            //channelId	是	String	渠道
            @RequestParam(name = "channelId", required = false) String channelId,
            //amount	是	String	金额
            @RequestParam(name = "amount", required = false) String amount,
            //proxyIp	否	String	代理
            @RequestParam(name = "proxyIp", required = false) String proxyIp,
            //upstreamId	否	String	上游单号
            @RequestParam(name = "upstreamId", required = false) String upstreamId,
            //smsCodes	否	String	验证码
            @RequestParam(name = "smsCodes", required = false) String smsCodes
    ) {

        log.info("计费开通结果同步senyue:phone={},outTradeNo={},linkId={},orderStatus={},resultCode={},resultMsg={},errorCode={},orderTime={},productId={},channelId={},amount={},proxyIp={},upstreamId={},smsCodes={}",
                phone, outTradeNo, linkId, orderStatus, resultCode, resultMsg, errorCode, orderTime, productId, channelId, amount, proxyIp, upstreamId, smsCodes);
        try {
            Date bizTime = new Date();
            if (orderTime != null && orderTime.length() == 14) {
                bizTime = DateUtils.str2Date(orderTime, DateUtils.yyyymmddhhmmss.get());
            }
            subscribeResultNotifyService.receiveBillingResult(phone, linkId, resultCode, resultMsg, bizTime, "");
        } catch (Exception e) {
            log.error("计费开通结果同步senyue异常", e);
        }
        return "ok";
    }

    /**
     * 合作支撑平台把合约订购用户登录信息同步给合作方(用户在咪咕app登录后通知我方进行权益发放)
     *
     * @return
     */
    ////@AutoLog(value = "合作支撑平台合约订购用户在咪咕app登录信息同步")
    @ApiOperation(value = "合作支撑平台合约订购用户在咪咕app登录信息同步", notes = "合作支撑平台合约订购用户在咪咕app登录信息同步")
    @PostMapping(value = "/contractUserMiguAppLoginNotify")
    public String contractUserMiguAppLoginNotify(@RequestParam Map<String, Object> requestMap, @RequestBody JsonNode jsonNode) {
        //{"channelCode":"00210Q6","msisdn":"a073kK15n1lWaujfzCC+xg==","bizCode":"698039020108689345","loginTime":"20220720151512","orderTime":"20220720151214"}
        MiguAppLoginRecord miguAppLoginRecord = new MiguAppLoginRecord();
        final String resultOK = "{\"resCode\":\"000000\",\"resMsg\":\"成功\"}";
        final String rights3Key = "权益三";
        final String rightsId = "mgyybjhy";
        try {
            String channelCode = jsonNode.at("/channelCode").asText();
            String msisdnEncrypted = jsonNode.at("/msisdn").asText();
            String mobile = miguApiService.decryptMobile(msisdnEncrypted, channelCode);
            if (StringUtils.isEmpty(mobile)) {
                log.warn("合作支撑平台合约订购用户在咪咕app登录信息同步-手机号解码失败=>手机号:{},渠道号:{}", msisdnEncrypted, channelCode);
                return resultOK;
            }
            log.info("合作支撑平台合约订购用户在咪咕app登录信息同步,参数map:{},json数据:{},手机号:{}", requestMap, jsonNode, mobile);
            String serviceId = jsonNode.at("/bizCode").asText();
            String loginTimeStr = jsonNode.at("/loginTime").asText();
            Date loginTime = DateUtils.str2Date(loginTimeStr, DateUtils.yyyymmddhhmmss.get());
            String orderTimeStr = jsonNode.at("/orderTime").asText();
            Date orderTime = DateUtils.str2Date(orderTimeStr, DateUtils.yyyymmddhhmmss.get());

            miguAppLoginRecord.setChannelCode(channelCode);
            miguAppLoginRecord.setMobile(mobile);
            miguAppLoginRecord.setServiceId(serviceId);
            miguAppLoginRecord.setLoginTime(loginTime);
            miguAppLoginRecord.setOrderTime(orderTime);
            miguAppLoginRecordService.save(miguAppLoginRecord);

            if (StringUtils.isEmpty(serviceId)) {
                log.warn("咪咕App登录领取白金会员-渠道号-业务ID错误=>手机号:{},权益领取业务ID:{},渠道号:{}", mobile, serviceId, channelCode);
                return resultOK;
            }
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, serviceId).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if (businessPack == null) {
                log.warn("咪咕App登录领取白金会员-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{}", mobile, serviceId);
                return resultOK;
            }
            RightsPackDto pack = rightsPackService.wholeMemberRightsList(serviceId).stream().filter(item -> rights3Key.equals(item.getRemark())).collect(
                    Collectors.toList()).get(0);
            if (pack == null) {
                log.warn("咪咕App登录领取白金会员-会员权益业务关联未正确配置=>手机号:{},权益领取业务ID:{}", mobile, serviceId);
                return resultOK;
            }
            final String serviceApiBeanName = businessPack.getServiceApiBeanName();
            log.info("咪咕App登录领取白金会员-ApiBean配置=>手机号:{},ApiBean:{},权益领取业务ID:{},账号:{},权益包:{},产品ID:{}", mobile, serviceApiBeanName, serviceId, mobile, pack.getPackName(), rightsId);
            if (StringUtils.isEmpty(serviceApiBeanName) || StringUtils.isEmpty(serviceId)) {
                log.warn("咪咕App登录领取白金会员-渠道号-业务渠道权益关联未配置=>手机号:{},权益领取业务ID:{}", mobile, serviceId);
                return resultOK;
            }
            final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessRightsSubService.class);
            businessRightsSubService.createScheduledRecharge(mobile, mobile, serviceId, pack.getPackName(), rightsId, channelCode);
        } catch (Exception e) {
            log.info("合作支撑平台合约订购用户在咪咕app登录信息同步,参数map:{},json数据:{},异常!", requestMap, jsonNode, e);
        }

        return resultOK;
    }

    /**
     * 电信爱音乐ISMP订购退订消息回调
     * 电信ISMP订购退订消息回调,company:maihe,timestamp:20240506101922,deviceId:2000000004764,signature:31c68f7e14e42e6d0cd2e6ff607859df,mobile:***********,productId:135999999999999000157,state:0,time:2024-05-06 10:19:22,sessionkey:null
     *
     * @param timestamp  时间戳(精确到秒，格式为： 年月日时分秒（yyyyMMddHHmmss)在Header 里传给SP
     * @param deviceId   调用方ID 在Header 里传给SP,由爱音乐统一分配
     * @param signature  md5签名生成方式MD5(keyword+timestamp)，keyword由系统分配在Header 里传给SP
     * @param mobile     用户手机号
     * @param productId  ISMP产品ID
     * @param state      状态 0 订购 1退订(短代点播产品 0成功 1失败)
     * @param time       操作时间 格式 yyyy-MM-dd HH:mm:ss 使用URLEncoder.encode(time, "UTF-8") 转码
     * @param sessionkey 自定义参数（主要用于短代计费）
     * @return
     */
    @ApiOperation(value = "电信ISMP订购退订消息回调", notes = "电信ISMP订购退订消息回调")
    @PostMapping("/dianxinVrbt/ismpNotify")
    public String dianxinVrbtIsmpNotify(@RequestHeader String timestamp,
                                        @RequestHeader("deviceid") String deviceId,
                                        @RequestHeader String signature,
                                        @RequestParam(name = "mobile", required = false) String mobile,
                                        @RequestParam(name = "productid", required = false) String productId,
                                        @RequestParam(name = "state", required = false) String state,
                                        @RequestParam(name = "time", required = false) String time,
                                        @RequestParam(name = "sessionkey", required = false) String sessionkey) {
        Date actionTime = null;
        try {
            time = URLDecoder.decode(time, StandardCharsets.UTF_8.name());
            actionTime = DateUtils.datetimeFormat.get().parse(time);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("电信ISMP订购退订消息回调,timestamp:{},deviceId:{},signature:{},mobile:{},productId:{},state:{},time:{},sessionkey:{}",
                timestamp, deviceId, signature, mobile, productId, state, time, sessionkey);
        String company = dianxinVrbtProperties.getCompanyByPackageId(productId);

        if (DianxinAuthUtils.validateNotifySign(deviceId, timestamp, signature, company)) {
            subscribeResultNotifyService.receiveDianxinIsmpNotify(mobile, state, actionTime);
        } else {
            log.warn("电信ISMP订购退订消息回调-验签失败,timestamp:{},deviceId:{},signature:{},mobile:{},productId:{},state:{},time:{},sessionkey:{}",
                    timestamp, deviceId, signature, mobile, productId, state, time, sessionkey);
        }

        return "{\"code\": \"0000\",\"description\": \"成功\"}";
    }


    /**
     * 电信爱音乐ISMP订购退订消息回调
     *
     * @param timestamp  时间戳(精确到秒，格式为： 年月日时分秒（yyyyMMddHHmmss)在Header 里传给SP
     * @param deviceId   调用方ID 在Header 里传给SP,由爱音乐统一分配
     * @param signature  md5签名生成方式MD5(keyword+timestamp)，keyword由系统分配在Header 里传给SP
     * @param mobile     用户手机号
     * @param productId  ISMP产品ID
     * @param state      状态 0 订购 1退订(短代点播产品 0成功 1失败)
     * @param time       操作时间 格式 yyyy-MM-dd HH:mm:ss 使用URLEncoder.encode(time, "UTF-8") 转码
     * @param sessionkey 自定义参数（主要用于短代计费）
     * @param company    maihe
     * @return
     */
    @ApiOperation(value = "电信ISMP订购退订消息回调", notes = "电信ISMP订购退订消息回调")
    @PostMapping("/dianxinVrbt/{company}/ismpNotify")
    public String dianxinVrbtCompanyIsmpNotify(@RequestHeader String timestamp,
                                               @RequestHeader("deviceid") String deviceId,
                                               @RequestHeader String signature,
                                               @RequestParam(name = "mobile", required = false) String mobile,
                                               @RequestParam(name = "productid", required = false) String productId,
                                               @RequestParam(name = "state", required = false) String state,
                                               @RequestParam(name = "time", required = false) String time,
                                               @RequestParam(name = "sessionkey", required = false) String sessionkey,
                                               @PathVariable String company) {
        Date actionTime = null;
        try {
            time = URLDecoder.decode(time, StandardCharsets.UTF_8.name());
            actionTime = DateUtils.datetimeFormat.get().parse(time);
        } catch (Exception e) {
            e.printStackTrace();
        }

        log.info("电信ISMP订购退订消息回调,company:{},timestamp:{},deviceId:{},signature:{},mobile:{},productId:{},state:{},time:{},sessionkey:{}",
                company, timestamp, deviceId, signature, mobile, productId, state, time, sessionkey);
        if (BIZ_DIANXIN_CHANNEL_MAIHE_SFZF.equals(company)) {
            return "{\"code\": \"0000\",\"description\": \"成功\"}";
        }
        if (DianxinAuthUtils.validateNotifySign(deviceId, timestamp, signature, company)) {
            subscribeResultNotifyService.receiveDianxinIsmpNotify(mobile, state, actionTime);
        } else {
            log.warn("电信ISMP订购退订消息回调-验签失败,company:{},timestamp:{},deviceId:{},signature:{},mobile:{},productId:{},state:{},time:{},sessionkey:{}",
                    company, timestamp, deviceId, signature, mobile, productId, state, time, sessionkey);
        }
        return "{\"code\": \"0000\",\"description\": \"成功\"}";
    }


    /**
     * 联通沃音乐能力开放平台——5G彩铃产品订购异步通知
     *
     * @param authorization 其中Authorization格式为：appKey:sign （客户端标识:签名）
     * @param timestamp     获取请求的时间戳timestampMillis，取值为距1970-01-01 00:00:00的毫秒数
     * @return 2020-12-29 16:26:36 INFO  http-nio-9527-exec-22 com.eleven.cms.controller.ApiController 联通沃音乐订购异步通知,ip:**************,authorization:3000008270:B023C18A6D442724BC73FD2D343CF1A5,actionTime:Tue Dec 29 16:26:35 CST 2020,jsonData:{"noticeType":"order","notice":{"returnCode":"0000","notify":{"billingId":"6820122901818674","callNumber":"18613222501","channelId":"3000008270","createTime":"20201229161706","devId":"401520","effectiveTime":"20201229161708","capId":"6820122901818674","id":"1201229162501855476","orderDirection":0,"orderType":1,"productId":"4900720600","provinceCode":"081","remoteSys":2,"returnCode":"000000","status":1,"updateTime":"20201229161708"}}}
     * 2020-12-29 16:28:36 INFO  http-nio-9527-exec-12 com.eleven.cms.controller.ApiController 联通沃音乐订购异步通知,ip:**************,authorization:3000008270:2649EE4FC03E2D47F6F1C921D6C76928,actionTime:Tue Dec 29 16:28:36 CST 2020,jsonData:{"noticeType":"order","notice":{"returnCode":"0000","notify":{"billingId":"6820122901818878","callNumber":"18613222501","channelId":"3000008270","createTime":"20201229162816","devId":"401520","effectiveTime":"20201229162818","capId":"6820122901818878","id":"1201229162501856712","orderDirection":0,"orderType":1,"productId":"4900720600","provinceCode":"081","remoteSys":2,"returnCode":"000000","status":1,"updateTime":"20201229162818"}}}
     * 2020-12-29 16:33:06 INFO  http-nio-9527-exec-8 com.eleven.cms.controller.ApiController 联通沃音乐订购异步通知,ip:**************,authorization:3000008270:25192A39A803D43F76B370A9ACC8A56E,actionTime:Tue Dec 29 16:33:05 CST 2020,jsonData:{"noticeType":"order","notice":{"returnCode":"0000","notify":{"billingId":"6820122901818118","callNumber":"18613222501","channelId":"3000008270","createTime":"20201229160326","devId":"401520","effectiveTime":"20201229160328","capId":"6820122901818118","id":"1201229162501853723","orderDirection":0,"orderType":1,"productId":"4900720600","provinceCode":"081","remoteSys":2,"returnCode":"000000","status":1,"updateTime":"20201229160328"}}}
     * {"noticeType":"order","notice":{"returnCode":"0000","notify":{"billingId":"6820123001845955","callNumber":"18613222501","channelId":"3000008270","createTime":"20201230105835","devId":"401520","effectiveTime":"20201230105838","capId":"6820123001845955","id":"1201230102501903450","orderDirection":0,"orderType":1,"productId":"4900720600","provinceCode":"081","remoteSys":2,"returnCode":"000000","status":1,"updateTime":"20201230105838"}}}
     * {"noticeType":"order","notice":{"returnCode":"0000","description":"订购并设置成功","orderId":"1201231102501000551"}}
     */
    @ApiOperation(value = "联通沃音乐订购异步通知", notes = "联通沃音乐订购异步通知")
    @PostMapping(value = "/liantongVrbt/orderNotify", produces = MediaType.TEXT_PLAIN_VALUE)
    public String liantongVrbtOrderNotify(@RequestHeader(value = "Authorization", required = false) String authorization,
                                          @RequestHeader(value = "timestamp", required = false) String timestamp,
                                          HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            Date actionTime = new Date(Long.parseLong(timestamp));
            log.info("联通沃音乐订购异步通知,ip:{},authorization:{},actionTime:{},jsonData:{}", ipAddr, authorization, actionTime, jsonData);
            if (LiantongAuthUtils.validateNotifySign(authorization, timestamp, jsonData, ipAddr)) {
                //log.info("联通沃音乐订购异步通知,ip:{},authorization:{},actionTime:{},校验通过!", ipAddr,authorization,actionTime);
                subscribeResultNotifyService.receiveLiantongOrderNotify(jsonData, BizConstant.BIZ_LT_CHANNEL_DEFAULT);
            } else {
                log.warn("联通沃音乐订购异步通知验签失败,ip:{},authorization:{},actionTime:{}", ipAddr, authorization, actionTime);
            }
        } catch (Exception e) {
            log.warn("联通沃音乐订购异步通知异常,ip:{},authorization:{},timestamp:{}", ipAddr, authorization, timestamp, e);
        }

        return "success";
    }

    @ApiOperation(value = "联通沃音乐三方订购异步通知", notes = "联通沃音乐三方订购异步通知")
    @PostMapping(value = "/liantongVrbt/{company}/orderNotify", produces = MediaType.TEXT_PLAIN_VALUE)
    public String liantongVrbtTpOrderNotify(@RequestHeader(value = "Authorization", required = false) String authorization,
                                            @RequestHeader(value = "timestamp", required = false) String timestamp,
                                            @PathVariable String company,
                                            HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            Date actionTime = new Date(Long.parseLong(timestamp));
            log.info("联通沃音乐第三方订购异步通知,company:{}:,ip:{},authorization:{},actionTime:{},jsonData:{}", company, ipAddr, authorization, actionTime, jsonData);
            if (LiantongAuthUtils.validateNotifyOtherSign(authorization, timestamp, jsonData, ipAddr, company)) {
                //log.info("联通沃音乐订购异步通知,ip:{},authorization:{},actionTime:{},校验通过!", ipAddr,authorization,actionTime);
                subscribeResultNotifyService.receiveLiantongOrderNotify(jsonData, company);
            } else {
                log.warn("联通沃音乐第三方订购异步通知验签失败,company:{},ip:{},authorization:{},actionTime:{}", company, ipAddr, authorization, actionTime);
            }
        } catch (Exception e) {
            log.warn("联通沃音乐第三方订购异步通知异常,company:{},ip:{},authorization:{},timestamp:{}", company, ipAddr, authorization, timestamp, e);
        }

        return "success";
    }

    @ApiOperation(value = "联通沃音乐视频彩铃三方支付消息回执", notes = "联通沃音乐视频彩铃三方支付消息回执")
    @RequestMapping(value = "/liantongWoMusic/vrbtNotify")
    public String miguNotifyMemberStatus(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("联通沃音乐视频彩铃三方支付消息回执,参数map:{},json数据:{}", requestMap, jsonNode);
        liantongRingService.vrbtNotify(jsonNode);
        return "success";
    }

    /**
     * 天翼空间计费回调
     *
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/tianyiSpacResultSync")
    @ResponseBody
    public TianyiResult tianyiSpacResultSync(
            //接口版本
            @RequestParam(name = "version", required = false) Integer version,
            //平台订购流水号
            @RequestParam(name = "orderid", required = false) String orderId,
            //手机号
            @RequestParam(name = "phone", required = false) String phone,
            //集团订购流水号
            @RequestParam(name = "correlator", required = false) String correlator,
            //计费结果编码（200为成功，其它均失败）
            @RequestParam(name = "resCode", required = false) String resCode,
            //计费结果说明
            @RequestParam(name = "resDesc", required = false) String resDesc,
            //费用(单位元)
            @RequestParam(name = "fee", required = false) String fee,
            //该参数仅包月订购时必回调，点播订购时无该参数 通知类型（0：退订；1：订购； 2：续订）
            @RequestParam(name = "type", required = false) String type,
            //渠道透传参数
            @RequestParam(name = "ext_data", required = false) String extData,
            //注意：该参数仅包月订购时必回调，点播订购时无该参数 当月是否退订（1：退订；0：其他未退订）
            @RequestParam(name = "unsubFlag", required = false) Integer unsubFlag,
            @RequestParam(name = "sign", required = false) String sign,
            HttpServletRequest request
    ) throws Exception {


        try {
            //String signStr = tianyiSpaceService.createSig(map, tianyiSpaceProperties.getSignKey());

            log.info("天翼空间计费回调:version={},orderid={},phone={},correlator={},resCode={},resDesc={},fee={},type={},ext_data={},unsubFlag={},sign={}",
                    version, orderId, phone, correlator, resCode, resDesc, fee, type, extData, unsubFlag, sign);

            TelecomOrder telecomOrder = new TelecomOrder();
            telecomOrder.setVersion(version);
            telecomOrder.setOrderid(orderId);
            telecomOrder.setPhone(phone);
            telecomOrder.setCorrelator(correlator);
            telecomOrder.setResCode(resCode);
            telecomOrder.setResDesc(resDesc);
            telecomOrder.setFee(fee);
            telecomOrder.setType(type);
            telecomOrder.setExtData(extData);
            telecomOrder.setUnsubFlag(unsubFlag);
            telecomOrder.setSign(sign);
            telecomOrder.setCreateTime(new Date());
            try {
                MobileRegionResult mobileRegionResult = mobileRegionService.query(phone);
                if (mobileRegionResult != null) {
                    telecomOrder.setProvince(mobileRegionResult.getProvince());
                    telecomOrder.setCity(mobileRegionResult.getCity());
                }
            } catch (Exception e) {
                log.error("获取省份错误:", e);
            }
            subscribeResultNotifyService.receiveTianyiResult(orderId, phone, resCode, resDesc, type, new Date(), telecomOrder);
        } catch (Exception e) {
            log.error("电信计费回调错误:", e);
        } finally {
            return TianyiResult.success(phone);
        }
    }


    /**
     * 订购结果通知
     *
     * @param orderResultNotify orderResultNotify
     * @return String
     */
    @ApiOperation("彦成江西移动爱豆订购结果通知")
    @GetMapping("/ycjxadld/orderResultNotify")
    public String orderResultNotify(YCJXADLDOrderResultNotifyDTO orderResultNotify) {
        log.info("彦成江西爱豆来电订购结果通知:{}", orderResultNotify);
        return subscribeResultNotifyService.ycjxadldOrderResultNotify(orderResultNotify);
    }


    //fee=1000, tradeid=c25aa1fbfcb3411e97f94890acf218f2, timestamp=1728455704009, rescode=0, exdata=hkajsd, tel=18968142039, app=126007830001, msg=成功
    @ApiOperation("电信视频彩铃订购结果")
    @GetMapping("/dianxinVrbtNotify")
    public String dianxinVrbtNotify(@RequestParam(name = "fee", required = false) String fee,
                                    @RequestParam(name = "tradeid", required = false) String tradeid,
                                    @RequestParam(name = "timestamp", required = false) String timestamp,
                                    @RequestParam(name = "rescode", required = false) String rescode,
                                    @RequestParam(name = "exdata", required = false) String exdata,
                                    @RequestParam(name = "tel", required = false) String tel,
                                    @RequestParam(name = "app", required = false) String app,
                                    @RequestParam(name = "msg", required = false) String msg) {

        log.info("电信开通结果同步:fee:{},tradeid:{},timestamp:{},rescode:{},exdata:{},tel:{},app:{},msg:{}", fee, tradeid, timestamp, rescode, exdata, tel, app, msg);
        subscribeResultNotifyService.dianxinVrbtNotify(tel, tradeid, rescode, msg);
        return "ok";
    }

    @ApiOperation("任务回调")
    @PostMapping("/notify")
    public void hyTaskCallBack(@RequestBody JsonNode jsonNode) {
        haiYiMediaNotifyService.hyTaskCallBack(jsonNode);
    }

    /**
     * 联通沃阅读破解计费通知
     *
     * @param cporderid
     * @param sid
     * @param time
     * @param fee
     * @param tel
     * @param code
     * @param vaccode
     * @param msg
     * @return
     */
    @GetMapping(value = "/liantong/wydNotify")
    @ResponseBody
    public String wydNotify(@RequestParam(value = "cporderid", required = false, defaultValue = "") String cporderid,
                            @RequestParam(value = "sid", required = false, defaultValue = "") String sid,
                            @RequestParam(value = "time", required = false, defaultValue = "") String time,
                            @RequestParam(value = "fee", required = false, defaultValue = "") String fee,
                            @RequestParam(value = "tel", required = false, defaultValue = "") String tel,
                            @RequestParam(value = "code", required = false, defaultValue = "") String code,
                            @RequestParam(value = "vaccode", required = false, defaultValue = "") String vaccode,
                            @RequestParam(value = "msg", required = false, defaultValue = "") String msg) {
        log.info("联通沃阅读计费通知,异步通知=>cporderid:{},sid:{},time:{},fee:{},tel:{},code:{},vaccode:{},msg:{}", cporderid, sid, time, fee, tel, code, vaccode, msg);
        subscribeResultNotifyService.liantongWydNotify(tel, sid);
        return "ok";
    }

    /**
     * 微信获客助手回调通知
     *
     * @param msgSignature
     * @param timestamp
     * @param nonce
     * @param echostr
     * @param xmlData
     * @return
     */
    @SneakyThrows
    @RequestMapping(value = "/huoke/callback", produces = MediaType.APPLICATION_XML_VALUE)
    public String huokeCallbak(@RequestParam(name = "msg_signature", required = false) String msgSignature,
                               @RequestParam(name = "timestamp", required = false) String timestamp,
                               @RequestParam(name = "nonce", required = false) String nonce,
                               @RequestParam(name = "echostr", required = false) String echostr,
                               @RequestBody(required = false) String xmlData,
                               HttpServletRequest request) {

        log.info("获客助手回调通知:msg_signature:{},timestamp:{},nonce:{},echostr:{},xmlData:{}", msgSignature, timestamp, nonce, echostr, xmlData);

        String sToken = "qxCq9Wy83AAXAQc3DdM";
        String sCorpID = "wwbf471723c2afa55a";
        String sEncodingAESKey = "1J2TlrGG6VVJBqdlSo3FW0z18w5grNoTTAzoVa6R3zW";

        WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(sToken, sEncodingAESKey, sCorpID);
		/*
		------------使用示例一：验证回调URL---------------
		*企业开启回调模式时，企业微信会向验证url发送一个get请求
		假设点击验证时，企业收到类似请求：
		* GET /cgi-bin/wxpush?msg_signature=5c45ff5e21c57e6ad56bac8758b79b1d9ac89fd3&timestamp=1409659589&nonce=263014780&echostr=P9nAzCzyDtyTWESHep1vC5X9xho%2FqYX3Zpb4yKa9SKld1DsH3Iyt3tP3zNdtp%2B4RPcs8TgAE7OaBO%2BFZXvnaqQ%3D%3D
		* HTTP/1.1 Host: qy.weixin.qq.com

		接收到该请求时，企业应
		1.解析出Get请求的参数，包括消息体签名(msg_signature)，时间戳(timestamp)，随机数字串(nonce)以及企业微信推送过来的随机加密字符串(echostr),
		这一步注意作URL解码。
		2.验证消息体签名的正确性
		3. 解密出echostr原文，将原文当作Get请求的response，返回给企业微信
		第2，3步可以用企业微信提供的库函数VerifyURL来实现。

		*/

        /*
		------------使用示例二：对用户回复的消息解密---------------
		用户回复消息或者点击事件响应时，企业会收到回调消息，此消息是经过企业微信加密之后的密文以post形式发送给企业，密文格式请参考官方文档
		假设企业收到企业微信的回调消息如下：
		POST /cgi-bin/wxpush? msg_signature=477715d11cdb4164915debcba66cb864d751f3e6&timestamp=1409659813&nonce=1372623149 HTTP/1.1
		Host: qy.weixin.qq.com
		Content-Length: 613
		<xml>		<ToUserName><![CDATA[wx5823bf96d3bd56c7]]></ToUserName><Encrypt><![CDATA[RypEvHKD8QQKFhvQ6QleEB4J58tiPdvo+rtK1I9qca6aM/wvqnLSV5zEPeusUiX5L5X/0lWfrf0QADHHhGd3QczcdCUpj911L3vg3W/sYYvuJTs3TUUkSUXxaccAS0qhxchrRYt66wiSpGLYL42aM6A8dTT+6k4aSknmPj48kzJs8qLjvd4Xgpue06DOdnLxAUHzM6+kDZ+HMZfJYuR+LtwGc2hgf5gsijff0ekUNXZiqATP7PF5mZxZ3Izoun1s4zG4LUMnvw2r+KqCKIw+3IQH03v+BCA9nMELNqbSf6tiWSrXJB3LAVGUcallcrw8V2t9EL4EhzJWrQUax5wLVMNS0+rUPA3k22Ncx4XXZS9o0MBH27Bo6BpNelZpS+/uh9KsNlY6bHCmJU9p8g7m3fVKn28H3KDYA5Pl/T8Z1ptDAVe0lXdQ2YoyyH2uyPIGHBZZIs2pDBS8R07+qN+E7Q==]]></Encrypt>
		<AgentID><![CDATA[218]]></AgentID>
		</xml>

		企业收到post请求之后应该		1.解析出url上的参数，包括消息体签名(msg_signature)，时间戳(timestamp)以及随机数字串(nonce)
		2.验证消息体签名的正确性。
		3.将post请求的数据进行xml解析，并将<Encrypt>标签的内容进行解密，解密出来的明文即是用户回复消息的明文，明文格式请参考官方文档
		第2，3步可以用企业微信提供的库函数DecryptMsg来实现。
		*/

        /*
		------------使用示例三：企业回复用户消息的加密---------------
		企业被动回复用户的消息也需要进行加密，并且拼接成密文格式的xml串。
		假设企业需要回复用户的明文如下：
		<xml>
		<ToUserName><![CDATA[mycreate]]></ToUserName>
		<FromUserName><![CDATA[wx5823bf96d3bd56c7]]></FromUserName>
		<CreateTime>1348831860</CreateTime>
		<MsgType><![CDATA[text]]></MsgType>
		<Content><![CDATA[this is a test]]></Content>
		<MsgId>1234567890123456</MsgId>
		<AgentID>128</AgentID>
		</xml>

		为了将此段明文回复给用户，企业应：			1.自己生成时间时间戳(timestamp),随机数字串(nonce)以便生成消息体签名，也可以直接用从企业微信的post url上解析出的对应值。
		2.将明文加密得到密文。	3.用密文，步骤1生成的timestamp,nonce和企业在企业微信设定的token生成消息体签名。			4.将密文，消息体签名，时间戳，随机数字串拼接成xml格式的字符串，发送给企业。
		以上2，3，4步可以用企业微信提供的库函数EncryptMsg来实现。
		*/

        if ("GET".equalsIgnoreCase(request.getMethod())) {
            return wxcpt.VerifyURL(msgSignature, timestamp, nonce, echostr);
        } else {
            String sMsg = wxcpt.DecryptMsg(msgSignature, timestamp, nonce, xmlData);
            log.info("获客助手回调通知解密后数据:{} ", sMsg);
            //响应示例
            /* 添加联系人事件
             <xml>
                <ToUserName><![CDATA[wwbf471723c2afa55a]]></ToUserName>
                <FromUserName><![CDATA[sys]]></FromUserName>
                <CreateTime>1730337620</CreateTime>
                <MsgType><![CDATA[event]]></MsgType>
                <Event><![CDATA[change_external_contact]]></Event>
                <ChangeType><![CDATA[add_external_contact]]></ChangeType>
                <UserID><![CDATA[GaZi]]></UserID>
                <ExternalUserID><![CDATA[wm5LsYRgAA4rLlKLztlS9xqw5-DKN8ow]]></ExternalUserID>
                <State><![CDATA[4d0bbedf-4eab-4366-8294-adcfc74d9b15]]></State>
                <WelcomeCode><![CDATA[2-kpZCYYLAWrj0luNQVHkafXtvjWgh86SeBswq3Bzss]]></WelcomeCode>
            </xml>
             */

            /* 通过获客链接发起好友请求事件
            <xml>
                <ToUserName><![CDATA[wwbf471723c2afa55a]]></ToUserName>
                <FromUserName><![CDATA[sys]]></FromUserName>
                <CreateTime>1730337617</CreateTime>
                <MsgType><![CDATA[event]]></MsgType>
                <Event><![CDATA[customer_acquisition]]></Event>
                <ChangeType><![CDATA[friend_request]]></ChangeType>
                <LinkId><![CDATA[cawcdedd35029b2bb7]]></LinkId>
                <State><![CDATA[4d0bbedf-4eab-4366-8294-adcfc74d9b15]]></State>
            </xml>

             */

            JsonNode dataNode = xmlMapper.readTree(sMsg);
            String linkId = dataNode.at("/LinkId").asText();
            String customerChannel = dataNode.at("/State").asText();
            String changeType = dataNode.at("/ChangeType").asText();
            String userId = dataNode.at("/UserID").asText();
            String event = dataNode.at("/Event").asText();
//            //通过获客链接发起好友请求事件
            if ("friend_request".equals(changeType) && "customer_acquisition".equals(event)) {
                qywxService.clickLinkAddContactNotify(linkId, customerChannel);
            }
            //添加联系人事件
            if ("add_external_contact".equals(changeType) && "change_external_contact".equals(event)) {
                qywxService.addContactNotify(userId, customerChannel);
                EsQywxAction qywxAction = new EsQywxAction();
                qywxAction.setUserId(userId);
                qywxAction.setAction("ADD");
                qywxAction.setCreateTime(new Date());
                qywxAction.setFmtCreateTime(LocalDateTime.now());
                esDataService.saveEsQywxAction(qywxAction);
            }
            //删除联系人事件
            if ("del_follow_user".equals(changeType) && "change_external_contact".equals(event)) {
                EsQywxAction qywxAction = new EsQywxAction();
                qywxAction.setUserId(userId);
                qywxAction.setAction("DEL");
                qywxAction.setCreateTime(new Date());
                qywxAction.setFmtCreateTime(LocalDateTime.now());
                esDataService.saveEsQywxAction(qywxAction);
            }
            return "OK";
        }
    }

    /**
     * 视宣号精英版订购结果通知
     *
     * @param jsonNode jsonNode
     * @return String
     */
    @PostMapping("/notify/sxhjyb")
    public String schjyb(@RequestBody JsonNode jsonNode) {
        log.info("视宣号精英版订购结果通知：{}", jsonNode);
        return "OK";
    }

    /**
     * 欣网发财树-当日未回调的收益数据通知
     *
     * @param jsonNode jsonNode
     * @return String
     */
    @PostMapping("/notify/xwfcs")
    public String xwfcsNotify(@RequestBody JsonNode jsonNode) throws ParseException {
        log.info("欣网发财树-当日未回调的收益数据通知：{}", jsonNode);

        List<CmsNotifyXwfcs> data = new ArrayList<>();
        JsonNode list = jsonNode.at("/list");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (JsonNode node : list) {
            String isp = node.at("/isp").asText();
            String orderTime = node.at("/orderTime").asText();
            String phone = node.at("/phone").asText();
            String pointNum = node.at("/pointNum").asText();
            String province = node.at("/province").asText();
            String sessionId = node.at("/sessionId").asText();

            // 判断数据是否从入口页进来，入口页进来会先生成订单，若订单存在则修改订单状态
            Subscribe one = subscribeService.getById(sessionId);
            if (one != null) {
                // 修改订单表
                Subscribe subscribe = new Subscribe();
                subscribe.setId(sessionId);
                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                subscribe.setResult("下单成功");
                subscribe.setOpenTime(sdf.parse(orderTime));
                subscribeService.updateSubscribeDbAndEs(subscribe);
                // 上报广告平台
                channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
            }

            // 记录原始信息
            CmsNotifyXwfcs cmsNotifyXwfcs = new CmsNotifyXwfcs();
            cmsNotifyXwfcs.setIsp(isp);
            cmsNotifyXwfcs.setOrdertime(sdf.parse(orderTime));
            cmsNotifyXwfcs.setPhone(phone);
            cmsNotifyXwfcs.setVPhone(new String(Base64Utils.decodeFromString(phone)));
            cmsNotifyXwfcs.setPointnum(pointNum);
            cmsNotifyXwfcs.setProvince(province);
            cmsNotifyXwfcs.setSessionid(sessionId);
            data.add(cmsNotifyXwfcs);
        }
        iCmsNotifyXwfcsService.saveBatch(data);
        return "OK";
    }
}
