package com.eleven.cms.controller;

import com.aliyuncs.utils.StringUtils;
import com.eleven.cms.config.PPTVConfigProperties;
import com.eleven.cms.config.PPTVRechargeMap;
import com.eleven.cms.dto.PptvApiResult;
import com.eleven.cms.entity.PptvGiveLog;
import com.eleven.cms.service.IPPTVApiService;
import com.eleven.cms.service.IPptvGiveLogService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.MD5Util;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * pptv充值 Controller
 */
@Api(tags = "pptv充值功能")
@RestController
@RequestMapping("/api/pptv")
@Slf4j
@Validated
public class PPTVRechargeController {
    private static final Interner<String> interner = Interners.newWeakInterner();
    @Autowired
    private IPPTVApiService pptvApiService;
    @Autowired
    private PPTVConfigProperties pptvConfigProperties;
    @Autowired
    IPptvGiveLogService pptvGiveLogService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @ApiOperation(value = "订单充值", notes = "订单充值")
    @PostMapping(value = "/recharge")
    @ResponseBody
    public Result<?> pptvRecharge(@RequestBody PptvApiResult pptvApiResult, HttpServletRequest request){
        log.info("pptv,订单充值,第三方订单充值,请求=>pptv:{}",pptvApiResult);
        //加密数据
        String sign = request.getHeader("sign");
        if(StringUtils.isEmpty(sign) || StringUtils.isEmpty(pptvApiResult.getServiceId()) || StringUtils.isEmpty(pptvApiResult.getSubmitTime())){
            return Result.error("参数错误！");
        }
        if(StringUtils.isEmpty(pptvApiResult.getMobile())){
            return Result.error("请输入订购手机号！");
        }
        if(StringUtils.isEmpty(pptvApiResult.getOrderId())){
            return Result.error("请输入渠道订单号码！");
        }
        final PPTVRechargeMap pptvRechargeMap = pptvConfigProperties.getPptvRechargeMap().get(pptvApiResult.getServiceId());
        if(pptvRechargeMap==null){
            return Result.error("渠道业务ID错误！");
        }
        try {
            String json = mapper.writeValueAsString(pptvApiResult);
            Map<String, Object> map= mapper.readValue(json, Map.class);
            String ourSign = MD5Util.getSign(map,pptvRechargeMap.getKey());
            if(!StringUtil.equals(sign,ourSign)){
                return Result.error("签名错误！");
            }
            log.info("pptv充值,sign:{},ourSign:{},pptvApiResult:{}",sign,ourSign,pptvApiResult);
            if(!StringUtil.equals(sign,ourSign)){
                return Result.error("验签失败！");
            }
            synchronized (interner.intern(pptvApiResult.getMobile())) {
                String pptvMonth = DateUtil.formatYearMonth(LocalDateTime.now());
                Integer count=pptvGiveLogService.lambdaQuery()
                        .eq(PptvGiveLog::getAccount,pptvApiResult.getMobile())
                        .eq(PptvGiveLog::getServiceId,pptvApiResult.getServiceId())
                        .eq(PptvGiveLog::getPptvMonth,pptvMonth)
                        .eq(PptvGiveLog::getStatus,BizConstant.PPTV_GIVE_STATUS_SUCCESS)
                        .count();
                if(count<=0){
                    return pptvApiService.pptvRightsRecharge(pptvApiResult.getMobile(), pptvApiResult.getOrderId(),pptvApiResult.getServiceId(),pptvApiResult.getServiceId());
                }else{
                    return Result.error("本月PP会员已赠送！");
                }
            }
        } catch (Exception e) {
            log.error("pptv,发送会员权益,第三方充值,异常信息=>pptv:{},msg:{},error:{}",pptvApiResult,e.getMessage(),e);
            return Result.error("参数错误！");
        }
    }


    @ApiOperation(value = "订单查询", notes = "订单查询")
    @PostMapping(value = "/order/query")
    @ResponseBody
    public Result<?> pptvRightsQuery(@RequestBody PptvApiResult pptvApiResult, HttpServletRequest request){
        log.info("pptv,订单查询,第三方订单查询,请求=>pptv:{}",pptvApiResult);
        //加密数据
        String sign = request.getHeader("sign");
        if(StringUtils.isEmpty(sign) || StringUtils.isEmpty(pptvApiResult.getServiceId())){
            return Result.error("参数错误！");
        }
        if(StringUtils.isEmpty(pptvApiResult.getMobile())){
            return Result.error("请输入订购手机号！");
        }
        if(StringUtils.isEmpty(pptvApiResult.getOrderId())){
            return Result.error("请输入渠道订单号码！");
        }
        final PPTVRechargeMap pptvRechargeMap = pptvConfigProperties.getPptvRechargeMap().get(pptvApiResult.getServiceId());
        if(pptvRechargeMap==null){
            return Result.error("渠道业务ID错误！");
        }
        try {
            String json = mapper.writeValueAsString(pptvApiResult);
            Map<String, Object> map= mapper.readValue(json, Map.class);
            String ourSign = MD5Util.getSign(map,pptvRechargeMap.getKey());
            if(!StringUtil.equals(sign,ourSign)){
                return Result.error("签名错误！");
            }
            log.info("pptv充值,sign:{},ourSign:{},pptvApiResult:{}",sign,ourSign,pptvApiResult);
            if(!StringUtil.equals(sign,ourSign)){
                return Result.error("验签失败！");
            }
            synchronized (interner.intern(pptvApiResult.getMobile())) {
                return pptvApiService.pptvRightsQuery(pptvApiResult);
            }
        } catch (Exception e) {
            log.error("pptv,订单查询,第三方订单查询,异常信息=>pptv:{},msg:{},error:{}",pptvApiResult,e.getMessage(),e);
            return Result.error("参数错误！");
        }
    }

}
