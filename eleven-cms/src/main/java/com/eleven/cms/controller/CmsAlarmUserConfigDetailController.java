package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CmsAlarmUserConfigDetail;
import com.eleven.cms.service.ICmsAlarmUserConfigDetailService;
import com.eleven.cms.service.ICmsAlarmUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: cms_alarm_user_config_detail
 * @Author: jeecg-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
@Api(tags="cms_alarm_user_config_detail")
@RestController
@RequestMapping("/cms/cmsAlarmUserConfigDetail")
@Slf4j
public class CmsAlarmUserConfigDetailController extends JeecgController<CmsAlarmUserConfigDetail, ICmsAlarmUserConfigDetailService> {
	@Autowired
	private ICmsAlarmUserConfigDetailService cmsAlarmUserConfigDetailService;
	@Autowired
	private ICmsAlarmUserService cmsAlarmUserService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cmsAlarmUserConfigDetail
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config_detail-分页列表查询")
	@ApiOperation(value="cms_alarm_user_config_detail-分页列表查询", notes="cms_alarm_user_config_detail-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CmsAlarmUserConfigDetail cmsAlarmUserConfigDetail,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		return Result.ok(cmsAlarmUserConfigDetailService.queryPageList(new Page<>(pageNo, pageSize) ,cmsAlarmUserConfigDetail));
	}
	
	/**
	 *   添加
	 *
	 * @param cmsAlarmUserConfigDetail
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config_detail-添加")
	@ApiOperation(value="cms_alarm_user_config_detail-添加", notes="cms_alarm_user_config_detail-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CmsAlarmUserConfigDetail cmsAlarmUserConfigDetail) {
		List<CmsAlarmUserConfigDetail> data = new ArrayList<>();

		List<String> alarmUserIds = cmsAlarmUserConfigDetail.getAlarmUserIds();
		if (CollectionUtils.isNotEmpty(alarmUserIds)) {
			for (String alarmUserId : alarmUserIds) {
				CmsAlarmUserConfigDetail entity = new CmsAlarmUserConfigDetail();
				entity.setAlarmUserConfigId(cmsAlarmUserConfigDetail.getAlarmUserConfigId());
				entity.setAlarmUserId(alarmUserId);
				entity.setEnableSms(cmsAlarmUserConfigDetail.getEnableSms());
				entity.setEnableVx(cmsAlarmUserConfigDetail.getEnableVx());
				data.add(entity);
			}
		}
		cmsAlarmUserConfigDetailService.saveBatch(data);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cmsAlarmUserConfigDetail
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config_detail-编辑")
	@ApiOperation(value="cms_alarm_user_config_detail-编辑", notes="cms_alarm_user_config_detail-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody CmsAlarmUserConfigDetail cmsAlarmUserConfigDetail) {
		cmsAlarmUserConfigDetailService.updateById(cmsAlarmUserConfigDetail);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config_detail-通过id删除")
	@ApiOperation(value="cms_alarm_user_config_detail-通过id删除", notes="cms_alarm_user_config_detail-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		cmsAlarmUserConfigDetailService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config_detail-批量删除")
	@ApiOperation(value="cms_alarm_user_config_detail-批量删除", notes="cms_alarm_user_config_detail-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cmsAlarmUserConfigDetailService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config_detail-通过id查询")
	@ApiOperation(value="cms_alarm_user_config_detail-通过id查询", notes="cms_alarm_user_config_detail-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CmsAlarmUserConfigDetail cmsAlarmUserConfigDetail = cmsAlarmUserConfigDetailService.getById(id);
		if(cmsAlarmUserConfigDetail==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(cmsAlarmUserConfigDetail);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmsAlarmUserConfigDetail
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsAlarmUserConfigDetail cmsAlarmUserConfigDetail) {
        return super.exportXls(request, cmsAlarmUserConfigDetail, CmsAlarmUserConfigDetail.class, "cms_alarm_user_config_detail");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CmsAlarmUserConfigDetail.class);
    }

}
