package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AccountCofig;
import com.eleven.cms.service.IAccountCofigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 账号配置表 Controller
 *
 * <AUTHOR>
 * @date 2021-06-02 16:57:09
 */
@Api(tags="account_cofig")
@RestController
@RequestMapping("/cms/accountcofig")
@Slf4j
public class AccountCofigController extends JeecgController<AccountCofig, IAccountCofigService> {

    @Autowired
    private IAccountCofigService accountCofigService;

    /**
     * 分页列表查询
     *
     * @param accountCofig
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    ////@AutoLog(value = "cms_AccountCofig-分页列表查询")
    @ApiOperation(value="cms_AccountCofig-分页列表查询", notes="cms_AccountCofig-分页列表查询")
    @GetMapping(value = "/pageList")
    public Result<?> queryPageList(AccountCofig accountCofig,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<AccountCofig> queryWrapper = QueryGenerator.initQueryWrapper(accountCofig, req.getParameterMap());
        Page<AccountCofig> page = new Page<AccountCofig>(pageNo, pageSize);
        IPage<AccountCofig> pageList = accountCofigService.findByPage(page, queryWrapper);
        return Result.ok(pageList);
    }
    /**
     * 查询
     *
     * @param accountCofig
     * @param
     * @return
     */
    ////@AutoLog(value = "cms_AccountCofig")
    @ApiOperation(value="cms_AccountCofig", notes="cms_AccountCofig")
    @PostMapping(value = "/list")
    public Result<?> queryPageList(AccountCofig accountCofig) {
        List<AccountCofig> list = accountCofigService.findAccountCofigs(accountCofig);
        return Result.ok(list);
    }
    /**
     * 查询
     *
     * @param
     * @param
     * @return
     */
    ////@AutoLog(value = "cms_AccountCofig")
    @ApiOperation(value="cms_AccountCofig", notes="cms_AccountCofig")
    @PostMapping(value = "/findByAdPlatformId")
    public Result<?> findByAdPlatformId(@RequestBody  AccountCofig accountCofig) {
        List<AccountCofig> list = accountCofigService.findByAdPlatformId(accountCofig);
        return Result.ok(list);
    }

    /**
     *   添加
     *
     * @param AccountCofig
     * @return
     */
    ////@AutoLog(value = "cms_AccountCofig-添加")
    @ApiOperation(value="cms_AccountCofig-添加", notes="cms_AccountCofig-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody AccountCofig AccountCofig) {
        accountCofigService.save(AccountCofig);
        return Result.ok("添加成功！");
    }


    /**
     *  编辑
     *
     * @param AccountCofig
     * @return
     */
    ////@AutoLog(value = "cms_AccountCofig-编辑")
    @ApiOperation(value="cms_AccountCofig-编辑", notes="cms_AccountCofig-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody AccountCofig AccountCofig) {
        accountCofigService.updateById(AccountCofig);
        return Result.ok("编辑成功!");
    }

    /**
     *   通过id删除
     *
     * @param id
     * @return
     */
    ////@AutoLog(value = "cms_AccountCofig-通过id删除")
    @ApiOperation(value="cms_AccountCofig-通过id删除", notes="cms_AccountCofig-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        accountCofigService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     *  批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "cms_AccountCofig-批量删除")
    @ApiOperation(value="cms_AccountCofig-批量删除", notes="cms_AccountCofig-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.accountCofigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_AccountCofig-通过id查询")
    @ApiOperation(value="cms_AccountCofig-通过id查询", notes="cms_AccountCofig-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        AccountCofig AccountCofig = accountCofigService.getById(id);
        if(AccountCofig==null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(AccountCofig);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param AccountCofig
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AccountCofig AccountCofig) {
        return super.exportXls(request, AccountCofig, AccountCofig.class, "cms_AccountCofig");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AccountCofig.class);
    }

}
