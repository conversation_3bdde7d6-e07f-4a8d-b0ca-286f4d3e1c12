package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.BusinessPack;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.MiguPack;
import com.eleven.cms.service.IBusinessPackService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.IMiguPackService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Arrays;

/**
 * @Description: 俊博直充记录
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Api(tags="俊博直充记录")
@RestController
@RequestMapping("/cms/junboChargeLog")
@Slf4j
public class JunboChargeLogController extends JeecgController<JunboChargeLog, IJunboChargeLogService> {
	@Autowired
	private IJunboChargeLogService junboChargeLogService;
	@Autowired
	private IMiguPackService miguPackService;
	/**
	 * 分页列表查询
	 *
	 * @param junboChargeLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "俊博直充记录-分页列表查询")
	@ApiOperation(value="俊博直充记录-分页列表查询", notes="俊博直充记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(JunboChargeLog junboChargeLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

		QueryWrapper<JunboChargeLog> queryWrapper = QueryGenerator.initQueryWrapper(junboChargeLog, req.getParameterMap());
		if(!req.getParameterMap().containsKey("createTime_begin") || req.getParameterMap().get("createTime_begin")==null){
			queryWrapper.between("create_time",DateUtil.formatSplitTime(DateUtil.dayMinTime(LocalDate.now())),DateUtil.localDateTimeToDate(DateUtil.getLastDayOfMonthWithMaxTime()));
		}
		Page<JunboChargeLog> page = new Page<JunboChargeLog>(pageNo, pageSize);
		IPage<JunboChargeLog> pageList = junboChargeLogService.page(page, queryWrapper);
		if(pageList.getRecords()!=null && !pageList.getRecords().isEmpty()){
			pageList.getRecords().forEach(item -> {
				MiguPack miguPack=miguPackService.lambdaQuery().select(MiguPack::getTitleName).eq(MiguPack::getServiceId,item.getServiceId()).orderByDesc(MiguPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
				item.setServiceName(miguPack!=null?miguPack.getTitleName():"");
			});
		}
		return Result.ok(pageList);
	}






	/**
	 *   添加
	 *
	 * @param junboChargeLog
	 * @return
	 */
	//@AutoLog(value = "俊博直充记录-添加")
	@ApiOperation(value="俊博直充记录-添加", notes="俊博直充记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody JunboChargeLog junboChargeLog) {
		junboChargeLogService.save(junboChargeLog);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param junboChargeLog
	 * @return
	 */
	//@AutoLog(value = "俊博直充记录-编辑")
	@ApiOperation(value="俊博直充记录-编辑", notes="俊博直充记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody JunboChargeLog junboChargeLog) {
		junboChargeLogService.updateById(junboChargeLog);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "俊博直充记录-通过id删除")
	@ApiOperation(value="俊博直充记录-通过id删除", notes="俊博直充记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		junboChargeLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "俊博直充记录-批量删除")
	@ApiOperation(value="俊博直充记录-批量删除", notes="俊博直充记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.junboChargeLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "俊博直充记录-通过id查询")
	@ApiOperation(value="俊博直充记录-通过id查询", notes="俊博直充记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		JunboChargeLog junboChargeLog = junboChargeLogService.getById(id);
		if(junboChargeLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(junboChargeLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param junboChargeLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, JunboChargeLog junboChargeLog) {
        return super.exportXls(request, junboChargeLog, JunboChargeLog.class, "俊博直充记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, JunboChargeLog.class);
    }
	 /**
	  *   手动充值会员权益记录-添加
	  *
	  * @param junboChargeLog
	  * @return
	  */
	 //@AutoLog(value = "手动充值会员权益记录-添加")
	 @ApiOperation(value="手动充值会员权益记录-添加", notes="手动充值会员权益记录-添加")
	 @PostMapping(value = "/manualRecharge")
	 public Result<?> manualRecharge(@RequestBody JunboChargeLog junboChargeLog) {
		 log.info("手动充值会员权益记录-添加=>junboChargeLog:{}",junboChargeLog);
		 junboChargeLogService.manualRecharge(junboChargeLog);
		 return Result.ok("添加成功！");
	 }

	/**
	 * 更新酷狗续订
	 * @return
	 */
	//@AutoLog(value = "更新酷狗续订")
	@ApiOperation(value="更新酷狗续订", notes="更新酷狗续订")
	@PostMapping(value = "/updateKuGouStock")
	public Result<?> updateKuGouStock() {
		log.info("更新酷狗续订开始");
		junboChargeLogService.updateKuGouStock();
		return Result.ok("更新酷狗续订完成！");
	}

	/**
	 * 酷狗续订权益发送
	 * @return
	 */
	//@AutoLog(value = "酷狗续订权益发送")
	@ApiOperation(value="酷狗续订权益发送", notes="酷狗续订权益发送")
	@PostMapping(value = "/kuGouRightsSend")
	public Result<?> kuGouRightsSend() {
		log.info("酷狗续订权益发送");
		junboChargeLogService.kuGouRightsSend();
		return Result.ok("酷狗续订权益发送完成！");
	}
}
