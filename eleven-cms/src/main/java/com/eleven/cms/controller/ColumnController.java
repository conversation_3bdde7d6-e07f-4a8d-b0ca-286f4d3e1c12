package com.eleven.cms.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.entity.ColumnMusicVo;
import com.eleven.cms.entity.Music;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.HttpUtil;
import org.jeecg.common.util.oss.OssBootUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.ColumnMusic;
import com.eleven.cms.entity.Column;
import com.eleven.cms.vo.ColumnPage;
import com.eleven.cms.service.IColumnService;
import com.eleven.cms.service.IColumnMusicService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 栏目
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
@Api(tags="栏目")
@RestController
@RequestMapping("/cms/column")
@Slf4j
public class ColumnController {
	@Autowired
	private IColumnService columnService;
	@Autowired
	private IColumnMusicService columnMusicService;
	
	/**
	 * 分页列表查询
	 *
	 * @param column
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "栏目-分页列表查询")
	@ApiOperation(value="栏目-分页列表查询", notes="栏目-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(Column column,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<Column> queryWrapper = QueryGenerator.initQueryWrapper(column, req.getParameterMap());
		Page<Column> page = new Page<Column>(pageNo, pageSize);
		IPage<Column> pageList = columnService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	 /**
	  * 分页列表查询(新)
	  * 增加排序
	  * @param column
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "栏目-分页列表查询")
	 @ApiOperation(value="栏目-分页列表查询", notes="栏目-分页列表查询")
	 @GetMapping(value = "/listNew")
	 public Result<?> queryPageListNew(Column column,
									@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
									@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
									HttpServletRequest req) {
		 QueryWrapper<Column> queryWrapper = QueryGenerator.initQueryWrapper(column, req.getParameterMap());
		 Page<Column> page = new Page<Column>(pageNo, pageSize);
		 Map<String, String[]> parameterMap = req.getParameterMap();
		 IPage<Column> pageList = columnService.pageNew(page, queryWrapper,parameterMap);
		 return Result.ok(pageList);
	 }

	
	/**
	 *   添加
	 *
	 * @param columnPage
	 * @return
	 */
	//@AutoLog(value = "栏目-添加")
	@ApiOperation(value="栏目-添加", notes="栏目-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ColumnPage columnPage) {
		Column column = new Column();
		BeanUtils.copyProperties(columnPage, column);
		columnService.saveMain(column, columnPage.getColumnMusicList());
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param columnPage
	 * @return
	 */
	//@AutoLog(value = "栏目-编辑")
	@ApiOperation(value="栏目-编辑", notes="栏目-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ColumnPage columnPage) {
		Column column = new Column();
		BeanUtils.copyProperties(columnPage, column);
		Column columnEntity = columnService.getById(column.getId());
		if(columnEntity==null) {
			return Result.error("未找到对应数据");
		}
		columnService.updateMain(column, columnPage.getColumnMusicList());
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "栏目-通过id删除")
	@ApiOperation(value="栏目-通过id删除", notes="栏目-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		columnService.delMain(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "栏目-批量删除")
	@ApiOperation(value="栏目-批量删除", notes="栏目-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.columnService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "栏目-通过id查询")
	@ApiOperation(value="栏目-通过id查询", notes="栏目-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		Column column = columnService.getById(id);
		if(column==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(column);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "栏目歌曲通过主表ID查询")
	@ApiOperation(value="栏目歌曲主表ID查询", notes="栏目歌曲-通主表ID查询")
	@GetMapping(value = "/queryColumnMusicByMainId")
	public Result<?> queryColumnMusicListByMainId(@RequestParam(name="id",required=true) String id) {
		List<ColumnMusic> columnMusicList = columnMusicService.selectByMainId(id);
		return Result.ok(columnMusicList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param column
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Column column) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<Column> queryWrapper = QueryGenerator.initQueryWrapper(column, request.getParameterMap());
      LoginUser sysUser = HttpUtil.getCurrUser();

      //Step.2 获取导出数据
      List<Column> queryList = columnService.list(queryWrapper);
      // 过滤选中数据
      String selections = request.getParameter("selections");
      List<Column> columnList = new ArrayList<Column>();
      if(oConvertUtils.isEmpty(selections)) {
          columnList = queryList;
      }else {
          List<String> selectionList = Arrays.asList(selections.split(","));
          columnList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
      }

      // Step.3 组装pageList
      List<ColumnPage> pageList = new ArrayList<ColumnPage>();
      for (Column main : columnList) {
          ColumnPage vo = new ColumnPage();
          BeanUtils.copyProperties(main, vo);
          List<ColumnMusic> columnMusicList = columnMusicService.selectByMainId(main.getId());
          vo.setColumnMusicList(columnMusicList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "栏目列表");
      mv.addObject(NormalExcelConstants.CLASS, ColumnPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("栏目数据", "导出人:"+sysUser.getRealname(), "栏目"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          MultipartFile file = entity.getValue();// 获取上传文件对象
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<ColumnPage> list = ExcelImportUtil.importExcel(file.getInputStream(), ColumnPage.class, params);
              for (ColumnPage page : list) {
                  Column po = new Column();
                  BeanUtils.copyProperties(page, po);
                  columnService.saveMain(po, page.getColumnMusicList());
              }
              return Result.ok("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.ok("文件导入失败！");
    }
	 /**
	  * 导出歌曲excel
	  *
	  * @param request
	  * @param id
	  */
	 @RequestMapping(value = "/exportMusic")
	 public ModelAndView exportMusic(HttpServletRequest request, String id) {

		 LoginUser sysUser = HttpUtil.getCurrUser();
		 //Step.1 获取导出数据
		 List<ColumnMusicVo> columnMusicList = columnMusicService.selectByMainIdNew(id);

		 // Step.2 AutoPoi 导出Excel
		 ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		 mv.addObject(NormalExcelConstants.FILE_NAME, "歌曲列表");
		 mv.addObject(NormalExcelConstants.CLASS, ColumnMusicVo.class);
		 mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("歌曲数据", "导出人:"+sysUser.getRealname(), "歌曲"));
		 mv.addObject(NormalExcelConstants.DATA_LIST, columnMusicList);
		 return mv;
	 }
	 /**
	  * 通过excel导入歌曲
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @RequestMapping(value = "/importMusic", method = RequestMethod.POST)
	 public Result<?> importMusic(HttpServletRequest request, HttpServletResponse response,String id) {
		 MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		 Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		 for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			 MultipartFile file = entity.getValue();// 获取上传文件对象
			 ImportParams params = new ImportParams();
			 params.setTitleRows(2);
			 params.setHeadRows(1);
			 params.setNeedSave(true);
			 try {
				 List<ColumnMusicVo> list = ExcelImportUtil.importExcel(file.getInputStream(), ColumnMusicVo.class, params);
				 columnMusicService.importMusic(list,id);
				 return Result.ok("文件导入成功！数据行数:" + list.size());
			 } catch (Exception e) {
				 log.error(e.getMessage(),e);
				 return Result.error("文件导入失败:"+e.getMessage());
			 } finally {
				 try {
					 file.getInputStream().close();
				 } catch (IOException e) {
					 e.printStackTrace();
				 }
			 }
		 }
		 return Result.ok("文件导入失败！");
	 }


	 /**
	  * 复制栏目歌曲关联关系
	  *
	  * @return
	  */
	 //@AutoLog(value = "复制栏目歌曲关联关系")
	 @ApiOperation(value="复制栏目歌曲关联关系", notes="复制栏目歌曲关联关系")
	 @GetMapping(value = "/copy")
	 public Result<?> copy(String oldColumnClassName,String newColumnClassName) {
		 columnService.copy(oldColumnClassName,newColumnClassName);
		 return Result.ok("复制成功");
	 }
	 /*
	  *  图片上传
	  *
	  * */
	 @ResponseBody
	 @RequestMapping("/upload")
	 public Result<?> upload(MultipartFile file){
		 Result<?> result = new Result<>();
		 String url = "";
		 try{
			 if(file!=null) {
				 url = OssBootUtil.upload(file,"column/");
				 if(oConvertUtils.isNotEmpty(url)){
					 result.setMessage(url);
					 result.setSuccess(true);
				 }else {
					 result.setMessage("上传失败！");
					 result.setSuccess(false);
				 }
			 }
		 }catch (Exception e){
			 log.error("上传失败：",e);
		 }
		 return result;
	 }
}
