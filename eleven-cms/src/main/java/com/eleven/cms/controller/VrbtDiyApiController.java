package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.VrbtDiyRing;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.SubscribeResultNotifyService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.qycl.config.EnterpriseVrbtProperties;
import com.eleven.qycl.config.TtsProperties;
import com.eleven.qycl.entity.*;
import com.eleven.qycl.service.*;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @author: cai lei
 * @create: 2022-11-09 15:56
 */
@Api(tags = "vrbt_diy_api")
@RestController
@RequestMapping("/vrbtdiy/api")
@Slf4j
public class VrbtDiyApiController {
    private static final Interner<String> interner = Interners.newWeakInterner();

    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IVrbtDiyRingService vrbtDiyRingService;
    @Autowired
    ISmsModelService smsModelService;
    @Autowired
    IMemberService memberService;
    @Autowired
    ISmsValidateService smsValidateService;


    public String getCompanyOwner(HttpServletRequest request) {
        String companyOwner = request.getHeader(QyclConstant.QYCL_COMPANY_OWNER_HEAD);
        if (StringUtils.isEmpty(companyOwner)) {
            companyOwner = QyclConstant.QYCL_COMPANY_OWNER_YRJY;
        }
        return companyOwner;
    }

    //    @ApiOperation(value = "生成铃音文本", notes = "生成铃音文本")
//    @RequestMapping(value = "/createRingTxt")
//    public Result<?> createRingTxt(@RequestParam(name = "title") String title,
//                                   String companyTitle,
//                                   HttpServletRequest req) {
//        log.info("title:{},companyTitle:{}", title, companyTitle);
//        QyclIndustryTemplate qyclIndustryTemplate = qyclIndustryTemplateService.createRingTxt(companyTitle, title);
//        return Result.ok(qyclIndustryTemplate);
//    }
//
//    @ApiOperation(value = "查询行业名称", notes = "查询行业名称")
//    @RequestMapping(value = "/findIndustryTitles")
//    public Result<?> findIndustryTitles(HttpServletRequest req) {
//        List<String> list = qyclIndustryTemplateService.findIndustryTitles();
//        return Result.ok(list);
//    }
//
//
//
//    @ApiOperation(value = "生成试听铃音", notes = "生成试听铃音")
//    @RequestMapping(value = "/createRing")
//    public Result<?> createRing(@RequestParam String mobile,
//                                @RequestParam(name = "ringTxt") String ringTxt,
//                                String companyTitle,
//                                HttpServletRequest req) {
//        String openId = getOpenId(req);
//        String companyOwner = getCompanyOwner(req);
//        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
//            return Result.error("请正确填写手机号");
//        }
//        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
//        if (!mobileRegionResult.isIspYidong()) {
//            return Result.error("当前业务只支持移动用户!");
//        }
//        Map<String, String> map = qyclRingService.createRing(ringTxt, companyTitle, openId, mobile, companyOwner);
//        return Result.ok(map);
//    }
//
//
    @ApiOperation(value = "合成diy铃音", notes = "合成diy铃音")
    @RequestMapping(value = "/createDiyRing")
    public Result<?> createDiyRing(@RequestParam String mobile,
                                   @RequestParam(required = false) String[] imageArray,
                                   @RequestParam(defaultValue = "BGM1") String bgm,
                                   String subChannel,
                                   HttpServletRequest req) {
        if (!memberService.isVrbtDiyMember(mobile)) {
            return Result.error("你还不是会员");
        }
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        Map<String, String> map = vrbtDiyRingService.createDiyRing(mobile, imageArray, BackgroundMusic.valueOf(bgm), subChannel);
        return Result.ok(map);
    }

    @ApiOperation(value = "生成铃音(模板)", notes = "生成铃音(模板)")
    @RequestMapping(value = "/createTemplateRing")
    public Result<?> createTemplateRing(@RequestBody JsonNode jsonNode,
                                        HttpServletRequest req) {
        String mobile = jsonNode.at("/mobile").asText();
        if (!memberService.isVrbtDiyMember(mobile)) {
            return Result.error("你还不是会员");
        }
        String templateId = jsonNode.at("/templateId").asText();
        String subChannel = jsonNode.at("/subChannel").asText();
        String clipsParam = jsonNode.at("/clipsParam").asText();
        log.info("生成模板铃声数据:{},", jsonNode);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        Map<String, String> map = vrbtDiyRingService.createTemplateRing(mobile, templateId, clipsParam, subChannel);
        return Result.ok(map);
    }

    @ApiOperation(value = "生成铃音(上传视频)", notes = "生成铃音(上传视频)")
    @RequestMapping(value = "/createVideoRing")
    public Result<?> createVideoRing(@RequestBody JsonNode jsonNode,
                                     HttpServletRequest req) {
        String mobile = jsonNode.at("/mobile").asText();
        if (!memberService.isVrbtDiyMember(mobile)) {
            return Result.error("你还不是会员");
        }
        String subChannel = jsonNode.at("/subChannel").asText();
        String videoPath = jsonNode.at("/videoPath").asText();
        log.info("上传视频铃音:{},", jsonNode);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        Map<String, String> map = vrbtDiyRingService.createVideoRing(mobile, videoPath, subChannel);
        return Result.ok(map);
    }


    @ApiOperation(value = "查询铃音", notes = "查询铃音")
    @RequestMapping(value = "/queryRing")
    public Result queryRing(@RequestParam String mobile) {
        List<VrbtDiyRing> list = vrbtDiyRingService.queryRing(mobile);
        return Result.ok(list);
    }



    @ApiOperation(value = "查询铃声制作状态", notes = "查询铃声制造状态")
    @RequestMapping(value = "/queryRingById/{ringId}")
    public Result<?> queryRingById(HttpServletRequest req, @PathVariable String ringId) {
        VrbtDiyRing vrbtDiyRing = vrbtDiyRingService.getById(ringId);
        return Result.ok(vrbtDiyRing);
    }

    /**
     * 发送登录短信
     *
     * @return
     */
    @ApiOperation(value = "支付宝视频彩铃发送短信通知", notes = "企业彩铃发送短信通知")
    @PostMapping(value = "/sms")
    @ResponseBody
    public Result sendSms(@RequestParam String mobile) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!memberService.isVrbtDiyMember(mobile)) {
            return Result.error("获取验证码失败，你还不是会员");
        }
        boolean result = smsValidateService.create(mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID);
        if (!result) {
            return Result.error("发送验证码失败");
        }
        return Result.ok("获取验证码成功");
    }


//
//
//    @ApiOperation(value = "查询铃音栏目", notes = "查询铃音栏目")
//    @RequestMapping(value = "/ring/column")
//    public Result<?> ringColumn() {
//        List<QyclRingColumn> list = qyclRingColumnService.ringColumn();
//        return Result.ok(list);
//    }
//
//    @ApiOperation(value = "查询铃音列表", notes = "查询铃音列表")
//    @RequestMapping(value = "/ring/queryByColumnId")
//    public Result<?> ringList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//                              @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//                              String columnId) {
//        Page<QyclRingVideo> page = new Page<QyclRingVideo>(pageNo, pageSize);
//        IPage<QyclRingVideo> pageList = qyclRingVideoService.ringListByColumnId(page, columnId);
//        return Result.ok(pageList);
//    }
//
//    @ApiOperation(value = "内容版企业成员查询接口", notes = "内容版企业成员查询接口")
//    @RequestMapping(value = "/queryMemberStatus")
//    public EntVrbtResult queryMemberStatus(@RequestParam(value = "departmentId", required = false, defaultValue ="")String departmentId, @RequestParam(value = "mobile")String mobile) {
//        String companyOwner = qyclCompanyService.getCompanyOwnerByMobile(mobile);
//        return enterpriseVrbtService.queryContentMembersImmediate(departmentId, mobile, companyOwner);
//    }
//
//    @ApiOperation(value = "内容版企业成员查询接口-根据openId查询", notes = "内容版企业成员查询接口-根据openId查询")
//    @RequestMapping(value = "/queryMemberStatusByOpenId")
//    public EntVrbtResult queryMemberStatusByOpenId(@RequestParam(value = "openId") String openId) {
//        log.info("内容版企业成员查询接口-根据openId查询,openId:{}", openId);
//        QyclCompanyMember qyclCompanyMember = qyclCompanyMemberService.getMemberByOpenId(openId);
//        if(qyclCompanyMember ==null){
//            return EntVrbtResult.FAIL_RESULT;
//        }
//        String companyOwner = qyclCompanyService.getCompanyOwnerByMobile(qyclCompanyMember.getMobile());
//        if (companyOwner != null) {
//            return enterpriseVrbtService.queryContentMembersImmediate("", qyclCompanyMember.getMobile(),companyOwner);
//        }
//        return EntVrbtResult.FAIL_RESULT;
//    }
//
//    @ApiOperation(value = "获取阿里云媒体模板信息", notes = "获取阿里云媒体模板信息")
//    @RequestMapping(value = "/fetchTemplateInfo")
//    public String fetchTemplateInfo(@RequestParam(value = "templateId") String templateId) {
//        return aliMediaService.fetchTemplateInfo(templateId);
//    }
//
//    @ApiOperation(value = "查询铃音模板栏目", notes = "查询铃音模板栏目")
//    @RequestMapping(value = "/ring/template/column")
//    public Result<?> ringTemplateColumn() {
//        List<QyclRingColumn> list = qyclRingColumnService.ringTemplateColumn();
//        return Result.ok(list);
//    }
//
//    @ApiOperation(value = "查询铃音模板", notes = "查询铃音模板")
//    @RequestMapping(value = "/ring/template/queryByColumnId")
//    public Result<?> ringTemplateList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//                                      @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//                                      String columnId) {
//        Page<QyclRingTemplate> page = new Page<QyclRingTemplate>(pageNo, pageSize);
//        IPage<QyclRingTemplate> list = qyclRingTemplateService.ringTemplateListByColumnId(page,columnId);
//        return Result.ok(list);
//    }
//
}
