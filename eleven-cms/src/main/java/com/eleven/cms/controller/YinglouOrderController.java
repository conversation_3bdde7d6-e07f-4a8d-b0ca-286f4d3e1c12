package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.YinglouOrder;
import com.eleven.cms.service.IYinglouOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 影楼订单表
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
@Api(tags="影楼订单表")
@RestController
@RequestMapping("/cms/yinglouOrder")
@Slf4j
public class YinglouOrderController extends JeecgController<YinglouOrder, IYinglouOrderService> {
	@Autowired
	private IYinglouOrderService yinglouOrderService;

	/**
	 * 分页列表查询
	 *
	 * @param yinglouOrder
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "影楼订单表-分页列表查询")
	@ApiOperation(value="影楼订单表-分页列表查询", notes="影楼订单表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(YinglouOrder yinglouOrder,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<YinglouOrder> queryWrapper = QueryGenerator.initQueryWrapper(yinglouOrder, req.getParameterMap());
		Page<YinglouOrder> page = new Page<YinglouOrder>(pageNo, pageSize);
		IPage<YinglouOrder> pageList = yinglouOrderService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param yinglouOrder
	 * @return
	 */
	//@AutoLog(value = "影楼订单表-添加")
	@ApiOperation(value="影楼订单表-添加", notes="影楼订单表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody YinglouOrder yinglouOrder) {
		yinglouOrderService.save(yinglouOrder);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param yinglouOrder
	 * @return
	 */
	//@AutoLog(value = "影楼订单表-编辑")
	@ApiOperation(value="影楼订单表-编辑", notes="影楼订单表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody YinglouOrder yinglouOrder) {
		yinglouOrderService.updateById(yinglouOrder);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "影楼订单表-通过id删除")
	@ApiOperation(value="影楼订单表-通过id删除", notes="影楼订单表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		yinglouOrderService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "影楼订单表-批量删除")
	@ApiOperation(value="影楼订单表-批量删除", notes="影楼订单表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.yinglouOrderService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "影楼订单表-通过id查询")
	@ApiOperation(value="影楼订单表-通过id查询", notes="影楼订单表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		YinglouOrder yinglouOrder = yinglouOrderService.getById(id);
		if(yinglouOrder==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(yinglouOrder);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param yinglouOrder
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, YinglouOrder yinglouOrder) {
        return super.exportXls(request, yinglouOrder, YinglouOrder.class, "影楼订单表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, YinglouOrder.class);
    }
	 /**
	  * 微信退款
	  * @param orderId 订单号
	  * @param totalAmount
	  * @return
	  */
	 @ApiOperation(value = "微信退款", notes = "微信退款")
	 @PostMapping(value = "/refund")
	 @ResponseBody
	 public Result wechatRefund(@RequestParam(value = "orderId", required = false, defaultValue ="")String orderId,@RequestParam(value = "totalAmount", required = false, defaultValue ="")String totalAmount){
		 if(StringUtils.isEmpty(orderId)){
			 return Result.error("订单号不能为空");
		 }
		 if(StringUtils.isEmpty(totalAmount)){
			 return Result.error("退款金额不能为空");
		 }
		 return this.yinglouOrderService.wechatRefund(orderId,totalAmount);
	 }
}
