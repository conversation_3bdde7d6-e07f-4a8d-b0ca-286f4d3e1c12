package com.eleven.cms.controller;

import com.eleven.cms.dto.DataCollectDto;
import com.eleven.qycl.service.IDataCollectService;
import com.eleven.qycl.service.IDownXlsxDataCollectService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/11 15:36
 **/
@Api(tags="企业彩铃数据汇总")
@RestController
@RequestMapping("/qycl/dataCollect")
@Slf4j
public class DownXlsxDataCollectController  extends JeecgController<DataCollectDto, IDownXlsxDataCollectService> {
    @Autowired
    private IDataCollectService dataCollectService;
    /**
     * 导出excel
     * @param dto
     * @return
     */
    @RequestMapping(value = "/downXlsxFindExecute")
    public ModelAndView downXlsxFindExecute(DataCollectDto dto) {
        List<DataCollectDto> pageList = dataCollectService.downXlsxFindExecuteDateList(dto);
        return super.downXlsx(DataCollectDto.class, "企业彩铃数据汇总",pageList);
    }
}
