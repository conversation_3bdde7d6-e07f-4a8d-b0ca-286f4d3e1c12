package com.eleven.cms.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.entity.VrbtDiyVideo;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.service.IVrbtDiyVideoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_vrbt_diy_video
 * @Author: jeecg-boot
 * @Date:   2023-06-21
 * @Version: V1.0
 */
@Api(tags="cms_vrbt_diy_video")
@RestController
@RequestMapping("/cms/vrbtDiyVideo")
@Slf4j
public class VrbtDiyVideoController extends JeecgController<VrbtDiyVideo, IVrbtDiyVideoService> {
	@Autowired
	private IVrbtDiyVideoService vrbtDiyVideoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param vrbtDiyVideo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_diy_video-分页列表查询")
	@ApiOperation(value="cms_vrbt_diy_video-分页列表查询", notes="cms_vrbt_diy_video-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(VrbtDiyVideo vrbtDiyVideo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<VrbtDiyVideo> queryWrapper = QueryGenerator.initQueryWrapper(vrbtDiyVideo, req.getParameterMap());
		Page<VrbtDiyVideo> page = new Page<VrbtDiyVideo>(pageNo, pageSize);
		IPage<VrbtDiyVideo> pageList = vrbtDiyVideoService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param vrbtDiyVideo
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_diy_video-添加")
	@ApiOperation(value="cms_vrbt_diy_video-添加", notes="cms_vrbt_diy_video-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody VrbtDiyVideo vrbtDiyVideo) {
		vrbtDiyVideoService.save(vrbtDiyVideo);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param vrbtDiyVideo
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_diy_video-编辑")
	@ApiOperation(value="cms_vrbt_diy_video-编辑", notes="cms_vrbt_diy_video-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody VrbtDiyVideo vrbtDiyVideo) {
		vrbtDiyVideoService.updateById(vrbtDiyVideo);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_diy_video-通过id删除")
	@ApiOperation(value="cms_vrbt_diy_video-通过id删除", notes="cms_vrbt_diy_video-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		vrbtDiyVideoService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_diy_video-批量删除")
	@ApiOperation(value="cms_vrbt_diy_video-批量删除", notes="cms_vrbt_diy_video-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.vrbtDiyVideoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_vrbt_diy_video-通过id查询")
	@ApiOperation(value="cms_vrbt_diy_video-通过id查询", notes="cms_vrbt_diy_video-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.getById(id);
		if(vrbtDiyVideo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(vrbtDiyVideo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param vrbtDiyVideo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, VrbtDiyVideo vrbtDiyVideo) {
        return super.exportXls(request, vrbtDiyVideo, VrbtDiyVideo.class, "cms_vrbt_diy_video");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, VrbtDiyVideo.class);
    }

}
