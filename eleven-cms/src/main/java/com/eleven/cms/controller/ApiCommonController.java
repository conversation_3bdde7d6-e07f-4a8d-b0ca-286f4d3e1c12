package com.eleven.cms.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipayUserAgreementUnsignResponse;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.ad.HuYuQuanWangProperties;
import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.aivrbt.service.impl.HaiYiAiService;
import com.eleven.cms.aivrbt.utils.HttpUtils;
import com.eleven.cms.client.UnifyRightsFeignClient;
import com.eleven.cms.config.*;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.*;
import com.eleven.cms.enums.ReportTypeEnum;
import com.eleven.cms.es.entity.EsBusinessLog;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.BizTypeServiceImpl;
import com.eleven.cms.service.impl.JiangXiVrbtBusinessServiceImpl;
import com.eleven.cms.service.impl.JunboGuiZhouBusinessServiceImpl;
import com.eleven.cms.service.impl.JunboSiChuanBusinessServiceImpl;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.*;
import com.eleven.qycl.entity.QyclOrderPayLog;
import com.eleven.qycl.service.IQyclOrderPayLogService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.wxpay.sdk.WXPayConstants;
import com.google.common.base.Strings;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.MD5Util;
import org.jeecg.common.util.*;
import org.jeecg.common.util.oss.OssBootUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.UriUtils;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 新的ApiController,注意访问路径不要和旧的冲突
 */
@Api(tags = "api")
@RestController
@RequestMapping("/api")
@Slf4j
@Validated
public class ApiCommonController {
    private static final Map<String, Integer> LOTTERY_COUNT_MAP= new HashMap<String, Integer>(){
        {
        put(BIZ_CHANNEL_SXYD_JBAICL,3);
        }
    };
    public static final String DELIMITER_AMP = "&";
    private static final Interner<String> interner = Interners.newWeakInterner();
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Autowired
    private ILuckDrawLogService luckDrawLogService;
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    private IAlipayService alipayService;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    private IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private JunboApiProperties junboApiProperties;
    @Autowired
    private IRightsPackService rightsPackService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private YuxunHunanDianxinService yuxunHunanDianxinService;
    @Autowired
    private YuxunHunanDianxinMindService yuxunHunanDianxinMindService;
    @Autowired
    private BlackListService blackListService;
    @Autowired
    private IAliChannelConfigService aliChannelConfigService;
    @Autowired
    private WxMiniApiService wxMiniApiService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISmsValidateService smsValidateService;
    @Autowired
    private IAliSignChargingOrderService chargingOrderService;
    @Autowired
    JiangxiYidongService jiangxiYidongService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    private IWoReadOrderService woReadOrderService;
    @Autowired
    private WoReadApiService woReadApiService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private IWyyMmOrderService wyyMmOrderService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private ICompanyChannelConfigService companyChannelConfigService;
    @Autowired
    private SubscribeResultNotifyService subscribeResultNotifyService;
    @Autowired
    private IKuaiShouService kuaiShouService;
    @Autowired
    private IVrbtDiyVideoService vrbtDiyVideoService;
    @Autowired
    private VrbtDiyService vrbtDiyService;
    @Autowired
    private YycpReportService yycpReportService;
    @Autowired
    private IKeyValueConfigService keyValueConfigService;
    @Autowired
    private WangyiyunMmService wangyiyunMmService;
    @Autowired
    private IVrbtDiyRingService vrbtDiyRingService;
    @Autowired
    private IPageBusinessProvinceConfigService pageBusinessProvinceConfigService;

    @Autowired
    private IPageBusinessProvinceConfigVpopService pageBusinessProvinceConfigVpopService;

    @Autowired
    private IPageBusinessProvinceConfigSelfService pageBusinessProvinceConfigSelfService;

    @Autowired
    private IBizPageConfigService bizPageConfigService;
    @Autowired
    private IBizTypeService bizTypeService;
    @Autowired
    private IPageBusinessConfigService pageBusinessConfigService;

    @Autowired
    private IPageBusinessConfigVpopService pageBusinessConfigVpopService;
    @Autowired
    private IPageBusinessConfigSelfService pageBusinessConfigSelfService;


    @Autowired
    private LiantongVrbtService liantongVrbtService;
    @Autowired
    private LiantongVrbtProperties liantongVrbtProperties;
    @Autowired
    private ILiantongRingService liantongRingService;

    @Autowired
    private SmsApiProperties smsApiProperties;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IProvinceTreeService provinceTreeService;
    @Autowired
    private IOutsideConfigService outsideConfigService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    private IWoReadSinglePayOrderService woReadSinglePayOrderService;
    @Autowired
    private ITianyiCommAssistService tianyiCommAssistService;
    @Autowired
    private IColumnService columnService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private BizProperties bizProperties;
    @Autowired
    private IMusicCollectService musicCollectService;
    @Autowired
    private IMusicHotLevelService musicHotLevelService;
    @Autowired
    InnerUnionMemberService innerUnionMemberService;
    @Autowired
    private IKaSaiStockService kaSaiStockService;
    @Autowired
    ISiChuanExclusiveCardService siChuanExclusiveCardService;
    @Autowired
    private IQyclOrderPayLogService orderPayLogService;
    @Autowired
    private IQyclWxpayService qyclWxpayService;
    @Autowired
    private IAiVrbtService aiVrbtService;
    @Autowired
    private IMobileFeeChargeLogService mobileFeeChargeLogService;
    @Autowired
    ISdxcLianTongService sdxcLianTongService;
    @Autowired
    IXuTongYiDongService xuTongYiDongService;
    @Autowired
    IRuanYouTongService ruanYouTongService;
    @Autowired
    IVrJingmengService vrJingmengService;
    @Autowired
    private GuizhouYidongService guizhouYidongService;
    @Autowired
    private IMiguActiveSettingsService miguActiveSettingsService;

    //联通沃阅读联通号码省份限制
    public static final List<String> WO_READ_PROVINCE_lIMIT_LIST
            = Arrays.asList("四川", "山东", "广东");
    //已支付
    private static final Integer PAY_STATUS_SUCCESS = 1;
    //支付失败
    private static final Integer PAY_STATUS_FAIL = 0;
    //退款失败
    private static final Integer REFUND_FAIL = 5;
    //退款成功
    private static final Integer REFUND_SUCCESS = 4;
    @Autowired
    private IColumnDyService columnDyService;

    @Autowired
    private IColumnMusicDyService columnMusicDyService;
    @Autowired
    private ShandongHexiaoyuanService shandongHexiaoyuanService;
    @Autowired
    private ITuniuCouponCodeChargeLogService tuniuCouponCodeChargeLogService;
    @Autowired
    private IFacaishuNotifyLogService facaishuNotifyLogService;
    @Autowired
    private IDianxinCloudGameService dianxinCloudGameService;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private ILianlianChargeLogService lianlianChargeLogService;
    @Autowired
    CaixunService caixunService;
    @Autowired
    private WoReadOutSideApiService woReadOutSideApiService;
    @Autowired
    private IEsDataService esDataService;
    @Autowired
    private IJunboCunLiangService junboCunLiangService;
    @Autowired
    private OutsideCallbackService outsideCallbackService;

    @Autowired
    private IXiMiLeApiService xiMiLeApiService;
    @Autowired
    private ICommonCouponService commonCouponService;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private IWxpayService wxpayService;
    @Autowired
    private WenshengVideoService wenshengVideoService;
    @Autowired
    private IHeTuFenShengService heTuFenShengService;
    @Autowired
    private IWenshengRecordService wenshengRecordService;
    @Autowired
    private UnifyRightsFeignClient unifyRightsFeignClient;
    @Autowired
    private IHuYuQuanWangService huYuQuanWangService;
    @Autowired
    private HuYuQuanWangProperties huyuQuanWangProperties;
    @Autowired
    private ICmsColumnComicService cmsColumnComicService;
    @Autowired
    private ICmsComicVideoService cmsComicVideoService;
    @Autowired
    private HuoshanApiService huoshanApiService;
    @Autowired
    private IBusinessChannelRightsService businessChannelRightsService;
    @Autowired
    private IHeTuFenShengChongQingService heTuFenShengChongQingService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    IOrderFailLogService orderFailLogService;
    @Autowired
    private YycpProperties yycpProperties;
    @Autowired
    private IZhongZhiHuiRongService zhongZhiHuiRongService;
    @Autowired
    private IQywxService qywxService;
    @Autowired
    private HaiYiAiService haiYiAiService;
    public static final String WENSHENG_VIDEO_KEY_PREFIX = "wensheng::video:";
    @Autowired
    private IMiguVrbtPayOrderService miguVrbtPayOrderService;
    @Autowired
    private IDuodianCouponCodeChargeLogService duodianCouponCodeChargeLogService;
    /**
     * 阿里云oss签名
     *
     * @param region oss区域
     * @param bucket oss桶
     * @param dir    设置上传到OSS文件的前缀，可置空此项。置空后，文件将上传至Bucket的根目录下
     * @return
     */
    @PostMapping("/aliOssSign")
    @ResponseBody
    public Result<?> aliOssSign(@RequestParam(name = "region", required = false, defaultValue = "cn-beijing") String region,
                                @RequestParam(name = "bucket", required = false, defaultValue = "ims-media") String bucket,
                                @RequestParam(name = "dir", required = false, defaultValue = "user-media") String dir) {
        // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
        String accessId = OssBootUtil.getAccessKeyId();
        String accessKey = OssBootUtil.getAccessKeySecret();
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "oss-" + region + ".aliyuncs.com";
        // 填写Host地址，格式为https://bucketname.endpoint。
        String host = "https://" + bucket + "." + endpoint;
        // 设置上传回调URL，即回调服务器地址，用于处理应用服务器与OSS之间的通信。OSS会在文件上传完成后，把文件上传信息通过此回调URL发送给应用服务器。
        //String callbackUrl = "https://***********:8888";

        // 创建ossClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessId, accessKey);
        try {
            //有效时间30分钟
            Date expiration = Date.from(LocalDateTime.now().plusMinutes(30L).atZone(ZoneId.systemDefault()).toInstant());
            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);

            String postPolicy = ossClient.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = ossClient.calculatePostSignature(postPolicy);

            final ObjectNode objectNode = mapper.createObjectNode()
                    .put("accessId", accessId)
                    .put("policy", encodedPolicy)
                    .put("signature", postSignature)
                    .put("dir", dir)
                    .put("host", host);
            //.put("expire", String.valueOf(expireEndTime / 1000));

            //JSONObject jasonCallback = new JSONObject();
            //jasonCallback.put("callbackUrl", callbackUrl);
            //jasonCallback.put("callbackBody", "filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}");
            //jasonCallback.put("callbackBodyType", "application/x-www-form-urlencoded");
            //String base64CallbackBody = BinaryUtil.toBase64String(jasonCallback.toString().getBytes());
            //respMap.put("callback", base64CallbackBody);

            //JSONObject ja1 = JSONObject.fromObject(respMap);
            //// System.out.println(ja1.toString());
            //response.setHeader("Access-Control-Allow-Origin", "*");
            //response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT");
            //response(request, response, ja1.toString());

            return Result.ok(objectNode);
        } catch (Exception e) {
            log.info("阿里云oss签名异常!", e);
            return Result.error("阿里云oss签名异常");
        }
    }

    /**
     * 阿里云oss签名
     *
     * @param region oss区域
     * @param bucket oss桶
     * @param dir    设置上传到OSS文件的前缀，可置空此项。置空后，文件将上传至Bucket的根目录下
     * @return
     */
    @PostMapping("/aliOssSignYrjy")
    @ResponseBody
    public Result<?> aliOssSignYrjy(@RequestParam(name = "region", required = false, defaultValue = "cn-shanghai") String region,
                                    @RequestParam(name = "bucket", required = false, defaultValue = "vibkt") String bucket,
                                    @RequestParam(name = "dir", required = false, defaultValue = "vision") String dir) {
        // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
        //AccessKey ID:LTAI5tMDER1XFD66UhuBCcLS
        //AccessKey Secret:******************************
        //已授权AliyunVIAPIFullAccess
        String accessId = "LTAI5tMDER1XFD66UhuBCcLS";
        String accessKey = "******************************";
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "oss-" + region + ".aliyuncs.com";
        // 填写Host地址，格式为https://bucketname.endpoint。
        String host = "https://" + bucket + "." + endpoint;
        // 设置上传回调URL，即回调服务器地址，用于处理应用服务器与OSS之间的通信。OSS会在文件上传完成后，把文件上传信息通过此回调URL发送给应用服务器。
        //String callbackUrl = "https://***********:8888";

        // 创建ossClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessId, accessKey);
        try {
            //有效时间30分钟
            Date expiration = Date.from(LocalDateTime.now().plusMinutes(30L).atZone(ZoneId.systemDefault()).toInstant());
            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);

            String postPolicy = ossClient.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = ossClient.calculatePostSignature(postPolicy);

            final ObjectNode objectNode = mapper.createObjectNode()
                    .put("accessId", accessId)
                    .put("policy", encodedPolicy)
                    .put("signature", postSignature)
                    .put("dir", dir)
                    .put("host", host);
            //.put("expire", String.valueOf(expireEndTime / 1000));

            //JSONObject jasonCallback = new JSONObject();
            //jasonCallback.put("callbackUrl", callbackUrl);
            //jasonCallback.put("callbackBody", "filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}");
            //jasonCallback.put("callbackBodyType", "application/x-www-form-urlencoded");
            //String base64CallbackBody = BinaryUtil.toBase64String(jasonCallback.toString().getBytes());
            //respMap.put("callback", base64CallbackBody);

            //JSONObject ja1 = JSONObject.fromObject(respMap);
            //// System.out.println(ja1.toString());
            //response.setHeader("Access-Control-Allow-Origin", "*");
            //response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT");
            //response(request, response, ja1.toString());

            return Result.ok(objectNode);
        } catch (Exception e) {
            log.info("阿里云oss签名异常!", e);
            return Result.error("阿里云oss签名异常");
        }
    }

    /**
     * 支付宝支付(会员大礼盒)【刷单专属】
     *
     * @return
     */
    @ApiOperation(value = "支付宝支付", notes = "支付宝支付")
    @PostMapping(value = "/alipay/brushSheetPay")
    public void aliSignUp(HttpServletRequest request, HttpServletResponse response) {


        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("渠道订阅请求=>ip:{},{}", ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            String businessType = StringUtil.isEmpty(subscribe.getBusinessType()) ? BizConstant.BIZ_LHHY_QYB_010_SERVICE_ID : subscribe.getBusinessType();
            Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType, businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
            if (alipay == null) {
                throw new AlipayApiException("支付参数错误");
            }
            subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            subscribe.setReferer(finger);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());
            String totalAmount = alipay.getSingleAmount();
            String subject = alipay.getBusinessName();
            String outTradeNo = "AliPay" + IdWorker.getIdStr();
            String mobile = subscribe.getMobile();
            if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
                throw new AlipayApiException("请正确填写手机号");
            }
            alipayService.aliTradeBrushSheetPay(subscribe.getMobile(),
                    outTradeNo,
                    totalAmount,
                    subject, alipay.getAppId(), alipay.getBusinessType(), alipay.getBusinessName(), response, subscribe);
        } catch (Exception e) {
            log.info("支付宝支付出错：", e);
        }
    }

    /**
     * 支付宝周期扣款解约(渠道订单统计解约)
     *
     * @param ispOrderNo
     * @return
     */
    //@AutoLog(value = "支付宝周期扣款解约")
    @RequestMapping(value = "/esSubscribe/rescind")
    @ResponseBody
    public Result<?> rescind(@RequestParam(value = "ispOrderNo", required = true) String ispOrderNo) {

        //通过订单号查询对应签约信息
        AliSignRecord aliSignRecord = aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo, ispOrderNo).one();
        if (aliSignRecord != null) {
            AlipayUserAgreementUnsignResponse response = alipayService.alipayRescind(aliSignRecord.getAgreementNo(), aliSignRecord.getBusinessType());
            String remark = "";
            if (response.isSuccess()) {
                remark = "解约调用成功=>{code:" + response.getCode() + ",msg:" + response.getMsg() + ",sub_code:" + response.getSubCode() + ",sub_msg:" + response.getSubMsg() + "}";
            } else {
                remark = "解约调用失败=>{code:" + response.getCode() + ",msg:" + response.getMsg() + ",sub_code:" + response.getSubCode() + ",sub_msg:" + response.getSubMsg() + "}";
            }
            aliSignRecordService.lambdaUpdate()
                    .eq(AliSignRecord::getExternalAgreementNo, ispOrderNo).set(AliSignRecord::getRemark, remark).update();
            return Result.ok(remark);
        }
        return Result.error("数据不存在");
    }

    /**
     * 长沙权益对接（支付宝三方支付）
     *
     * @param orderStatus
     * @return
     */
    //@AutoLog(value = "长沙权益对接")
    @RequestMapping(value = "/alipay/query/chargingOrders")
    @ResponseBody
    public FebsResponse alipayQueryChargingOrders(String startTime,
                                                  String endTime,
                                                  Integer orderStatus) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime) || orderStatus == null) {
            return new FebsResponse().fail().message("参数不能为空");
        }
        FebsResponse febsResponse = aliSignChargingOrderService.alipayQueryChargingOrders(startTime, endTime, orderStatus);
        return febsResponse;
    }

    /**
     * APP渠道业务黑名单配置
     *
     * @param channel 渠道号
     * @param adSite  点位
     * @return
     */
    //@AutoLog(value = "APP渠道业务黑名单配置")
    @RequestMapping(value = "/adSiteBusinessConfig/isBalck")
    @ResponseBody
    public FebsResponse adSiteBusinessConfigIsBalck(String channel,
                                                    String adSite) {
        Boolean b = adSiteBusinessConfigService.isBlack(channel, adSite);
        return new FebsResponse().success().data(b);
    }

    /**
     * APP渠道业务黑名单配置
     *
     * @param channel 渠道号
     * @param adSite  点位
     * @return
     */
    //@AutoLog(value = "APP渠道业务黑名单配置")
    @RequestMapping(value = "/adSiteBusinessConfig/isOsVrbtBlack")
    @ResponseBody
    public FebsResponse isOsVrbtBlack(String channel, String adSite) {
        Boolean b = adSiteBusinessConfigService.isOsVrbtBlack(channel, adSite);
        return new FebsResponse().success().data(b);
    }

    /**
     * 支付宝权益充值
     *
     * @param channelId
     * @param sign
     * @param mobile
     * @param account
     * @param serviceId
     * @param rightsId
     * @param packName
     * @return
     */
    //@AutoLog(value = "支付宝权益充值")
    @PostMapping("/alipay/rights/recharge")
    @ResponseBody
    public FebsResponse alipayRightsRecharge(@RequestParam(value = "channelId", required = false, defaultValue = "") String channelId,
                                             @RequestParam(value = "sign", required = false, defaultValue = "") String sign,
                                             @RequestParam(value = "mobile", required = false, defaultValue = "") String mobile,
                                             @RequestParam(value = "account", required = false, defaultValue = "") String account,
                                             @RequestParam(value = "serviceId", required = false, defaultValue = "") String serviceId,
                                             @RequestParam(value = "rightsId", required = false, defaultValue = "") String rightsId,
                                             @RequestParam(value = "packName", required = false, defaultValue = "") String packName,
                                             @RequestParam(value = "orderId", required = false, defaultValue = "") String orderId,
                                             @RequestParam(value = "submitTime", required = false, defaultValue = "") String submitTime) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("手机号错误！");
        }
        if (Strings.isNullOrEmpty(sign)) {
            return new FebsResponse().fail().message("sign不能为空！");
        }
        if (Strings.isNullOrEmpty(channelId)) {
            return new FebsResponse().fail().message("渠道号不能为空！");
        }
        if (Strings.isNullOrEmpty(serviceId)) {
            return new FebsResponse().fail().message("业务ID不能为空！");
        }
        if (Strings.isNullOrEmpty(rightsId)) {
            return new FebsResponse().fail().message("产品Id不能为空！");
        }
        if (Strings.isNullOrEmpty(packName)) {
            return new FebsResponse().fail().message("业务包名不能为空！");
        }
        if (Strings.isNullOrEmpty(submitTime)) {
            return new FebsResponse().fail().message("创建时间不能为空！");
        }
        if (Strings.isNullOrEmpty(orderId)) {
            return new FebsResponse().fail().message("订单号不能为空！");
        }


        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(channelId);
        if (otherRecharge == null) {
            return new FebsResponse().fail().message("渠道号错误！");
        }

        if (!checkSign(channelId, sign, mobile, account, serviceId, rightsId, packName, otherRecharge, submitTime, orderId)) {
            return new FebsResponse().fail().message("验签失败！");
        }

        synchronized (interner.intern(mobile)) {
            return memberService.alipayRightsRecharge(channelId, mobile, account, serviceId, rightsId, packName, orderId);
        }
    }


    /**
     * 支付宝权益列表
     *
     * @param channelId
     * @param sign
     * @return
     */
    @ApiOperation(value = "支付宝权益列表", notes = "支付宝权益列表")
    @PostMapping(value = "/alipay/rights/list")
    @ResponseBody
    public FebsResponse alipayRightsList(@RequestParam(value = "channelId", required = false, defaultValue = "") String channelId,
                                         @RequestParam(value = "sign", required = false, defaultValue = "") String sign,
                                         @RequestParam(value = "submitTime", required = false, defaultValue = "") String submitTime) {
        if (Strings.isNullOrEmpty(channelId)) {
            return new FebsResponse().fail().message("渠道号不能为空！");
        }
        if (Strings.isNullOrEmpty(submitTime)) {
            return new FebsResponse().fail().message("创建时间不能为空！");
        }
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(channelId);
        if (otherRecharge == null) {
            return new FebsResponse().code(HttpStatus.SERVICE_UNAVAILABLE).message("渠道号错误！");
        }

        if (!checkSign(channelId, sign, null, null, null, null, null, otherRecharge, submitTime, null)) {
            return new FebsResponse().fail().message("验签失败！");
        }
        return new FebsResponse().success().data(rightsPackService.aliPayRightsList(BizConstant.ALIPAY_RIGHTS_LIST));
    }


    /**
     * 支付宝用户权益列表
     *
     * @param channelId
     * @param sign
     * @return
     */
    @ApiOperation(value = "支付宝用户权益列表", notes = "支付宝用户权益列表")
    @PostMapping(value = "/alipay/user/rights/list")
    @ResponseBody
    public FebsResponse alipayUserRightsList(@RequestParam(value = "mobile", required = false, defaultValue = "") String mobile,
                                             @RequestParam(value = "channelId", required = false, defaultValue = "") String channelId,
                                             @RequestParam(value = "sign", required = false, defaultValue = "") String sign,
                                             @RequestParam(value = "submitTime", required = false, defaultValue = "") String submitTime) {
        if (Strings.isNullOrEmpty(channelId)) {
            return new FebsResponse().fail().message("渠道号不能为空！");
        }
        if (Strings.isNullOrEmpty(submitTime)) {
            return new FebsResponse().fail().message("创建时间不能为空！");
        }
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(channelId);
        if (otherRecharge == null) {
            return new FebsResponse().code(HttpStatus.SERVICE_UNAVAILABLE).message("渠道号错误！");
        }

        if (!checkSign(channelId, sign, mobile, null, null, null, null, otherRecharge, submitTime, null)) {
            return new FebsResponse().fail().message("验签失败！");
        }

        //订购权益大礼包是会员可以登录
        List<String> businessTypeList = alipayConfigService.aliPayRightsRechargeList().stream().map(Alipay::getBusinessType).collect(Collectors.toList());
        //支付宝订单与其他业务查询不同
        if (businessTypeList.isEmpty()) {
            return new FebsResponse().code(HttpStatus.SERVICE_UNAVAILABLE).message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }

        AliSignChargingOrder chargingOrder = chargingOrderService.lambdaQuery()
                .eq(AliSignChargingOrder::getMobile, mobile)
                .eq(AliSignChargingOrder::getOrderStatus, 1)
                .in(AliSignChargingOrder::getBusinessType, businessTypeList)
                .eq(AliSignChargingOrder::getRefundStatus, 0)
                .between(AliSignChargingOrder::getPayTime, LocalDateTime.now().minusMonths(1), LocalDateTime.now())
                .last("ORDER BY order_amount*1 DESC , create_time DESC LIMIT 1").one();
        if (chargingOrder == null) {
            return new FebsResponse().fail().message(BizConstant.NOT_MEMBER_MSG);
        }
        List<String> serviceId = Lists.newArrayList();
        serviceId.add(chargingOrder.getBusinessType());
        return new FebsResponse().success().data(rightsPackService.aliPayRightsList(serviceId));
    }

    /**
     * 支付宝查询权益充值
     *
     * @param channelId
     * @param sign
     * @param orderId
     * @param submitTime
     * @return
     */
    //@AutoLog(value = "支付宝查询权益充值")
    @PostMapping("/alipay/query/recharge")
    @ResponseBody
    public FebsResponse alipayQueryRecharge(@RequestParam(value = "channelId", required = false, defaultValue = "") String channelId,
                                            @RequestParam(value = "sign", required = false, defaultValue = "") String sign,
                                            @RequestParam(value = "orderId", required = false, defaultValue = "") String orderId,
                                            @RequestParam(value = "submitTime", required = false, defaultValue = "") String submitTime) {

        if (Strings.isNullOrEmpty(sign)) {
            return new FebsResponse().fail().message("sign不能为空！");
        }
        if (Strings.isNullOrEmpty(channelId)) {
            return new FebsResponse().fail().message("渠道号不能为空！");
        }
        if (Strings.isNullOrEmpty(orderId)) {
            return new FebsResponse().fail().message("订单号不能为空！");
        }
        if (Strings.isNullOrEmpty(submitTime)) {
            return new FebsResponse().fail().message("创建时间不能为空！");
        }
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(channelId);
        if (otherRecharge == null) {
            return new FebsResponse().fail().message("渠道号错误！");
        }

        if (!checkSign(channelId, sign, null, null, null, null, null, otherRecharge, submitTime, orderId)) {
            return new FebsResponse().fail().message("验签失败！");
        }

        synchronized (interner.intern(orderId)) {
            return memberService.alipayQueryRechargeByOrderId(orderId);
        }
    }

    /**
     * 验证签名
     *
     * @param channelId
     * @param sign
     * @param mobile
     * @param account
     * @param serviceId
     * @param rightsId
     * @param packName
     * @param otherRecharge
     * @return
     */
    private Boolean checkSign(String channelId, String sign, String mobile, String account, String serviceId, String rightsId, String packName, OtherRecharge otherRecharge, String submitTime, String orderId) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("channelId", channelId);
        dataMap.put("submitTime", submitTime);
        if (!Strings.isNullOrEmpty(mobile)) {
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return false;
            }
            dataMap.put("mobile", mobile);
        }
        if (!Strings.isNullOrEmpty(account)) {
            dataMap.put("account", account);
        }
        if (!Strings.isNullOrEmpty(serviceId)) {
            dataMap.put("serviceId", serviceId);
        }
        if (!Strings.isNullOrEmpty(rightsId)) {
            dataMap.put("rightsId", rightsId);
        }
        if (!Strings.isNullOrEmpty(rightsId)) {
            dataMap.put("packName", packName);
        }
        if (!Strings.isNullOrEmpty(orderId)) {
            dataMap.put("orderId", orderId);
        }
        dataMap = dataMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oleValue, newValue) -> oleValue, LinkedHashMap::new));
        String parameterStr = dataMap.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining(DELIMITER_AMP));
        parameterStr += DELIMITER_AMP + "key=" + otherRecharge.getKey();
        try {
            String ourSign = DigestUtils.md5DigestAsHex(parameterStr.getBytes(StandardCharsets.UTF_8.name()));
            log.info("支付宝权益充值,加密参数:{},密钥:{},本机密钥:{}", parameterStr, sign, ourSign);
            if (StringUtils.equals(sign, ourSign)) {
                return true;
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.error("支付宝权益充值,加密参数:{},密钥:{}", parameterStr, sign, e);
        }
        return false;
    }

    @ApiOperation(value = "江苏移动组合包回调", notes = "江苏移动组合包回调")
    @RequestMapping(value = "/jsyd/combinPack/resultNotify")
    //public String jsydCombinPackResultNotify(@RequestParam Map<String,Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
    public String jsydCombinPackResultNotify(HttpServletRequest request) throws JsonProcessingException {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String rawData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //JsonNode jsonNode = mapper.readTree(rawData);
            log.info("江苏移动组合包回调,xml转json数据:{}", StringUtils.normalizeSpace(rawData));
        } catch (Exception e) {
            log.info("江苏移动组合包回调,处理回调数据异常:", e);
        }
        return mapper.writeValueAsString(JiangsuResponseCallback.OK());
    }

    @ApiOperation(value = "湖南电信视频彩铃(铃音盒)和智能接听(小秘书)下单接口", notes = "湖南电信视频彩铃(铃音盒)和智能接听(小秘书)下单接口")
    @PostMapping(value = "/hndx/genOrder")
    public Result<?> hndxGenOrder(@RequestBody JsonNode jsonNode) {
        String mobile = jsonNode.at("/mobile").asText();
        String channelCode = jsonNode.at("/channel").asText();
        String returnUrl = jsonNode.at("/source").asText();
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult == null || !(mobileRegionResult.isIspDianxin() && IBusinessCommonService.HUNAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
            return Result.error("当前业务只支持湖南电信用户!");
        }
        try {
            if (BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT.equals(channelCode)) {
                YuxunHunanDianxinResult yuxunHunanDianxinResult = yuxunHunanDianxinService.accpectOrder(mobile, returnUrl);
                return yuxunHunanDianxinResult.isOk() ? Result.redirect("redirect telecom vrbt page", yuxunHunanDianxinResult.getOrderPage()) : Result.error(yuxunHunanDianxinResult.getMsg());
            } else if (BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_13.equals(channelCode) || BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_9.equals(channelCode)) {
                String busProductType = BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_9.equals(channelCode) ? "9" : "13";
                YuxunHunanDianxinMindResult yuxunHunanDianxinMindResult = yuxunHunanDianxinMindService.accpectOrder(mobile, busProductType, returnUrl);
                return yuxunHunanDianxinMindResult.isOk() ? Result.redirect("redirect telecom vrbt page", yuxunHunanDianxinMindResult.getData().getUrl()) : Result.error(yuxunHunanDianxinMindResult.getMsg());
            } else {
                return Result.error("渠道号请求参数错误");
            }
        } catch (Exception e) {
            log.error("湖南电信业务下单异常:", e);
            return Result.error("系统错误，请稍后再试");
        }
    }


    /**
     * 抽奖
     *
     * @param mobile
     * @return
     */
    @PostMapping(value = "/alipay/luck")
    @ResponseBody
    public FebsResponse alipayLuck(@RequestParam("mobile") String mobile,
                                   @RequestParam("channel") String channel,
                                   @RequestParam(value = "productName", required = false, defaultValue = "") String productName,
                                   @RequestParam(value = "consignee", required = false, defaultValue = "") String consignee,
                                   @RequestParam(value = "shippingAddress", required = false, defaultValue = "") String shippingAddress,
                                   @RequestParam(value = "detailAddress", required = false, defaultValue = "") String detailAddress,
                                   @RequestParam(value = "consigneeMobile", required = false, defaultValue = "") String consigneeMobile,
                                   @RequestParam(value = "prizeName", required = false, defaultValue = "") String prizeName,
                                   @RequestParam(value = "prizeType", required = false, defaultValue = "") String prizeType,
                                   @RequestParam(value = "luckDrawStatus", required = false, defaultValue = "") String luckDrawStatus) {
        FebsResponse febsResponse = new FebsResponse();
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(Regexp.MOBILE_REG)) {
            return febsResponse.fail().message("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(channel)) {
            return febsResponse.fail().message("请输入渠道号");
        }
        //黑名单直接返回
        if (blackListService.isBlackList(mobile)) {
            return febsResponse.fail().message(Result.MSG_BLACK_LIMIT);
        }

        synchronized (interner.intern(mobile)) {
            return luckDrawLogService.alipayLuck(mobile, channel, productName, consignee, shippingAddress, detailAddress, consigneeMobile, prizeName, prizeType, luckDrawStatus);
        }
    }


    /**
     * 河图会员直充结果回调
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "河图会员直充结果回调", notes = "河图会员直充结果回调")
    @PostMapping("/hetuRechargeResult")
    public FebsResponse notifyHeTuRechargeResult(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String requestBody = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("河图会员直充结果回调=>通知:{}", requestBody);
            final HeTuResult heTuResult = new ObjectMapper().readValue(requestBody, HeTuResult.class);
            return junboChargeLogService.notifyHeTuRechargeResult(heTuResult);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return new FebsResponse().fail().message(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new FebsResponse().fail().message("系统异常！");
        }
    }

    /**
     * 阿里支付渠道配置获取
     *
     * @param key
     * @return
     */
    @RequestMapping(value = "/getAliChannelConfig")
    public FebsResponse getAliChannelConfig(String key) {

        return aliChannelConfigService.getAliChannelConfig(key);
    }

    @GetMapping("/getWxPhoneNum")
    @ApiOperation("微信小程序获取手机号")
    public FebsResponse getPhoneNum(String code) {
        FebsResponse febsResponse = new FebsResponse();
        if (StringUtils.isBlank(code)) {
            return febsResponse.fail().message("code不能为空");
        }
        String phoneNum = wxMiniApiService.getPhoneNum(code,null);
        return febsResponse.success().data(phoneNum);
    }


    /**
     * 河图寻仙记权益充值
     *
     * @param requestBody
     * @return
     */
    @RequestMapping(value = "/miguhuyu/prop/recharge")
    public MiGuHuYuNotifyResp miguPropRecharge(@RequestBody String requestBody, HttpServletRequest request) {

        try {
            final MiGuHuYuResult miGuHuYuResult = new ObjectMapper().readValue(requestBody, MiGuHuYuResult.class);
            log.info("咪咕互娱权益下发通知-game:{},手机号:{},body:{}", "hetu", MiGuHuYuResult.decodeUserId2Mobile(miGuHuYuResult.getUserId()), requestBody);
            final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("MIGUHUYU");
            if (otherRecharge == null) {
                log.warn("咪咕互娱权益下发通知-业务渠道未配置-game:{},手机号:{},body:{}", "hetu", MiGuHuYuResult.decodeUserId2Mobile(miGuHuYuResult.getUserId()), requestBody);
                return MiGuHuYuNotifyResp.error("业务渠道未配置");
            }
            String sign = request.getHeader("sign");
            if (!checkSign(sign, miGuHuYuResult.getOrderId(), miGuHuYuResult.getUserId(), miGuHuYuResult.getCreateTime(), otherRecharge)) {
                log.warn("咪咕互娱权益下发通知-签名验证失败-game:{},手机号:{},body:{}", "hetu", MiGuHuYuResult.decodeUserId2Mobile(miGuHuYuResult.getUserId()), requestBody);
                return MiGuHuYuNotifyResp.error("签名验证失败");
            }

            return junboChargeLogService.miguPropRecharge(miGuHuYuResult);

        } catch (Exception e) {
            log.error("咪咕互娱权益下发异常-game:{},body:{}", "hetu", requestBody, e);
        }
        return MiGuHuYuNotifyResp.error("系统错误");
    }


    /**
     * 验证签名
     *
     * @param sign
     * @param orderId
     * @param userId
     * @param createTime
     * @return
     */
    private Boolean checkSign(String sign, String orderId, String userId, String createTime, OtherRecharge otherRecharge) {
        String parameterStr = orderId + userId + createTime + otherRecharge.getKey();
        try {
            String ourSign = DigestUtils.md5DigestAsHex(parameterStr.getBytes(StandardCharsets.UTF_8.name()));
            if (StringUtils.equals(sign, ourSign)) {
                return true;
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.error("咪咕互娱权益充值,加密参数:{},密钥:{}", parameterStr, sign, e);
        }
        return false;
    }


    /**
     * 支付宝领取权益生成图形验证码
     *
     * @param response
     * @param key
     * @return
     */
    @ApiOperation("获取验证码")
    @GetMapping(value = "/alipay/recharge/randomImage/{key}")
    public Result<String> alipayRechargeRandomImage(HttpServletResponse response, @PathVariable String key) {
        Result<String> res = new Result<String>();
        try {
            String code = RandomUtil.randomString(BizConstant.BASE_CHECK_CODES, 6);
            String lowerCaseCode = code.toLowerCase();
            String realKey = MD5Util.MD5Encode(lowerCaseCode + key, "utf-8");
            redisUtil.set(realKey, lowerCaseCode, 60);
            String base64 = RandImageUtils.outputVerifyImage(code);
            res.setSuccess(true);
            res.setResult(base64);
        } catch (Exception e) {
            res.error500("获取验证码出错" + e.getMessage());
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 图形验证码校验
     *
     * @param imgCaptcha
     * @param checkKey
     * @return
     */
    @ApiOperation(value = "图形验证码校验", notes = "图形验证码校验")
    @PostMapping(value = "/check/randomImage")
    @ResponseBody
    public FebsResponse checkRandomImage(@RequestParam("imgCaptcha") String imgCaptcha,
                                         @RequestParam("checkKey") String checkKey) {
        log.info("图形验证码校验=>imgCaptcha:{},checkKey:{}", imgCaptcha, checkKey);
        if (Strings.isNullOrEmpty(imgCaptcha) || !imgCaptcha.matches("^[A-Za-z0-9]{6}$") || Strings.isNullOrEmpty(checkKey)) {
            return new FebsResponse().fail().message("图形验证码错误");
        }

        String lowerCaseCaptcha = imgCaptcha.toLowerCase();
        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha + checkKey, "utf-8");
        Object checkCode = redisUtil.get(realKey);
        if (checkCode == null || !checkCode.equals(lowerCaseCaptcha)) {
            return new FebsResponse().fail().message("图形验证码错误");
        }
        return new FebsResponse().success();
    }

    /**
     * 支付宝领取权益会员登录
     *
     * @param mobile
     * @param captcha
     * @return
     */
    @ApiOperation(value = "支付宝领取权益会员登录", notes = "支付宝领取权益会员登录")
    @PostMapping(value = "/alipay/recharge/member/login")
    @ResponseBody
    public FebsResponse alipayMemberLogin(@RequestParam("mobile") String mobile,
                                          @RequestParam("captcha") String captcha,
                                          @RequestParam(name = "dev", required = false, defaultValue = "") String dev,
                                          @RequestParam(name = "channelCode", required = false, defaultValue = BizConstant.BIZ_QYLI_ALL_CHANNEL_CODE) String channelCode,
                                          @RequestParam(name = "serviceId", required = false, defaultValue = "") String serviceId,
                                          @RequestParam("imgCaptcha") String imgCaptcha,
                                          @RequestParam("checkKey") String checkKey) {
        log.info("支付宝领取权益会员登录=>mobile:{},captcha:{},imgCaptcha:{},checkKey:{}", mobile, captcha, imgCaptcha, checkKey);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        //测试手机，不校验验证码
        if (StringUtils.equalsAny("18482155682", mobile)) {
            return memberService.unifyLogin(mobile, channelCode, serviceId);
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return new FebsResponse().fail().message("验证码错误");
        }
        if (Strings.isNullOrEmpty(imgCaptcha) || !imgCaptcha.matches("^[A-Za-z0-9]{6}$") || Strings.isNullOrEmpty(checkKey)) {
            return new FebsResponse().fail().message("图形验证码错误");
        }

        String lowerCaseCaptcha = imgCaptcha.toLowerCase();
        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha + checkKey, "utf-8");
        Object checkCode = redisUtil.get(realKey);
        if (checkCode == null || !checkCode.equals(lowerCaseCaptcha)) {
            return new FebsResponse().fail().message("图形验证码错误");
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            return new FebsResponse().fail().message(e.getMessage());
        }
        if (StringUtils.isNotEmpty(dev)) {
            return memberService.unifyTestLogin(mobile, channelCode);
        } else {
            return memberService.unifyLogin(mobile, channelCode, serviceId);
        }
    }

    @PostMapping("/wyy/order/notify")
    @ApiOperation("网易云MM回调通知")
    public String wangyiyunMMNotify(HttpServletRequest request) {
        String succRespMsg = "success";
        String failRespMsg = "failure";
        int num = 0;
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        DateFormat mDf = new SimpleDateFormat("yyyy-MM");

        final String mercId = request.getParameter("merc_id");
        final String appId = request.getParameter("app_id");
        final String orderId = request.getParameter("orderid");
        final String appOrderId = request.getParameter("app_orderid");
        final String userId = request.getParameter("userid");
        final String phone = request.getParameter("phone");
        final String payTime = request.getParameter("pay_time");
        final String recAmount = request.getParameter("rec_amount");
        final String payAmount = request.getParameter("pay_amount");
        final String status = request.getParameter("status");
        final String isMonthly = request.getParameter("is_monthly");
        final String isRenew = request.getParameter("is_renew");
        WangyiyunMMNotifyParam wangyiyunMMNotifyParam = new WangyiyunMMNotifyParam();
        wangyiyunMMNotifyParam.setMercId(mercId);
        wangyiyunMMNotifyParam.setAppId(appId);
        wangyiyunMMNotifyParam.setOrderId(orderId);
        wangyiyunMMNotifyParam.setAppOrderId(appOrderId);
        wangyiyunMMNotifyParam.setUserId(userId);
        wangyiyunMMNotifyParam.setPayTime(payTime);
        wangyiyunMMNotifyParam.setRecAmount(recAmount);
        wangyiyunMMNotifyParam.setPayAmount(payAmount);
        wangyiyunMMNotifyParam.setStatus(status);
        wangyiyunMMNotifyParam.setIsMonthly(isMonthly);
        wangyiyunMMNotifyParam.setIsRenew(isRenew);

        try {
            //log.info("网易云MM回调通知,全部入参:{}",mapper.writeValueAsString(request.getParameterMap()));
            //log.info("网易云MM回调通知,wangyiyunMMNotifyParam:{}",mapper.writeValueAsString(wangyiyunMMNotifyParam));
            log.info("网易云MM回调通知,wangyiyunMMNotifyParam:{}", wangyiyunMMNotifyParam);
            if (StringUtils.isBlank(wangyiyunMMNotifyParam.getOrderId())) {
                log.error("网易云MM回调通知,订单号(orderId)为空,手机号:{}", phone);
                return succRespMsg;
            }
            if (StringUtils.isBlank(wangyiyunMMNotifyParam.getAppOrderId())) {
                log.error("网易云MM回调通知,订单号(appOrderId)为空,手机号:{}", phone);
                return succRespMsg;
            }
            if (StringUtils.isBlank(wangyiyunMMNotifyParam.getStatus())) {
                log.error("网易云MM回调通知,订单状态(status)为空,手机号:{}", phone);
                return succRespMsg;
            }
            //查找订单，修改状态
            Subscribe subscribe = subscribeService.findByIspOrderId(orderId);
            List<WyyMmOrder> list = wyyMmOrderService.lambdaQuery().eq(WyyMmOrder::getAppOrderId, appOrderId).list();
            if (subscribe == null || list == null || list.size() == 0) {
                log.error("网易云MM回调通知,找不到订单信息，订单号:{},手机号:{}", orderId, phone);
                return succRespMsg;
            }

            //1=开通成功 续费成功
            if ("1".equals(wangyiyunMMNotifyParam.getStatus())) {

                //0=首次开通成功
                if (("0".equals(isRenew) || StringUtils.isBlank(isRenew)) && list.size() == 1 && list.get(0).getStatus() == 0) {
                    WyyMmOrder wyyMmOrder = list.get(0);
                    wyyMmOrder.setStatus(1);
                    wyyMmOrder.setIsRenew(0);
                    wyyMmOrder.setPayAmount(StringUtils.isBlank(payAmount) ? 1990 : Integer.parseInt(payAmount));
                    wyyMmOrder.setRecAmount(StringUtils.isBlank(recAmount) ? 1990 : Integer.parseInt(recAmount));
                    wyyMmOrder.setPayNo(1);
                    wyyMmOrder.setPayTime(df.parse(payTime));
                    wyyMmOrder.setSubStatus(1);
                    wyyMmOrder.setUserId(userId);
                    wyyMmOrder.setPayMonth(mDf.format(wyyMmOrder.getPayTime()));
                    wyyMmOrder.setRemark("开通成功");
                    wyyMmOrderService.updateById(wyyMmOrder);

                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setResult("开通成功");
                    subscribe.setOpenTime(new Date());
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    BizLogUtils.logSubscribe(subscribe);
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
                }

                //2=续费成功
                if ("2".equals(isRenew)) {

                    //最近三天如果有成功订单就不再新增
                    for (WyyMmOrder wyyMmOrder : list) {
                        if (wyyMmOrder.getStatus() == 1 && (new Date().getTime() - wyyMmOrder.getPayTime().getTime() < 3 * 24 * 60 * 60 * 1000)) {
                            return succRespMsg;
                        }
                    }

                    //查询扣费成功的次数
                    for (WyyMmOrder wyyMmOrder : list) {
                        if (wyyMmOrder.getStatus().equals(1)) {
                            num++;
                        }
                    }
                    WyyMmOrder wyyMmOrder = new WyyMmOrder();
                    wyyMmOrder.setChannel(list.get(0).getChannel());
                    wyyMmOrder.setOrderId(list.get(0).getOrderId());
                    wyyMmOrder.setAppOrderId(list.get(0).getAppOrderId());
                    wyyMmOrder.setMobile(list.get(0).getMobile());
                    wyyMmOrder.setStatus(1);
                    wyyMmOrder.setAppOrderId(appOrderId);
                    wyyMmOrder.setIsRenew(2);
                    wyyMmOrder.setPayAmount(StringUtils.isBlank(payAmount) ? 1990 : Integer.parseInt(payAmount));
                    wyyMmOrder.setRecAmount(StringUtils.isBlank(recAmount) ? 1990 : Integer.parseInt(recAmount));
                    wyyMmOrder.setPayNo(num + 1);
                    wyyMmOrder.setPayTime(df.parse(payTime));
                    wyyMmOrder.setSubStatus(1);
                    wyyMmOrder.setUserId(userId);
                    wyyMmOrder.setPayMonth(mDf.format(wyyMmOrder.getPayTime()));
                    wyyMmOrder.setCreateTime(new Date());
                    wyyMmOrder.setRemark("续订成功");
                    wyyMmOrderService.save(wyyMmOrder);

                    subscribe.setResult("开通成功" + num + 1);
                    subscribe.setModifyTime(new Date());
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                }

            }

            //3=包月退订
            if ("3".equals(wangyiyunMMNotifyParam.getStatus())) {
                WyyMmOrder wyyMmOrder = new WyyMmOrder();
                wyyMmOrder.setChannel(list.get(0).getChannel());
                wyyMmOrder.setOrderId(list.get(0).getOrderId());
                wyyMmOrder.setAppOrderId(list.get(0).getAppOrderId());
                wyyMmOrder.setMobile(list.get(0).getMobile());
                wyyMmOrder.setStatus(3);
                wyyMmOrder.setAppOrderId(appOrderId);
                wyyMmOrder.setPayAmount(StringUtils.isBlank(payAmount) ? 1990 : Integer.parseInt(payAmount));
                wyyMmOrder.setRecAmount(StringUtils.isBlank(recAmount) ? 1990 : Integer.parseInt(recAmount));
                wyyMmOrder.setSubStatus(2);
                wyyMmOrder.setUserId(userId);
                wyyMmOrder.setCreateTime(new Date());
                wyyMmOrder.setRemark("包月退订");
                wyyMmOrder.setUnSignTime(new Date());
                wyyMmOrderService.save(wyyMmOrder);
                subscribe.setResult("包月退订");
                subscribe.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(subscribe);
            }
        } catch (Exception e) {
            log.error("网易云MM回调通知,出错,订单号:{},手机号:{},异常:", orderId, phone, e);
            return failRespMsg;
        }
        return succRespMsg;
    }


    /**
     * 沃阅读查询充值列表
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "沃阅读查询充值列表", notes = "沃阅读查询充值列表")
    @PostMapping(value = "/woRead/charge/qry")
    @ResponseBody
    public ObjectNode woReadQueryCharge(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonStr = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("沃阅读查询充值列表=>参数:{}", jsonStr);
            JsonNode jsonNode = mapper.readTree(jsonStr);
            String phone = jsonNode.at("/phone").asText();
            String date = jsonNode.at("/date").asText();
            return memberService.woReadQueryCharge(phone, date);
        } catch (Exception e) {
            e.printStackTrace();
            ObjectNode objectNode = mapper.createObjectNode();
            objectNode.put("code", "500");
            objectNode.putPOJO("message", "系统错误！");
            return objectNode;
        }
    }

    /**
     * 无纸化预览接口
     *
     * @param transactionId
     * @return
     */
    @ApiOperation(value = "无纸化预览", notes = "无纸化预览")
    @RequestMapping(value = "/jiangxiYidong/noPageWrite")
    @ResponseBody
    public Result noPageWrite(@RequestParam String transactionId) {
        Subscribe subscribe = subscribeService.getById(transactionId);
        JiangxiYidongNoPageWriteResult jiangxiYidongNoPageWriteResult = jiangxiYidongService.noPageWrite(subscribe.getMobile(), subscribe.getChannel(), subscribe.getIspOrderNo(), subscribe.getServiceId(), "Y", "");
        if (jiangxiYidongNoPageWriteResult.isOk()) {
            return Result.okAndSetData(jiangxiYidongNoPageWriteResult.getData().getResultMsg().getResult().getPdfBase64());
        } else {
            return Result.error("无纸化预览失败");
        }
    }

    /**
     * 联通沃悦读微信支付宝异步通知
     *
     * @param woReadNotifyResult 只要回调了，就代表扣费成功
     * @return
     */
    @PostMapping("/pay/wyd/notify")
    @ApiOperation("联通沃悦读微信支付宝异步通知")
    public String chudianNotify(@RequestBody WoReadNotifyResult woReadNotifyResult) {
        String succRespMsg = "success";
        String failRespMsg = "failure";
        try {
            log.info("联通沃悦读回调通知,参数:{}", mapper.writeValueAsString(woReadNotifyResult));
            String orderId = woReadNotifyResult.getOrderId();
            if (StringUtils.isBlank(orderId)) {
                log.info("联通沃悦读微信支付宝异步通知,订单号为空,参数:{}", mapper.writeValueAsString(woReadNotifyResult));
                return failRespMsg;
            }
            WoReadOrder woReadOrder = woReadOrderService.lambdaQuery()
                    .eq(WoReadOrder::getOutTradeNo, orderId)
                    .eq(WoReadOrder::getMobile, woReadNotifyResult.getUserAccount())
                    .one();
            if (woReadOrder == null) {
                log.info("联通沃悦读微信支付宝异步通知,找不到订单,参数:{}", mapper.writeValueAsString(woReadNotifyResult));
                return failRespMsg;
            }
            woReadOrder.setOrderStatus(1);
            woReadOrder.setUserId(woReadNotifyResult.getUserId());
            woReadOrder.setSubStatus(2);
            woReadOrder.setPayTime(new Date());
            woReadOrder.setPayMonth(LocalDate.now().getYear() + "-" + LocalDate.now().getMonthValue());
            woReadOrder.setPayNo(1);
            woReadOrderService.updateById(woReadOrder);
            Subscribe subscribe = subscribeService.lambdaQuery()
                    .eq(Subscribe::getIspOrderNo, orderId)
                    .last("limit 1")
                    .one();
            if (subscribe != null) {
                subscribe.setStatus(1);
                subscribe.setOpenTime(new Date());
                subscribe.setResult("付费成功");
                subscribe.setRemark(orderId);
                subscribeService.updateSubscribeDbAndEs(subscribe);
                BizLogUtils.logSubscribe(subscribe);
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }
        } catch (Exception e) {
            log.error("联通沃悦读回调通知,参数:{},异常:", woReadNotifyResult.toString(), e);
            return failRespMsg;
        }
        return succRespMsg;
    }

    /**
     * 联通沃悦读-微信周期扣款
     *
     * @return
     */
    @ApiOperation(value = "联通沃悦读-微信周期扣款", notes = "联通沃悦读-微信周期扣款")
    @PostMapping(value = "/woReadWxSignUp")
    public FebsResponse woReadWxSignUp(HttpServletRequest request, HttpServletResponse response) {

        FebsResponse febsResponse = new FebsResponse();
        String contractCode = "";
        String company = "";
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("渠道订阅请求=>ip:{},{}", ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);

            String businessType = subscribe.getBusinessType();
            String mobile = subscribe.getMobile();

            if (StringUtils.isBlank(businessType)) {
                return febsResponse.fail().message("支付参数错误");
            }

            /*Alipay alipayConfig = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
            if(alipayConfig==null){
                return febsResponse.fail().message("支付参数错误");
            }*/

            subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            subscribe.setReferer(finger);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());

            if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
                return febsResponse.fail().message("请正确填写手机号");
            }
            //黑名单直接返回
            if (blackListService.isBlackList(subscribe.getMobile())) {
                return febsResponse.fail().message(Result.MSG_BLACK_LIMIT);
            }

            //YRWYD1990 屏蔽四川 山东 广东的联通号码
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if ("YRWYD1990".equals(businessType)
                    && mobileRegionResult != null
                    && mobileRegionResult.isIspLiantong()
                    && WO_READ_PROVINCE_lIMIT_LIST.contains(mobileRegionResult.getProvince())) {
                return febsResponse.fail().message("今日活动已结束，请明日再来!");
            }

            if (!BizConstant.ALIPAY_LIST.contains(businessType)) {
                //同一手机号只能签约一次
                List<AliSignRecord> signList = aliSignRecordService.lambdaQuery()
                        .eq(AliSignRecord::getMobile, subscribe.getMobile())
                        .eq(AliSignRecord::getSignStatus, 1)
                        .notIn(AliSignRecord::getBusinessType, BizConstant.ALIPAY_LIST)
                        .list();
                List<WoReadOrder> woReadSignList = woReadOrderService.lambdaQuery()
                        .eq(WoReadOrder::getMobile, mobile)
                        .eq(WoReadOrder::getSubStatus, 2)
                        .list();
                if ((signList != null && signList.size() > 0) || (woReadSignList != null && woReadSignList.size() > 0)) {
                    return febsResponse.fail().message("您已是会员，请勿重复订购");
                }
                //同一产品同一手机号解约后半年内不能再次签约
                LocalDateTime start = LocalDateTime.now().plusMonths(-6);
                LocalDateTime end = LocalDateTime.now();
                List<AliSignRecord> unSignList = aliSignRecordService.lambdaQuery()
                        .eq(AliSignRecord::getMobile, subscribe.getMobile())
                        .between(AliSignRecord::getUpdateTime, start, end)
                        .eq(AliSignRecord::getSignStatus, 3)
                        .notIn(AliSignRecord::getBusinessType, BizConstant.ALIPAY_LIST)
                        .list();
                List<WoReadOrder> woReadUnSignList = woReadOrderService.lambdaQuery()
                        .eq(WoReadOrder::getMobile, mobile)
                        .eq(WoReadOrder::getSubStatus, 3)
                        .between(WoReadOrder::getUnSignTime, start, end)
                        .list();
                if ((unSignList != null && unSignList.size() > 0) || (woReadUnSignList != null && woReadUnSignList.size() > 0)) {
                    return febsResponse.fail().message("您已是会员，请勿重复订购");
                }
            }

            //根据渠道号查询公司
            List<AliChannelConfig> list = aliChannelConfigService.lambdaQuery()
                    .eq(AliChannelConfig::getConfigValue, businessType)
                    .list();
            if (list == null) {
                return febsResponse.fail().message("支付参数错误");
            }
            for (AliChannelConfig aliChannelConfig : list) {
                if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 2) {
                    contractCode = "yr" + IdWorker.getId();
                    company = "yrjy";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 3) {
                    contractCode = "mh" + IdWorker.getId();
                    company = "maihe";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 4) {
                    contractCode = "hy" + IdWorker.getId();
                    company = "hydt";
                }
            }

            //发起签约
            WoReadWxBeforeOrderResult woReadWxBeforeOrderResult = woReadApiService.wxBeforeorder(mobile, ipAddr, contractCode, businessType, company);
            if (woReadWxBeforeOrderResult.isOK()) {
                febsResponse.success().data(woReadWxBeforeOrderResult.getData().getReqUrl());
                subscribe.setIspOrderNo(woReadWxBeforeOrderResult.getData().getOutTradeNo());
            } else {
                febsResponse.fail().message(woReadWxBeforeOrderResult.getResMessage());
            }
            //保存签约信息
            woReadOrderService.saveOrder(subscribe, contractCode, 1, company);
        } catch (Exception e) {
            log.info("联通沃悦读-微信周期扣款出错：", e);
        }
        return febsResponse;
    }


    /**
     * 联通沃悦读-支付宝周期扣款
     *
     * @return
     */
    @ApiOperation(value = "联通沃悦读-支付宝周期扣款", notes = "联通沃悦读-支付宝周期扣款")
    @PostMapping(value = "/woReadAliSignUp")
    public FebsResponse woReadAliSignUp(HttpServletRequest request, HttpServletResponse response) {

        FebsResponse febsResponse = new FebsResponse();
        String contractCode = "";
        String company = "";
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("渠道订阅请求=>ip:{},{}", ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);

            String businessType = subscribe.getBusinessType();
            String mobile = subscribe.getMobile();

            if (StringUtils.isBlank(businessType)) {
                return febsResponse.fail().message("支付参数错误");
            }

            /*Alipay alipayConfig = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
            if(alipayConfig==null){
                return febsResponse.fail().message("支付参数错误");
            }*/

            subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            subscribe.setReferer(finger);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());

            if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
                return febsResponse.fail().message("请正确填写手机号");
            }
            //黑名单直接返回
            if (blackListService.isBlackList(subscribe.getMobile())) {
                return febsResponse.fail().message(Result.MSG_BLACK_LIMIT);
            }

            if (!BizConstant.ALIPAY_LIST.contains(businessType)) {
                //同一手机号只能签约一次
                List<AliSignRecord> signList = aliSignRecordService.lambdaQuery()
                        .eq(AliSignRecord::getMobile, subscribe.getMobile())
                        .eq(AliSignRecord::getSignStatus, 1)
                        .notIn(AliSignRecord::getBusinessType, BizConstant.ALIPAY_LIST)
                        .list();
                List<WoReadOrder> woReadSignList = woReadOrderService.lambdaQuery()
                        .eq(WoReadOrder::getMobile, mobile)
                        .eq(WoReadOrder::getSubStatus, 2)
                        .list();
                if ((signList != null && signList.size() > 0) || (woReadSignList != null && woReadSignList.size() > 0)) {
                    return febsResponse.fail().message("您已是会员，请勿重复订购");
                }
                //同一手机号解约后半年内不能再次签约
                LocalDateTime start = LocalDateTime.now().plusMonths(-6);
                LocalDateTime end = LocalDateTime.now();
                List<AliSignRecord> unSignList = aliSignRecordService.lambdaQuery()
                        .eq(AliSignRecord::getMobile, subscribe.getMobile())
                        .between(AliSignRecord::getUpdateTime, start, end)
                        .eq(AliSignRecord::getSignStatus, 3)
                        .notIn(AliSignRecord::getBusinessType, BizConstant.ALIPAY_LIST)
                        .list();
                List<WoReadOrder> woReadUnSignList = woReadOrderService.lambdaQuery()
                        .eq(WoReadOrder::getMobile, mobile)
                        .eq(WoReadOrder::getSubStatus, 3)
                        .between(WoReadOrder::getUnSignTime, start, end)
                        .list();
                if ((unSignList != null && unSignList.size() > 0) || (woReadUnSignList != null && woReadUnSignList.size() > 0)) {
                    return febsResponse.fail().message("您已是会员，请勿重复订购");
                }
            }

            //根据渠道号查询公司
            List<AliChannelConfig> list = aliChannelConfigService.lambdaQuery()
                    .eq(AliChannelConfig::getConfigValue, businessType)
                    .list();
            if (list == null) {
                return febsResponse.fail().message("支付参数错误");
            }
            for (AliChannelConfig aliChannelConfig : list) {
                if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 2) {
                    contractCode = "yr" + IdWorker.getId();
                    company = "yrjy";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 3) {
                    contractCode = "mh" + IdWorker.getId();
                    company = "maihe";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 4) {
                    contractCode = "hy" + IdWorker.getId();
                    company = "hydt";
                }
            }

            //发起签约
            WoReadAliBeforeOrderResult woReadAliBeforeOrderResult = woReadApiService.aliBeforeorder(mobile, ipAddr, businessType, company);
            if (woReadAliBeforeOrderResult.isOK()) {
                febsResponse.success().data(woReadAliBeforeOrderResult.getData().getReqUrl());
                subscribe.setIspOrderNo(woReadAliBeforeOrderResult.getData().getOrderId());
            } else {
                febsResponse.fail().message(woReadAliBeforeOrderResult.getResMessage());
            }
            //保存签约信息
            woReadOrderService.saveOrder(subscribe, contractCode, 2, company);
        } catch (Exception e) {
            log.info("联通沃悦读-支付宝周期扣款出错：", e);
        }
        return febsResponse;
    }


    /**
     * vr互娱订阅
     *
     * @return
     */
    @ApiOperation(value = "vr互娱订阅", notes = "vr互娱订阅")
    @PostMapping("/vrsub")
    public Result<?> channelSubscribe(HttpServletRequest request) {

        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String app = request.getHeader("x-requested-with");
        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("vr互娱订阅=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonData, userAgent);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            //不保存ua,仅在日志输出
            //subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            //final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            //referer字段存储广告app的包名
            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());
            return subscribeService.vrReceiveOrder(subscribe);

        } catch (JeecgBootException e) {
            log.warn("vr互娱订阅请求,处理异常", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.warn("vr互娱订阅请求,处理异常", e);
            return Result.error("请求参数错误");
        }
    }

    /**
     * 咪咕阅读破解计费通知
     * url?orderid=abcdxyz123&a=3001&tel=13800138000&timestamp=2019011314556&fee=10000&cpparam=testcpparam&msg=%12%12%12&code=400;
     * url?orderid=abcdxyz123&a=3001&tel=13800138000&timestamp=2019011314556&fee=10000&cpparam=testcpparam&msg=%12%12%12&code=400
     *
     * @return
     */
    @RequestMapping(value = "/vrNotify")
    @ResponseBody
    //使用map接受所有参数,多个同名参数只留第一个,因为没有用数组作为参数值
    public String vrNotify(@RequestParam Map<String, String> parameters) {
        String orderid = parameters.getOrDefault("orderid", "");
        String a = parameters.getOrDefault("a", "");
        String tel = parameters.getOrDefault("tel", "");
        String timestamp = parameters.getOrDefault("timestamp", "");
        String fee = parameters.getOrDefault("fee", "");
        String cpparam = parameters.getOrDefault("cpparam", "");
        String msg = parameters.getOrDefault("msg", "");
        String code = parameters.getOrDefault("code", "");
        try {
            log.info("咪咕互娱计费通知参数=>orderid:{},a:{},tel:{},timestamp:{},fee:{},cppram:{},msg:{},code:{}", orderid, a, tel, timestamp, fee, cpparam, msg, code);
            subscribeService.wujiongCrackNotify(tel, orderid, code, msg);
        } catch (Exception e) {
            log.error("咪咕互娱计费通知参数=>map:{}", parameters, e);
        }
        return "ok";
    }


    /**
     * 咪咕阅读破解计费通知
     * url?orderid=abcdxyz123&a=3001&tel=13800138000&timestamp=2019011314556&fee=10000&cpparam=testcpparam&msg=%12%12%12&code=400;
     * url?orderid=abcdxyz123&a=3001&tel=13800138000&timestamp=2019011314556&fee=10000&cpparam=testcpparam&msg=%12%12%12&code=400
     *
     * @return
     */
    @RequestMapping(value = "/bjhyNotify")
    @ResponseBody
    //使用map接受所有参数,多个同名参数只留第一个,因为没有用数组作为参数值
    public String bjhyNotify(@RequestParam Map<String, String> parameters) {
        String orderid = parameters.getOrDefault("orderid", "");
        String fee = parameters.getOrDefault("fee", "");
        String tel = parameters.getOrDefault("tel", "");
        String time = parameters.getOrDefault("time", "");
        String channel = parameters.getOrDefault("channel", "");
        String cpparam = parameters.getOrDefault("cpparam", "");
        String key = parameters.getOrDefault("key", "");

        try {
            log.info("白金会员组合包计费通知参数=>orderid:{},fee:{},tel:{},time:{},channel:{},cppram:{},key:{}", orderid, fee, tel, time, channel, cpparam, key);
            if (StringUtils.isNotBlank(time) && time.length() > 14) {
                time = time.substring(0, 14);
            }
            subscribeResultNotifyService.receiveBillingResult(tel, orderid, "0000", "开通成功(wj)", new Date(Long.parseLong(time)), "");
        } catch (Exception e) {
            log.error("咪咕互娱计费通知参数=>map:{}", parameters, e);
        }
        return "ok";
    }


    /**
     * 公司渠道配置获取
     *
     * @param key
     * @return
     */
    @RequestMapping(value = "/config/company/channel")
    public FebsResponse getCompanyChannelConfig(String key) {

        return companyChannelConfigService.getCompanyChannelConfig(key);
    }

    /**
     * 讯飞视频彩铃咪咕开通退订数据接收-(基地发给讯飞,讯飞转发给我方的,目前只回传24小时退订)
     *
     * @return 订购 {"timestamp":"20230616163206","oprType":"07","orderType":"00","orderId":"200BIP5A13520230616162903595584","msisdn":"15766075557","channelCode":"1060000","serviceId":"698039035100000043","price":"600"}
     * 退订
     */
    ////@AutoLog(value = "讯飞视频彩铃咪咕开通退订数据接收目前只回传24小时退订")
    @ApiOperation(value = "讯飞视频彩铃咪咕开通退订数据接收(目前只回传24小时退订)", notes = "讯飞视频彩铃咪咕开通退订数据接收(目前只回传24小时退订)")
    @RequestMapping(value = "/xunfei/miguNotifyVrbtOrderResult")
    public String xunfeiMiguNotifyVrbtOrderResult(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            log.info("讯飞视频彩铃咪咕开通退订数据接收,requestMap:{},json数据:{}", requestMap, jsonNode);
        } catch (Exception e) {
            log.info("讯飞视频彩铃咪咕开通退订数据接收,equestMap:{},json数据:{},异常!", requestMap, jsonNode, e);
            return "{\"retcode\":\"9999\",\"retdesc\":\"系统错误\"}";
        }
        return "{\"retcode\":\"0000\",\"retdesc\":\"成功\"}";
    }


    /**
     * 咪咕CMS资源上报回执
     *
     * @return
     */
    ////@AutoLog(value = "咪咕CMS资源上报回执")
    @ApiOperation(value = "咪咕CMS资源上报回执", notes = "咪咕CMS资源上报回执")
    @RequestMapping(value = "/zhongyinNotify/resUpload")
    public VrbtDiyResponse zhongyinNotifyResUpload(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            log.info("咪咕CMS资源上报回执,requestMap:{},json数据:{}", requestMap, jsonNode);
            JsonNode headNode = jsonNode.at("/head");
            JsonNode bodyNode = jsonNode.at("/body");
            if (headNode == null || bodyNode == null) {
                return VrbtDiyResponse.success();
            }
            String dId = headNode.at("/dID") == null ? "" : headNode.at("/dID").asText();
            String sEq = headNode.at("/sEQ") == null ? "" : headNode.at("/sEQ").asText();
            String dIdPwd = headNode.at("/dIDPwd") == null ? "" : headNode.at("/dIDPwd").asText();
            String accessPlatformId = headNode.at("/accessPlatformID") == null ? "" : headNode.at("/accessPlatformID").asText();

            String transactionId = bodyNode.at("/transactionID") == null ? "" : bodyNode.at("/transactionID").asText();
            String copyrightType = bodyNode.at("/copyrightType") == null ? "" : bodyNode.at("/copyrightType").asText();
            String copyrightId = bodyNode.at("/copyrightID") == null ? "" : bodyNode.at("/copyrightID").asText();
            String returnCode = bodyNode.at("/returnCode") == null ? "" : bodyNode.at("/returnCode").asText();
            String returnDesc = bodyNode.at("/returnDesc") == null ? "" : bodyNode.at("/returnDesc").asText();

            if (StringUtils.isBlank(dIdPwd)
                    || StringUtils.isBlank(transactionId)
                    || StringUtils.isBlank(returnCode)) {
                return VrbtDiyResponse.success();
            }
            //验证订单存在
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.lambdaQuery()
                    .eq(VrbtDiyVideo::getTransactionId, transactionId)
                    .one();
            if (vrbtDiyVideo == null) {
                log.info("咪咕CMS资源上报回执,订单不存在,transactionId:{}", transactionId);
                return VrbtDiyResponse.success();
            }
            //验证dIdPwd
            /*boolean b = vrbtDiyVideoService.checkHead(dIdPwd,vrbtDiyVideo);
            if(!b){
                return VrbtDiyResponse.success();
            }*/
            //修改状态
            vrbtDiyVideo.setCopyrightId(copyrightId);
            vrbtDiyVideo.setCopyrightType(Integer.parseInt(copyrightType));
            vrbtDiyVideo.setReportStatus("000000".equals(returnCode) ? 1 : 0);
            vrbtDiyVideo.setReportReturnCode(returnCode);
            vrbtDiyVideo.setReportReturnDesc(returnDesc);
            vrbtDiyVideoService.updateById(vrbtDiyVideo);
        } catch (Exception e) {
            log.info("咪咕CMS资源上报回执,equestMap:{},json数据:{},异常!", requestMap, jsonNode, e);
            return VrbtDiyResponse.success();
        }
        return VrbtDiyResponse.success();
    }

    /**
     * 咪咕CMS资源审核回执
     *
     * @return
     */
    ////@AutoLog(value = "咪咕CMS资源审核回执")
    @ApiOperation(value = "咪咕CMS资源审核回执", notes = "咪咕CMS资源审核回执")
    @RequestMapping(value = "/zhongyinNotify/resUploadAudit")
    public VrbtDiyResponse zhongyinNotifyResUploadAudit(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            log.info("咪咕CMS资源审核回执,requestMap:{},json数据:{}", requestMap, jsonNode);
            JsonNode headNode = jsonNode.at("/head");
            JsonNode bodyNode = jsonNode.at("/body");
            if (headNode == null || bodyNode == null) {
                return VrbtDiyResponse.success();
            }
            String dId = headNode.at("/dID") == null ? "" : headNode.at("/dID").asText();
            String sEq = headNode.at("/sEQ") == null ? "" : headNode.at("/sEQ").asText();
            String dIdPwd = headNode.at("/dIDPwd") == null ? "" : headNode.at("/dIDPwd").asText();
            String accessPlatformId = headNode.at("/accessPlatformID") == null ? "" : headNode.at("/accessPlatformID").asText();

            String transactionId = bodyNode.at("/transactionID") == null ? "" : bodyNode.at("/transactionID").asText();
            String copyrightType = bodyNode.at("/copyrightType") == null ? "" : bodyNode.at("/copyrightType").asText();
            String copyrightId = bodyNode.at("/copyrightID") == null ? "" : bodyNode.at("/copyrightID").asText();
            String returnCode = bodyNode.at("/returnCode") == null ? "" : bodyNode.at("/returnCode").asText();
            String returnDesc = bodyNode.at("/returnDesc") == null ? "" : bodyNode.at("/returnDesc").asText();

            if (StringUtils.isBlank(dIdPwd)
                    || StringUtils.isBlank(transactionId)
                    || StringUtils.isBlank(returnCode)) {
                return VrbtDiyResponse.success();
            }
            //验证订单存在
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.lambdaQuery()
                    .eq(VrbtDiyVideo::getTransactionId, transactionId)
                    .one();
            if (vrbtDiyVideo == null) {
                log.info("咪咕CMS资源审核回执,订单不存在,transactionId:{}", transactionId);
                return VrbtDiyResponse.success();
            }
            //验证dIdPwd
            /*boolean b = vrbtDiyVideoService.checkHead(dIdPwd,vrbtDiyVideo);
            if(!b){
                return VrbtDiyResponse.success();
            }*/
            //修改状态
            vrbtDiyVideo.setAuditStatus("000000".equals(returnCode) ? 1 : 0);
            vrbtDiyVideo.setAuditReturnCode(returnCode);
            vrbtDiyVideo.setAuditReturnDesc(returnDesc);
            vrbtDiyVideoService.updateById(vrbtDiyVideo);
        } catch (Exception e) {
            log.info("咪咕CMS资源审核回执,equestMap:{},json数据:{},异常!", requestMap, jsonNode, e);
            return VrbtDiyResponse.success();
        }
        return VrbtDiyResponse.success();

    }

    /**
     * 咪咕曲库资源分发信息回执
     *
     * @return
     */
    ////@AutoLog(value = "咪咕曲库资源分发信息回执")
    @ApiOperation(value = "咪咕曲库资源分发信息回执", notes = "咪咕曲库资源分发信息回执")
    @RequestMapping(value = "/zhongyinNotify/resDeploy/vRBTDeployIMPProductNotify.json")
    public VrbtDiyResponse zhongyinNotifyResDeployIMPProduct(@RequestParam Map<String, Object> requestMap) {

        log.info("咪咕曲库资源分发信息回执,requestMap:{}", requestMap);
        try {
            String data = requestMap.get("VRBTDeployIMPProductNotifyEvt") == null ? "" : requestMap.get("VRBTDeployIMPProductNotifyEvt").toString();
            JsonNode jsonNode = mapper.readTree(data);
            String dId = jsonNode.at("/DID") == null ? "" : jsonNode.at("/DID").asText();
            String sEq = jsonNode.at("/SEQ") == null ? "" : jsonNode.at("/SEQ").asText();
            String dIdPwd = jsonNode.at("/DIDPwd") == null ? "" : jsonNode.at("/DIDPwd").asText();
            String accessPlatformId = jsonNode.at("/accessPlatformID") == null ? "" : jsonNode.at("/accessPlatformID").asText();

            String transactionId = jsonNode.at("/transactionID") == null ? "" : jsonNode.at("/transactionID").asText();
            String eventType = jsonNode.at("/eventType") == null ? "" : jsonNode.at("/eventType").asText();
            JsonNode contentInfoNode = jsonNode.at("/contentInfo");

            if (contentInfoNode == null) {
                log.info("咪咕曲库资源分发信息回执,参数错误");
                return VrbtDiyResponse.success();
            }
            String musicId = contentInfoNode.at("/musicID") == null ? "" : contentInfoNode.at("/musicID").asText();
            String toneId = contentInfoNode.at("/toneID") == null ? "" : contentInfoNode.at("/toneID").asText();

            if (StringUtils.isBlank(dIdPwd)
                    || StringUtils.isBlank(musicId)) {
                log.info("咪咕曲库资源分发信息回执,参数错误,musicId:{}", musicId);
                return VrbtDiyResponse.success();
            }
            //验证订单存在
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.lambdaQuery()
                    .eq(VrbtDiyVideo::getCopyrightId, StringUtils.substringBeforeLast(musicId, "M"))
                    .one();
            if (vrbtDiyVideo == null) {
                log.info("咪咕曲库资源分发信息回执,订单不存在,musicId:{}", musicId);
                return VrbtDiyResponse.success();
            }
            //验证dIdPwd
            /*boolean b = vrbtDiyVideoService.checkHead(dIdPwd,vrbtDiyVideo);
            if(!b){
                return VrbtDiyResponse.success();
            }*/
            //修改状态
            vrbtDiyVideo.setMusicId(musicId);
            vrbtDiyVideo.setToneId(toneId);
            vrbtDiyVideo.setDistributeStatus(1);
            vrbtDiyVideo.setAuditStatus(1);//审核成功
            vrbtDiyVideoService.updateById(vrbtDiyVideo);
        } catch (Exception e) {
            log.info("咪咕曲库资源分发信息回执,equestMap:{},异常!", requestMap, e);
            return VrbtDiyResponse.success();
        }
        return VrbtDiyResponse.success();
    }

    /**
     * 咪咕曲库资源分发成功率回执
     *
     * @return
     */
    ////@AutoLog(value = "咪咕曲库资源分发成功率回执")
    @ApiOperation(value = "咪咕曲库资源分发成功率回执", notes = "咪咕曲库资源分发成功率回执")
    @RequestMapping(value = "/zhongyinNotify/resDeploy/syncCrbtDeployResult.json")
    public VrbtDiyResponse zhongyinNotifyResDeployResult(@RequestParam Map<String, Object> requestMap) {

        log.info("咪咕曲库资源分发成功率回执,requestMap:{}", requestMap);
        try {
            String data = requestMap.get("syncCrbtDeployResultEvt") == null ? "" : requestMap.get("syncCrbtDeployResultEvt").toString();
            JsonNode jsonNode = mapper.readTree(data);
            String pId = jsonNode.at("/PID") == null ? "" : jsonNode.at("/PID").asText();
            String sEq = jsonNode.at("/SEQ") == null ? "" : jsonNode.at("/SEQ").asText();
            String key = jsonNode.at("/KEY") == null ? "" : jsonNode.at("/KEY").asText();
            String accessPlatformId = jsonNode.at("/accessPlatformID") == null ? "" : jsonNode.at("/accessPlatformID").asText();

            String transactionId = jsonNode.at("/transactionID") == null ? "" : jsonNode.at("/transactionID").asText();
            String eventType = jsonNode.at("/eventType") == null ? "" : jsonNode.at("/eventType").asText();
            String successRate = jsonNode.at("/successRate") == null ? "" : jsonNode.at("/successRate").asText();
            String successProvinceName = jsonNode.at("/successProvinceName") == null ? "" : jsonNode.at("/successProvinceName").asText();
            String failProvinceName = jsonNode.at("/failProvinceName") == null ? "" : jsonNode.at("/failProvinceName").asText();
            String toneId = jsonNode.at("/toneID") == null ? "" : jsonNode.at("/toneID").asText();

            if (StringUtils.isBlank(toneId)) {
                log.info("咪咕曲库资源分发成功率回执,参数错误");
                return VrbtDiyResponse.success();
            }
            //验证订单存在
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.lambdaQuery()
                    .eq(VrbtDiyVideo::getToneId, toneId)
                    .one();
            if (vrbtDiyVideo == null) {
                log.info("咪咕曲库资源分发成功率回执,订单不存在,toneId:{}", toneId);
                return VrbtDiyResponse.success();
            }
            //修改状态
            vrbtDiyVideo.setDistributeRatio(successRate);
            vrbtDiyVideo.setSuccessProvinceName(successProvinceName);
            vrbtDiyVideo.setFailProvinceName(failProvinceName);
            //已设置过就不再进行铃声设置
            if (RING_SETTING_STATUS_INIT.equals(vrbtDiyVideo.getSettingStatus()) || RING_SETTING_STATUS_FAIL.equals(vrbtDiyVideo.getSettingStatus())) {
                //设置铃声
                RemoteResult result = vrbtDiyRingService.settingRing(vrbtDiyVideo.getId());
                if (result.isOK()) {
                    vrbtDiyVideo.setSettingStatus(1);
                } else {
                    vrbtDiyVideo.setSettingStatus(0);
                }
                vrbtDiyVideo.setSettingResult(mapper.writeValueAsString(result));
            }
            vrbtDiyVideoService.updateById(vrbtDiyVideo);

        } catch (Exception e) {
            log.info("咪咕曲库资源分发成功率回执,equestMap:{},异常!", requestMap, e);
            return VrbtDiyResponse.success();
        }
        return VrbtDiyResponse.success();
    }


    /**
     * 一语成片上报回执
     *
     * @return
     */
    ////@AutoLog(value = "一语成片上报回执")
    @ApiOperation(value = "一语成片上报回执", notes = "一语成片上报回执")
    @RequestMapping(value = "/ringNotify/resUpload")
    public VrbtDiyResponse ringNotifyResUpload(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("一语成片上报回执,requestMap:{},json数据:{}", requestMap, jsonNode);

        try {
            JsonNode headNode = jsonNode.at("/head");
            JsonNode bodyNode = jsonNode.at("/body");
            if (headNode == null || bodyNode == null) {
                return VrbtDiyResponse.success();
            }
            String dId = headNode.at("/dID") == null ? "" : headNode.at("/dID").asText();
            String sEq = headNode.at("/sEQ") == null ? "" : headNode.at("/sEQ").asText();
            String dIdPwd = headNode.at("/dIDPwd") == null ? "" : headNode.at("/dIDPwd").asText();
            String accessPlatformId = headNode.at("/accessPlatformID") == null ? "" : headNode.at("/accessPlatformID").asText();

            String transactionId = bodyNode.at("/transactionID") == null ? "" : bodyNode.at("/transactionID").asText();
            String copyrightType = bodyNode.at("/copyrightType") == null ? "" : bodyNode.at("/copyrightType").asText();
            String copyrightId = bodyNode.at("/copyrightID") == null ? "" : bodyNode.at("/copyrightID").asText();
            String returnCode = bodyNode.at("/returnCode") == null ? "" : bodyNode.at("/returnCode").asText();
            String returnDesc = bodyNode.at("/returnDesc") == null ? "" : bodyNode.at("/returnDesc").asText();

            if (StringUtils.isBlank(dIdPwd)
                    || StringUtils.isBlank(transactionId)
                    || StringUtils.isBlank(returnCode)) {
                return VrbtDiyResponse.success();
            }
            //验证订单存在
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.lambdaQuery()
                    .eq(VrbtDiyVideo::getTransactionId, transactionId)
                    .one();
            if (vrbtDiyVideo == null) {
                log.info("咪咕CMS资源上报回执,订单不存在,transactionId:{}", transactionId);
                return VrbtDiyResponse.success();
            }
            //验证dIdPwd
            /*boolean b = vrbtDiyVideoService.checkHead(dIdPwd,vrbtDiyVideo);
            if(!b){
                return VrbtDiyResponse.success();
            }*/
            //修改状态
            vrbtDiyVideo.setCopyrightId(copyrightId);
            vrbtDiyVideo.setCopyrightType(Integer.parseInt(copyrightType));
            vrbtDiyVideo.setReportStatus("000000".equals(returnCode) ? 1 : 0);
            vrbtDiyVideo.setReportReturnCode(returnCode);
            vrbtDiyVideo.setReportReturnDesc(returnDesc);
            vrbtDiyVideoService.updateById(vrbtDiyVideo);
        } catch (Exception e) {
            log.info("咪咕CMS资源上报回执,equestMap:{},json数据:{},异常!", requestMap, jsonNode, e);
            return VrbtDiyResponse.success();
        }
        return VrbtDiyResponse.success();
    }

    /**
     * 一语成片审核回执
     *
     * @return
     */
    ////@AutoLog(value = "一语成片审核回执")
    @ApiOperation(value = "一语成片审核回执", notes = "一语成片审核回执")
    @RequestMapping(value = "/ringNotify/resUploadAudit")
    public VrbtDiyResponse ringNotifyResUploadAudit(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("一语成片审核回执,requestMap:{},json数据:{}", requestMap, jsonNode);

        try {
            JsonNode headNode = jsonNode.at("/head");
            JsonNode bodyNode = jsonNode.at("/body");
            if (headNode == null || bodyNode == null) {
                return VrbtDiyResponse.success();
            }
            String dId = headNode.at("/dID") == null ? "" : headNode.at("/dID").asText();
            String sEq = headNode.at("/sEQ") == null ? "" : headNode.at("/sEQ").asText();
            String dIdPwd = headNode.at("/dIDPwd") == null ? "" : headNode.at("/dIDPwd").asText();
            String accessPlatformId = headNode.at("/accessPlatformID") == null ? "" : headNode.at("/accessPlatformID").asText();

            String transactionId = bodyNode.at("/transactionID") == null ? "" : bodyNode.at("/transactionID").asText();
            String copyrightType = bodyNode.at("/copyrightType") == null ? "" : bodyNode.at("/copyrightType").asText();
            String copyrightId = bodyNode.at("/copyrightID") == null ? "" : bodyNode.at("/copyrightID").asText();
            String returnCode = bodyNode.at("/returnCode") == null ? "" : bodyNode.at("/returnCode").asText();
            String returnDesc = bodyNode.at("/returnDesc") == null ? "" : bodyNode.at("/returnDesc").asText();

            if (StringUtils.isBlank(dIdPwd)
                    || StringUtils.isBlank(transactionId)
                    || StringUtils.isBlank(returnCode)) {
                return VrbtDiyResponse.success();
            }
            //验证订单存在
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.lambdaQuery()
                    .eq(VrbtDiyVideo::getTransactionId, transactionId)
                    .one();
            if (vrbtDiyVideo == null) {
                log.info("一语成片审核回执,订单不存在,transactionId:{}", transactionId);
                return VrbtDiyResponse.success();
            }
            //验证dIdPwd
            /*boolean b = vrbtDiyVideoService.checkHead(dIdPwd,vrbtDiyVideo);
            if(!b){
                return VrbtDiyResponse.success();
            }*/
            //修改状态
            vrbtDiyVideo.setAuditStatus("000000".equals(returnCode) ? 1 : 0);
            vrbtDiyVideo.setAuditReturnCode(returnCode);
            vrbtDiyVideo.setAuditReturnDesc(returnDesc);
            vrbtDiyVideoService.updateById(vrbtDiyVideo);
        } catch (Exception e) {
            log.info("一语成片审核回执,equestMap:{},json数据:{},异常!", requestMap, jsonNode, e);
            return VrbtDiyResponse.success();
        }
        return VrbtDiyResponse.success();

    }

    /**
     * 一语成片分发信息回执
     *
     * @return
     */
    ////@AutoLog(value = "一语成片分发信息回执")
    @ApiOperation(value = "一语成片分发信息回执", notes = "一语成片分发信息回执")
    @RequestMapping(value = "/ringNotify/resDeploy/vRBTDeployIMPProductNotify.json")
    public VrbtDiyResponse ringNotifyResDeployIMPProduct(@RequestParam Map<String, Object> requestMap) {

        log.info("一语成片分发信息回执,requestMap:{}", requestMap);
        try {
            String data = requestMap.get("VRBTDeployIMPProductNotifyEvt") == null ? "" : requestMap.get("VRBTDeployIMPProductNotifyEvt").toString();
            JsonNode jsonNode = mapper.readTree(data);
            String dId = jsonNode.at("/DID") == null ? "" : jsonNode.at("/DID").asText();
            String sEq = jsonNode.at("/SEQ") == null ? "" : jsonNode.at("/SEQ").asText();
            String dIdPwd = jsonNode.at("/DIDPwd") == null ? "" : jsonNode.at("/DIDPwd").asText();
            String accessPlatformId = jsonNode.at("/accessPlatformID") == null ? "" : jsonNode.at("/accessPlatformID").asText();

            String transactionId = jsonNode.at("/transactionID") == null ? "" : jsonNode.at("/transactionID").asText();
            String eventType = jsonNode.at("/eventType") == null ? "" : jsonNode.at("/eventType").asText();
            JsonNode contentInfoNode = jsonNode.at("/contentInfo");

            if (contentInfoNode == null) {
                log.info("一语成片分发信息回执,参数错误");
                return VrbtDiyResponse.success();
            }
            String musicId = contentInfoNode.at("/musicID") == null ? "" : contentInfoNode.at("/musicID").asText();
            String toneId = contentInfoNode.at("/toneID") == null ? "" : contentInfoNode.at("/toneID").asText();

            if (StringUtils.isBlank(dIdPwd)
                    || StringUtils.isBlank(musicId)) {
                log.info("一语成片分发信息回执,参数错误,musicId:{}", musicId);
                return VrbtDiyResponse.success();
            }
            //验证订单存在
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.lambdaQuery()
                    .eq(VrbtDiyVideo::getCopyrightId, StringUtils.substringBeforeLast(musicId, "M"))
                    .one();
            if (vrbtDiyVideo == null) {
                log.info("一语成片分发信息回执,订单不存在,musicId:{}", musicId);
                return VrbtDiyResponse.success();
            }
            //验证dIdPwd
            /*boolean b = vrbtDiyVideoService.checkHead(dIdPwd,vrbtDiyVideo);
            if(!b){
                return VrbtDiyResponse.success();
            }*/
            //修改状态
            vrbtDiyVideo.setMusicId(musicId);
            vrbtDiyVideo.setToneId(toneId);
            vrbtDiyVideo.setDistributeStatus(1);
            vrbtDiyVideo.setAuditStatus(1);//审核成功
            vrbtDiyVideoService.updateById(vrbtDiyVideo);
        } catch (Exception e) {
            log.info("一语成片分发信息回执,equestMap:{},异常!", requestMap, e);
            return VrbtDiyResponse.success();
        }
        return VrbtDiyResponse.success();
    }

    /**
     * 一语成片分发成功率回执
     *
     * @return
     */
    ////@AutoLog(value = "咪咕曲库资源分发成功率回执")
    @ApiOperation(value = "一语成片分发成功率回执", notes = "一语成片分发成功率回执")
    @RequestMapping(value = "/ringNotify/resDeploy/syncCrbtDeployResult.json")
    public VrbtDiyResponse ringNotifyResDeployResult(@RequestParam Map<String, Object> requestMap) throws JsonProcessingException {
        try {
            log.info("一语成片分发成功率回执,requestMap:{}", requestMap);
            String data = requestMap.get("syncCrbtDeployResultEvt") == null ? "" : requestMap.get("syncCrbtDeployResultEvt").toString();
            JsonNode jsonNode = mapper.readTree(data);
            String transactionId = jsonNode.at("/transactionID") == null ? "" : jsonNode.at("/transactionID").asText();
            String pId = jsonNode.at("/PID") == null ? "" : jsonNode.at("/PID").asText();
            String sEq = jsonNode.at("/SEQ") == null ? "" : jsonNode.at("/SEQ").asText();
            String key = jsonNode.at("/KEY") == null ? "" : jsonNode.at("/KEY").asText();
            String accessPlatformId = jsonNode.at("/accessPlatformID") == null ? "" : jsonNode.at("/accessPlatformID").asText();

            String eventType = jsonNode.at("/eventType") == null ? "" : jsonNode.at("/eventType").asText();
            String successRate = jsonNode.at("/successRate") == null ? "" : jsonNode.at("/successRate").asText();
            String successProvinceName = jsonNode.at("/successProvinceName") == null ? "" : jsonNode.at("/successProvinceName").asText();
            String failProvinceName = jsonNode.at("/failProvinceName") == null ? "" : jsonNode.at("/failProvinceName").asText();
            String toneId = jsonNode.at("/toneID") == null ? "" : jsonNode.at("/toneID").asText();

            if (StringUtils.isBlank(toneId)) {
                log.info("一语成片分发成功率回执,参数错误");
                return VrbtDiyResponse.success();
            }
            //验证订单存在
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.lambdaQuery()
                    .eq(VrbtDiyVideo::getToneId, toneId)
                    .one();
            if (vrbtDiyVideo == null) {
                log.info("一语成片分发成功率回执,订单不存在,toneId:{}", toneId);
                return VrbtDiyResponse.success();
            }
            //修改状态
            vrbtDiyVideo.setDistributeRatio(successRate);
            vrbtDiyVideo.setSuccessProvinceName(successProvinceName);
            vrbtDiyVideo.setFailProvinceName(failProvinceName);
            //已设置过就不再进行铃声设置
            if (RING_SETTING_STATUS_INIT.equals(vrbtDiyVideo.getSettingStatus()) || RING_SETTING_STATUS_FAIL.equals(vrbtDiyVideo.getSettingStatus())) {
                String channel;
                String vrbtToneType;
                if (vrbtDiyVideo.getTransactionId().contains(ReportTypeEnum.ZX.getCode())) {
                    // 一语成片
                    vrbtToneType = MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE;
                    channel = yycpProperties.getChannel();
                } else {
                    // 抖音小程序
                    vrbtToneType = MiguApiService.VRBT_TONE_ORDER_SETFLAG_BOTH_ZBJ;
                    channel = MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX;
                }
                //设置铃声
                RemoteResult result = miguApiService.vrbtToneFreeMonthOrder(vrbtDiyVideo.getMobile(), channel, vrbtDiyVideo.getCopyrightId(), vrbtToneType);
                vrbtDiyVideo.setSettingStatus(result.isOK() ? RING_SETTING_STATUS_SUCC : RING_SETTING_STATUS_FAIL);
                vrbtDiyVideo.setSettingResult(mapper.writeValueAsString(result));
            }
            vrbtDiyVideoService.updateById(vrbtDiyVideo);
            return VrbtDiyResponse.success();
        } catch (Exception e) {
            log.info("一语成片分发成功率回执,equestMap:{},异常!", requestMap, e);
            return VrbtDiyResponse.success();
        }
    }

    /**
     * 咪咕阅读破解计费通知
     * url?orderid=C91F0BFE2DF00001D1D555D31E701E8F&a=3190&tel=手机号&timestamp=yyyyMMddHHmmssSSS&fee=1990&cpparam=touchuancanshu&msg=success&code=200000;
     *
     * @return
     */
    @RequestMapping(value = "/hetuNotify")
    @ResponseBody
    //使用map接受所有参数,多个同名参数只留第一个,因为没有用数组作为参数值
    public String hetuNotify(@RequestParam Map<String, String> parameters) {
        String orderid = parameters.getOrDefault("orderid", "");
        String a = parameters.getOrDefault("a", "");
        String tel = parameters.getOrDefault("tel", "");
        String timestamp = parameters.getOrDefault("timestamp", "");
        String fee = parameters.getOrDefault("fee", "");
        String cpparam = parameters.getOrDefault("cpparam", "");
        String msg = parameters.getOrDefault("msg", "");
        String code = parameters.getOrDefault("code", "");
        try {
            log.info("河图寻仙游记礼包计费通知参数=>orderid:{},a:{},tel:{},timestamp:{},fee:{},cppram:{},msg:{},code:{}", orderid, a, tel, timestamp, fee, cpparam, msg, code);
            subscribeService.hetuCrackNotify(tel, orderid, code, msg);
        } catch (Exception e) {
            log.error("河图寻仙游记礼包计费通知参数=>map:{}", parameters, e);
        }
        return "ok";
    }


    /**
     * 快手登录获取openId
     *
     * @return
     */
    @PostMapping(value = "/kuaishou/openId")
    public FebsResponse getKuaiShouOpenId(HttpServletRequest request, HttpServletResponse response) {
        FebsResponse febsResponse = new FebsResponse();
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("快手小程序登录获取openId请求=>ip:{},请求参数:{}", ipAddr, jsonData);
            JsonNode jsonNode = mapper.readTree(jsonData);
            String code = jsonNode.at("/code").asText();
            if (StringUtils.isBlank(code)) {
                return febsResponse.fail().message("登录凭证失效");
            }
            return kuaiShouService.getOpenId(code, TRADE_TYPE_KUAISHOU, BUSINESS_TYPE_KS_QYCL);
        } catch (Exception e) {
            log.info("快手小程序登录获取openId出错：", e);
        }
        return febsResponse.fail().message("系统错误");
    }

    /**
     * 快手小程序用户信息解密
     *
     * @return
     */
    @PostMapping(value = "/kuaishou/decrypt")
    public FebsResponse getKuaiShouDecrypt(HttpServletRequest request, HttpServletResponse response) {
        FebsResponse febsResponse = new FebsResponse();
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("快手小程序用户信息解密请求=>ip:{},请求参数:{}", ipAddr, jsonData);
            JsonNode jsonNode = mapper.readTree(jsonData);
            String sessionKey = jsonNode.at("/sessionKey").asText();
            String encryptedData = jsonNode.at("/encryptedData").asText();
            String iv = jsonNode.at("/iv").asText();
            if (StringUtils.isBlank(sessionKey) || StringUtils.isBlank(encryptedData) || StringUtils.isBlank(iv)) {
                return febsResponse.fail().message("登录凭证失效");
            }
            return kuaiShouService.decrypt(sessionKey, encryptedData, iv);
        } catch (Exception e) {
            log.info("快手小程序用户信息解密出错：", e);
        }
        return febsResponse.fail().message("系统错误");
    }


    /**
     * 支付宝小程序用户信息解密
     *
     * @return
     */
    @PostMapping(value = "/alipay/decrypt")
    public FebsResponse getAlipayDecrypt(HttpServletRequest request) {
        FebsResponse febsResponse = new FebsResponse();
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("支付宝小程序用户信息解密请求=>ip:{},请求参数:{}", ipAddr, jsonData);
            JsonNode jsonNode = mapper.readTree(jsonData);
            String response = jsonNode.at("/response").asText();
            String sign = jsonNode.at("/sign").asText();
            String businessType = jsonNode.at("/businessType").asText();
            if (StringUtils.isBlank(response) || StringUtils.isBlank(sign)) {
                return febsResponse.fail().message("登录凭证失效");
            }
            return alipayService.decrypt(response, sign, businessType);
        } catch (Exception e) {
            log.info("支付宝小程序用户信息解密出错：", e);
        }
        return febsResponse.fail().message("系统错误");
    }

    /**
     * oss文件上传ftp
     *
     * @return
     */
    @RequestMapping(value = "/vrbtDiy/ftp/upload")
    public FebsResponse vrbtDiyFtpUpload(String mobile, String filePath, String mvName) {
        FebsResponse febsResponse = new FebsResponse();
        if (StringUtils.isBlank(mobile) || StringUtils.isBlank(filePath) || StringUtils.isBlank(mvName)) {
            return febsResponse.fail().message("参数错误");
        }
        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        String pathName = "/userDiy" + df.format(new Date());
        String fileName = "video" + IdWorker.getId() + ".mp4";
        boolean b = vrbtDiyVideoService.uploadToFtp(pathName, filePath, fileName);
        if (b) {
            vrbtDiyService.report(mobile, pathName + "/" + fileName, mvName);
            febsResponse.success().message("上传成功");
        } else {
            febsResponse.fail().message("上传失败");
        }
        return febsResponse;
    }


    /**
     * 骏伯流量包通知
     *
     * @param requestBody
     * @return
     */
    @RequestMapping(value = "/junbolinliangbao/notify")
    public String junBoLinLiangBaoNotify(@RequestBody String requestBody) {
        log.info("骏伯流量包下单=>通知:{}", requestBody);
        try {
            JsonNode tree = mapper.readTree(requestBody);
            String sysOrderId = tree.at("/sysOrderId").asText();
            String orderStatus = tree.at("/orderStatus").asText();
            String orderStatusMsg = tree.at("/orderStatusMsg").asText();
            String contactNumber = tree.at("/contactNumber").asText();
            synchronized (interner.intern(sysOrderId)) {
                subscribeService.junBoLiuLiangBaoCrackNotify(sysOrderId, orderStatus, orderStatusMsg, contactNumber);
            }
            return "ok";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * keyValue配置获取
     *
     * @param key
     * @return
     */
    @RequestMapping(value = "/config/getConfigByKey")
    public FebsResponse getConfigByKey(String key) {

        return keyValueConfigService.getConfigByKey(key);
    }


    /**
     * 华逸多会员直充结果回调
     *
     * @return
     */
    @RequestMapping(value = "/huayiRechargeResult")
    public String notifyHuaYiRechargeResult(@RequestBody String requestBody) {
        log.info("华逸多会员直充结果回调=>requestBody:{}", requestBody);
        try {
            final HuaYiResult huaYiResult = new ObjectMapper().readValue(requestBody, HuaYiResult.class);
            junboChargeLogService.receiveRechargeNotify(huaYiResult);
        } catch (Exception e) {
            log.info("华逸多会员直充结果回调异常", e);
        }
        return "ok";
    }


    /**
     * 三方支付订单状态查询
     *
     * @param mobile
     * @param captcha
     * @return
     */
    @ApiOperation(value = "三方支付订单查询", notes = "三方支付订单查询")
    @PostMapping(value = "/query/pay/order")
    @ResponseBody
    public FebsResponse queryPayOrder(@RequestParam("mobile") String mobile,
                                      @RequestParam("captcha") String captcha,
                                      @RequestParam(value = "bizType", required = false, defaultValue = BIZ_TYPE_VRBT_YD_ALIPAY) String bizType) {
        log.info("支付宝订单查询=>mobile:{},captcha:{},bizType:{}", mobile, captcha, bizType);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return new FebsResponse().fail().message("验证码错误");
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            return new FebsResponse().fail().message(e.getMessage());
        }
        List<String> bizTypeList = com.google.common.collect.Lists.newArrayList(bizType);
        return memberService.queryPayOrder(mobile, bizTypeList);
    }

    /**
     * 网易云退订接口
     *
     * @param mobile
     * @return
     */
    @ApiOperation(value = "网易云退订接口", notes = "网易云退订接口")
    @GetMapping(value = "/wyy/unsubscribe")
    @ResponseBody
    public FebsResponse queryPayOrder(@RequestParam("mobile") String mobile) {
        FebsResponse febsResponse = new FebsResponse();
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        try {
            //先查询订单信息
            WyyMmOrder order = wyyMmOrderService.lambdaQuery()
                    .eq(WyyMmOrder::getMobile, mobile)
                    .orderByDesc(WyyMmOrder::getCreateTime)
                    .last("limit 1")
                    .one();
            if (order == null || !(order.getStatus() == 1)) {
                return febsResponse.fail().message("订单信息不存在");
            }
            WangyiyunMmUnsubscribeResult unsubscribe = wangyiyunMmService.unsubscribe(mobile, mobile, order.getOrderId());
            if (unsubscribe.isOK()) {
                febsResponse.success().message("退订成功");
            } else {
                febsResponse.fail().message("退订失败");
            }
        } catch (Exception e) {
            febsResponse.fail().message(e.getMessage());
        }
        return febsResponse;
    }


    /**
     * 获取业务开通链接
     *
     * @param mobile
     * @param pageId
     * @return
     */
    @SneakyThrows
    @RequestMapping("common/getBizLink")
    public Result getBizLink(@RequestParam String mobile, @RequestParam String pageId, @RequestParam(required = false) String subChannel, HttpServletRequest request) {
        log.info("获取业务开通链接,手机号:{},pageId:{},subChannel:{}", mobile, pageId, subChannel);
        String app = request.getHeader("x-requested-with");
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        String link = pageBusinessProvinceConfigService.getLinkByPageIdAndIspProvince(pageId, mobileRegionResult.getOperator(), mobileRegionResult.getProvince());
        String sparLink = pageBusinessProvinceConfigService.getSpareLinkByPageIdAndIspProvince(pageId, mobileRegionResult.getOperator(), mobileRegionResult.getProvince());
        PageBusinessConfig pageBusinessConfig = pageBusinessConfigService.selectById(pageId);
        Integer appPkgNullFilter = pageBusinessConfig.getAppPkgNullFilter();
        Map<String, String> resultMap = new HashMap<>();
        //
        if (appPkgNullFilter != null && appPkgNullFilter == 1 && StringUtils.isBlank(app)) {
            log.warn("页面id:{},手机号:{},空包名屏蔽", pageId, mobile);
            resultMap.put("bizRedirectLink", pageBusinessConfig.getProvinceRedirectStatus() == 1 ? pageBusinessConfig.getProvinceRedirectLink() : "");
            resultMap.put("returnRedirectLink", pageBusinessConfig.getReturnRedirectLink());
            return Result.errorAndSetData(resultMap);
        }
        if (StringUtils.isBlank(link)) {
            log.warn("页面id:{},手机号:{},未查询到可开通业务", pageId, mobile);
            resultMap.put("bizRedirectLink", pageBusinessConfig.getProvinceRedirectStatus() == 1 ? pageBusinessConfig.getProvinceRedirectLink() : "");
            resultMap.put("returnRedirectLink", pageBusinessConfig.getReturnRedirectLink());
            return Result.errorAndSetData(resultMap);
        } else {
            String linkPageId = UrlStringUtil.getPageId(link);
            if (StringUtils.isNotBlank(linkPageId) && StringUtils.isNumeric(linkPageId)) {
                BizPageConfig linkPageConfig = bizPageConfigService.selectById(linkPageId);
                JsonNode jsonNode = mapper.readTree(linkPageConfig.getConfigJson());
                BizType bizType = bizTypeService.getBizType(jsonNode.at("/bizTypeList"));
                Subscribe subscribe = new Subscribe();
                subscribe.setChannel(bizType.getChannelCode());
                subscribe.setMobile(mobile);
                subscribe.setSubChannel(subChannel);
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setServiceId(bizType.getCompanyOwner());
                if (BizTypeServiceImpl.VRBT_SERVER.equals(bizType.getServiceAddrType())) {
                    IBizCommonService bizCommonService = SpringUtil.getBean(bizType.getBeanName());
                    Result filterResult = bizCommonService.filerCheck(subscribe);
                    if (!filterResult.isOK()) {
                        log.info("业务开通链接前置校验失败,手机号:{},entry pageId:{},link pageId:{},province:{},channel:{},subChannel:{},前置校验结果:{}", mobile, pageId, linkPageId, subscribe.getProvince(), bizType.getChannelCode(), subChannel, filterResult.getMessage());
                        resultMap.put("bizRedirectLink", StringUtils.isNotBlank(sparLink) ? sparLink : "");
                        resultMap.put("returnRedirectLink", pageBusinessConfig.getReturnRedirectLink());



                        subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                        if(StringUtils.isNotBlank(filterResult.getErrorMsg())){
                            subscribe.setResult(filterResult.getErrorMsg());
                        }else{
                            subscribe.setResult("{\"resCode\":\""+filterResult.getCode()+"\",\"resMsg\":\""+filterResult.getMessage()+"\"}");
                        }
                        subscribe.setModifyTime(new Date());
                        subscribeService.updateSubscribeDbAndEs(subscribe);
                        filterResult.setErrorMsg("");
                        return Result.okAndSetData(resultMap);
                    }
                } else {
                    final Result filterResult = innerUnionMemberService.callFilterCheck(subscribe, bizType.getBeanName());
                    if (!filterResult.isOK()) {
                        log.info("业务开通链接前置校验失败,手机号:{},entry pageId:{},link pageId:{},province:{},channel:{},subChannel:{},前置校验结果:{}", mobile, pageId, linkPageId, subscribe.getProvince(), bizType.getChannelCode(), subChannel, filterResult.getMessage());
                        resultMap.put("bizRedirectLink", StringUtils.isNotBlank(sparLink) ? sparLink : "");
                        resultMap.put("returnRedirectLink", pageBusinessConfig.getReturnRedirectLink());

                        subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                        if(StringUtils.isNotBlank(filterResult.getErrorMsg())){
                            subscribe.setResult(filterResult.getErrorMsg());
                        }else{
                            subscribe.setResult("{\"resCode\":\""+filterResult.getCode()+"\",\"resMsg\":\""+filterResult.getMessage()+"\"}");
                        }
                        subscribe.setModifyTime(new Date());
                        subscribeService.updateSubscribeDbAndEs(subscribe);
                        filterResult.setErrorMsg("");



                        return Result.okAndSetData(resultMap);
                    }
                }
            }
            resultMap.put("bizRedirectLink", link);
            resultMap.put("returnRedirectLink", pageBusinessConfig.getReturnRedirectLink());
            return Result.okAndSetData(resultMap);
        }
    }


    /**
     * 获取业务开通链接
     *
     * @param mobile
     * @param pageId
     * @return
     */
    @SneakyThrows
    @RequestMapping("common/getBizPop")
    public Result getBizPop(@RequestParam String mobile, @RequestParam String pageId, @RequestParam(required = false) String subChannel, HttpServletRequest request) {
        log.info("获取业务开通弹窗和尾量,手机号:{},pageId:{},subChannel:{}", mobile, pageId, subChannel);
        mobileRegionService.query(mobile);
        String app = request.getHeader("x-requested-with");
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        ObjectNode popNode = pageBusinessProvinceConfigVpopService.getPopAndQuantityByPageIdAndIspProvince(pageId, mobileRegionResult.getOperator(), mobileRegionResult.getProvince());
        ObjectNode sparePopNode = pageBusinessProvinceConfigVpopService.getSparePopAndQuantityByPageIdAndIspProvince(pageId, mobileRegionResult.getOperator(), mobileRegionResult.getProvince());
        PageBusinessConfigVpop pageBusinessConfigVpop = pageBusinessConfigVpopService.selectById(pageId);
        if (popNode == null) {
            log.warn("页面id:{},手机号:{},未查询到可开通业务", pageId, mobile);
            return Result.error("当前省份未配置业务");
        }
        String popId = popNode.at("/" + BizCommonConstant.POP_ID).asText();
        String tailQuantityId = popNode.at("/" + BizCommonConstant.TAIL_QUANTITY_ID).asText();
        String bizTypeId = popNode.at("/" + BizCommonConstant.BIZ_TYPE_ID).asText();
        String refPageId = popNode.at("/" + BizCommonConstant.REF_PAGE_ID).asText();
        Integer appPkgNullFilter = pageBusinessConfigVpop.getAppPkgNullFilter();
        Map<String, String> resultMap = new HashMap<>();
        //
        if (appPkgNullFilter != null && appPkgNullFilter == 1 && StringUtils.isBlank(app)) {
            log.warn("页面id:{},手机号:{},空包名屏蔽", pageId, mobile);
            return Result.msgAppLimit();
        }
        if (StringUtils.isBlank(bizTypeId)) {
            log.warn("页面id:{},手机号:{},未查询到可开通业务", pageId, mobile);
            return Result.error("当前省份未配置业务");
        } else {
            BizType bizType = bizTypeService.selectById(bizTypeId);
            Subscribe subscribe = new Subscribe();
            subscribe.setChannel(bizType.getChannelCode());
            subscribe.setMobile(mobile);
            subscribe.setSubChannel(subChannel);
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setServiceId(bizType.getCompanyOwner());
            if (BizTypeServiceImpl.VRBT_SERVER.equals(bizType.getServiceAddrType())) {
                IBizCommonService bizCommonService = SpringUtil.getBean(bizType.getBeanName());
                Result filterResult = bizCommonService.filerCheck(subscribe);
                if (!filterResult.isOK()) {
                    log.info("业务开通链接前置校验失败,手机号:{},entry pageId:{},province:{},channel:{},subChannel:{},前置校验结果:{}", mobile, pageId, subscribe.getProvince(), bizType.getChannelCode(), subChannel, filterResult.getMessage());
                    String sparePopId = sparePopNode.at("/" + BizCommonConstant.POP_ID).asText();
                    popId = StringUtils.isNotBlank(sparePopId) ? sparePopId : popId;
                    String spareTailQuantityId = sparePopNode.at("/" + BizCommonConstant.TAIL_QUANTITY_ID).asText();
                    tailQuantityId = StringUtils.isNotBlank(spareTailQuantityId) ? spareTailQuantityId : tailQuantityId;
                    String spareBizTypeId = sparePopNode.at("/" + BizCommonConstant.BIZ_TYPE_ID).asText();
                    bizTypeId = StringUtils.isNotBlank(spareBizTypeId) ? spareBizTypeId : bizTypeId;
                    String spareRefPageId = sparePopNode.at("/" + BizCommonConstant.REF_PAGE_ID).asText();
                    refPageId = StringUtils.isNotBlank(spareRefPageId) ? spareRefPageId : refPageId;


                    subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                    if(StringUtils.isNotBlank(filterResult.getErrorMsg())){
                        subscribe.setResult(filterResult.getErrorMsg());
                    }else{
                        subscribe.setResult("{\"resCode\":\""+filterResult.getCode()+"\",\"resMsg\":\""+filterResult.getMessage()+"\"}");
                    }
                    subscribe.setModifyTime(new Date());
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    filterResult.setErrorMsg("");
                }
            } else {
                final Result filterResult = innerUnionMemberService.callFilterCheck(subscribe, bizType.getBeanName());
                if (!filterResult.isOK()) {
                    log.info("业务开通链接前置校验失败,手机号:{},entry pageId:{},province:{},channel:{},subChannel:{},前置校验结果:{}", mobile, pageId, subscribe.getProvince(), bizType.getChannelCode(), subChannel, filterResult.getMessage());
                    String sparePopId = sparePopNode.at("/" + BizCommonConstant.POP_ID).asText();
                    popId = StringUtils.isNotBlank(sparePopId) ? sparePopId : popId;
                    String spareTailQuantityId = sparePopNode.at("/" + BizCommonConstant.TAIL_QUANTITY_ID).asText();
                    tailQuantityId = StringUtils.isNotBlank(spareTailQuantityId) ? spareTailQuantityId : tailQuantityId;
                    String spareBizTypeId = sparePopNode.at("/" + BizCommonConstant.BIZ_TYPE_ID).asText();
                    bizTypeId = StringUtils.isNotBlank(spareBizTypeId) ? spareBizTypeId : bizTypeId;
                    String spareRefPageId = sparePopNode.at("/" + BizCommonConstant.REF_PAGE_ID).asText();
                    refPageId = StringUtils.isNotBlank(spareRefPageId) ? spareRefPageId : refPageId;


                    subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                    if(StringUtils.isNotBlank(filterResult.getErrorMsg())){
                        subscribe.setResult(filterResult.getErrorMsg());
                    }else{
                        subscribe.setResult("{\"resCode\":\""+filterResult.getCode()+"\",\"resMsg\":\""+filterResult.getMessage()+"\"}");
                    }
                    subscribe.setModifyTime(new Date());
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    filterResult.setErrorMsg("");

                }
            }
            resultMap.put(BizCommonConstant.POP_ID, popId);
            resultMap.put(BizCommonConstant.TAIL_QUANTITY_ID, tailQuantityId);
            resultMap.put(BizCommonConstant.BIZ_TYPE_ID, bizTypeId);
            resultMap.put(BizCommonConstant.REF_PAGE_ID, refPageId);
            return Result.okAndSetData(resultMap);
        }
    }


    /**
     * 获取业务开通链接
     *
     * @param mobile
     * @param pageId
     * @return
     */
    @SneakyThrows
    @RequestMapping("common/self/getBizPop")
    public Result getSelfBizPop(@RequestParam String mobile, @RequestParam String pageId, @RequestParam(required = false) String subChannel, HttpServletRequest request) {
        log.info("获取业务开通弹窗和尾量(自有),手机号:{},pageId:{},subChannel:{}", mobile, pageId, subChannel);
        mobileRegionService.query(mobile);
        String app = request.getHeader("x-requested-with");
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        ObjectNode popNode = pageBusinessProvinceConfigSelfService.getPopAndQuantityByPageIdAndIspProvince(pageId, mobileRegionResult.getOperator(), mobileRegionResult.getProvince());
        ObjectNode sparePopNode = pageBusinessProvinceConfigSelfService.getSparePopAndQuantityByPageIdAndIspProvince(pageId, mobileRegionResult.getOperator(), mobileRegionResult.getProvince());
        PageBusinessConfigSelf pageBusinessConfigSelf = pageBusinessConfigSelfService.selectById(pageId);
        if (popNode == null) {
            log.warn("页面id:{},手机号:{},未查询到可开通业务", pageId, mobile);
            return Result.error("当前省份未配置业务");
        }
        String popId = popNode.at("/" + BizCommonConstant.POP_ID).asText();
        String tailQuantityId = popNode.at("/" + BizCommonConstant.TAIL_QUANTITY_ID).asText();
        String bizTypeId = popNode.at("/" + BizCommonConstant.BIZ_TYPE_ID).asText();
        String refPageId = popNode.at("/" + BizCommonConstant.REF_PAGE_ID).asText();
        Integer appPkgNullFilter = pageBusinessConfigSelf.getAppPkgNullFilter();
        Map<String, String> resultMap = new HashMap<>();
        //
        if (appPkgNullFilter != null && appPkgNullFilter == 1 && StringUtils.isBlank(app)) {
            log.warn("页面id:{},手机号:{},空包名屏蔽", pageId, mobile);
            return Result.msgAppLimit();
        }
        if (StringUtils.isBlank(bizTypeId)) {
            log.warn("页面id:{},手机号:{},未查询到可开通业务", pageId, mobile);
            return Result.error("当前省份未配置业务");
        } else {
            BizType bizType = bizTypeService.selectById(bizTypeId);
            Subscribe subscribe = new Subscribe();
            subscribe.setChannel(bizType.getChannelCode());
            subscribe.setMobile(mobile);
            subscribe.setSubChannel(subChannel);
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setServiceId(bizType.getCompanyOwner());
            if (BizTypeServiceImpl.VRBT_SERVER.equals(bizType.getServiceAddrType())) {
                IBizCommonService bizCommonService = SpringUtil.getBean(bizType.getBeanName());
                Result filterResult = bizCommonService.filerCheck(subscribe);
                if (!filterResult.isOK()) {
                    log.info("业务开通链接前置校验失败,手机号:{},entry pageId:{},province:{},channel:{},subChannel:{},前置校验结果:{}", mobile, pageId, subscribe.getProvince(), bizType.getChannelCode(), subChannel, filterResult.getMessage());
                    String sparePopId = sparePopNode.at("/" + BizCommonConstant.POP_ID).asText();
                    popId = StringUtils.isNotBlank(sparePopId) ? sparePopId : popId;
                    String spareTailQuantityId = sparePopNode.at("/" + BizCommonConstant.TAIL_QUANTITY_ID).asText();
                    tailQuantityId = StringUtils.isNotBlank(spareTailQuantityId) ? spareTailQuantityId : tailQuantityId;
                    String spareBizTypeId = sparePopNode.at("/" + BizCommonConstant.BIZ_TYPE_ID).asText();
                    bizTypeId = StringUtils.isNotBlank(spareBizTypeId) ? spareBizTypeId : bizTypeId;
                    String spareRefPageId = sparePopNode.at("/" + BizCommonConstant.REF_PAGE_ID).asText();
                    refPageId = StringUtils.isNotBlank(spareRefPageId) ? spareRefPageId : refPageId;


                    subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                    if(StringUtils.isNotBlank(filterResult.getErrorMsg())){
                        subscribe.setResult(filterResult.getErrorMsg());
                    }else{
                        subscribe.setResult("{\"resCode\":\""+filterResult.getCode()+"\",\"resMsg\":\""+filterResult.getMessage()+"\"}");
                    }
                    subscribe.setModifyTime(new Date());
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    filterResult.setErrorMsg("");

                }
            } else {
                final Result filterResult = innerUnionMemberService.callFilterCheck(subscribe, bizType.getBeanName());
                if (!filterResult.isOK()) {
                    log.info("业务开通链接前置校验失败,手机号:{},entry pageId:{},province:{},channel:{},subChannel:{},前置校验结果:{}", mobile, pageId, subscribe.getProvince(), bizType.getChannelCode(), subChannel, filterResult.getMessage());
                    String sparePopId = sparePopNode.at("/" + BizCommonConstant.POP_ID).asText();
                    popId = StringUtils.isNotBlank(sparePopId) ? sparePopId : popId;
                    String spareTailQuantityId = sparePopNode.at("/" + BizCommonConstant.TAIL_QUANTITY_ID).asText();
                    tailQuantityId = StringUtils.isNotBlank(spareTailQuantityId) ? spareTailQuantityId : tailQuantityId;
                    String spareBizTypeId = sparePopNode.at("/" + BizCommonConstant.BIZ_TYPE_ID).asText();
                    bizTypeId = StringUtils.isNotBlank(spareBizTypeId) ? spareBizTypeId : bizTypeId;
                    String spareRefPageId = sparePopNode.at("/" + BizCommonConstant.REF_PAGE_ID).asText();
                    refPageId = StringUtils.isNotBlank(spareRefPageId) ? spareRefPageId : refPageId;


                    subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                    if(StringUtils.isNotBlank(filterResult.getErrorMsg())){
                        subscribe.setResult(filterResult.getErrorMsg());
                    }else{
                        subscribe.setResult("{\"resCode\":\""+filterResult.getCode()+"\",\"resMsg\":\""+filterResult.getMessage()+"\"}");
                    }
                    subscribe.setModifyTime(new Date());
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    filterResult.setErrorMsg("");

                }
            }
            resultMap.put(BizCommonConstant.POP_ID, popId);
            resultMap.put(BizCommonConstant.TAIL_QUANTITY_ID, tailQuantityId);
            resultMap.put(BizCommonConstant.BIZ_TYPE_ID, bizTypeId);
            resultMap.put(BizCommonConstant.REF_PAGE_ID, refPageId);
            return Result.okAndSetData(resultMap);
        }
    }


    /**
     * 获取短信验证码
     *
     * @param jsonNode
     * @param request
     * @return
     */
    @RequestMapping("common/getSmsCode")
    public Result getSmsCode(@RequestBody JsonNode jsonNode, @RequestHeader Map<String, String> headers, HttpServletRequest request) {

        try {
            //if (LocalDateTime.now().isBefore(LocalDate.of(2024, 8, 14).atTime(12, 00, 00))) {
            //    log.info("head打印:{}", mapper.writeValueAsString(headers));
            //}
            String userAgent = request.getHeader("User-Agent");
            if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
                userAgent = userAgent.substring(0, 512);
            }
            String app = request.getHeader("x-requested-with");
            String ipAddr = IPUtils.getIpAddr(request);
            log.info("渠道订阅获取短验SXB,ip:{},app:{},参数:{},ua:{}", ipAddr, app, jsonNode, userAgent);
            //log.info("公共接口获取短信验证码,参数:{}", jsonNode);

            String mobile = jsonNode.at("/mobile").asText();
            String linkId = jsonNode.at("/linkId").asText();
            String source = jsonNode.at("/source").asText();
            String subChannel = jsonNode.at("/subChannel").asText();
            String deviceInfo = jsonNode.at("/deviceInfo").asText();
            String reportTicket = jsonNode.at("/reportTicket").asText();
            BizPageConfig bizPageConfig = bizPageConfigService.selectById(linkId);
            if (bizPageConfig == null || "0".equals(bizPageConfig.getIsEnable())) {
                return Result.error("暂时无法使用，请稍后再试!");
            }
            Subscribe subscribe = new Subscribe();
            subscribe.setMobile(mobile);
            subscribe.setSource(source);
            subscribe.setSubChannel(subChannel);
            subscribe.setDeviceInfo(deviceInfo);
            subscribe.setReferer(app);
            subscribe.setUserAgent(userAgent);
            subscribe.setIp(ipAddr);
            subscribe.setReportTicket(reportTicket);
            JsonNode pageConfigJsonNode = mapper.readTree(bizPageConfig.getConfigJson());
            Result result = bizTypeService.callGetSmsCode(subscribe, pageConfigJsonNode);
            if (!result.isOK()) {
                subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                if(StringUtils.isNotBlank(result.getErrorMsg())){
                    subscribe.setResult(result.getErrorMsg());
                }else{
                    subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
                }
                subscribe.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(subscribe);
                result.setErrorMsg("");
//                orderFailLogService.createOrderFailLog(subscribe, result.getMessage());
            }
            return result;
        } catch (Exception e) {
            log.error("渠道订阅获取短验SXB,参数:{},错误:", jsonNode, e);
            return Result.error("系统错误，请稍后再试!");


        }
    }

    /**
     * 获取短信验证码
     *
     * @param jsonNode
     * @param request
     * @return
     */
    @RequestMapping("common/pop/getSmsCode")
    public Result popGetSmsCode(@RequestBody JsonNode jsonNode, HttpServletRequest request) {
        try {
            String userAgent = request.getHeader("User-Agent");
            if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
                userAgent = userAgent.substring(0, 512);
            }
            String app = request.getHeader("x-requested-with");
            String ipAddr = IPUtils.getIpAddr(request);
            log.info("渠道订阅获取短验TCB,ip:{},app:{},参数:{},ua:{}", ipAddr, app, jsonNode, userAgent);
            //log.info("弹窗版公共接口获取短信验证码,参数:{}", jsonNode);

            String mobile = jsonNode.at("/mobile").asText();
            String bizTypeId = jsonNode.at("/bizTypeId").asText();
            String source = jsonNode.at("/source").asText();
            String subChannel = jsonNode.at("/subChannel").asText();
            String deviceInfo = jsonNode.at("/deviceInfo").asText();
            String reportTicket = jsonNode.at("/reportTicket").asText();
            if (StringUtils.isBlank(bizTypeId)) {
                return Result.error("暂时无法使用，请稍后再试!");
            }
            Subscribe subscribe = new Subscribe();
            subscribe.setMobile(mobile);
            subscribe.setSource(source);
            subscribe.setSubChannel(subChannel);
            subscribe.setDeviceInfo(deviceInfo);
            subscribe.setReferer(app);
            subscribe.setUserAgent(userAgent);
            subscribe.setIp(ipAddr);
            subscribe.setReportTicket(reportTicket);
            Result result = bizTypeService.callPopGetSmsCode(subscribe, bizTypeId);
            if (!result.isOK()) {
                subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                if(StringUtils.isNotBlank(result.getErrorMsg())){
                    subscribe.setResult(result.getErrorMsg());
                }else{
                    subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
                }
                subscribe.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(subscribe);
                result.setErrorMsg("");
//                orderFailLogService.createOrderFailLog(subscribe, result.getMessage());
            }
            return result;
        } catch (Exception e) {
            log.error("渠道订阅获取短验TCB,参数:{},错误:", jsonNode, e);
            return Result.error("系统错误，请稍后再试!");


        }
    }


    @RequestMapping("common/encryption/getSmsCode")
    public Result getEncryptionSmsCode(@RequestBody String encryptionData, HttpServletRequest request, @RequestHeader Map<String, String> headers) throws JsonProcessingException {
        log.info("公共接口获取短信验证码,加密前参数:{}", encryptionData);
        String result = SMUtil.sm2Decrypt(encryptionData);
        log.info("公共接口获取短信验证码,解密后参数:{}", result);
        JsonNode jsonNode = mapper.readTree(result);
        String key = "rateLimit:ip:" + IPUtils.getIpAddr(request);
        boolean isLimit = redisUtil.rateLimitV2(key, 10, 3600);
        if (isLimit) {
            return Result.error("获取验证码失败");
        }
        return getSmsCode(jsonNode, headers, request);
    }

    /**
     * 提交短信验证码
     *
     * @param jsonNode
     * @param request
     * @return
     */
    @RequestMapping("common/submitCode")
    public Result submitSmsCode(@RequestBody JsonNode jsonNode, HttpServletRequest request) {
        log.info("渠道订阅提交短验,参数:{}", jsonNode);
        try {
            String mobile = jsonNode.at("/mobile").asText();
            String transactionId = jsonNode.at("/transactionId").asText();
            String smsCode = jsonNode.at("/smsCode").asText();
            String linkId = jsonNode.at("/linkId").asText();
            String encryption = jsonNode.at("/encryption").asText();
            String reportTicket = jsonNode.at("/reportTicket").asText();
            String deviceCode = jsonNode.get("deviceCode") != null? jsonNode.get("deviceCode").asText() : "";
            String platformCode = jsonNode.get("platformCode") != null? jsonNode.get("platformCode").asText() : "";
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            Subscribe subscribe = subscribeService.getById(transactionId);
            if (subscribe != null) {
                subscribe.setSmsCode(smsCode);
                subscribe.setEncryption(encryption);
                subscribe.setReportTicket(reportTicket);
                subscribe.setDeviceCode(deviceCode);
                subscribe.setPlatformCode(platformCode);
                //本次transactionId已开通业成功不可再次提交验证码
                if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
                    return Result.error("你已开通成功，请勿重复提交");
                }
            }
            return bizTypeService.callSumbitSmsCode(subscribe, smsCode, transactionId);
        } catch (Exception e) {
            log.error("渠道订阅提交短验,参数:{},错误:", jsonNode, e);
            return Result.error("系统错误，请稍后再试!");
        }
    }


    @RequestMapping("common/encryption/submitCode")
    public Result getEncryptionSubmitCode(@RequestBody String encryptionData, HttpServletRequest request) throws JsonProcessingException {
        log.info("公共接口提交短信验证码,加密前参数:{}", encryptionData);
        String result = SMUtil.sm2Decrypt(encryptionData);
        log.info("公共接口提交短信验证码,解密后参数:{}", result);
        JsonNode jsonNode = mapper.readTree(result);
        return submitSmsCode(jsonNode, request);
    }


//    /**
//     * 蜂助手四川移动获取验证码
//     *
//     * @param jsonNode
//     * @param request
//     * @return
//     */
//    @RequestMapping("fengzhushou/sichuanYidong/getSmsCode")
//    public Result getfengzhushouSichuanYidongSmsCode(@RequestBody JsonNode jsonNode, HttpServletRequest request) {
//        log.info("蜂助手四川移动获取短信验证码,参数:{}", jsonNode);
//        try {
//            String mobile = jsonNode.at("/mobile").asText();
//            String linkId = jsonNode.at("/channel").asText();
//            String source = jsonNode.at("/source").asText();
//            String subChannel = jsonNode.at("/subChannel").asText();
//            String deviceInfo = jsonNode.at("/deviceInfo").asText();
//            String userAgent = request.getHeader("User-Agent");
//            if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
//                userAgent = userAgent.substring(0, 512);
//            }
//            String app = request.getHeader("x-requested-with");
//            String ipAddr = IPUtils.getIpAddr(request);
//            log.info("蜂助手四川移动订阅请求=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonNode, userAgent);
//            Subscribe subscribe = new Subscribe();
//            subscribe.setMobile(mobile);
//            subscribe.setSource(source);
//            subscribe.setSubChannel(subChannel);
//            subscribe.setDeviceInfo(deviceInfo);
//            subscribe.setReferer(app);
//            subscribe.setUserAgent(userAgent);
//            subscribe.setIp(ipAddr);
//            return su.(subscribe);
//        } catch (Exception e) {
//            log.error("公共接口获取短信验证码,参数:{},错误:", jsonNode, e);
//            return Result.error("系统错误，请稍后再试!");
//        }
//    }


    /**
     * 联通上传企业视频彩铃
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/lianTongUploadCompanyRing")
    public FebsResponse lianTongUploadCompanyRing(HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("联通上传企业视频彩铃请求=>ip:{},请求参数:{}", ipAddr, jsonData);
            JsonNode jsonNode = mapper.readTree(jsonData);
            String mobile = jsonNode.at("/mobile").asText();
            String videoUrl = jsonNode.at("/videoUrl").asText();
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return new FebsResponse().fail().message("请输入正确格式手机号");
            }
            if (Strings.isNullOrEmpty(videoUrl)) {
                return new FebsResponse().fail().message("请上传视频铃音");
            }
            LiantongLicenseResp liantongLicenseResp = liantongVrbtService.licenseQuery(mobile, BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
            if (liantongLicenseResp == null || !"SUCCESS".equals(liantongLicenseResp.getResultCode())) {
                return new FebsResponse().fail().message("视频彩铃能力无效");
            }
            LocalDateTime endTime = DateUtil.parseString(liantongLicenseResp.getEndTime(), DateUtil.FULL_TIME_SPLIT_PATTERN);
            if (endTime.isBefore(LocalDateTime.now())) {
                return new FebsResponse().fail().message("视频彩铃能力已过期");
            }
            String videoImg = liantongVrbtProperties.getVideoImg();
            return liantongRingService.liantongUploadRingQueue(mobile, videoUrl, videoImg);
        } catch (Exception e) {
            log.error("联通上传企业视频彩铃请求：", e);
        }
        return new FebsResponse().fail();
    }


    /**
     * 联通查询铃音列表
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/query/lianTongRingList")
    public FebsResponse queryLianTongRingList(HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("联通查询铃音列表=>ip:{},请求参数:{}", ipAddr, jsonData);
            JsonNode jsonNode = mapper.readTree(jsonData);
            String mobile = jsonNode.at("/mobile").asText();
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return new FebsResponse().fail().message("请输入正确格式手机号");
            }
            LiantongLicenseResp liantongLicenseResp = liantongVrbtService.licenseQuery(mobile, BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
            if (liantongLicenseResp == null || !"SUCCESS".equals(liantongLicenseResp.getResultCode())) {
                return new FebsResponse().fail().message("视频彩铃能力无效");
            }
            LocalDateTime endTime = DateUtil.parseString(liantongLicenseResp.getEndTime(), DateUtil.FULL_TIME_SPLIT_PATTERN);
            if (endTime.isBefore(LocalDateTime.now())) {
                return new FebsResponse().fail().message("视频彩铃能力已过期");
            }
            return liantongRingService.queryLianTongRingList(mobile);
        } catch (Exception e) {
            log.error("联通查询铃音列表铃请求：", e);
        }
        return new FebsResponse().fail();
    }


    /**
     * 联通设置铃音
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/set/lianTongRing")
    public FebsResponse setLianTongRing(HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("联通查询铃音列表=>ip:{},请求参数:{}", ipAddr, jsonData);
            JsonNode jsonNode = mapper.readTree(jsonData);
            String mobile = jsonNode.at("/mobile").asText();
            String ringId = jsonNode.at("/ringId").asText();
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return new FebsResponse().fail().message("请输入正确格式手机号");
            }
            if (Strings.isNullOrEmpty(ringId)) {
                return new FebsResponse().fail().message("请选择铃音");
            }
            LiantongLicenseResp liantongLicenseResp = liantongVrbtService.licenseQuery(mobile, BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
            if (liantongLicenseResp == null || !"SUCCESS".equals(liantongLicenseResp.getResultCode())) {
                return new FebsResponse().fail().message("视频彩铃能力无效");
            }
            LocalDateTime endTime = DateUtil.parseString(liantongLicenseResp.getEndTime(), DateUtil.FULL_TIME_SPLIT_PATTERN);
            if (endTime.isBefore(LocalDateTime.now())) {
                return new FebsResponse().fail().message("视频彩铃能力已过期");
            }
            //设置状态审核通过
            liantongRingService.lambdaUpdate().eq(LiantongRing::getMobile, mobile).eq(LiantongRing::getRingStatus, "05").set(LiantongRing::getRingStatus, "04").update();

            LiantongResp liantongResp = liantongVrbtService.settingRingOnePointMon(mobile, ringId, BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
            if (liantongResp.isOK()) {
                liantongRingService.lambdaUpdate().eq(LiantongRing::getContentId, ringId).set(LiantongRing::getRingStatus, "05").update();
                return new FebsResponse().success();
            }
            liantongRingService.lambdaUpdate().eq(LiantongRing::getContentId, ringId).set(LiantongRing::getRingStatus, "06").update();
            return new FebsResponse().fail();
        } catch (Exception e) {
            log.error("联通查询铃音列表铃请求：", e);
        }
        return new FebsResponse().fail();
    }

    /**
     * 四川移动订单接收
     *
     * @param jsonNode
     * @return
     */
    @RequestMapping(value = "/sichuanYidongOrderReceive")
    public String sichuanYidongOrderReceive(@RequestBody JsonNode jsonNode) {
        log.info("四川移动订单接收数据:{}", jsonNode);
        Subscribe subscribe = mapper.convertValue(jsonNode, Subscribe.class);
        String mobile = subscribe.getMobile();
        String ispOrderNo = subscribe.getIspOrderNo();
        Subscribe sub = subscribeService.findByIspOrderId(ispOrderNo);
        if (sub == null) {
//            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
//                return "请输入正确格式手机号";
//            }
            if (StringUtils.isBlank(subscribe.getChannel())) {
                subscribe.setChannel(BIZ_CHANNEL_SCYD_YLCWB);
            }
            String bizType = getBizTypeByMiguChannel(subscribe.getChannel());
            subscribe.setBizType(bizType);
            subscribe.setSubChannel("junbo_scyd");
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setIsp(mobileRegionResult.getOperator());
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//            }
            subscribeService.createSubscribeDbAndEs(subscribe);
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(sub.getId());
            upd.setStatus(subscribe.getStatus());
            upd.setResult(subscribe.getResult());
            upd.setCreateTime(subscribe.getCreateTime());
            upd.setRemark(subscribe.getRemark());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        return "OK";
    }

    /**
     * 四川移动明星来电白银组合包c包数据接收
     *
     * @param jsonNode
     * @return
     */
    @RequestMapping(value = "/sichuanYidongByzhbcOrderReceive")
    public String sichuanYidongByzhbcOrderReceive(@RequestBody JsonNode jsonNode) {
        log.info("四川移动明星来电白银组合包c包数据接收:{}", jsonNode);
        Subscribe subscribe = mapper.convertValue(jsonNode, Subscribe.class);
        String mobile = subscribe.getMobile();
        String ispOrderNo = subscribe.getIspOrderNo();
        Subscribe sub = subscribeService.findByIspOrderId(ispOrderNo);
        if (sub == null) {
//            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
//                return "请输入正确格式手机号";
//            }
            if (StringUtils.isBlank(subscribe.getChannel())) {
                subscribe.setChannel(BIZ_CHANNEL_SCYD_BYZHBC);
            }
            String bizType = getBizTypeByMiguChannel(subscribe.getChannel());
            subscribe.setBizType(bizType);
            subscribe.setSubChannel("gdyz_scyd");
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setIsp(mobileRegionResult.getOperator());
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//            }
            subscribeService.createSubscribeDbAndEs(subscribe);
            if (BIZ_CHANNEL_SCYD_BYZHBC.equals(subscribe.getChannel())) {
                outsideCallbackService.zclyCallbackAsync(subscribe.getId());
            }
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(sub.getId());
            upd.setStatus(subscribe.getStatus());
            upd.setResult(subscribe.getResult());
            upd.setCreateTime(subscribe.getCreateTime());
            upd.setRemark(subscribe.getRemark());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        return "OK";
    }


    /**
     * 加密方式使用短信模板发送短信
     *
     * @param request
     * @return
     */
    @PostMapping("/sendSmsMessage")
    @ResponseBody
    public FebsResponse sendSmsMessage(HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("加密方式使用短信模板发送短信=>ip:{},json:{}", ipAddr, jsonData);
            JsonNode jsonNode = mapper.readTree(jsonData);
            String serviceId = jsonNode.at("/serviceId").asText("");
            Map<String, Object> smsConfigMap = smsApiProperties.getSmsConfigMap().get(serviceId);
            if (StringUtils.isEmpty(serviceId) || smsConfigMap == null || smsConfigMap.size() == 0) {
                return new FebsResponse().fail().message("业务参数错误");
            }
            String channel = jsonNode.at("/channel").asText("");
            Map<String, String> map = (Map<String, String>) smsConfigMap.get("channelList");
            List<String> channelList = new ArrayList(map.values());
            if (StringUtils.isEmpty(channel) || !channelList.contains(channel)) {
                return new FebsResponse().fail().message("渠道参数错误");
            }
            String mobile = jsonNode.at("/mobile").asText("");
            if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
                return new FebsResponse().fail().message("请正确填写手机号");
            }
            String submitTime = jsonNode.at("/submitTime").asText("");
            if (StringUtils.isEmpty(submitTime) || !DateUtil.isLegalDate(submitTime, DateUtil.FULL_TIME_SPLIT_PATTERN)) {
                return new FebsResponse().fail().message("日期格式错误");
            }
            String encryptCode = DigestUtils.md5DigestAsHex((channel + serviceId + submitTime + mobile + smsConfigMap.get("key").toString()).getBytes(StandardCharsets.UTF_8));
            String sign = jsonNode.at("/sign").asText("");
            if (StringUtils.isEmpty(sign) || !sign.equals(encryptCode)) {
                return new FebsResponse().fail().message("签名错误");
            }
            String argument = jsonNode.at("/argument").asText("");
            if (StringUtils.isNotBlank(argument)) {
                Map<String, String> smsMap = mapper.readValue(argument, Map.class);
                boolean result = smsModelService.sendSms(mobile, channel, serviceId, BizConstant.BUSINESS_TYPE_CODE, smsMap);
                if (!result) {
                    return new FebsResponse().code(String.valueOf(CommonConstant.SC_JEECG_NO_AUTH)).message("短信发送失败");
                }
                return new FebsResponse().success("短信发送成功");
            }
            boolean result = smsModelService.sendSms(mobile, channel, serviceId, BizConstant.BUSINESS_TYPE_CODE);
            if (!result) {
                return new FebsResponse().code(String.valueOf(CommonConstant.SC_JEECG_NO_AUTH)).message("短信发送失败");
            }
            return new FebsResponse().success("短信发送成功");
        } catch (Exception e) {
            log.info("加密方式使用短信模板发送短信出错：", e);
        }
        return new FebsResponse().fail();
    }


    /**
     * 联通沃阅读一键登录
     *
     * @return
     */
    @RequestMapping(value = "/woRead/login")
    public FebsResponse woReadLogin(String mobile, String channel) {
        FebsResponse febsResponse = new FebsResponse();
        String company = "";
        try {
            if (StringUtils.isBlank(mobile) || StringUtils.isBlank(channel)) {
                return febsResponse.fail().message("参数错误");
            }
            //根据渠道号查询公司
            List<AliChannelConfig> list = aliChannelConfigService.lambdaQuery()
                    .eq(AliChannelConfig::getConfigValue, channel)
                    .list();
            if (list == null) {
                return febsResponse.fail().message("支付参数错误");
            }
            for (AliChannelConfig aliChannelConfig : list) {
                if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 2) {
                    company = "yrjy";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 3) {
                    company = "maihe";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 4) {
                    company = "hydt";
                }
            }
            WoReadLoginResult woReadLoginResult = woReadApiService.shouTingLogin(mobile, company);
            if (woReadLoginResult.isOK() && woReadLoginResult.getData() != null) {
                Map<String, String> map = new HashMap<>();
                map.put("userid", woReadLoginResult.getData().getUserid());
                map.put("token", woReadLoginResult.getData().getToken());
                map.put("timestamp", new Date().getTime() + "");
                String data = Base64.getEncoder().encodeToString(mapper.writeValueAsBytes(map));
                febsResponse.success().data(data);
            } else {
                febsResponse.fail();
            }
        } catch (Exception e) {
            febsResponse.fail();
        }
        return febsResponse;
    }

    /**
     * 获取省份列表
     *
     * @return
     */
    @RequestMapping(value = "/getProvinceList")
    @ResponseBody
    public Object getProvinceList() {
        return provinceTreeService.getProvinceList();
    }


    //江西移动视频彩铃下单通知
    @PostMapping("/jiangxiVrbtNotify")
    public String jiangxiVrbtNotify(HttpServletRequest request) {
        final String CODE_SUCCESS = "SUCCESS";
        final String CODE_ERROR = "ERROR";
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("江西移动视频彩铃下单通知=>ip:{},json:{}", ipAddr, jsonData);
            JiangXiVrbtNotifyParam jiangXiVrbtNotifyParam = mapper.readValue(jsonData, JiangXiVrbtNotifyParam.class);
            if (jiangXiVrbtNotifyParam == null || StringUtils.isEmpty(jiangXiVrbtNotifyParam.getCustomerOrderNo())) {
                log.info("江西移动视频彩铃下单通知,订单号为空,参数:{}", jiangXiVrbtNotifyParam);
                return CODE_ERROR;
            }
            final Subscribe target = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, jiangXiVrbtNotifyParam.getCustomerOrderNo()).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
            if (target == null) {
                log.info("江西移动视频彩铃下单通知,订单查询失败,参数:{}", jiangXiVrbtNotifyParam);
                return CODE_ERROR;
            }

            final String targetId = target.getId();
            int subscribeStatus = BizConstant.JIANGXI_VRBT_ORDER_CODE_SUCCESS.equals(jiangXiVrbtNotifyParam.getOrderStatus()) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL;
            Subscribe upd = new Subscribe();
            upd.setId(targetId);
            upd.setStatus(subscribeStatus);
            upd.setOpenTime(new Date());
            upd.setResult(subscribeStatus == SUBSCRIBE_STATUS_SUCCESS ? "开通成功" : jiangXiVrbtNotifyParam.getMessage());
            subscribeService.updateSubscribeDbAndEs(upd);
            channelService.AdEffectFeedbackNew(subscribeService.getById(targetId), subscribeStatus);

            return CODE_SUCCESS;

        } catch (Exception e) {
            log.info("江西移动视频彩铃下单通知,系统异常", e);
            return CODE_ERROR;
        }
    }


    //江西移动视频彩铃无纸化协议查询
    @PostMapping("/queryAgreement")
    public Result<?> queryAgreement(HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("江西移动视频彩铃无纸化协议查询=>ip:{},json:{}", ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            String mobile = subscribe.getMobile();
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return Result.error("请输入正确格式手机号");
            }
            String channel = subscribe.getChannel();
            if (Strings.isNullOrEmpty(channel)) {
                return Result.error("请输入渠道号");
            }
            JiangXiVrbtBusinessServiceImpl jiangXiVrbtBusinessService = SpringContextUtils.getBean(JiangXiVrbtBusinessServiceImpl.class);
            return jiangXiVrbtBusinessService.getAgreement(mobile, channel);
        } catch (Exception e) {
            log.info("江西移动视频彩铃无纸化协议查询,系统异常", e.getMessage(), e);
            return Result.error("系统异常");
        }
    }

    /**
     * 外部视频彩铃发送短信
     *
     * @return
     */
    @SneakyThrows
    @PostMapping("/outside/getCaptcha/v2")
    public Result outsideSendMsgV2(@RequestParam("mobile") String mobile,
                                   @RequestParam(value = "subChannel") String subChannel,
                                   @RequestParam(value = "userAgent", required = false) String userAgent,
                                   @RequestParam(value = "appPackage", required = false) String appPackage,
                                   @RequestParam(value = "ip", required = false) String ip,
                                   @RequestParam(value = "openId", required = false) String openId,
                                   @RequestHeader Map<String, String> headers,
                                   HttpServletRequest request) {
        log.info("新接口第三方渠道获取验证码请求(v2),mobile:{},渠道号:{},ip:{},app:{},ua:{},openId:{}", mobile, subChannel, ip, appPackage, userAgent, openId);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (blackListService.isBlackList(mobile)) {
            return Result.msgBlackLimit();
        }
        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setSubChannel(subChannel);
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subChannel);
        if (outsideConfig == null || StringUtils.isBlank(outsideConfig.getBizTypeId())) {
            log.error("无效的外部渠道号:{}", subChannel);
            return Result.error("无效的渠道号!");
        }
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        subscribe.setUserAgent(userAgent);
        subscribe.setIp(ip);
        subscribe.setReferer(appPackage);
        subscribe.setDeviceInfo(openId);
//        subscribe.setCreateTime(new Date());
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //if (mobileRegionResult.isIspLiantong()) {
                //    return Result.error("该业务不支持联通用户");
                //}
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统繁忙,请稍后再试!");
        }
        OutsideBusinessConfig outsideBusinessConfig = outsideConfigService.getOutsideBusinessConfig(outsideConfig.getId(), subscribe.getProvince());
        //获取当前省份可以开通业务的渠道号
        String bizTypeId = outsideConfig.getBizTypeId();
        if (outsideBusinessConfig != null) {
            subscribe.setChannel(outsideBusinessConfig.getChannel());
            bizTypeId = outsideBusinessConfig.getBizTypeId();
        } else {
            subscribe.setChannel(outsideConfig.getChannel());
        }
        if ((!"JXYD_ADLD_YR".equals(subscribe.getChannel())) && subscribeService.checkBizRepeat(mobile, subscribe.getChannel())) {
            log.warn("手机号:{}3个月内已开通过业务", mobile);
            Result<Object>  result=Result.msgDuplicateLimit();
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
            subscribe.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(subscribe);
            return result;
        }
        if (adSiteBusinessConfigService.isOsVrbtBlack(subscribe.getChannel(), appPackage)) {
            log.warn("app限制,渠道号:{},手机号:{},app:{}", subscribe.getChannel(), subscribe.getMobile(), appPackage);
            Result<Object>  result= Result.msgAppLimit();
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
            subscribe.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(subscribe);
            return result;
        }
        subscribe.setBizType(BizConstant.getBizTypeByMiguChannel(subscribe.getChannel()));
        if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), subscribe.getProvince())) {
            log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), subscribe.getMobile(), subscribe.getProvince());
            Result<Object>  result=Result.msgProvinceLimit();
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
            subscribe.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(subscribe);
            return result;
        }
        //四川移动业务固定source
        if (StringUtils.equals(subscribe.getBizType(), BIZ_TYPE_SCYD)) {
            subscribe.setSource("https://crbt.cdyrjygs.com/new_sc_mobile/991");
        }
        Result result = bizTypeService.outsideCallGetSmsCode(subscribe, bizTypeId);
        if (!result.isOK()) {
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            if(StringUtils.isNotBlank(result.getErrorMsg())){
                subscribe.setResult(result.getErrorMsg());
            }else{
                subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
            }
            subscribe.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(subscribe);
            result.setErrorMsg("");
//            orderFailLogService.createOrderFailLog(subscribe, result.getMessage());
        }
        return result;
    }

    /**
     * 外部视频彩铃订购
     *
     * @return
     */
    @SneakyThrows
    @PostMapping("/outside/order/v2")
    public Result outsideOrder(@RequestParam("mobile") String mobile,
                               @RequestParam("code") String code,
                               @RequestParam(value = "subChannel") String subChannel,
                               @RequestParam(value = "copyrightId", required = false) String copyrightId,
                               @Validated @RequestParam("orderNo") @Size(max = 32, message = "订单号长度最大为32") String osOrderId,
                               HttpServletRequest request) {

        log.info("第三方渠道订阅请求(v2),提交短信验证码 mobile:{},code:{},subChannel:{},copyrightId:{},osOrderId:{}", mobile, code, subChannel, copyrightId, osOrderId);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!code.matches(Regexp.MIGU_SMS_CODE_REG)) {
            return Result.error("验证码错误");
        }

        Subscribe subscribe = subscribeService.getById(osOrderId);
        if (subscribe != null) {
            subscribe.setSmsCode(code);
            //本次transactionId已开通业成功不可再次提交验证码
            if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
                return Result.error("你已开通成功，请勿重复提交");
            }
        }
        return bizTypeService.outsideCallSumbitSmsCode(subscribe, code, osOrderId);
    }

    //和裕视频彩铃通知接口
    @PostMapping("/heyuVrbtNotify")
    public String heyuVrbtNotify(@RequestBody JsonNode jsonNode) {
        log.info("和裕视频彩铃回调通知:{}", jsonNode.toString());
        String mobile = jsonNode.at("/mobile").asText();
        String orderId = jsonNode.at("/orderId").asText();
        String resCode = jsonNode.at("/resCode").asText();
        String resMsg = jsonNode.at("/resMsg").asText();
        subscribeResultNotifyService.heyuNotify(mobile, orderId, resCode, resMsg);
        return "success";
    }

    //爱豆来电通知接口
    @RequestMapping("/aidoulaidianNotify")
    public String aidoulaidianNotify(@RequestParam(name = "orderId", required = false) String orderId,
                                     @RequestParam(name = "status", required = false) String status,
                                     @RequestParam(name = "mobile", required = false) String mobile,
                                     @RequestParam(name = "cpShortPort", required = false) String cpShortPort,
                                     @RequestParam(name = "cpType", required = false) String cpType,
                                     @RequestParam(name = "startPrice", required = false) String startPrice,
                                     @RequestParam(name = "upPort", required = false) String upPort,
                                     @RequestParam(name = "cpparam", required = false) String cpparam) {
        log.info("爱豆来电通知接口:orderId:{},status:{},mobile:{},cpShortPort:{},cpType:{},startPrice:{},upPort:{},cpparam:{}", orderId, status, mobile, cpShortPort, cpType, startPrice, upPort, cpparam);
        subscribeResultNotifyService.aidoulaidianNotify(mobile, cpparam, status);
        return "ok";
    }

    //爱豆来电通知接口
    @RequestMapping("/shoujizixunNotify")
    public String shoujizixunNotify(@RequestParam(name = "pgid", required = false) String pgid,
                                    @RequestParam(name = "code", required = false) String code,
                                    @RequestParam(name = "mobile", required = false) String mobile,
                                    @RequestParam(name = "linkid", required = false) String linkid,
                                    @RequestParam(name = "stat", required = false) String stat,
                                    @RequestParam(name = "msg", required = false) String msg,
                                    @RequestParam(name = "spnum", required = false) String spnum,
                                    @RequestParam(name = "detail", required = false) String detail) {
        log.info("手机咨询通知接口:pgid:{},code:{},mobile:{},linkid:{},stat:{},msg:{},spnum:{},detail:{}", pgid, code, mobile, linkid, stat, msg, spnum, detail);
        subscribeResultNotifyService.shoujizixunNotify(mobile, detail, stat, msg);
        return "OK";
    }

    //重庆权益超市通知接口
    @RequestMapping("/qycsNotify")
    public String chongqingQycsNotify(@RequestParam(name = "status", required = false) String status,
                                      @RequestParam(name = "price", required = false) String price,
                                      @RequestParam(name = "tradeid", required = false) String tradeid,
                                      @RequestParam(name = "phone", required = false) String phone,
                                      @RequestParam(name = "content", required = false) String content,
                                      @RequestParam(name = "cpparam", required = false) String cpparam,
                                      @RequestParam(name = "amount", required = false) String amount,
                                      @RequestParam(name = "motime", required = false) String motime,
                                      @RequestParam(name = "mrtime", required = false) String mrtime) {
        log.info("重庆权益超市通知接口:status:{},price:{},tradeid:{},phone:{},content:{},cpparam:{},amount:{},motime:{},mrtime:{}", status, price, tradeid, phone, content, cpparam, amount, motime, mrtime);
        subscribeResultNotifyService.chongqingQycsNotify(status, phone, tradeid, mrtime);
        return "ok";
    }

    //北京亿汇通知接口
    @RequestMapping("/beijingyihuiNotify")
    public String beijingyihuiNotify(@RequestParam(name = "status", required = false) String status,
                                     @RequestParam(name = "msg", required = false) String msg,
                                     @RequestParam(name = "price", required = false) String price,
                                     @RequestParam(name = "cpparam", required = false) String cpparam,
                                     @RequestParam(name = "linkid", required = false) String linkid,
                                     @RequestParam(name = "channelNo", required = false) String channelNo,
                                     @RequestParam(name = "phone", required = false) String phone,
                                     @RequestParam(name = "spnumber", required = false) String spnumber,
                                     @RequestParam(name = "momsg", required = false) String momsg,
                                     @RequestParam(name = "flag", required = false) String flag,
                                     @RequestParam(name = "mobile", required = false) String mobile) {
        log.info("北京亿汇通知接口:status:{},msg:{},price:{},cpparam:{},linkid:{},channelNo:{},spnumber:{},momsg:{},flag:{}mobile:{}", status, msg, price, cpparam, linkid, channelNo, spnumber, momsg, flag, mobile);
        subscribeResultNotifyService.beijiyihuiNotify(mobile, status, linkid, msg);
        return "ok";
    }


    /**
     * 咪咕互娱通知发码
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/huyu/notify/coupon/{gameName}")
    public MiGuHuYuNotifyResp miGuHuYuNotify(@RequestBody String requestBody, @PathVariable String gameName, HttpServletRequest request) {

        try {
            final MiGuHuYuResult miGuHuYuResult = new ObjectMapper().readValue(requestBody, MiGuHuYuResult.class);
            log.info("咪咕互娱权益下发通知-game:{},手机号:{},body:{}", gameName, MiGuHuYuResult.decodeUserId2Mobile(miGuHuYuResult.getUserId()), requestBody);
            return junboChargeLogService.miGuHuYuSendCoupon(miGuHuYuResult, request, gameName);
        } catch (Exception e) {
            log.error("咪咕互娱权益下发异常-game:{},body:{}", gameName, requestBody, e);
        }
        return MiGuHuYuNotifyResp.error("系统错误");
    }

    /**
     * 游戏道具结果回调
     *
     * @param request
     * @return
     */
    @PostMapping("/gameprop/notify")
    public FebsResponse gamePropNotify(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String requestBody = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            final GamePropResult gamePropResult = new ObjectMapper().readValue(requestBody, GamePropResult.class);
            try {
                gamePropResult.setCallbackMsg(StringEscapeUtils.unescapeJava(gamePropResult.getCallbackMsg()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            log.info("游戏道具结果回调=>通知:{}", gamePropResult);
            return junboChargeLogService.gamePropNotify(gamePropResult);
        } catch (Exception e) {
            e.printStackTrace();
            return new FebsResponse().fail().message("系统异常！");
        }
    }

    /**
     * 联通沃悦读-微信单次扣款
     *
     * @return
     */
    @ApiOperation(value = "联通沃悦读-微信单次扣款", notes = "联通沃悦读-微信单次扣款")
    @PostMapping(value = "/woReadWxSinglePay")
    public FebsResponse woReadWxSinglePay(HttpServletRequest request, HttpServletResponse response) {

        FebsResponse febsResponse = new FebsResponse();
        String contractCode = "";
        String company = "";
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("渠道订阅请求=>ip:{},{}", ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);

            String businessType = subscribe.getBusinessType();
            String mobile = subscribe.getMobile();

            if (StringUtils.isBlank(businessType)) {
                return febsResponse.fail().message("支付参数错误");
            }

            subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            subscribe.setReferer(finger);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());

            if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
                return febsResponse.fail().message("请正确填写手机号");
            }
            //黑名单直接返回
            if (blackListService.isBlackList(subscribe.getMobile())) {
                return febsResponse.fail().message(Result.MSG_BLACK_LIMIT);
            }

            //根据渠道号查询公司
            List<AliChannelConfig> list = aliChannelConfigService.lambdaQuery()
                    .eq(AliChannelConfig::getConfigValue, businessType)
                    .list();
            if (list == null) {
                return febsResponse.fail().message("支付参数错误");
            }
            for (AliChannelConfig aliChannelConfig : list) {
                if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 2) {
                    contractCode = "yr" + IdWorker.getId();
                    company = "yrjy";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 3) {
                    contractCode = "mh" + IdWorker.getId();
                    company = "maihe";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 4) {
                    contractCode = "hy" + IdWorker.getId();
                    company = "hydt";
                }
            }

            //发起签约
            WoReadWxBeforeOrderResult woReadWxBeforeOrderResult = woReadApiService.wxBeforeSinglePay(mobile, ipAddr, businessType, company);
            if (woReadWxBeforeOrderResult.isOK()) {
                febsResponse.success().data(woReadWxBeforeOrderResult.getData().getReqUrl());
                subscribe.setIspOrderNo(woReadWxBeforeOrderResult.getData().getOutTradeNo());
            } else {
                febsResponse.fail().message(woReadWxBeforeOrderResult.getResMessage());
            }
            //保存签约信息
            woReadSinglePayOrderService.saveSinglePayOrder(subscribe, contractCode, 1, company);
        } catch (Exception e) {
            log.info("联通沃悦读-微信单次扣款：", e);
        }
        return febsResponse;
    }

    /**
     * 联通沃悦读-支付宝单次扣款
     *
     * @return
     */
    @ApiOperation(value = "联通沃悦读-支付宝单次扣款", notes = "联通沃悦读-支付宝单次扣款")
    @PostMapping(value = "/woReadAliSinglePay")
    public FebsResponse woReadAliSinglePay(HttpServletRequest request, HttpServletResponse response) {

        FebsResponse febsResponse = new FebsResponse();
        String contractCode = "";
        String company = "";
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("渠道订阅请求=>ip:{},{}", ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);

            String businessType = subscribe.getBusinessType();
            String mobile = subscribe.getMobile();

            if (StringUtils.isBlank(businessType)) {
                return febsResponse.fail().message("支付参数错误");
            }

            /*Alipay alipayConfig = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
            if(alipayConfig==null){
                return febsResponse.fail().message("支付参数错误");
            }*/

            subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            subscribe.setReferer(finger);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());

            if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
                return febsResponse.fail().message("请正确填写手机号");
            }
            //黑名单直接返回
            if (blackListService.isBlackList(subscribe.getMobile())) {
                return febsResponse.fail().message(Result.MSG_BLACK_LIMIT);
            }

            //根据渠道号查询公司
            List<AliChannelConfig> list = aliChannelConfigService.lambdaQuery()
                    .eq(AliChannelConfig::getConfigValue, businessType)
                    .list();
            if (list == null) {
                return febsResponse.fail().message("支付参数错误");
            }
            for (AliChannelConfig aliChannelConfig : list) {
                if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 2) {
                    contractCode = "yr" + IdWorker.getId();
                    company = "yrjy";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 3) {
                    contractCode = "mh" + IdWorker.getId();
                    company = "maihe";
                } else if (aliChannelConfig.getPaySupport() != null && aliChannelConfig.getPaySupport() == 4) {
                    contractCode = "hy" + IdWorker.getId();
                    company = "hydt";
                }
            }

            //发起扣款
            WoReadAliBeforeOrderResult woReadAliBeforeOrderResult = woReadApiService.aliBeforeSinglePay(mobile, ipAddr, businessType, company);
            if (woReadAliBeforeOrderResult.isOK()) {
                febsResponse.success().data(woReadAliBeforeOrderResult.getData().getReqUrl());
                subscribe.setIspOrderNo(woReadAliBeforeOrderResult.getData().getOrderId());
            } else {
                febsResponse.fail().message(woReadAliBeforeOrderResult.getResMessage());
            }
            //保存签约信息
            woReadSinglePayOrderService.saveSinglePayOrder(subscribe, contractCode, 2, company);
        } catch (Exception e) {
            log.info("联通沃悦读-支付宝单次扣款出错：", e);
        }
        return febsResponse;
    }

    /**
     * 联通沃悦读微信支付宝单次扣款异步通知
     *
     * @param woReadNotifyResult 只要回调了，就代表扣费成功
     * @return
     */
    @PostMapping("/pay/wyd/singlePayNotify")
    @ApiOperation("联通沃悦读微信支付宝单次扣款异步通知")
    public String singlePayNotify(@RequestBody WoReadNotifyResult woReadNotifyResult) {
        String succRespMsg = "success";
        String failRespMsg = "failure";
        try {
            log.info("联通沃悦读回调通知,参数:{}", mapper.writeValueAsString(woReadNotifyResult));
            String orderId = woReadNotifyResult.getOrderId();
            if (StringUtils.isBlank(orderId)) {
                log.info("联通沃悦读微信支付宝异步通知,订单号为空,参数:{}", mapper.writeValueAsString(woReadNotifyResult));
                return failRespMsg;
            }
            WoReadSinglePayOrder woReadSinglePayOrder = woReadSinglePayOrderService.lambdaQuery()
                    .eq(WoReadSinglePayOrder::getOutTradeNo, orderId)
                    .eq(WoReadSinglePayOrder::getMobile, woReadNotifyResult.getUserAccount())
                    .one();
            if (woReadSinglePayOrder == null) {
                log.info("联通沃悦读微信支付宝异步通知,找不到订单,参数:{}", mapper.writeValueAsString(woReadNotifyResult));
                return failRespMsg;
            }
            woReadSinglePayOrder.setOrderStatus(1);
            woReadSinglePayOrder.setUserId(woReadNotifyResult.getUserId());
            woReadSinglePayOrder.setPayTime(new Date());
            woReadSinglePayOrderService.updateById(woReadSinglePayOrder);
            Subscribe subscribe = subscribeService.lambdaQuery()
                    .eq(Subscribe::getIspOrderNo, orderId)
                    .last("limit 1")
                    .one();
            if (subscribe != null) {
                subscribe.setStatus(1);
                subscribe.setOpenTime(new Date());
                subscribe.setResult("付费成功");
                subscribe.setRemark(orderId);
                subscribeService.updateSubscribeDbAndEs(subscribe);
                BizLogUtils.logSubscribe(subscribe);
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }
        } catch (Exception e) {
            log.error("联通沃悦读回调通知,参数:{},异常:", woReadNotifyResult.toString(), e);
            return failRespMsg;
        }
        return succRespMsg;
    }


    /**
     * 天翼通讯助理订购回调通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/tianyiCommAssist/notify")
    public Map<String, Object> tianyiCommAssistNotify(@RequestBody String requestBody, HttpServletRequest request) {
        log.info("天翼通讯助理订购回调通知=>参数:{}", requestBody);
        Map<String, Object> map = Maps.newHashMap();
        try {
            final TianyiCommAssistRequest tianyiCommAssistRequest = new ObjectMapper().readValue(requestBody, TianyiCommAssistRequest.class);
            synchronized (interner.intern(tianyiCommAssistRequest.getRequestId())) {
                return tianyiCommAssistService.tianyiCommAssistNotify(tianyiCommAssistRequest.getPhone(), tianyiCommAssistRequest.getOrderStatus(), tianyiCommAssistRequest.getRequestId(), map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        map.put("code", "0");
        map.put("msg", "系统错误");
        return map;
    }


    /**
     * 视频彩铃抖音小程序首页查询图片
     *
     * @return
     */
    @PostMapping(value = "/home/<USER>")
    @ResponseBody
    public FebsResponse homeImgList() {
        ColumnObject columnObject = columnService.queryColumnList();
        return new FebsResponse().success().data(columnObject);
    }

    /**
     * 视频彩铃抖音小程序首页分页查询视频
     *
     * @return
     */
    @PostMapping(value = "/home/<USER>")
    @ResponseBody
    public FebsResponse homeMusicPageList(@RequestParam(value = "columnId", required = false, defaultValue = "") String columnId,
                                          @RequestParam(value = "columnClassName", required = false, defaultValue = "") String columnClassName,
                                          @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(value = "pageSize", required = false, defaultValue = "6") Integer pageSize) {
        Page<MusicVo> page = new Page<MusicVo>(pageNo, pageSize);
        IPage<MusicVo> pageList = columnService.queryMusicList(columnId, columnClassName, page);
        return new FebsResponse().success().data(pageList);
    }

    /**
     * 蜂助手四川移动前端通知接收接口
     *
     * @param jsonNode
     * @param request
     * @return
     */
    @RequestMapping(value = "/fengzhushou/sichuanYidong/receive")
    public Result fengzhushouSichuanYidongReceive(@RequestBody JsonNode jsonNode, HttpServletRequest request) {
        log.info("蜂助手四川移动前端通知接收接口参数:{}", jsonNode);
        String mobile = jsonNode.at("/mobile").asText();
        String transactionId = jsonNode.at("/transactionId").asText();
        String msg = jsonNode.at("/res_msg").asText();
        String code = jsonNode.at("/res_code").asText();
        String result = jsonNode.at("/result").asText();
        subscribeResultNotifyService.fengzhushouSichuanYidongReceive(mobile, code, msg, transactionId, result);
        return Result.ok("成功");
    }


    /**
     * 视频彩铃抖音小程序歌曲收藏
     *
     * @return
     */
    @PostMapping(value = "/music/collect")
    @ResponseBody
    public FebsResponse musicCollect(@RequestParam(value = "copyrightId", required = false, defaultValue = "") String copyrightId,
                                     @RequestParam(value = "mobile", required = false, defaultValue = "") String mobile) {
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return new FebsResponse().fail().message("请正确填写手机号");
        }
        if (StringUtils.isBlank(copyrightId)) {
            return new FebsResponse().fail().message("版权ID错误");
        }
        synchronized (interner.intern(mobile)) {
            return musicCollectService.musicCollect(copyrightId, mobile);
        }
    }

    /**
     * 视频彩铃抖音小程序查询歌曲是否收藏
     *
     * @return
     */
    @PostMapping(value = "/music/isCollect")
    @ResponseBody
    public FebsResponse musicIsCollect(@RequestParam(value = "copyrightId", required = false, defaultValue = "") String copyrightId,
                                       @RequestParam(value = "mobile", required = false, defaultValue = "") String mobile) {
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return new FebsResponse().fail().message("请正确填写手机号");
        }
        if (StringUtils.isBlank(copyrightId)) {
            return new FebsResponse().fail().message("版权ID错误");
        }
        synchronized (interner.intern(mobile)) {
            return musicCollectService.musicIsCollect(copyrightId, mobile);
        }
    }

    /**
     * 视频彩铃抖音小程序歌曲收藏分页查询视频
     *
     * @return
     */
    @PostMapping(value = "/query/music/collect")
    @ResponseBody
    public FebsResponse queryMusicCollect(@RequestParam(value = "mobile", required = false, defaultValue = "") String mobile,
                                          @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(value = "pageSize", required = false, defaultValue = "6") Integer pageSize) {
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return new FebsResponse().fail().message("请正确填写手机号");
        }
        Page<MusicVo> page = new Page<MusicVo>(pageNo, pageSize);
        return musicCollectService.queryMusicCollect(mobile, page);
    }


    /**
     * 视频彩铃抖音小程序设置视频彩铃铃音
     *
     * @param mobile
     * @param channelCode
     * @param copyrightId
     * @return
     */
    @PostMapping(value = "/set/vrbt/music")
    @ResponseBody
    public FebsResponse setVrbtMusic(@RequestParam(value = "mobile", required = false, defaultValue = "") String mobile,
                                     @RequestParam(value = "channelCode", required = false, defaultValue = "") String channelCode,
                                     @RequestParam(value = "copyrightId", required = false, defaultValue = "") String copyrightId) {
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return new FebsResponse().fail().message("请正确填写手机号");
        }
        if (StringUtils.isBlank(copyrightId)) {
            return new FebsResponse().fail().message("版权ID错误");
        }
        if (StringUtils.isBlank(channelCode)) {
            return new FebsResponse().fail().message("渠道号错误");
        }
        return musicHotLevelService.setVrbtMusic(mobile, channelCode, copyrightId);
    }

    /**
     * 视频彩铃抖音小程序热度分页查询视频
     *
     * @return
     */
    @PostMapping(value = "/query/music/hotLevel")
    @ResponseBody
    public FebsResponse queryMusicHotLevel(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                           @RequestParam(name = "pageSize", defaultValue = "5") Integer pageSize) {
        Page<MusicVo> page = new Page<MusicVo>(pageNo, pageSize);
        IPage<MusicVo> pageList = musicHotLevelService.queryMusicHotLevel(page);
        return new FebsResponse().success().data(pageList);
    }

    //抖音视频彩铃小程序下发短信验证码
    @RequestMapping(value = "/douyinVrbtSms")
    public Result getSms(@RequestParam String mobile) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult == null || !mobileRegionResult.isIspYidong()) {
            return Result.error("暂只支持移动用户");
        }
        boolean smsResult = smsValidateService.create(mobile, MiguApiService.CENTRALITY_CHANNEL_CODE_04C);
        if (!smsResult) {
            return Result.error("发送短信验证码失败,请稍后再试");
        }
        return Result.ok("发送短信成功");
    }

    //抖音视频彩铃小程序登录
    @RequestMapping(value = "/douyinVrbtLogin")
    public Result douyinVrbtLogin(@RequestParam String mobile, @RequestParam String code) {
        if (Strings.isNullOrEmpty(mobile) || Strings.isNullOrEmpty(code)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult == null || !mobileRegionResult.isIspYidong()) {
            return Result.error("暂只支持移动用户");
        }
        if (!code.matches("^\\d{6}$")) {
            return Result.noauth("验证码错误");
        }
        if (mobile.equals("17260807125")) {
            if (!"123456".equals(code)) {
                return Result.error("短信验证码错误");
            }
        } else {
            //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
            try {
                smsValidateService.check(mobile, code);
            } catch (JeecgBootException e) {
                //e.printStackTrace();
                return Result.error(e.getMessage());
            }
        }
        RemoteResult remoteResult = miguApiService.vrbtMonthStatusQuery(mobile, MiguApiService.CENTRALITY_CHANNEL_CODE_04C, true);
        return Result.ok("登录成功", remoteResult.isVrbtMember());
    }


    //获取当前手机号是否可开通视频彩铃彩铃中心业务参数
    @RequestMapping(value = "/douyinVrbtPrarameter")
    public Result douyinVrbtPrarameter(@RequestParam String mobile) {
        //设置归属地
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult == null || !mobileRegionResult.isIspYidong()) {
            return Result.error("暂只支持移动用户");
        }
        List<DictModel> channelList = sysBaseAPI.queryDictItemsByCode("vrbt_dy_channel");
        Optional<DictModel> any = channelList.stream().filter(channelModel -> provinceBusinessChannelConfigService.allow(channelModel.getValue(), mobileRegionResult.getProvince())).findAny();
        if (any.isPresent()) {
            String channelCode = any.get().getValue();
            RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
            if (!result.isOK()) {
                return Result.error("获取失败");
            }
            CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
            String serviceId = cmsCrackConfig.getServiceId();
            String token = result.getToken();
            ObjectNode objectNode = mapper.createObjectNode();
            objectNode.put("channelCode", channelCode);
            objectNode.put("serviceId", serviceId);
            objectNode.put("token", token);
            return Result.ok("成功", objectNode);
        } else {
            return Result.error("暂未开放，敬请期待");
        }
    }


    /**
     * 卡塞存量业务回调通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/up/syncMessage")
    public Map<String, Object> upSyncMessage(@RequestBody String requestBody, HttpServletRequest request) {
        log.info("卡塞存量业务回调通知=>参数:{}", requestBody);
        Map<String, Object> map = Maps.newHashMap();
        try {
            final KaSaiStockRequest kaSaiStockRequest = new ObjectMapper().readValue(requestBody, KaSaiStockRequest.class);
            synchronized (interner.intern(kaSaiStockRequest.getOrderNo())) {
                return kaSaiStockService.upSyncMessage(map, kaSaiStockRequest);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        map.put("code", "0");
        map.put("data", null);
        map.put("msg", "系统错误");
        return map;
    }


    /**
     * 四川个人名片订购回调通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/siChuanExclusiveCard/notify")
    public Map<String, Object> siChuanExclusiveCardNotify(@RequestBody String requestBody, HttpServletRequest request) {
        log.info("四川个人名片订购回调通知=>参数:{}", requestBody);
        Map<String, Object> map = Maps.newHashMap();
        try {
            final SiChuanExclusiveCardRequest siChuanExclusiveCardRequest = new ObjectMapper().readValue(requestBody, SiChuanExclusiveCardRequest.class);
            synchronized (interner.intern(siChuanExclusiveCardRequest.getPhone())) {
                return siChuanExclusiveCardService.siChuanBusinessCardNotify(siChuanExclusiveCardRequest.getPhone(), siChuanExclusiveCardRequest.getStatus(), siChuanExclusiveCardRequest.getSerialnum(), map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        map.put("code", "0");
        map.put("msg", "系统错误");
        return map;
    }


    /**
     * 抖音小程序支付结果回调
     * https://crbt.cdyrjygs.com/cms-vrbt/api/douyin/callback
     *
     * @param request
     * @return
     */
    @RequestMapping("/douyin/callback")
    @ResponseBody
    public String douyinPaymentCallback(HttpServletRequest request) {
        //log.info("抖音小程序担保支付回调,参数:{}", allRequestParams);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String raw = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            String contentType = request.getHeader("Content-Type");
            String idEntifyName = request.getHeader("Byte-Identifyname");
            String logId = request.getHeader("Byte-Logid");
            String nonceStr = request.getHeader("Byte-Nonce-Str");
            String signaTure = request.getHeader("Byte-Signature");
            String timeStamp = request.getHeader("Byte-Timestamp");
            log.info("抖音小程序支付结果回调,参数:{},type:{},idEntifyName:{},logId:{},nonceStr:{},signaTure:{},timeStamp:{}", raw, contentType, idEntifyName, logId, nonceStr, signaTure, timeStamp);
            DouYinPayNotifyMsg result = qyclWxpayService.douyinPaymentCallback(raw, contentType, idEntifyName, logId, nonceStr, signaTure, timeStamp);
            if (result == null) {
                log.error("抖音小程序支付结果回调,验签失败,raw:{}", raw);
                return "{\"err_no\": 0, \"err_tips\": \"success\"}";
            }
            boolean isPaySucc = StringUtil.equals(result.getStatus(), WXPayConstants.SUCCESS);
            QyclOrderPayLog order = orderPayLogService.queryOrder(result.getOutOrderNo());
            if (order == null) {
                log.error("抖音小程序支付结果回调,重复通知,raw:{}", raw);
                return "{\"err_no\": 0, \"err_tips\": \"success\"}";
            }
            if (isPaySucc) {
                orderPayLogService.updatePayStatus(result.getOutOrderNo(), result.getOrderId(), PAY_STATUS_SUCCESS);
            } else {
                orderPayLogService.updatePayStatus(result.getOutOrderNo(), result.getOrderId(), PAY_STATUS_FAIL);
            }
            return "{\"err_no\": 0, \"err_tips\": \"success\"}";
        } catch (Exception e) {
            log.warn("抖音小程序支付结果回调", e);
            return "{\"err_no\": 999, \"err_tips\": \"success\"}";
        }
    }


    /**
     * 抖音小程序新版本支付
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/douyin/trade/pay")
    @ResponseBody
    public FebsResponse tradePay(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String app = request.getHeader("x-requested-with");
        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("抖音小程序新版本支付请求=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonData, userAgent);
            DouYinPay douYinPay = mapper.readValue(jsonData, DouYinPay.class);
            if (adSiteBusinessConfigService.isGlobalBlackApp(app)) {
                log.error("手机号:{},渠道号:{},app:{}已被全局屏蔽", douYinPay.getMobile(), douYinPay.getChannel(), app);
                return new FebsResponse().fail().message("暂无资格，敬请期待！");
            }

            if (Strings.isNullOrEmpty(douYinPay.getBusinessType())) {
                return new FebsResponse().fail().message("支付渠道错误");
            }
            if (Strings.isNullOrEmpty(douYinPay.getSubject())) {
                return new FebsResponse().fail().message("产品名称错误");
            }
            if (Strings.isNullOrEmpty(douYinPay.getTradeType())) {
                douYinPay.setTradeType(BizConstant.TRADE_TYPE_TIKTOK_TRADE);
            }
            if (BizConstant.TRADE_TYPE_TIKTOK_TRADE.equals(douYinPay.getTradeType())) {
                synchronized (interner.intern(douYinPay.getMobile())) {
                    try {
                        if (Strings.isNullOrEmpty(douYinPay.getMobile()) || !douYinPay.getMobile().matches(BizConstant.MOBILE_REG)) {
                            return new FebsResponse().fail().message("请输入正确格式手机号");
                        }
                        if (Strings.isNullOrEmpty(douYinPay.getTransactionId())) {
                            return new FebsResponse().fail().message("渠道订单错误");
                        }
                        if (Strings.isNullOrEmpty(douYinPay.getChannel())) {
                            return new FebsResponse().fail().message("渠道号错误");
                        }
                        if (Strings.isNullOrEmpty(douYinPay.getCompanyOwner())) {
                            return new FebsResponse().fail().message("公司归属错误");
                        }

                        String orderId = orderPayLogService.saveNotPaymentOrder(douYinPay.getOpenId(), douYinPay.getCompanyTitle(), douYinPay.getMobile(), douYinPay.getCompanyOwner(), douYinPay.getChannel(), douYinPay.getSubChannel(), douYinPay.getTransactionId());
                        //查询视频彩铃订单
                        QyclOrderPayLog order = orderPayLogService.queryOrder(orderId);
                        if (order == null) {
                            return new FebsResponse().fail().message("订单错误");
                        }
                        Map<String, String> resp = qyclWxpayService.douYinPay(orderId, null, douYinPay.getSubject(), douYinPay.getTradeType(), douYinPay.getBusinessType());
                        return new FebsResponse().success().data(resp);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return new FebsResponse().fail().message("订单错误");
                }
            }
        } catch (Exception e) {
            log.error("抖音小程序新版本支付请求,处理异常", e);
            return new FebsResponse().fail().message(e.getMessage());
        }
        return new FebsResponse().fail().message("订单错误");
    }


    /**
     * 抖音小程序新版本退款结果回调
     * https://crbt.cdyrjygs.com/cms-vrbt/api/douyin/trade/refundNotify
     *
     * @param request
     * @return
     */
    @RequestMapping("/douyin/trade/refundNotify")
    @ResponseBody
    public String douyinTradeRefundNotify(HttpServletRequest request) {
        //log.info("抖音小程序担保支付回调,参数:{}", allRequestParams);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String raw = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            String contentType = request.getHeader("Content-Type");
            String idEntifyName = request.getHeader("Byte-Identifyname");
            String logId = request.getHeader("Byte-Logid");
            String nonceStr = request.getHeader("Byte-Nonce-Str");
            String signaTure = request.getHeader("Byte-Signature");
            String timeStamp = request.getHeader("Byte-Timestamp");
            log.info("抖音小程序新版本退款结果回调,参数:{},type:{},idEntifyName:{},logId:{},nonceStr:{},signaTure:{},timeStamp:{}", raw, contentType, idEntifyName, logId, nonceStr, signaTure, timeStamp);
            DouYinRefundNotifyMsg result = qyclWxpayService.douyinTradeRefundNotify(raw, contentType, idEntifyName, logId, nonceStr, signaTure, timeStamp);
            if (result == null) {
                log.error("抖音小程序新版本退款结果回调,验签失败,raw:{}", raw);
                return "{\"err_no\": 0, \"err_tips\": \"success\"}";
            }
            boolean isPaySucc = StringUtil.equals(result.getStatus(), WXPayConstants.SUCCESS);
            QyclOrderPayLog orderPayLog = orderPayLogService.queryRefundOrder(result.getOrderId());
            if (orderPayLog == null) {
                log.error("抖音小程序新版本退款结果回调,重复通知,raw:{}", raw);
                return "{\"err_no\": 0, \"err_tips\": \"success\"}";
            }
            String refundAmount = BigDecimal.valueOf(result.getRefundTotalAmount()).divide(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
            if (isPaySucc) {
                orderPayLogService.upadateRefundStatus(orderPayLog.getId(), refundAmount, orderPayLog.getRefundOrderNo(), "退款成功！", REFUND_SUCCESS);
            } else {
                orderPayLogService.upadateRefundStatus(orderPayLog.getId(), refundAmount, orderPayLog.getRefundOrderNo(), "退款失败！", REFUND_FAIL);
            }
            return "{\"err_no\": 0, \"err_tips\": \"success\"}";
        } catch (Exception e) {
            log.warn("抖音小程序新版本退款结果回调", e);
            return "{\"err_no\": 999, \"err_tips\": \"success\"}";
        }
    }


    /**
     * 抖音小程序新版本获取用户授权
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/douyin/user/auth")
    @ResponseBody
    public FebsResponse userAuth(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String app = request.getHeader("x-requested-with");
        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("抖音小程序新版本获取用户授权请求=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonData, userAgent);
            DYToken dyToken = mapper.readValue(jsonData, DYToken.class);

            if (Strings.isNullOrEmpty(dyToken.getBusinessType())) {
                return new FebsResponse().fail().message("支付渠道错误");
            }
            if (Strings.isNullOrEmpty(dyToken.getCode())) {
                return new FebsResponse().fail().message("登录凭证错误");
            }
            if (Strings.isNullOrEmpty(dyToken.getTradeType())) {
                dyToken.setTradeType(BizConstant.TRADE_TYPE_TIKTOK_TRADE);
            }
            if (BizConstant.TRADE_TYPE_TIKTOK_TRADE.equals(dyToken.getTradeType())) {
                synchronized (interner.intern(dyToken.getCode())) {
                    try {
                        DouYinTokenRequest douYinTokenRequest = qyclWxpayService.douYinAuth(dyToken.getCode(), dyToken.getAnonymousCode(), dyToken.getTradeType(), dyToken.getBusinessType());
                        if (douYinTokenRequest.isOK()) {
                            return new FebsResponse().success().data(douYinTokenRequest);
                        }
                        return new FebsResponse().fail().message("获取授权失败");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return new FebsResponse().fail().message("获取授权失败");
                }
            }
        } catch (Exception e) {
            log.error("抖音小程序新版本获取用户授权请求,处理异常", e);
            return new FebsResponse().fail().message(e.getMessage());
        }
        return new FebsResponse().fail().message("获取授权失败");
    }


    /**
     * AI视频彩铃订购回调通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/aiVrbt/notify")
    public Map<String, Object> aiVrbtNotify(@RequestBody String requestBody, HttpServletRequest request) {
        log.info("AI视频彩铃订购回调通知=>参数:{}", requestBody);
        Map<String, Object> map = Maps.newHashMap();
        try {
            final AiVrbtRequest aiVrbtRequest = new ObjectMapper().readValue(requestBody, AiVrbtRequest.class);
            synchronized (interner.intern(aiVrbtRequest.getMobile())) {
                return aiVrbtService.aiVrbtNotify(aiVrbtRequest.getMobile(), aiVrbtRequest.getState(), aiVrbtRequest.getOrderNo(), map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        map.put("code", "500");
        map.put("description", "系统错误");
        return map;
    }


    /**
     * 话费充值通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/feeCharge/notify")
    public String feeChargeNotify(@RequestBody String requestBody, HttpServletRequest request) {
        log.info("话费充值回调通知=>参数:{}", requestBody);
        try {
            final FeeChargeRequest feeChargeRequest = new ObjectMapper().readValue(requestBody, FeeChargeRequest.class);
            synchronized (interner.intern(feeChargeRequest.getOrderId())) {
                return mobileFeeChargeLogService.feeChargeNotify(feeChargeRequest);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "ok";
    }

    /**
     * 斯特普江苏移动通知
     *
     * @param jsonNode
     * @param request
     * @return
     */
    @RequestMapping(value = "/stpJsydNotify")
    @ResponseBody
    public ObjectNode stpJiangsuyidongNotify(@RequestBody JsonNode jsonNode, HttpServletRequest request) {
        log.info("斯特普移动通知接口参数:{}", jsonNode);
        String ispOrderNo = jsonNode.at("/userOrderSn").asText();
        String code = jsonNode.at("/status").asText();
        subscribeResultNotifyService.stpJiangsuyidongNotify(ispOrderNo, code);
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("code", "1");
        return objectNode;
    }


    /**
     * 时代星辰联通业务通知
     *
     * @param status
     * @param msg
     * @param price
     * @param cpparam
     * @param linkid
     * @param channelNo
     * @param phone
     * @param flag
     * @return
     */
    @GetMapping("/sdxcliantong/notify")
    public String sdxcLianTongNotify(@RequestParam("status") String status,
                                     @RequestParam("msg") String msg,
                                     @RequestParam("price") String price,
                                     @RequestParam("cpparam") String cpparam,
                                     @RequestParam("linkid") String linkid,
                                     @RequestParam("channelNo") String channelNo,
                                     @RequestParam("phone") String phone,
                                     @RequestParam("flag") String flag
    ) {
        log.info("时代星辰联通业务通知=>手机号:{},订购状态:{},订单号:{},描述:{}", phone, status, linkid, msg);
        try {
            synchronized (interner.intern(linkid)) {
                sdxcLianTongService.sdxcLianTongNotify(phone, status, linkid);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "ok";
    }

    /**
     * 广州旭同移动业务通知
     *
     * @param sid
     * @param phone
     * @param orderid
     * @param subscribeTime
     * @param result
     * @param resultMsg
     * @param price
     * @return
     */
    @GetMapping("/xutongyidong/notify")
    public XuTongYiDongResponse xuTongYiDongNotify(@RequestParam(name = "sid", defaultValue = "") String sid,
                                                   @RequestParam(name = "phone", defaultValue = "") String phone,
                                                   @RequestParam(name = "orderid", defaultValue = "") String orderid,
                                                   @RequestParam(name = "subscribeTime", defaultValue = "") String subscribeTime,
                                                   @RequestParam(name = "result", defaultValue = "") String result,
                                                   @RequestParam(name = "resultMsg", defaultValue = "") String resultMsg,
                                                   @RequestParam(name = "price", defaultValue = "") String price,
                                                   @RequestParam(name = "orderTime", defaultValue = "") String orderTime,
                                                   @RequestParam(name = "cancelTime", defaultValue = "") String cancelTime) {
        log.info("广州旭同移动业务通知=>手机号:{},订购状态:{},订单号:{},描述:{}", phone, result, sid, resultMsg);
        try {
            synchronized (interner.intern(sid)) {
                xuTongYiDongService.xuTongYiDongNotify(phone, result, sid, resultMsg, cancelTime);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new XuTongYiDongResponse(0, "操作成功");
    }


    /**
     * 软游通业务
     *
     * @param extdata
     * @param mobile
     * @param orderNo
     * @param status
     * @param amount
     * @return
     */
    @GetMapping("/ruanyoutong/notify")
    public String ruanyoutongNotify(@RequestParam("extdata") String extdata,
                                    @RequestParam("mobile") String mobile,
                                    @RequestParam("orderNo") String orderNo,
                                    @RequestParam("status") String status,
                                    @RequestParam("amount") String amount) {
        log.info("软游通业务通知=>手机号:{},订购状态:{},订单号:{}", mobile, status, extdata);
        try {
            synchronized (interner.intern(extdata)) {
                ruanYouTongService.ruanYouTongNotify(mobile, status, extdata);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "ok";
    }


    /**
     * 订阅包栏目分类列表查询
     *
     * @return
     */
    @PostMapping(value = "/columndyList")
    @ResponseBody
    public Result<?> queryColumnDyList(@RequestParam("columnClass") String columnClass, @RequestParam(name = "isCarousel", required = false, defaultValue = "0") String isCarousel) {
        List<ColumnDy> list = columnDyService.lambdaQuery().eq(ColumnDy::getColumnClassName, columnClass).orderByAsc(ColumnDy::getPriority).list();
        return Result.ok(list);
    }


    /**
     * 订阅包获取指定栏目id的歌曲列表
     *
     * @param columnId
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @RequestMapping(value = "/vrbtdyListPage")
    public Result<?> vrbtDyListPage(String columnId,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "6") Integer pageSize,
                                    HttpServletRequest req) {

        Page<MusicDyVo> page = new Page<MusicDyVo>(pageNo, pageSize);
        IPage<MusicDyVo> columnMusicVoIPage = columnMusicDyService.selectColumnMusicDyVoPage(page, columnId);
        return Result.ok(columnMusicVoIPage);
    }


    /**
     * 山东和校园获取验证码接口
     *
     * @param mobile
     * @param req
     * @return
     */
    @RequestMapping(value = "/sdhxy/getImgVerifyCode")
    public void sdhxyGetImgVerifyCode(String mobile, HttpServletRequest req, HttpServletResponse resp) {
        shandongHexiaoyuanService.getImgVerifyCodeForReporting(mobile, req, resp);
    }

    /**
     * 山东和校园回调接口
     *
     * @param action
     * @param time
     * @return
     */
    @RequestMapping(value = "/sdhxy/notify")
    public void sdhxyNotify(@RequestParam(value = "Action", required = false) String action,
                            @RequestParam(value = "time", required = false) String time,
                            @RequestParam(value = "ServiceId", required = false) String serviceId,
                            @RequestParam(value = "oid", required = false) String oid) {

        log.info("山东和校园业务通知=>手机号:{},订购状态:{},时间:{},业务编码:{}", oid, action, time, serviceId);
        if (StringUtils.isNotBlank(action) && StringUtils.isNotBlank(oid)) {
            Subscribe subscribe = subscribeService.lambdaQuery()
                    .eq(Subscribe::getMobile, oid)
                    .eq(Subscribe::getChannel, BizConstant.BIZ_TYPE_SD_HXY)
                    .orderByDesc(Subscribe::getCreateTime)
                    .last("limit 1")
                    .one();
            if (subscribe != null) {
                if ("1".equals(action)) {//开通
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult("订阅成功");
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    subscribeService.saveChannelLimit(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                if ("2".equals(action)) {//退订
                    subscribe.setResult("退订");
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                }
            }
        }
    }


    /**
     * 咪咕快游VR竞盟订购通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/vr/notify")
    public VrJingmengResponse vrJingmengNotify(@RequestBody String requestBody, HttpServletRequest request) {
        log.info("咪咕快游VR竞盟订购通知=>参数:{}", requestBody);
        try {
            final VrJingmengNotify vrJingmengNotify = new ObjectMapper().readValue(requestBody, VrJingmengNotify.class);
            synchronized (interner.intern(vrJingmengNotify.getTransactionId())) {
                return vrJingmengService.vrJingmengNotify(vrJingmengNotify);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 南山下发途牛权益通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/nanshan/open/equityNotice")
    public NanshanOpenResponse nanshanOpenNotify(@RequestBody String requestBody, HttpServletRequest request) {
        String sign = request.getHeader("sign");
        String appKey = request.getHeader("appKey");
        log.info("南山下发途牛权益通知=>参数:{}", requestBody);
        return tuniuCouponCodeChargeLogService.nanshanOpenNotify(requestBody, sign, appKey);
    }


    /**
     * 南山途牛激活码充值
     *
     * @param mobile
     * @param code
     * @return
     */
    @PostMapping(value = "/tuniu/recharge")
    @ResponseBody
    public FebsResponse tuniuRecharge(@RequestParam("mobile") String mobile, @RequestParam("code") String code) {
        log.info("南山途牛激活码充值=>mobile:{},code:{}", mobile, code);
        return tuniuCouponCodeChargeLogService.tuniuRecharge(mobile, code);
    }


    /**
     * 发财树通知回调
     *
     * @param jsonNode
     * @return
     */
    @RequestMapping(value = "/facaishu/notifyLog")
    public String faCaiShuNotify(@RequestBody(required = false) JsonNode jsonNode) {
        log.info("发财树通知回调,json数据:{}", jsonNode);
        facaishuNotifyLogService.faCaiShuNotify(jsonNode);
        return "OK";
    }

    /**
     * 贵州电信多彩惠开通数据同步
     *
     * @param subscribe
     * @return
     */
    @PostMapping("/gzdx/dch/openReceive")
    @ApiOperation("贵州电信多彩惠开通数据同步")
    public FebsResponse gzdxDchOpenReceive(@RequestBody Subscribe subscribe) {
        try {
            log.info("贵州电信多彩惠开通数据同步,参数:{}", mapper.writeValueAsString(subscribe));
            FebsResponse febsResponse = subscribeService.gzdxDchOpenReceive(subscribe);
            return febsResponse;
        } catch (Exception e) {
            log.error("贵州电信多彩惠开通数据同步,参数:{},异常:", subscribe.toString(), e);
            return new FebsResponse().fail().message("系统异常");
        }
    }

    /**
     * 电信云游戏通知回调
     *
     * @param requestBody
     * @return
     */
    @RequestMapping(value = "/dianxin/cloudgame/notify")
    public String dianxinCloudGameNotify(@RequestBody String requestBody) {
        log.info("电信云游戏通知回调,参数:{}", requestBody);
        try {
            final DianxinCloudGameNotify dianxinCloudGameNotify = new ObjectMapper().readValue(requestBody, DianxinCloudGameNotify.class);
            dianxinCloudGameService.dianxinCloudGameNotify(dianxinCloudGameNotify);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return "ok";
    }


    /**
     * 电信云游戏通知回调
     *
     * @param channelCode
     * @return
     */
    @RequestMapping(value = "/caixun/getToken")
    public Result caixunGetToken(@RequestParam String channelCode) {
        String token = caixunService.getToken(channelCode);
        if (StringUtils.isNotBlank(token)) {
            return Result.ok("获取token成功", token);
        }
        return Result.error("获取token失败");
    }

    /**
     * 彩讯获取验证码
     *
     * @return
     */
    @RequestMapping(value = "/caixun/getSms")
    public Result caixunGetSmsCode(@RequestParam(value = "mobile") String mobile,
                                   @RequestParam(value = "channel") String channel,
                                   @RequestParam(value = "reportTicket") String reportTicket, HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        CaixunResult caixunResult = caixunService.getSms(mobile, channel, userAgent, reportTicket);
        if (caixunResult.isOK()) {
            return Result.ok("获取验证码成功", caixunResult.getData());
        } else {
            return Result.error("获取验证码失败");
        }
    }

    /**
     * 彩讯提交验证码
     *
     * @param channelCode
     * @return
     */
    @RequestMapping(value = "/caixun/smsCode")
    public Result caixunSmsCode(@RequestParam(value = "mobile") String mobile,
                                @RequestParam(value = "channel") String channel,
                                @RequestParam(value = "guid") String guid,
                                @RequestParam(value = "orderId") String orderId,
                                @RequestParam(value = "token") String token,
                                @RequestParam(value = "code") String code,
                                @RequestParam(value = "reportTicket") String reportTicket, HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        CaixunResult caixunResult = caixunService.smsCode(mobile, channel, guid, orderId, code, userAgent, token, reportTicket);
        if (caixunResult.isOK()) {
            return Result.ok("订阅成功");
        } else {
            return Result.error("订阅失败");
        }
    }


    /**
     * 贵州移动省包在线支付
     *
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/jsapi/wechat/pay")
    @ResponseBody
    public FebsResponse jsapiWechatPay(HttpServletRequest request, HttpServletResponse response) {
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("贵州移动省包在线支付=>ip:{},{}", ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            String app = request.getHeader("x-requested-with");
            subscribe.setUserAgent(userAgent);
            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            String mobile = subscribe.getMobile();
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return new FebsResponse().fail().message("请输入正确格式手机号");
            }
            if (StringUtils.isBlank(subscribe.getTradeType())) {
                subscribe.setTradeType(BizConstant.TRADE_TYPE_WECHAT);
            }
            if (StringUtils.equalsAny(subscribe.getTradeType(), BizConstant.TRADE_TYPE_WECHAT) && StringUtils.isBlank(subscribe.getUserId())) {
                return new FebsResponse().fail().message("openId错误");
            }
            if (Strings.isNullOrEmpty(subscribe.getChannel())) {
                return new FebsResponse().fail().message("渠道号错误");
            }
            OrderPay orderPay = orderPayService.createPayOrder(subscribe);
            if (orderPay == null) {
                return new FebsResponse().fail().message("订单错误");
            }
            Map<String, String> resp = qyclWxpayService.pay(orderPay.getOrderId(), String.valueOf(orderPay.getTotalFee()), subscribe.getRemark(), null, subscribe.getTradeType(), subscribe.getUserId(), subscribe.getChannel());
            if (StringUtils.equalsAny(subscribe.getTradeType(), BizConstant.TRADE_TYPE_HTML)) {
                if (StringUtils.isBlank(subscribe.getReturnUrl())) {
                    return new FebsResponse().fail().message("重定向地址错误");
                }
                String mwebUrl = resp.get("mweb_url");
                String returnUrlEncode = UriUtils.encode(subscribe.getReturnUrl() + "?orderId=" + orderPay.getOrderId(), StandardCharsets.UTF_8);
                String mwebUrlWithRedirect = mwebUrl + "&redirect_url=" + returnUrlEncode;
                log.info("微信支付支付跳转地址->mwebUrlWithRedirect:{}", mwebUrlWithRedirect);
                return new FebsResponse().success().data(mwebUrlWithRedirect);
            }
            resp.put("orderId", orderPay.getOrderId());
            return new FebsResponse().success().data(resp);
        } catch (Exception e) {
            log.error("微信支付支付异常", e);
        }
        return new FebsResponse().fail();
    }


    //微信用户支付通知
    @RequestMapping(value = "/jsapi/wechat/pay/notify", produces = "text/plain")
    @ResponseBody
    public String jsapiWechatPayNotify(HttpServletRequest request) {
        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()) {
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            QyclWxpayNotifyParam notifyParam = qyclWxpayService.payResultNotify(notifyXml);
            log.info("微信支付通知notifyParam = " + notifyParam);
            final String resultCode = notifyParam.getResultCode();

            OrderPay orderPay = orderPayService.queryNotPayOrder(notifyParam.getOutTradeNo());
            if (orderPay == null) {
                log.error("微信回调通知重复通知-notifyParam:{}", notifyParam);
                return succRespXml;
            }
            orderPayService.modifyPayStatus(notifyParam.getOutTradeNo(), notifyParam.getTransactionId(), resultCode, orderPay.getMobile());
            return succRespXml;
        } catch (Exception e) {
            log.error("微信回调通知处理异常:", e);
            return failRespXml;
        }
    }


    /**
     * 查询订单是否支付
     *
     * @param mobile
     * @param orderId
     * @return
     */
    @PostMapping("/jsapi/queryOrder")
    @ResponseBody
    public FebsResponse queryOrder(@RequestParam("mobile") String mobile, @RequestParam("orderId") String orderId) {
        log.info("贵州移动省包（在线支付）=>手机号:{},订单号码:{}", mobile, orderId);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(orderId)) {
            return new FebsResponse().fail().message("订单号错误");
        }
        Boolean payStatus = orderPayService.queryOrder(mobile, orderId);
        if (!payStatus) {
            return new FebsResponse().fail().message("未支付");
        }
        return new FebsResponse().success();
    }

    /**
     * 联联分销充值回调
     *
     * @param lianlianNotifyLog
     * @return
     */
    @PostMapping("/lianlian/charge/notifyLog")
    public LianLianResponse lianlianChargeNotifyLog(@RequestBody LianlianNotifyLog lianlianNotifyLog) {
        log.info("联联分销充值回调-json数据:{}", lianlianNotifyLog);
        return lianlianChargeLogService.lianlianChargeNotifyLog(lianlianNotifyLog);
    }

    /**
     * 联联分销核销回调
     *
     * @param lianlianNotifyLog
     * @return
     */
    @PostMapping("/lianlian/use/notifyLog")
    public LianLianResponse lianlianUseNotifyLog(@RequestBody LianlianNotifyLog lianlianNotifyLog) {
        log.info("联联分销核销回调-json数据:{}", lianlianNotifyLog);
        return lianlianChargeLogService.lianlianUseNotifyLog(lianlianNotifyLog);
    }

    /**
     * 联联分销退款回调
     *
     * @param lianlianNotifyLog
     * @return
     */
    @PostMapping("/lianlian/refund/notifyLog")
    public LianLianResponse lianlianRefundNotifyLog(@RequestBody LianlianNotifyLog lianlianNotifyLog) {
        log.info("联联分销退款回调-json数据:{}", lianlianNotifyLog);
        return lianlianChargeLogService.lianlianRefundNotifyLog(lianlianNotifyLog);
    }

    /**
     * 联联分销预约回调
     *
     * @param lianlianNotifyLog
     * @return
     */
    @PostMapping("/lianlian/booking/notifyLog")
    public LianLianResponse lianlianBookingNotifyLog(@RequestBody LianlianNotifyLog lianlianNotifyLog) {
        log.info("联联分销预约回调-json数据:{}", lianlianNotifyLog);
        return lianlianChargeLogService.lianlianBookingNotifyLog(lianlianNotifyLog);
    }

    /**
     * 联联分销产品上下架回调
     *
     * @param lianlianNotifyLog
     * @return
     */
    @PostMapping("/lianlian/productStatusUpdate/notifyLog")
    public LianLianResponse lianlianProductStatusUpdateNotifyLog(@RequestBody LianlianNotifyLog lianlianNotifyLog) {
        log.info("联联分销产品上下架回调-json数据:{}", lianlianNotifyLog);
        return lianlianChargeLogService.lianlianProductStatusUpdateNotifyLog(lianlianNotifyLog);
    }

    /**
     * 联联分销产品更新回调
     *
     * @param lianlianNotifyLog
     * @return
     */
    @PostMapping("/lianlian/productUpdate/notifyLog")
    public LianLianResponse lianlianProductUpdateNotifyLog(@RequestBody LianlianNotifyLog lianlianNotifyLog) {
        log.info("联联分销产品更新回调-json数据:{}", lianlianNotifyLog);
        return lianlianChargeLogService.lianlianProductUpdateNotifyLog(lianlianNotifyLog);
    }

    /**
     * 联联分销店铺信息变更回调
     *
     * @param lianlianNotifyLog
     * @return
     */
    @PostMapping("/lianlian/shopUpdate/notifyLog")
    public LianLianResponse lianlianShopUpdateNotifyLog(@RequestBody LianlianNotifyLog lianlianNotifyLog) {
        log.info("联联分销店铺信息变更回调-json数据:{}", lianlianNotifyLog);
        return lianlianChargeLogService.lianlianShopUpdateNotifyLog(lianlianNotifyLog);
    }


    //微信用户退款通知
    @RequestMapping(value = "/jsapi/wechat/refund/notify", produces = "text/plain")
    @ResponseBody
    public String wxrefundResultNotify(HttpServletRequest request) {
        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()) {
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            WxInfoNotifyParam infoParam = qyclWxpayService.refundResultNotify(notifyXml);
            log.info("微信用户退款通知infoParam= " + infoParam);
            if (infoParam != null) {
                final String refundStatus = infoParam.getRefundStatus();
                OrderPay orderPay = orderPayService.queryNotRefundOrder(infoParam.getOutRefundNo());
                if (orderPay == null) {
                    log.error("微信用户退款通知重复通知-notifyParam:{}", infoParam);
                    return succRespXml;
                }
                orderPayService.modifyRefundStatus(orderPay.getOrderId(), infoParam.getOutRefundNo(), refundStatus);
            }
            return succRespXml;
        } catch (Exception e) {
            e.printStackTrace();
            return failRespXml;
        }
    }

    @ApiOperation(value = "联通沃悦读-话费支付", notes = "联通沃悦读-话费支付")
    @PostMapping(value = "/woReadOutSideMobilePay")
    public FebsResponse woReadOutSideMobilePay(HttpServletRequest request, HttpServletResponse response) {

        FebsResponse febsResponse = new FebsResponse();
        String contractCode = "yj" + IdWorker.getId();
        String app = request.getHeader("x-requested-with");
        String ipAddr = IPUtils.getIpAddr(request);

        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("渠道订阅请求=>ip:{},{}", ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            String businessType = subscribe.getBusinessType();
            String mobile = subscribe.getMobile();

            if (StringUtils.isBlank(businessType)) {
                return febsResponse.fail().message("支付参数错误");
            }

            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());

            if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
                return febsResponse.fail().message("请正确填写手机号");
            }
            //发起订购
            WoReadMobilePayResult woReadMobilePayResult = woReadOutSideApiService.mobilePay(mobile, ipAddr, subscribe.getSource());
            if (woReadMobilePayResult.isOK()) {
                febsResponse.success().data(woReadMobilePayResult.getData().getReqUrl());
                subscribe.setExtra(woReadMobilePayResult.getData().getSign());
            } else {
                return febsResponse.fail().message(woReadMobilePayResult.getResMessage());
            }
            //保存签约信息
            woReadOrderService.saveOutSideOrder(subscribe, contractCode, 3);
        } catch (Exception e) {
            log.info("联通沃悦读-话费扣款出错：", e);
        }
        return febsResponse;
    }

    @RequestMapping(value = "/gzyd/syncUrl")
    public Result<?> gzydSyncUrl(@RequestParam("mobile") String mobile,
                                 @RequestParam("channel") String channel, HttpServletRequest request) {
        try {
            final String bizType = getBizTypeByMiguChannel(channel);
            if (BizConstant.BIZ_TYPE_JUNBOLLB.equals(bizType)) {
                Subscribe sub = new Subscribe();
                sub.setMobile(mobile);
                sub.setChannel(channel);
                String userAgent = request.getHeader("User-Agent");
                if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
                    userAgent = userAgent.substring(0, 512);
                }
                String ipAddr = IPUtils.getIpAddr(request);
                sub.setUserAgent(userAgent);
                //referer字段存储指纹
                final String finger = Strings.nullToEmpty(request.getHeader("referer")) + ipAddr;
                String app = request.getHeader("x-requested-with");
                sub.setReferer(app);
                sub.setSource(finger);
                IBusinessCommonService businessCommonService = SpringContextUtils.getBean(JunboGuiZhouBusinessServiceImpl.class);
                Result result = businessCommonService.receiveOrderWithCache(sub);
                if (result.isOK()) {
                    return Result.ok("获取syncUrl成功", result.getResult());
                }
                return Result.error(result.getMessage());
            }
            if (BizConstant.BIZ_TYPE_HETU.equals(bizType)) {
                Subscribe sub = new Subscribe();
                sub.setMobile(mobile);
                sub.setChannel(channel);
                String userAgent = request.getHeader("User-Agent");
                if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
                    userAgent = userAgent.substring(0, 512);
                }
                String ipAddr = IPUtils.getIpAddr(request);
                sub.setUserAgent(userAgent);
                //referer字段存储指纹
                final String finger = Strings.nullToEmpty(request.getHeader("referer")) + ipAddr;
                String app = request.getHeader("x-requested-with");
                sub.setReferer(app);
                sub.setSource(finger);
                IBusinessCommonService businessCommonService = SpringContextUtils.getBean(JunboSiChuanBusinessServiceImpl.class);
                Result result = businessCommonService.receiveOrderWithCache(sub);
                if (result.isOK()) {
                    return Result.ok("获取syncUrl成功", result.getResult());
                }
                return Result.error(result.getMessage());
            }
            GuizhouMobileCodeResult guizhouMobileCodeResult = guizhouYidongService.getSms(mobile, UUID.randomUUID().toString().replace("-", ""), channel, "", "");
            if (guizhouMobileCodeResult != null && StringUtils.isNotEmpty(guizhouMobileCodeResult.getSyncUrl())) {
                return Result.ok("获取syncUrl成功", guizhouMobileCodeResult.getSyncUrl());
            } else {
                return Result.error("获取syncUrl失败");
            }
        } catch (Exception e) {
            return Result.error("获取syncUrl失败");
        }
    }

    @RequestMapping(value = "/gzyd/submit")
    public Result<?> gzydSubmit(@RequestParam("mobile") String mobile,
                                @RequestParam("channel") String channel,
                                @RequestParam("serialNumber") String serialNumber, HttpServletRequest request) {
        try {
            final String bizType = getBizTypeByMiguChannel(channel);
            if (BizConstant.BIZ_TYPE_JUNBOLLB.equals(bizType)) {
                Subscribe sub = new Subscribe();
                sub.setMobile(mobile);
                sub.setChannel(channel);
                sub.setIspOrderNo(serialNumber);
                String userAgent = request.getHeader("User-Agent");
                if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
                    userAgent = userAgent.substring(0, 512);
                }
                String ipAddr = IPUtils.getIpAddr(request);
                sub.setUserAgent(userAgent);
                //referer字段存储指纹
                final String finger = Strings.nullToEmpty(request.getHeader("referer")) + ipAddr;
                String app = request.getHeader("x-requested-with");
                sub.setReferer(app);
                sub.setSource(finger);
                JunboGuiZhouBusinessServiceImpl businessCommonService = SpringContextUtils.getBean(JunboGuiZhouBusinessServiceImpl.class);
                Result result = businessCommonService.verifyReceiveOrder(sub);
                return result;
            }
            String callback = "";
            Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, serialNumber).last("limit 1").one();
            if (subscribe != null) {
                callback = subscribe.getDeviceInfo();
            }
            GuizhouMobileResult.Result result = guizhouYidongService.smsCode(mobile,
                    "123456",
                    UUID.randomUUID().toString().replace("-", ""),
                    serialNumber,
                    channel,
                    "",
                    callback);
            if (result != null && result.isOK()) {
                return Result.ok("订阅成功");
            } else {
                return Result.error("订阅失败!");
            }
        } catch (Exception e) {
            return Result.error("订阅失败!");
        }
    }

    @RequestMapping("/pay/wyd/mobilePay/notify")
    @ApiOperation("联通沃悦读话费支付异步通知")
    public String chudianMobilePayNotify(String instatus, String sign) {
        String succRespMsg = "0";
        String failRespMsg = "1";
        try {
            log.info("联通沃悦读话费支付异步通知,instatus:{},sign:{}", instatus, sign);
            if (StringUtils.isBlank(sign) || StringUtils.isBlank(instatus)) {
                return failRespMsg;
            }
            WoReadOrder woReadOrder = woReadOrderService.lambdaQuery()
                    .eq(WoReadOrder::getOutTradeNo, sign)
                    .one();
            if (woReadOrder == null) {
                log.info("联通沃悦读话费支付异步通知,找不到订单,sign:{}", sign);
                return failRespMsg;
            }
            if ("3".equals(instatus)) {
                woReadOrder.setOrderStatus(1);
                woReadOrder.setUserId(woReadOrder.getMobile());
                woReadOrder.setSubStatus(2);
                woReadOrder.setPayTime(new Date());
                woReadOrder.setPayMonth(LocalDate.now().getYear() + "-" + LocalDate.now().getMonthValue());
                woReadOrder.setPayNo(1);
                woReadOrderService.updateById(woReadOrder);
                Subscribe subscribe = subscribeService.lambdaQuery()
                        .eq(Subscribe::getMobile, woReadOrder.getMobile())
                        .eq(Subscribe::getExtra, sign)
                        .last("limit 1")
                        .one();
                if (subscribe != null) {
                    subscribe.setStatus(1);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult("付费成功");
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    BizLogUtils.logSubscribe(subscribe);
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
            }

        } catch (Exception e) {
            log.info("联通沃悦读话费支付异步通知,instatus:{},sign:{},异常:", instatus, sign, e);
            return failRespMsg;
        }
        return succRespMsg;
    }

    /**
     * 广东插码数据记录
     *
     * @param jsonNode
     * @return
     */
    @PostMapping("/guangdong/insertCode")
    public Result insertCode(@RequestBody JsonNode jsonNode) {
        log.info("广东插码数据记录-json:{}", jsonNode);
        String mobile = jsonNode.at("/mobile").asText();
        String source = jsonNode.at("/source").asText();
        String company = jsonNode.at("/company").asText();
        EsBusinessLog esBusinessLog = new EsBusinessLog();
        esBusinessLog.setMobile(mobile);
        esBusinessLog.setUrl(source);
        esBusinessLog.setFinger(company);
        esBusinessLog.setCreateTime(new Date());
        esDataService.saveEsBusinessLog(esBusinessLog);
        return Result.ok("成功");
    }


    /**
     * 骏伯上海移动存量业务通知
     *
     * @param requestBody
     * @return
     */
    @PostMapping(value = "/junbo/cunliang/notify")
    public String junboCunLiangNotify(@RequestBody String requestBody) {
        log.info("骏伯上海移动存量业务通知=>通知:{}", requestBody);
        try {
            final JunboCunLiangNotify junboCunLiangNotify = new ObjectMapper().readValue(requestBody, JunboCunLiangNotify.class);
            synchronized (interner.intern(junboCunLiangNotify.getSysOrderId())) {
                return junboCunLiangService.junboCunLiangNotify(junboCunLiangNotify);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("骏伯上海移动存量业务通知异常=>订单信息:{}", requestBody, e);
        }
        return "ok";
    }

    @RequestMapping("/pay/wyd/mobilePay/orderStatus")
    @ApiOperation("联通沃悦读话费查询订单状态")
    public String chudianMobilePayOrderStatus(String sign) {
        String succRespMsg = "0";
        String failRespMsg = "1";
        try {
            log.info("联通沃悦读话费查询订单状态,sign:{}", sign);
            if (StringUtils.isBlank(sign)) {
                return failRespMsg;
            }
            WoReadOrder woReadOrder = woReadOrderService.lambdaQuery()
                    .eq(WoReadOrder::getOutTradeNo, sign)
                    .one();
            if (woReadOrder == null) {
                log.info("联通沃悦读话费查询订单状态,找不到订单,sign:{}", sign);
                return failRespMsg;
            }
            WoReadPkgOrderedStatusResult pkgOrderedStatus = woReadOutSideApiService.getPkgOrderedStatus(woReadOrder.getMobile(), woReadOrder.getChannelId());
            if (pkgOrderedStatus == null || !pkgOrderedStatus.isOK() || pkgOrderedStatus.getData() == null) {
                return failRespMsg;
            }
            if (StringUtils.isEmpty(pkgOrderedStatus.getData().getStatus())) {
                return failRespMsg;
            }
            //退订
            if (pkgOrderedStatus.getData().getStatus().equals("3")) {
                woReadOrder.setUserId(woReadOrder.getMobile());
                woReadOrder.setSubStatus(3);
                woReadOrderService.updateById(woReadOrder);
                Subscribe subscribe = subscribeService.lambdaQuery()
                        .eq(Subscribe::getMobile, woReadOrder.getMobile())
                        .eq(Subscribe::getExtra, sign)
                        .last("limit 1")
                        .one();
                if (subscribe != null) {
                    subscribe.setResult("退订");
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    BizLogUtils.logSubscribe(subscribe);
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                return succRespMsg;
            }
            //失效时间不为空
            if (StringUtils.isNotBlank(pkgOrderedStatus.getData().getExpiretime()) && LocalDateTime.now().isAfter(DateUtil.parseString(pkgOrderedStatus.getData().getExpiretime(), DateUtil.FULL_TIME_PATTERN))) {
                return failRespMsg;
            }
            //生效时间不能为空
            if (StringUtils.isBlank(pkgOrderedStatus.getData().getEffectivetime())) {
                return failRespMsg;
            }
            //当前时间是否大于生效时间
            LocalDateTime effectivetime = DateUtil.parseString(pkgOrderedStatus.getData().getEffectivetime(), DateUtil.FULL_TIME_PATTERN);
            if (pkgOrderedStatus.getData().getStatus().equals("2") && LocalDateTime.now().isAfter(effectivetime)) {
                woReadOrder.setOrderStatus(1);
                woReadOrder.setUserId(woReadOrder.getMobile());
                woReadOrder.setSubStatus(2);
                woReadOrder.setPayTime(new Date());
                woReadOrder.setPayMonth(LocalDate.now().getYear() + "-" + LocalDate.now().getMonthValue());
                woReadOrder.setPayNo(1);
                woReadOrderService.updateById(woReadOrder);
//                Subscribe subscribe = subscribeService.lambdaQuery()
//                    .eq(Subscribe::getMobile, woReadOrder.getMobile())
//                    .eq(Subscribe::getExtra, sign)
//                    .last("limit 1")
//                    .one();
//                if (subscribe != null) {
//                    subscribe.setStatus(1);
//                    subscribe.setOpenTime(new Date());
//                    subscribe.setResult("查询付费成功");
//                    subscribeService.updateSubscribeDbAndEs(subscribe);
//                    BizLogUtils.logSubscribe(subscribe);
////                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//                }
            }

        } catch (Exception e) {
            log.info("联通沃悦读话费查询订单状态,sign:{},异常:", sign, e);
            return failRespMsg;
        }
        return succRespMsg;
    }

    ////@AutoLog(value = "咪咕三方支付通知")
    @ApiOperation(value = "咪咕三方支付通知", notes = "咪咕三方支付通知")
    @RequestMapping(value = "/notify3rd")
    public String notify3rd(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("咪咕三方支付通知,参数map:{},json数据:{}", requestMap, jsonNode);
        return "OK";
    }

    /**
     * 西米乐权益充值通知
     *
     * @param requestBody
     * @return
     */
    @PostMapping(value = "/ximile/recharge/notify")
    public XimileRechargeNotifyResp ximileRechargeNotify(@RequestBody String requestBody) {
        log.info("西米乐权益充值通知=>通知:{}", requestBody);
        try {
            final XimileRechargeNotify ximileRechargeNotify = new ObjectMapper().readValue(requestBody, XimileRechargeNotify.class);
            synchronized (interner.intern(ximileRechargeNotify.getSysOrderId())) {
                return xiMiLeApiService.ximileRechargeNotify(ximileRechargeNotify);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("西米乐权益充值通知异常=>订单信息:{}", requestBody, e);
        }
        return XimileRechargeNotifyResp.error("系统异常");
    }

    /**
     * 查询订单状态
     *
     * @param orderNo
     * @return
     */
    @RequestMapping("/outside/queryOrder")
    public Result getSmsCode(@RequestParam String orderNo) {
        log.info("外部渠道订单状态查询接口-订单号:{}", orderNo);
        try {
            EsSubscribe esSubscribe = esDataService.searchById(orderNo);
            if (esSubscribe != null && StringUtils.startsWith(esSubscribe.getSubChannel(), "OSVRBT")) {
                return Result.ok(esSubscribe.getStatus());
            } else {
                return Result.error("无效的订单号");
            }
        } catch (Exception e) {
            log.error("外部渠道订单状态查询接口-订单号:{},错误:", orderNo, e);
            return Result.error("订单状态查询错误");
        }
    }


    /**
     * 南山下发联联权益通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/lianlian/nanshan/open/equityNotice")
    public LianlianNanshanOpenResponse lianlianNanshanOpenNotify(@RequestBody String requestBody, HttpServletRequest request) {
        String sign = request.getHeader("sign");
        String appKey = request.getHeader("appKey");
        log.info("南山下发联联权益通知=>参数:{}", requestBody);
        return commonCouponService.lianlianNanshanOpenNotify(requestBody, sign, appKey);
    }


    //核销事件回调通知通知
    @RequestMapping(value = "/wechat/cancel/code/notify")
    @ResponseBody
    public String wechatCancelCodeNotify(@RequestParam(value = "mchId", required = false, defaultValue = "") String mchId, @RequestBody String requestBody) {
        String succRespXml = "{  \n" +
                "    \"code\": \"SUCCESS\",\n" +
                "    \"message\": \"成功\"\n" +
                "}";
        String failRespXml = "{  \n" +
                "    \"code\": \"FAIL\",\n" +
                "    \"message\": \"失败\"\n" +
                "}";
        try {
            wxpayService.wechatCancelCodeNotify(mchId, requestBody);
            return succRespXml;
        } catch (Exception e) {
            log.error("微信代金券核销事件回调通知处理异常:", e);
            return failRespXml;
        }
    }

    /**
     * 骏伯流量包通知接口
     *
     * @param jsonNode
     */
    @RequestMapping(value = "/junboLlb/notify")
    public String junboLlbNotify(@RequestBody JsonNode jsonNode) {
        log.info("收到骏伯订购结果回调通知,json数据{}", jsonNode);
        String sysOrderId = jsonNode.at("/sysOrderId").asText();
        String orderStatus = jsonNode.at("/orderStatus").asText();
        String orderStatusMsg = jsonNode.at("/orderStatusMsg").asText("")+":"+jsonNode.at("/remark").asText("");
        subscribeResultNotifyService.junboLlbNotify(sysOrderId, orderStatus, orderStatusMsg);
        return "ok";
    }

    /**
     * 广东聚杰通知接口
     *
     * @param jsonNode
     */
    @RequestMapping(value = "/jujie/notify")
    @ResponseBody
    public JsonNode jujieNotify(@RequestBody JsonNode jsonNode) {
        log.info("聚杰回调通知,json数据:{}", jsonNode);
        String ispOrderNo = jsonNode.at("/outOrderId").asText();
        String orderId = jsonNode.at("/orderId").asText();
        Integer orderStatus = jsonNode.at("/status").asInt();
        String msg = jsonNode.at("/msg").asText();
        subscribeResultNotifyService.jujieNotify(ispOrderNo, orderStatus, msg);
        ObjectNode data = mapper.createObjectNode();
        data.put("status", GuangdongJujieResult.CODE_SMS_SUCCESS);
        data.put("msg", "success");
        data.put("orderId", orderId);
        data.put("outOrderId", ispOrderNo);
        return data;
    }

    /**
     * 文生视频-生成视频
     *
     * @param prompt 文字信息
     * @return
     */
    @RequestMapping("/wensheng/generateVideo")
    public Result<?> wenshengGenerateVideo(@RequestParam String mobile, @RequestParam String prompt, @RequestParam String duration) {

        int incr = redisUtil.get(WENSHENG_VIDEO_KEY_PREFIX + mobile) == null ? 0 : (int) redisUtil.get(WENSHENG_VIDEO_KEY_PREFIX + mobile);
        if (incr > 9) {
            return Result.error("次数已达限制");
        }

        WenshengVideoResult wenshengVideoResult = wenshengVideoService.commitTask(prompt, duration);
        if (wenshengVideoResult.isOK()) {
            //保存视频信息
            WenshengRecord record = new WenshengRecord();
            record.setMobile(mobile);
            record.setPrompt(prompt);
            record.setDuration(duration);
            record.setTaskId(wenshengVideoResult.getResult().getTaskId());
            wenshengRecordService.save(record);

            redisUtil.incr(WENSHENG_VIDEO_KEY_PREFIX + mobile, 1);
            final long expire = ChronoUnit.SECONDS.between(LocalDateTime.now(), DateUtil.getLastDayOfMonthWithMaxTime());
            redisUtil.expire(WENSHENG_VIDEO_KEY_PREFIX + mobile, expire);

            return Result.ok("成功", wenshengVideoResult.getResult().getTaskId());
        }
        return Result.error(wenshengVideoResult.getMessage());
    }

    /**
     * 文生视频-获取视频
     *
     * @param taskId 任务id
     * @return
     */
    @RequestMapping("/wensheng/getResult")
    public Result<?> wenshengResult(@RequestParam String taskId) {
        WenshengVideoResult wenshengVideoResult = wenshengVideoService.pullResult(taskId);
        if (wenshengVideoResult.isOK()) {
            //修改记录信息
            try {
                WenshengRecord record = wenshengRecordService.lambdaQuery().eq(WenshengRecord::getTaskId, taskId).one();
                record.setVideoStatus(Integer.parseInt(wenshengVideoResult.getResult().getSubTaskResults().get(0).getTaskStatus()));
                record.setVideoUrl(wenshengVideoResult.getResult().getSubTaskResults().get(0).getVideo());
                record.setCover(wenshengVideoResult.getResult().getSubTaskResults().get(0).getCover());
                wenshengRecordService.updateById(record);
                if (wenshengVideoResult.getResult().getSubTaskResults().get(0).getTaskStatus().equals("1")) {
                    return Result.ok("视频生成成功", wenshengVideoResult.getResult().getSubTaskResults().get(0));
                }
                if (wenshengVideoResult.getResult().getSubTaskResults().get(0).getTaskStatus().equals("0")) {
                    return Result.createOrder("视频生成等待中", wenshengVideoResult.getResult().getSubTaskResults().get(0));
                }
                if (wenshengVideoResult.getResult().getSubTaskResults().get(0).getTaskStatus().equals("2")) {
                    return Result.createOrder("视频生成处理中", wenshengVideoResult.getResult().getSubTaskResults().get(0));
                }
                if (wenshengVideoResult.getResult().getSubTaskResults().get(0).getTaskStatus().equals("3")) {
                    return Result.createOrder("视频生成失败", wenshengVideoResult.getResult().getSubTaskResults().get(0));
                }
                if (wenshengVideoResult.getResult().getSubTaskResults().get(0).getTaskStatus().equals("4")) {
                    return Result.createOrder("视频生成未通过审核", wenshengVideoResult.getResult().getSubTaskResults().get(0));
                }
            } catch (Exception e) {
                log.error("文生视频-获取视频出错：", e);
            }
        }
        return Result.error("系统异常");
    }

    //@AutoLog(value = "文生视频回调")
    @ApiOperation(value = "文生视频回调", notes = "文生视频回调")
    @RequestMapping(value = "/wensheng/notify")
    public String notifyLog(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("文生视频回调,参数map:{},json数据:{}", requestMap, jsonNode);
        try {
            WenshengVideoResult.Result result = mapper.treeToValue(jsonNode, WenshengVideoResult.Result.class);
            if (result != null) {
                WenshengRecord record = wenshengRecordService.lambdaQuery().eq(WenshengRecord::getTaskId, result.getTaskId()).one();
                record.setVideoStatus(Integer.parseInt(result.getSubTaskResults().get(0).getTaskStatus()));
                record.setVideoUrl(result.getSubTaskResults().get(0).getVideo());
                record.setCover(result.getSubTaskResults().get(0).getCover());
                record.setVideoStatus(Integer.parseInt(result.getSubTaskResults().get(0).getTaskStatus()));
                wenshengRecordService.updateById(record);
            }
        } catch (Exception e) {
            log.error("文生视频回调出错：", e);
        }
        return "OK";
    }

    @ApiOperation(value = "查询设置彩铃审核结果", notes = "查询设置彩铃审核结果")
    @GetMapping(value = "/wensheng/settingResult")
    public Result<?> settingRingResult(@RequestParam String vrbtDiyVideoId) {
        log.info("查询设置彩铃审核结果,参数:{}", vrbtDiyVideoId);
        return Result.ok(vrbtDiyVideoService.querySettingRingStatus(vrbtDiyVideoId));
    }


    /**
     * 河图分省订单通知
     *
     * @param jsonNode
     * @return
     */
    @RequestMapping("/heTuOrderNotify")
    public HeTuFenShengNotify heTuOrderNotify(@RequestBody JsonNode jsonNode, HttpServletRequest request) {
        String signature = request.getHeader("signature");
        String timestamp = request.getHeader("timestamp");
        String transactionId = request.getHeader("transactionId");
        String tel = request.getHeader("tel");
        String busiSerial = jsonNode.at("/busiSerial").asText("");
        String respCode = jsonNode.at("/respCode").asText("");
        String respMsg = jsonNode.at("/respMsg").asText("");
        String orderStatus = jsonNode.at("/orderStatus").asText("");
        log.info("河图分省订单通知=>手机号:{},jsonNode:{},timestamp:{},transactionId:{}", tel, jsonNode, timestamp, transactionId);
        return heTuFenShengService.heTuFenShengNotify(signature, timestamp, jsonNode.toString(), transactionId, tel, busiSerial, respCode, respMsg, orderStatus);
    }

    //@AutoLog(value = "cms_wensheng_record-分页列表查询")
    @ApiOperation(value = "cms_wensheng_record-分页列表查询", notes = "cms_wensheng_record-分页列表查询")
    @GetMapping(value = "/wensheng/record/list")
    public Result<?> queryPageList(WenshengRecord wenshengRecord,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WenshengRecord> queryWrapper = QueryGenerator.initQueryWrapper(wenshengRecord, req.getParameterMap());
        Page<WenshengRecord> page = new Page<WenshengRecord>(pageNo, pageSize);
        queryWrapper.setEntity(wenshengRecord);
        IPage<WenshengRecord> pageList = wenshengRecordService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 咪咕动漫短剧登录-发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/comicLogin/getCaptcha")
    @ResponseBody
    public Result<?> comicLoginGetCaptcha(@RequestParam("mobile") String mobile,
                                          @RequestParam(name = "channelCode", required = false, defaultValue = BizConstant.BIZ_QYLI_COMIC_CHANNEL_CODE) String channelCode,
                                          @RequestParam(name = "serviceId", required = false, defaultValue = BizConstant.SMS_MODEL_COMMON_SERVICE_ID) String serviceId) {
        log.info("短剧登录-发送短信验证码=>手机号:{},渠道号:{},业务ID:{}", mobile, channelCode, serviceId);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.rightsCreate(mobile, channelCode, serviceId);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }
        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }

    /**
     * 咪咕动漫短剧登录
     *
     * @param mobile
     * @return
     */
    @PostMapping(value = "/comicLogin")
    @ResponseBody
    public Result<?> comicLogin(@RequestParam("mobile") String mobile, @RequestParam("captcha") String captcha) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return Result.error("验证码错误");
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
        final List<String> channelList = Lists.newArrayList("COMIC", "COMIC_JUNBO");
        final List<BusinessChannelRights> businessList = businessChannelRightsService.lambdaQuery().select(BusinessChannelRights::getId).in(BusinessChannelRights::getBusinessChannel, channelList).eq(BusinessChannelRights::getIsEffect, 1).list();

        final List<String> businessId = businessList.stream().map(BusinessChannelRights::getId).collect(Collectors.toList());

        final List<BusinessPack> businessPackList = businessPackService.lambdaQuery().select(BusinessPack::getServiceId).in(BusinessPack::getBusinessId, businessId).list();
        for (BusinessPack businessPack : businessPackList) {
            FebsResponse comicIsSubFebs = unifyRightsFeignClient.miGuComicCheckMember(mobile, businessPack.getServiceId());
            if (comicIsSubFebs.isOK()) {
                String token = TokenUtil.setLoginTime(mobile);
                Result result = new Result();
                result.setCode(CommonConstant.SC_OK_200);
                result.setToken(token);
                result.setMessage("已包月");
                return result;
            }
        }


        return Result.error("未包月");
    }

    /**
     * 咪咕动漫栏目
     *
     * @return
     */
//    @Login
    @RequestMapping(value = "/comic/column")
    @ResponseBody
    public Result<?> comicColumn() {
        QueryWrapper<CmsColumnComic> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("priority");
        List<CmsColumnComic> list = cmsColumnComicService.list(queryWrapper);
        return Result.ok(list);
    }

    /**
     * 咪咕动漫栏目
     *
     * @return
     */
//    @Login
    @RequestMapping(value = "/comic/column/video/{columnId}")
    @ResponseBody
    public Result<?> comicColumn(@PathVariable String columnId,
                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        CmsComicVideo cmsComicVideo = new CmsComicVideo();
        cmsComicVideo.setColumnId(columnId);
        QueryWrapper<CmsComicVideo> queryWrapper = new QueryWrapper<>(cmsComicVideo);
        queryWrapper.orderByAsc("priority");
        Page<CmsComicVideo> page = new Page<>(pageNo, pageSize);
        IPage<CmsComicVideo> pageList = cmsComicVideoService.page(page, queryWrapper);
        return Result.ok(pageList);
    }


    /**
     * 咪咕互娱报备订购
     *
     * @param mobile
     * @return
     */
    @PostMapping(value = "/huyuBaoBeiSub")
    @ResponseBody
    public Result<?> huyuBaoBeiSub(@RequestParam("mobile") String mobile, @RequestParam("channel") String channel) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        return huYuQuanWangService.mobileSm2(mobile, channel);
    }

    /**
     * 咪咕互娱报备订购
     *
     * @param mobile
     * @return
     */
    @PostMapping(value = "/huyuBaoBeiUnSub")
    @ResponseBody
    public Result<?> huyuBaoBeiUnSub(@RequestParam("mobile") String mobile) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        return huYuQuanWangService.unSub(mobile);
    }

    /**
     * 咪咕互娱全网订购页面
     *
     * @return
     */
    @RequestMapping("/huyuSub")
    public ModelAndView huyuSub() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/huyu/huyuSub");
        return modelAndView;
    }

    /**
     * 互娱全网订单通知
     *
     * @param requestBody
     * @return
     */
    @RequestMapping("/huyuOrderNotify")
    public HeTuFenShengNotify huyuOrderNotify(@RequestBody String requestBody) {
        return huYuQuanWangService.huyuOrderNotify(requestBody);
    }

    /**
     * 火山一键登录
     *
     * @param token
     * @param traceId
     * @param userInformation
     * @return
     */
    @RequestMapping("/huoshan/tokenValidate")
    public Result<?> tokenValidate(String token, String traceId, String userInformation) {
        HuoshanResult huoshanResult = huoshanApiService.tokenValidate(token, traceId, userInformation);
        if (huoshanResult.isOK()) {
            return Result.ok("成功", huoshanResult.getData().getPhone());
        }
        return Result.error(huoshanResult.getDesc());
    }

    /**
     * 枣米糖话费充值通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/feeRecharge/notify")
    public String feeRechargeNotify(@RequestBody String requestBody, HttpServletRequest request) {
        log.info("枣米糖话费充值通知=>参数:{}", requestBody);
        return mobileFeeChargeLogService.feeChargeNotifyPlus(requestBody);
    }

    /**
     * 云南移动插码手机号加密
     *
     * @param mobile
     * @return
     */
    @PostMapping(value = "/yn/encMobi")
    @ResponseBody
    public Result<?> yunnanEncodeMobile(@RequestParam("mobile") String mobile) {
        return Result.ok("success", PhoneCodeUtil.encodeMobile(mobile));
    }

    /**
     * 和包话费充值通知
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/hebaoFeeCharge/notify")
    public String hebaoFeeCharge(HttpServletRequest request) {
        String szAgentId = request.getParameter("szAgentId");
        String szOrderId = request.getParameter("szOrderId");
        String szPhoneNum = request.getParameter("szPhoneNum");
        String nDemo = request.getParameter("nDemo");
        String fSalePrice = request.getParameter("fSalePrice");
        String nFlag = request.getParameter("nFlag");
        String szRtnMsg = request.getParameter("szRtnMsg");
        String szVerifyString = request.getParameter("szVerifyString");
        log.info("和包话费充值通知=>订单号:{},手机号:{},充值状态:{},充值描述:{}", szOrderId, szPhoneNum, nFlag, szRtnMsg);
        return mobileFeeChargeLogService.hebaoFeeChargeNotifyPlus(szAgentId, szOrderId, szPhoneNum, nDemo, fSalePrice, nFlag, szRtnMsg, szVerifyString);
    }


    /**
     * 中核话费充值通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/zhongheFeeCharge/notify")
    public String zhongheFeeChargeNotify(@RequestBody String requestBody, HttpServletRequest request) {
        log.info("中核话费充值通知=>参数:{}", requestBody);
        return mobileFeeChargeLogService.zhongheFeeChargeNotify(requestBody);
    }

    /**
     * 一语成片视频文件上传ftp
     *
     * @return
     */
    @RequestMapping(value = "/yycp/ftp/upload")
    public FebsResponse yycpFtpUpload(String mobile, String filePath, String mvName) {
        FebsResponse febsResponse = new FebsResponse();
        if (StringUtils.isBlank(mobile) || StringUtils.isBlank(filePath) || StringUtils.isBlank(mvName)) {
            return febsResponse.fail().message("参数错误");
        }
        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        String pathName = "/yycp" + df.format(new Date());
        String fileName = "video" + IdWorker.getId() + ".mp4";
        boolean b = vrbtDiyVideoService.uploadVideoToFtp(pathName, filePath, fileName);
        if (b) {
            ReportDTO reportDTO = new ReportDTO();
            reportDTO.setPhone(mobile);
            reportDTO.setMvUrl(pathName + "/" + fileName);
            reportDTO.setMvName(mvName);
            reportDTO.setTransactionId(ReportTypeEnum.ZX.getCode() + IdWorker.getId());
            reportDTO.setChannelCode(yycpProperties.getChannel());
            yycpReportService.report(reportDTO);
            febsResponse.success().message("上传成功");
        } else {
            febsResponse.fail().message("上传失败");
        }
        return febsResponse.success().data(b);
    }

    /**
     * 海艺视频文件上传ftp
     *
     * @param hyFtpUploadDTO hyFtpUploadDTO
     * @return FebsResponse
     */
    @PostMapping("/yycp/sync/upload")
    public Result<Object> yycpSyncUpload(@RequestBody @Validated HyFtpUploadDTO hyFtpUploadDTO) {
        return Result.ok(wenshengVideoService.setRing(hyFtpUploadDTO));
    }

    /**
     * 海艺视频文件上传ftp
     *
     * @param hyFtpUploadDTO hyFtpUploadDTO
     * @return FebsResponse
     */
    @PostMapping("/hy/ftp/upload")
    public Result<Object> hyFtpUpload(@RequestBody @Validated HyFtpUploadDTO hyFtpUploadDTO) {
        haiYiAiService.setRing(hyFtpUploadDTO);
        return Result.ok();
    }


    /**
     * 互娱重庆分省订单通知
     *
     * @param requestBody
     * @return
     */
    @RequestMapping(value = "/province/huyuOrderNotify")
    public HeTuFenShengNotify huyuOrderChongQingNotify(@RequestBody String requestBody) {
        return heTuFenShengChongQingService.huyuOrderChongQingNotify(requestBody);
    }

    /**
     * 青海文航话费充值通知
     *
     * @param userId
     * @param orderId
     * @param serialno
     * @param orderStatus
     * @param errDesc
     * @param sign
     * @return
     */
    @RequestMapping(value = "/QingHaiWenHangCharge/notify")
    public String qingHaiWenHangChargeNotify(@RequestParam("userId") String userId,
                                             @RequestParam("orderId") String orderId,
                                             @RequestParam("serialno") String serialno,
                                             @RequestParam("orderStatus") String orderStatus,
                                             @RequestParam("errDesc") String errDesc,
                                             @RequestParam("sign") String sign) {
        log.info("青海文航话费充值通知=>渠道方用户编号:{},充值平台方订单号:{},渠道方商户系统的流水号:{},订单状态:{},失败原因描述:{}", userId, orderId, serialno, orderStatus, errDesc);
        return mobileFeeChargeLogService.qingHaiWenHangChargeNotify(userId, orderId, serialno, orderStatus, errDesc, sign);
    }


    /**
     * 众智汇融业务
     *
     * @param orderId
     * @param launchId
     * @param extraData
     * @param mobile
     * @param status
     * @param code
     * @param message
     * @return
     */
    @RequestMapping(value = "/zzhr/notify")
    public String zhongZhiHuiRongNotify(@RequestParam("orderId") String orderId,
                                        @RequestParam("launchId") String launchId,
                                        @RequestParam("createTime") String createTime,
                                        @RequestParam("extraData") String extraData,
                                        @RequestParam("mobile") String mobile,
                                        @RequestParam("status") String status,
                                        @RequestParam("code") String code,
                                        @RequestParam("message") String message) {
        log.info("众智汇融业务通知=>手机号:{},订购状态:{},订单号:{},创建时间:{},透传参数:{},状态码:{},状态码说明:{},cpid:{}", mobile, status, orderId, createTime, extraData, code, message, launchId);
        synchronized (interner.intern(orderId)) {
            zhongZhiHuiRongService.zhongZhiHuiRongNotify(orderId, mobile, status, code, message);
        }
        return "ok";
    }

    /**
     * 用户点击获客链接
     *
     * @param jsonNode
     * @return
     */
    @RequestMapping(value = "/hkLink/click")
    public Result hkLinkClick(@RequestBody JsonNode jsonNode) {
        log.info("用户点击获客链接:{}", jsonNode);
        String customerChannel = jsonNode.at("/customerChannel").asText();
        String pageUrl = jsonNode.at("/pageUrl").asText();
        String hkLinkId = jsonNode.at("/hkLinkId").asText();
        String subChannel = jsonNode.at("/subChannel").asText();
        Qywx qywx = new Qywx();
        qywx.setHkLinkId(hkLinkId);
        qywx.setPageUrl(pageUrl);
        qywx.setCustomerChannel(customerChannel);
        qywx.setSubChannel(subChannel);
        qywxService.saveOrUpdateData(qywx);
        return Result.ok();
    }


    /**
     * 抖音小程序新版本支付
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/douyin/tradePay")
    @ResponseBody
    public FebsResponse tradePays(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String app = request.getHeader("x-requested-with");
        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("抖音小程序新版本支付请求=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonData, userAgent);
            DouYinPay douYinPay = mapper.readValue(jsonData, DouYinPay.class);
            if (Strings.isNullOrEmpty(douYinPay.getBusinessType())) {
                return new FebsResponse().fail().message("支付渠道错误");
            }
            if (Strings.isNullOrEmpty(douYinPay.getSubject())) {
                return new FebsResponse().fail().message("产品名称错误");
            }
            if (Strings.isNullOrEmpty(douYinPay.getTradeType())) {
                douYinPay.setTradeType(BizConstant.TRADE_TYPE_TIKTOK_TRADE);
            }
            if (BizConstant.TRADE_TYPE_TIKTOK_TRADE.equals(douYinPay.getTradeType())) {
                Map<String, String> resp = qyclWxpayService.douYinPay(UUID.randomUUID().toString().replace("-", ""), null, douYinPay.getSubject(), douYinPay.getTradeType(), douYinPay.getBusinessType());
                return new FebsResponse().success().data(resp);
            }
        } catch (Exception e) {
            log.error("抖音小程序新版本支付请求,处理异常", e);
            return new FebsResponse().fail().message(e.getMessage());
        }
        return new FebsResponse().fail().message("订单错误");
    }


    /**
     * 畅想铃音消息同步通知
     *
     * @param requestMap
     * @param jsonNode
     * @return
     */
    @ApiOperation(value = "畅想铃音消息同步通知", notes = "铃音消息同步通知")
    @RequestMapping(value = "/ringMessage/notify")
    public String ringMessageNotify(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("畅想铃音消息同步通知,参数map:{},json数据:{}", requestMap, jsonNode);
        return "OK";
    }


    /**
     * 鸿盛铃音消息同步通知
     *
     * @param requestMap
     * @param jsonNode
     * @return
     */
    @ApiOperation(value = "鸿盛铃音消息同步通知", notes = "铃音消息同步通知")
    @RequestMapping(value = "/ringMsg/notify")
    public String ringMsgNotify(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("鸿盛铃音消息同步通知,参数map:{},json数据:{}", requestMap, jsonNode);
        return "OK";
    }

    //微信用户支付通知
    @RequestMapping(value = "/miguVrbt/wechatPay/notify", produces = "text/plain")
    @ResponseBody
    public String miguVrbtWechatPayNotify(HttpServletRequest request) {
        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()) {
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            QyclWxpayNotifyParam notifyParam = qyclWxpayService.payResultNotify(notifyXml);
            log.info("咪咕视频彩铃三方支付微信支付通知notifyParam = " + notifyParam);
            final String resultCode = notifyParam.getResultCode();

            MiguVrbtPayOrder orderPay = miguVrbtPayOrderService.queryNotPayOrder(notifyParam.getOutTradeNo());
            if (orderPay == null) {
                log.error("咪咕视频彩铃三方支付微信回调通知重复通知-notifyParam:{}", notifyParam);
                return succRespXml;
            }
            miguVrbtPayOrderService.wechatModifyPayStatus(notifyParam.getOutTradeNo(), notifyParam.getTransactionId(), resultCode, orderPay.getMobile());
            return succRespXml;
        } catch (Exception e) {
            log.error("咪咕视频彩铃三方支付微信回调通知处理异常:", e);
            return failRespXml;
        }
    }


    /**
     * 咪咕视频彩铃三方支付支付宝用户支付通知
     *
     * @param requestParams
     * @return
     */
    @RequestMapping(value = "/miguVrbt/aliPay/notify", produces = "text/plain")
    @ResponseBody
    public String miguVrbtAliPayNotify(@RequestParam Map<String, String> requestParams) {
        String succRespMsg = "success";
        String failRespMsg = "failure";
        try {
            AlipayNotifyParam notifyParam = alipayService.aliTradePayResultNotify(requestParams);
            log.info("咪咕视频彩铃三方支付支付宝支付通知notifyParam:{} ", notifyParam);
            MiguVrbtPayOrder orderPay = miguVrbtPayOrderService.queryNotPayOrder(notifyParam.getOutTradeNo());
            if (orderPay == null) {
                log.error("咪咕视频彩铃三方支付支付宝回调通知重复通知-notifyParam:{}", notifyParam);
                return failRespMsg;
            }
            final String tradeStatus = notifyParam.getTradeStatus();
            miguVrbtPayOrderService.aliPayModifyPayStatus(notifyParam.getOutTradeNo(), notifyParam.getTradeNo(), tradeStatus, orderPay.getMobile());
        } catch (Exception e) {
            log.error("咪咕视频彩铃三方支付支付宝支付通知出错：", e);
            return failRespMsg;
        }
        return succRespMsg;
    }

    /**
     * 咪咕视频彩铃三方支付查询订单是否支付
     *
     * @param request
     * @return
     */
    @PostMapping("/miguVrbt/queryOrder")
    @ResponseBody
    public Result<?> miguVrbtQueryOrder(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("咪咕视频彩铃三方支付查询订单是否支付=>jsonData:{}", jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            if (Strings.isNullOrEmpty(subscribe.getIspOrderNo())) {
                return Result.error("订单号错误");
            }
            MiguVrbtPayOrder payOrder = miguVrbtPayOrderService.queryOrder(subscribe.getIspOrderNo());
            if (payOrder!=null) {
                return Result.ok("支付成功",payOrder.getRingName());
            }
            return Result.error("未支付");
        } catch (JeecgBootException e) {
            log.warn("咪咕视频彩铃三方支付查询订单是否支付,处理异常", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.warn("咪咕视频彩铃三方支付查询订单是否支付,处理异常", e);
            return Result.error("请求参数错误");
        }
    }



    /**
     * 咪咕视频彩铃三方支付设置铃音
     * @param request
     * @return
     */
    @PostMapping("/set/ring")
    @ResponseBody
    public Result<?> setRing(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("咪咕视频彩铃三方支付设置铃音=>jsonData:{}",jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            if(Strings.isNullOrEmpty(subscribe.getMobile())) {
                return Result.error("手机号错误");
            }
            if(Strings.isNullOrEmpty(subscribe.getChannel())) {
                return Result.error("渠道号错误");
            }
            if(Strings.isNullOrEmpty(subscribe.getCopyrightId())) {
                return Result.error("版权id错误");
            }
            if(Strings.isNullOrEmpty(subscribe.getContentId())) {
                return Result.error("内容id错误");
            }
            return miguVrbtPayOrderService.setRing(subscribe);
        } catch (JeecgBootException e) {
            log.warn("咪咕视频彩铃三方支付设置铃音,处理异常",e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.warn("咪咕视频彩铃三方支付设置铃音,处理异常",e);
            return Result.error("请求参数错误");
        }
    }


    /**
     * 悠然通知
     *
     * @param requestBody
     * @return
     */
    @RequestMapping(value = "/youran/notify")
    public String youranNotify(@RequestBody String requestBody) {
        log.info("悠然下单=>通知:{}", requestBody);
        try {
            JsonNode tree = mapper.readTree(requestBody);
            String orderNo = tree.at("/orderNo").asText();
            String status = tree.at("/status").asText();
            String msg = tree.at("/msg").asText();
            synchronized (interner.intern(orderNo)) {
                subscribeService.youranCrackNotify(orderNo, status,msg);
            }
            return "SUCCESS";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 咪咕客回调
     * @param requestMap
     * @param jsonNode
     * @return
     */
    @ApiOperation(value = "咪咕客回调", notes = "咪咕客回调")
    @RequestMapping(value = "/miguKeNotify")
    public String miguKeNotify(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("咪咕客回调,参数map:{},json数据:{}", requestMap, jsonNode);
        if (jsonNode == null) {
            return "OK";
        }
        return "OK";
    }


    /**
     * 浩荡话费充值通知
     * @param orderId
     * @param respCode
     * @param respMsg
     * @param transNo
     * @return
     */
    @RequestMapping(value = "/haoDangCharge/notify")
    public FeeChargeOrderHaoDangResult haoDangChargeNotify(@RequestParam("orderId") String orderId,
                                                           @RequestParam("respCode") String respCode,
                                                           @RequestParam("respMsg") String respMsg,
                                                           @RequestParam("transNo") String transNo) {
        log.info("浩荡话费充值通知=>平台订单号:{},订单状态:{},失败原因描述:{},订单号:{}", orderId, respCode, respMsg, transNo);
        return mobileFeeChargeLogService.haoDangChargeNotify(orderId, respCode,respMsg, transNo);
    }
    /**
     * 骏伯多会员直充结果回调
     *
     * @return
     */
    @RequestMapping(value = "/junboRechargeNotify")
    public String junboRechargeNotify(@RequestBody String requestBody) {
        log.info("骏伯三方权益充值通知-返回参数:{}", requestBody);
        try {
            final JunboNotifyResult junboResult = new ObjectMapper().readValue(requestBody, JunboNotifyResult.class);
            junboChargeLogService.receiveRechargeNotify(junboResult);
            return "1";
        } catch (Exception e) {
            log.info("骏伯三方权益充值通知异常", e);
        }
        return "error";
    }



    /**
     * 多彩下发多点商超权益通知
     *
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/dcxm/openApi/v1/create-order")
    public DuocaiOpenResponse duocaiOpenNotify(@RequestBody String requestBody, HttpServletRequest request) {
        String sign = request.getHeader("sign");
        String appId = request.getHeader("app-id");
        String timestamp = request.getHeader("timestamp");
        log.info("多彩下发多点商超权益通知=>参数:{}", requestBody);
        return duodianCouponCodeChargeLogService.duocaiOpenNotify(requestBody, sign, appId,timestamp);
    }

    /**
     * 多彩查询产品详细
     * @param requestBody
     * @param request
     * @return
     */
    @RequestMapping(value = "/dcxm/openApi/v1/get-goods")
    public DuocaiProductResponse duocaiProduct(@RequestBody String requestBody, HttpServletRequest request) {
        String sign = request.getHeader("sign");
        String appId = request.getHeader("app-id");
        String timestamp = request.getHeader("timestamp");
        log.info("多彩查询产品详细=>参数:{}", requestBody);
        return duodianCouponCodeChargeLogService.duocaiProduct(requestBody, sign, appId,timestamp);
    }

    /**
     * 多彩多点商超激活码充值
     *
     * @param mobile
     * @param code
     * @return
     */
    @PostMapping(value = "/duocai/recharge")
    @ResponseBody
    public FebsResponse duocaiRecharge(@RequestParam("mobile") String mobile, @RequestParam("code") String code) {
        log.info("多彩多点商超激活码充值=>mobile:{},code:{}", mobile, code);
        return duodianCouponCodeChargeLogService.duocaiRecharge(mobile, code);
    }


    @ApiOperation(value = "互娱途牛充值通知", notes = "互娱途牛充值通知")
    @RequestMapping(value = "/huyu/tuniuRechargeNotify")
    public String huyuTuniuRechargeNotify(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("互娱途牛充值通知,参数map:{},json数据:{}", requestMap, jsonNode);
        if (jsonNode == null) {
            return "OK";
        }
        return "OK";
    }

    @ApiOperation(value = "互娱迅游⼿游充值通知", notes = "互娱迅游⼿游充值通知")
    @RequestMapping(value = "/huyu/xunyouRechargeNotify")
    public String huyuXunyouRechargeNotify(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        log.info("互娱迅游⼿游充值通知,参数map:{},json数据:{}", requestMap, jsonNode);
        if (jsonNode == null) {
            return "OK";
        }
        return "OK";
    }
    /**
     * 易尊通知
     *
     * @param requestBody
     * @return
     */
    @RequestMapping(value = "/yizun/notify")
    public YiZunCpaNotify yizunNotify(@RequestBody String requestBody) {
        log.info("易尊下单通知-参数:{}", requestBody);
        try {
            JsonNode tree = mapper.readTree(requestBody);
            String tradeId = tree.at("/tradeId").asText("");
            String sourceTradeId = tree.at("/sourceTradeId").asText("");
            String tradeStatus = tree.at("/tradeStatus").asText("");
            String merchantId = tree.at("/merchantId").asText("");
            String merchantPid = tree.at("/merchantPid").asText("");
            synchronized (interner.intern(sourceTradeId)) {
                return subscribeService.yizunNotify(sourceTradeId,tradeStatus);
            }
        } catch (Exception e) {
            log.error("易尊下单通知异常-参数:{}",requestBody, e);
            return YiZunCpaNotify.fail();
        }
    }

    /**
     * 一图穿越-图片安审结果通知
     *
     * @param jsonNode jsonNode
     * @return Result
     */
    @PostMapping(value = "/ytcyNotify")
    public void ytcyNotify(@RequestBody JsonNode jsonNode) {
        log.info("咪咕安审-回调通知：{}", jsonNode);
        String dataId = jsonNode.at("/dataId").asText();
        String status = jsonNode.at("/status").asText();

        String key = CommonSecurityService.PREFIX_KEY + dataId;
        if ("NORMAL".equals(status)) {
            redisUtil.set(key, 1, 90);
        } else {
            redisUtil.set(key, -1, 90);
        }
    }

    /**
     * OKHTTPCLIENT监控
     */
    @GetMapping(value = "/monitor/okhttpclient")
    public String okHttpClientMonitor(String clientName) {
        OkHttpClient client = HttpUtils.map.get(clientName);
        Dispatcher dispatcher = client.dispatcher();

        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("runningCallsCount", dispatcher.runningCallsCount());
        objectNode.put("queuedCallsCount", dispatcher.queuedCallsCount());
        return objectNode.toString();
    }

    private static final String LIMIT_SETTINGS_KYE = "limit:settings";

    @PostMapping("/migu-limit/update")
    public Result miguLimitUpdate(@RequestBody MiguActiveSettings miguActiveSettings) {
        miguActiveSettingsService.saveOrUpdate(miguActiveSettings);

        // 设置为null则不限量
        if (miguActiveSettings.getLimitCount() == null) {
            Map map = new HashMap<>();
            map.put("total", null);
            map.put("url", miguActiveSettings.getJumpUrl());
            redisUtil.del(LIMIT_SETTINGS_KYE);
            redisUtil.hmset(LIMIT_SETTINGS_KYE, map);
            return Result.ok();
        }

        if (redisUtil.hasKey(LIMIT_SETTINGS_KYE)) {
            Map map = redisUtil.hmget(LIMIT_SETTINGS_KYE);
            map.put("total", miguActiveSettings.getLimitCount());
            map.put("url", miguActiveSettings.getJumpUrl());
            redisUtil.hmset(LIMIT_SETTINGS_KYE, map, getSecond());
        }
        return Result.ok();
    }

    @GetMapping("/migu-limit/list")
    public Result miguLimitList() {
        return Result.okAndSetData(miguActiveSettingsService.list());
    }


    @GetMapping(value = "/limit/can-jump")
    public Result incrLimit() {
        if (redisUtil.hasKey(LIMIT_SETTINGS_KYE)) {
            Map map = redisUtil.hmget(LIMIT_SETTINGS_KYE);
            Object total = map.get("total");
            if (total == null) {
                return Result.okAndSetData(map.get("url"));
            }

            int requestCount = (int) map.get("requestCount");
            if (requestCount < (int) total) {
                map.put("requestCount", requestCount + 1);
                redisUtil.hmset(LIMIT_SETTINGS_KYE, map, getSecond());
                return Result.okAndSetData(map.get("url"));
            } else {
                return Result.error("今日访问次数已达上限!");
            }
        } else {
            List<MiguActiveSettings> list = miguActiveSettingsService.list();
            if (CollectionUtils.isNotEmpty(list)) {
                MiguActiveSettings miguActiveSettings = list.get(0);
                Long total = miguActiveSettings.getLimitCount();

                Map map = new HashMap<>();
                map.put("total", total);
                map.put("url", miguActiveSettings.getJumpUrl());
                if (total != null && total == 0) {
                    map.put("requestCount", 0);
                    redisUtil.hmset(LIMIT_SETTINGS_KYE, map, getSecond());
                    return Result.error("今日访问次数已达上限!");
                } else {
                    map.put("requestCount", 1);
                    redisUtil.hmset(LIMIT_SETTINGS_KYE, map, getSecond());
                    return Result.okAndSetData(miguActiveSettings.getJumpUrl());
                }
            } else {
                return Result.error("请前往系统进行配置!");
            }
        }
    }

    /**
     * 查询可用抽奖次数
     *
     * @param phoneNumber 用户手机号
     * @return 可用抽奖次数
     */
    @GetMapping("/available/lottery/count")
    public Result<?> getAvailableTimes(@RequestParam @NotBlank(message = "手机号不能为空") String phoneNumber,
                                       @RequestParam @NotBlank(message = "渠道不能为空") String channelCode) {
        String key = getKey(phoneNumber, channelCode);
        Integer times = (Integer) redisUtil.get(key);
        if (times == null) {
            return Result.ok(getLotteryCount(channelCode));
        }
        int usedTimes = times;
        return Result.ok(getLotteryCount(channelCode) - usedTimes);
    }

    /**
     * 消费一次抽奖次数
     *
     * @param phoneNumber 用户手机号
     * @return true 表示消费成功，false 表示次数已用完
     */
    @GetMapping("/consume/lottery/count")
    public Result<?> consumeLottery(@RequestParam @NotBlank(message = "手机号不能为空") String phoneNumber,
                                    @RequestParam @NotBlank(message = "渠道id不能为空") String channelCode) {
        String key = getKey(phoneNumber, channelCode);
        Integer times = (Integer) redisUtil.get(key);
        int usedTimes = 0;
        if (times != null) {
            usedTimes = times;
        }
        if (usedTimes >= getLotteryCount(channelCode)) {
            return Result.ok(false);
        }
        usedTimes++;
        if (usedTimes == 1) {
            // 设置过期时间为当天凌晨
            long expireSeconds = getSecondsToMidnight();
            redisUtil.set(key, usedTimes, expireSeconds);
        } else {
            redisUtil.incr(key, 1);
        }
        return Result.ok(true);
    }

    /**
     * 生成 Redis 存储的键
     * @param phoneNumber 用户手机号
     * @return 键
     */
    private String getKey(String phoneNumber,String channelCode) {
        LocalDate today = LocalDate.now();
        return "fakeLottery:" + phoneNumber + ":" +channelCode+":"+ today;
    }

    /**
     * 计算到当天凌晨的秒数
     * @return 秒数
     */
    private long getSecondsToMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = LocalDateTime.of(now.toLocalDate().plusDays(1),  LocalTime.MIDNIGHT);
        return midnight.toEpochSecond(ZoneOffset.UTC)  - now.toEpochSecond(ZoneOffset.UTC);
    }
    private int getLotteryCount(String channelCode) {
        return Optional.ofNullable(LOTTERY_COUNT_MAP).map(e->e.get(channelCode)).orElse(0);
    }


    public static long getSecond() {
        // 获取当前时间
        ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());
        // 获取今天的结束时间（23:59:59）
        ZonedDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59).atZone(ZoneId.systemDefault());
        // 计算当前时间到当天结束的秒数
        return java.time.Duration.between(now, endOfDay).getSeconds();
    }

    /**
     * 提交手机号，生成订单
     *
     * @param jsonNode jsonNode
     * @return Result
     */
    @RequestMapping("/common/submitMobile")
    public Result submitMobile(@RequestBody JsonNode jsonNode, HttpServletRequest request) {
        log.info("submitMobile,参数:{}", jsonNode);
        String mobile = jsonNode.at("/mobile").asText();
        String channel = jsonNode.at("/channel").asText();
        String subChannel = jsonNode.at("/subChannel").asText();
        String source = jsonNode.at("/source").asText();
        String deviceInfo = jsonNode.at("/deviceInfo").asText();
        String app = request.getHeader("x-requested-with");

        Subscribe subscribe = new Subscribe();
        subscribe.setId(IdUtil.getSnowflake().nextIdStr());
        subscribe.setBizType("XWFCS");
        subscribe.setMobile(mobile);
        subscribe.setChannel(channel);
        subscribe.setSubChannel(subChannel);
        subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
        subscribe.setIp(IPUtils.getIpAddr(request));
        subscribe.setReferer(app);
        subscribe.setSource(source);
        subscribe.setDeviceInfo(deviceInfo);
        subscribe.setServiceId("none");

        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            subscribe.setIsp(mobileRegionResult.getOperator());
        }
        subscribeService.updateSubscribeDbAndEs(subscribe);
        return Result.okAndSetData(subscribe.getId());
    }


    /**
     * 外部渠道业务订购
     *
     * @return
     */
    @SneakyThrows
    @PostMapping("/outside/subOrder")
    public Result outsideSubOrder(@RequestParam("mobile") String mobile,
                                   @RequestParam(value = "subChannel") String subChannel,
                                   @RequestParam(value = "userAgent", required = false) String userAgent,
                                   @RequestParam(value = "appPackage", required = false) String appPackage,
                                   @RequestParam(value = "ip", required = false) String ip,
                                   @RequestParam(value = "openId", required = false) String openId) {
        log.info("外部渠道业务订购-手机号:{},渠道号:{},ip:{},app:{},ua:{},openId:{}", mobile, subChannel, ip, appPackage, userAgent, openId);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (blackListService.isBlackList(mobile)) {
            return Result.msgBlackLimit();
        }
        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setSubChannel(subChannel);
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subChannel);
        if (outsideConfig == null || StringUtils.isBlank(outsideConfig.getBizTypeId())) {
            log.error("无效的外部渠道号:{}", subChannel);
            return Result.error("无效的渠道号!");
        }
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        subscribe.setUserAgent(userAgent);
        subscribe.setIp(ip);
        subscribe.setReferer(appPackage);
        subscribe.setDeviceInfo(openId);
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统繁忙,请稍后再试!");
        }
        OutsideBusinessConfig outsideBusinessConfig = outsideConfigService.getOutsideBusinessConfig(outsideConfig.getId(), subscribe.getProvince());
        //获取当前省份可以开通业务的渠道号
        if (outsideBusinessConfig != null) {
            subscribe.setChannel(outsideBusinessConfig.getChannel());
        } else {
            subscribe.setChannel(outsideConfig.getChannel());
        }
        if ((!"JXYD_ADLD_YR".equals(subscribe.getChannel())) && subscribeService.checkBizRepeat(mobile, subscribe.getChannel())) {
            log.warn("手机号:{}3个月内已开通过业务", mobile);
            Result<Object>  result=Result.msgDuplicateLimit();
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
            subscribe.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(subscribe);
            return result;
        }
        if (adSiteBusinessConfigService.isOsVrbtBlack(subscribe.getChannel(), appPackage)) {
            log.warn("app限制,渠道号:{},手机号:{},app:{}", subscribe.getChannel(), subscribe.getMobile(), appPackage);
            Result<Object>  result= Result.msgAppLimit();
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
            subscribe.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(subscribe);
            return result;
        }
        subscribe.setBizType(BizConstant.getBizTypeByMiguChannel(subscribe.getChannel()));
        if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), subscribe.getProvince())) {
            log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), subscribe.getMobile(), subscribe.getProvince());
            Result<Object>  result=Result.msgProvinceLimit();
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
            subscribe.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(subscribe);
            return result;
        }
        Result result = bizTypeService.outsideSubOrder(subscribe);
        if (!result.isOK()) {
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            if(StringUtils.isNotBlank(result.getErrorMsg())){
                subscribe.setResult(result.getErrorMsg());
            }else{
                subscribe.setResult("{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMessage()+"\"}");
            }
            subscribe.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(subscribe);
            result.setErrorMsg("");
        }
        return result;
    }



    /**
     * 外部渠道业务回执
     *
     * @return
     */
    @SneakyThrows
    @PostMapping("/outside/orderNotify")
    public Result outsideOrderNotify(@RequestParam("mobile") String mobile,
                               @RequestParam("orderId") String orderId) {
        log.info("外部渠道业务回执-手机号:{},订单号:{}", mobile, orderId);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(orderId)) {
            return Result.error("请输入正确订单号");
        }
        return bizTypeService.receiveBillingResult(mobile, orderId);
    }
}




