package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.remote.WechatComplainService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.WechatComplain;
import com.eleven.cms.service.IWechatComplainService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 微信投诉明细
 * @Author: jeecg-boot
 * @Date:   2023-04-11
 * @Version: V1.0
 */
@Api(tags="微信投诉明细")
@RestController
@RequestMapping("/cms/wechatComplain")
@Slf4j
public class WechatComplainController extends JeecgController<WechatComplain, IWechatComplainService> {
	@Autowired
	private IWechatComplainService wechatComplainService;
	 @Autowired
	 private WechatComplainService complainService;

	/**
	 * 分页列表查询
	 *
	 * @param wechatComplain
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "微信投诉明细-分页列表查询")
	@ApiOperation(value="微信投诉明细-分页列表查询", notes="微信投诉明细-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(WechatComplain wechatComplain,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WechatComplain> queryWrapper = QueryGenerator.initQueryWrapper(wechatComplain, req.getParameterMap());
		Page<WechatComplain> page = new Page<WechatComplain>(pageNo, pageSize);
		IPage<WechatComplain> pageList = wechatComplainService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param wechatComplain
	 * @return
	 */
	//@AutoLog(value = "微信投诉明细-添加")
	@ApiOperation(value="微信投诉明细-添加", notes="微信投诉明细-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody WechatComplain wechatComplain) {
		wechatComplainService.save(wechatComplain);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param wechatComplain
	 * @return
	 */
	//@AutoLog(value = "微信投诉明细-编辑")
	@ApiOperation(value="微信投诉明细-编辑", notes="微信投诉明细-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody WechatComplain wechatComplain) {
		wechatComplainService.updateById(wechatComplain);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "微信投诉明细-通过id删除")
	@ApiOperation(value="微信投诉明细-通过id删除", notes="微信投诉明细-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		wechatComplainService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "微信投诉明细-批量删除")
	@ApiOperation(value="微信投诉明细-批量删除", notes="微信投诉明细-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wechatComplainService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "微信投诉明细-通过id查询")
	@ApiOperation(value="微信投诉明细-通过id查询", notes="微信投诉明细-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		WechatComplain wechatComplain = wechatComplainService.getById(id);
		if(wechatComplain==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(wechatComplain);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wechatComplain
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WechatComplain wechatComplain) {
        return super.exportXls(request, wechatComplain, WechatComplain.class, "微信投诉明细");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WechatComplain.class);
    }

	 /**
	  * 微信查询投诉详细
	  * @param complaintId
	  * @param mchId
	  * @return
	  */
	 @ApiOperation(value = "微信查询投诉详细", notes = "微信查询投诉详细")
	 @PostMapping(value = "/wechat/complain")
	 @ResponseBody
	 public Result<?> wechatComplain(@RequestParam(value = "complaintId", required = false, defaultValue ="")String complaintId,
									 @RequestParam(value = "mchId", required = false, defaultValue ="")String mchId){
		 if(StringUtils.isEmpty(complaintId)){
			 return Result.error("订单号不能为空");
		 }
		 return complainService.wechatQueryComplainDetail(complaintId,mchId);
	 }


	 /**
	  * 商家处理交易投诉
	  * @param complaintId 投诉单号
	  * @param mchId 商户号
	  * @param responseContent 回复内容
	  * @param responseImages 回复图片
	  * @param jumpUrl 跳转地址
	  * @param jumpUrlText  实际展示给用户的文案，附在回复内容之后。用户点击文案，即可进行跳转。
	  * 注:若传入跳转链接，则跳转链接文案为必传项，二者缺一不可
	  * @return
	  */

	 @ApiOperation(value = "微信处理交易投诉", notes = "微信处理交易投诉")
	 @PostMapping(value = "/wechat/solve/complain")
	 @ResponseBody
	 public Result<?> wechatSolveComplain(@RequestParam(value = "complaintId", required = false, defaultValue ="")String complaintId,
										  @RequestParam(value = "mchId", required = false, defaultValue ="")String mchId,
										  @RequestParam(value = "responseContent", required = false, defaultValue ="")String responseContent,
										  @RequestParam(value = "responseImages", required = false, defaultValue ="")String responseImages,
										  @RequestParam(value = "jumpUrl", required = false, defaultValue ="")String jumpUrl,
										  @RequestParam(value = "jumpUrlText", required = false, defaultValue ="")String jumpUrlText){
		 log.info("商家处理交易投诉,投诉订单号:{},商户号:{},内容:{},图片:{},跳转地址:{},链接文案:{}",complaintId,mchId,responseContent,responseImages,jumpUrl,jumpUrlText);
		 if(StringUtils.isEmpty(complaintId)){
			 return Result.error("订单号不能为空");
		 }
		 return complainService.wechatSolveComplain(complaintId,mchId,responseContent,responseImages,jumpUrl,jumpUrlText);
	 }


	 /**
	  * 反馈投诉处理完成
	  * @param complaintId
	  * @param mchId
	  * @return
	  */

	 @ApiOperation(value = "反馈投诉处理完成", notes = "反馈投诉处理完成")
	 @PostMapping(value = "/wechat/feedback/over/complain")
	 @ResponseBody
	 public Result<?> feedbackOverComplain(@RequestParam(value = "complaintId", required = false, defaultValue ="")String complaintId,
										  @RequestParam(value = "mchId", required = false, defaultValue ="")String mchId){
		 log.info("商家处理交易投诉,投诉订单号:{},商户号:{}",complaintId,mchId);
		 if(StringUtils.isEmpty(complaintId)){
			 return Result.error("订单号不能为空");
		 }
		 return complainService.feedbackOverComplain(complaintId,mchId);
	 }
}
