package com.eleven.cms.controller;

import cn.hutool.core.util.RandomUtil;
import com.eleven.cms.dto.WechatPayDecodeNotifyParam;
import com.eleven.cms.dto.WxpayNotifyParam;
import com.eleven.cms.entity.CommonCoupon;
import com.eleven.cms.entity.OrderPay;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.RightsRechargeService;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.ShopRechargeRightsServiceImpl;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.RandImageUtils;
import com.eleven.cms.util.TokenUtil;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.wxpay.sdk.WXPayConstants;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.RightsLogin;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.MD5Util;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 权益领取相关接口
 */
@Api(tags = "api")
@RestController
@RequestMapping("/api/rights")
@Slf4j
@Validated
public class ApiRightsController {
    public static final String SHOP_ORDER_PACK_NAME = "shop_rights_pack_1";
    public static final String SHOP_ORDER_SERVICE_ID = "shop_order";
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Autowired
    private ISmsValidateService smsValidateService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RightsRechargeService rightsRechargeService;
    @Autowired
    private IRightsSubService rightsSubService;
    @Autowired
    private IShopProductService shopProductService;
    @Autowired
    private IWxpayService wxpayService;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IQyclWxpayService qyclWxpayService;
    @Autowired
    private ICommonCouponService commonCouponService;
    /**
     * 权益领取登录生成图形验证码
     * @param key
     * @return
     */
    @GetMapping(value = "/randomImage/{key}")
    public Result<String> alipayRechargeRandomImage(@PathVariable String key){
        Result<String> res = new Result<String>();
        try {
            String code = RandomUtil.randomString(BizConstant.BASE_CHECK_CODES,6);
            String lowerCaseCode = code.toLowerCase();
            String realKey = MD5Util.MD5Encode(lowerCaseCode+key, "utf-8");
            redisUtil.set(realKey, lowerCaseCode, 60);
            String base64 = RandImageUtils.outputVerifyImage(code);
            res.setSuccess(true);
            res.setResult(base64);
        } catch (Exception e) {
            res.error500("获取验证码出错"+e.getMessage());
            e.printStackTrace();
        }
        return res;
    }
    /**
     * 权益领取图形验证码校验
     * @param imgCaptcha
     * @param checkKey
     * @return
     */
    @PostMapping(value = "/check/randomImage")
    @ResponseBody
    public FebsResponse checkRandomImage(@RequestParam("imgCaptcha")String imgCaptcha,
                                         @RequestParam("checkKey")String checkKey) {
        log.info("权益领取图形验证码校验=>图形验证码:{},图形验证码编号:{}",imgCaptcha,checkKey);
        if (Strings.isNullOrEmpty(imgCaptcha) || !imgCaptcha.matches("^[A-Za-z0-9]{6}$") || Strings.isNullOrEmpty(checkKey)) {
            return new FebsResponse().fail().message("图形验证码错误");
        }

        String lowerCaseCaptcha = imgCaptcha.toLowerCase();
        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha+checkKey, "utf-8");
        Object checkCode = redisUtil.get(realKey);
        if(checkCode==null || !checkCode.equals(lowerCaseCaptcha)) {
            return new FebsResponse().fail().message("图形验证码错误");
        }
        return new FebsResponse().success();
    }
    /**
     * 发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getCaptcha")
    @ResponseBody
    public Result<?> getCaptcha(@RequestParam("mobile") String mobile,
                                @RequestParam(name = "channelCode", required = false, defaultValue ="") String channelCode,
                                @RequestParam(name = "serviceId",   required = false, defaultValue =BizConstant.SMS_MODEL_COMMON_SERVICE_ID) String serviceId) {
        log.info("权益领取登录-发送短信验证码=>手机号:{},渠道号:{},业务ID:{}",mobile,channelCode,serviceId);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.rightsCreate(mobile,channelCode,serviceId);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }
        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }

    /**
     * 权益领取登录接口
     * @param mobile
     * @param captcha
     * @return
     */
    @PostMapping(value = "/login")
    @ResponseBody
    public FebsResponse rightsLogin(@RequestParam("mobile") String mobile,
                                    @RequestParam("captcha")String captcha,
                                    @RequestParam(name = "imgCaptcha", required = false, defaultValue ="")String imgCaptcha,
                                    @RequestParam(name = "checkKey", required = false, defaultValue ="")String checkKey,
                                    @RequestParam(name = "bizType",   required = false, defaultValue ="") String bizType,HttpServletRequest request) {

        String referer = request.getHeader("Referer");
        log.info("权益领取登录=>手机号:{},短信验证码:{},referer:{}",mobile,captcha,referer);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return new FebsResponse().fail().message("验证码错误");
        }
        if("ALIPAY".equals(bizType)){
            if (Strings.isNullOrEmpty(imgCaptcha) || !imgCaptcha.matches("^[A-Za-z0-9]{6}$") || Strings.isNullOrEmpty(checkKey)) {
                return new FebsResponse().fail().message("图形验证码错误");
            }
            String lowerCaseCaptcha = imgCaptcha.toLowerCase();
            String realKey = MD5Util.MD5Encode(lowerCaseCaptcha+checkKey, "utf-8");
            Object checkCode = redisUtil.get(realKey);
            if(checkCode==null || !checkCode.equals(lowerCaseCaptcha)) {
                return new FebsResponse().fail().message("图形验证码错误");
            }
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            return new FebsResponse().fail().message(e.getMessage());
        }
        String token =TokenUtil.setLoginTime(mobile);
        return new FebsResponse().success().data(token);
    }


    /**
     * 查询业务列表
     * @param bizType
     * @return
     */
    @PostMapping(value = "/query/serviceList")
    @ResponseBody
    @RightsLogin(menu = "查询业务列表")
    public FebsResponse serviceList(@RequestParam(name = "bizType",   required = false, defaultValue ="") String bizType,HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("查询业务列表=>手机号:{},业务类型:{},referer:{}",mobile,bizType,referer);
        if("COMIC".equals(bizType)){
            return rightsRechargeService.miguComicGetServiceList(mobile);
        }else  if("READ".equals(bizType)){
            return rightsRechargeService.miguReadGetServiceList(mobile);
        }else  if("VRBT_CW".equals(bizType)){
            return rightsRechargeService.yueDongCaiLingChuanWangGetServiceList(mobile);
        }else if("WO_READ_SHOP".equals(bizType)){
            return rightsRechargeService.woReadShopGetServiceList(mobile);
        }
        return rightsRechargeService.getServiceList(mobile);
    }

//    /**
//     * 支付宝权益领取登录接口
//     * @param mobile
//     * @param captcha
//     * @return
//     */
//    @PostMapping(value = "/alipay/login")
//    @ResponseBody
//    public FebsResponse alipayRightsLogin(@RequestParam("mobile") String mobile,
//                                          @RequestParam("captcha")String captcha,
//                                          @RequestParam("imgCaptcha")String imgCaptcha,
//                                          @RequestParam("checkKey")String checkKey,HttpServletRequest request) {
//        String referer = request.getHeader("Referer");
//        log.info("支付宝权益领取登录=>手机号:{},短信验证码:{},图形验证码:{},图形验证码编号:{},referer:{}",mobile,captcha,imgCaptcha,checkKey,referer);
//        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
//            return new FebsResponse().fail().message("请输入正确格式手机号");
//        }
//        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
//            return new FebsResponse().fail().message("短信验证码错误");
//        }
//        if (Strings.isNullOrEmpty(imgCaptcha) || !imgCaptcha.matches("^[A-Za-z0-9]{6}$") || Strings.isNullOrEmpty(checkKey)) {
//            return new FebsResponse().fail().message("图形验证码错误");
//        }
//
//        String lowerCaseCaptcha = imgCaptcha.toLowerCase();
//        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha+checkKey, "utf-8");
//        Object checkCode = redisUtil.get(realKey);
//        if(checkCode==null || !checkCode.equals(lowerCaseCaptcha)) {
//            return new FebsResponse().fail().message("图形验证码错误");
//        }
//        try {
//            smsValidateService.check(mobile, captcha);
//        } catch (JeecgBootException e) {
//            return new FebsResponse().fail().message(e.getMessage());
//        }
//        String token =TokenUtil.setLoginTime(mobile);
//        return new FebsResponse().success().data(token);
//    }


    /**
     * 查询充值记录
     * @return
     */
    @PostMapping(value = "/query/rechargeList")
    @ResponseBody
    @RightsLogin(menu = "查询充值记录")
    public FebsResponse queryRechargeList(HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("查询充值记录=>手机号:{},referer:{}",mobile,referer);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        return rightsRechargeService.queryRechargeList(mobile);
    }


    /**
     * 根据渠道号查询通用权益包
     * @param channel
     * @return
     */
    @PostMapping(value = "/query/rightsPackList/channel")
    @ResponseBody
    @RightsLogin(menu = "查询通用权益包")
    public FebsResponse queryRightsListByChannel(@RequestParam("channel") String channel, HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("根据渠道号查询通用权益包=>渠道号:{},referer:{}",channel,referer);
        if (Strings.isNullOrEmpty(channel)) {
            return new FebsResponse().fail().message("渠道号错误");
        }
        return rightsRechargeService.queryRightsListByChannel(channel,request);
    }



    /**
     * 通用权益领取
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @PostMapping(value = "/scheduled/recharge")
    @ResponseBody
    @RightsLogin(menu = "通用权益领取")
    public FebsResponse createScheduledRecharge(@RequestParam(name = "account",   required = false, defaultValue ="") String account,
                                                @RequestParam("serviceId")String serviceId,
                                                @RequestParam("packName")String packName,
                                                @RequestParam("rightsId")String rightsId,
                                                @RequestParam(name = "channel",   required = false, defaultValue ="") String channel,HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("通用权益领取=>手机号:{},权益领取业务ID:{},账号:{},权益包:{},产品ID:{},渠道号:{},referer:{}",mobile,serviceId,account,packName,rightsId,channel,referer);
        if (Strings.isNullOrEmpty(serviceId)) {
            return new FebsResponse().fail().message("权益领取业务ID错误");
        }
        if (Strings.isNullOrEmpty(packName)) {
            return new FebsResponse().fail().message("权益包错误");
        }
        if (Strings.isNullOrEmpty(rightsId)) {
            return new FebsResponse().fail().message("产品ID错误");
        }
        if(rightsSubService.isAccount(rightsId,account)){
            return new FebsResponse().fail().message("账号错误");
        }
        return rightsRechargeService.createScheduledRecharge(mobile,account,serviceId,packName,rightsId, channel,request);
    }


    /**
     * 获取微信用户openId接口
     * @param code
     * @return
     */
    @ApiOperation(value = "获取微信用户openId接口", notes = "获取微信用户openId接口")
    @PostMapping(value = "/shop/wechat/openId")
    @ResponseBody
    public FebsResponse wechatOpenId(@RequestParam(value = "code", required = false, defaultValue ="") String code,
                                     @RequestParam(value = "openId", required = false, defaultValue ="")String openId,
                                     @RequestParam(value = "tradeType", required = false, defaultValue = BizConstant.TRADE_TYPE_WECHAT) String tradeType,
                                     @RequestParam(value = "anonymousCode", required = false, defaultValue ="") String anonymousCode,
                                     @RequestParam(value = "businessType", required = false, defaultValue = BizConstant.BUSINESS_TYPE_WX_QYCLZY) String businessType) throws Exception {
        if(org.springframework.util.StringUtils.isEmpty(code)){
            return new FebsResponse().fail().message("code不能为空");
        }
        return qyclWxpayService.weChatAuth(openId,code,businessType);
    }

    /**
     * 商城产品列表查询
     * @param productClass
     * @return
     */
    @PostMapping(value = "/shop/productList")
    @ResponseBody
    public FebsResponse queryShopProductList(@RequestParam(name = "productClass",   required = false, defaultValue ="") String productClass, HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("商城产品列表查询=>产品分类:{},referer:{}",productClass,referer);
        return shopProductService.queryShopProductList(productClass);
    }


    /**
     * 商城支付
     * @param request
     * @return
     */
    @PostMapping(value = "/shop/pay")
    @ResponseBody
    public FebsResponse shopPay(HttpServletRequest request) {
        if(!this.shopPayLogin(request)){
            return new FebsResponse().needLogin().message("请登录");
        }
        String mobile = String.valueOf(TokenUtil.getMobile());
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String app = request.getHeader("x-requested-with");
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            String referer = request.getHeader("Referer");
            log.info("商城支付请求=>登录手机号:{},ip:{},app:{},json:{},ua:{},referer:{}",mobile, ipAddr, app, jsonData,userAgent,referer);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            if (Strings.isNullOrEmpty(subscribe.getRightsId())) {
                return new FebsResponse().fail().message("请选择权益产品");
            }
            if (Strings.isNullOrEmpty(subscribe.getChannel())) {
                return new FebsResponse().fail().message("渠道号错误");
            }
            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());
            return orderPayService.shopPay(subscribe,mobile);
        } catch (Exception e) {
            log.info("商城支付请求出错：", e);
        }
        return new FebsResponse().fail().message("系统错误");
    }
    private Boolean shopPayLogin(HttpServletRequest request){
        final String token = request.getHeader("token");
        String key = CacheConstant.CMS_CACHE_LOGIN_TOKEN;
        Map<Object, Object> entries = redisUtil.hmget(key);
        if(entries.values().contains(token)){
            return true;
        }
        final String mobile = request.getHeader("mobile");
        if (StringUtils.isNotBlank(mobile) && mobile.matches(BizConstant.MOBILE_REG)) {
            return true;
        }
        return false;
    }

    //微信用户支付通知
    @RequestMapping(value="wxpayResultNotify",produces="text/plain")
    @ResponseBody
    public String wxpayResultNotify(HttpServletRequest request) {

        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()){
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);

            WxpayNotifyParam notifyParam = wxpayService.payResultNotify(notifyXml);
            log.info("微信支付通知notifyParam = " + notifyParam);
            final String outTradeNo = notifyParam.getOutTradeNo();
            OrderPay order= orderPayService.lambdaQuery().eq(OrderPay::getOrderId,outTradeNo).eq(OrderPay::getStatus,-1).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
            if(order==null){
                log.error("微信支付通知未找到支付日志或者订单已支付,订单号:{}",outTradeNo);
                return succRespXml;
            }
            final String resultCode = notifyParam.getResultCode();
            boolean isPaySucc=WXPayConstants.SUCCESS.equals(resultCode);
            //更新支付日志
            order.setStatus(isPaySucc?1:0);
            order.setOutTradeNo(notifyParam.getTransactionId());
            order.setPayTime(DateUtil.localDateTimeToDate(notifyParam.getTimeEnd()));
            String remark="{\"totalFee\":\""+notifyParam.getTotalFee()+"\",\"appId\":\""+notifyParam.getAppId()+"\",\"mchId\":\""+notifyParam.getMchId()+"\",\"openId\":\"\""+notifyParam.getOpenId()+"\"}";
            order.setRemark(remark);
            orderPayService.updateById(order);
            if(isPaySucc){
                //校验权益是否包月以及领取
                IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(ShopRechargeRightsServiceImpl.class);
                FebsResponse memberVerifyFebs = businessRightsSubService.memberVerify(outTradeNo, SHOP_ORDER_SERVICE_ID);
                FebsResponse rechargRecordVerifyFebs = businessRightsSubService.rechargRecordVerify(outTradeNo, SHOP_ORDER_SERVICE_ID);
                if(memberVerifyFebs.isOK() && rechargRecordVerifyFebs.isOK()) {
                    businessRightsSubService.createScheduledRecharge(outTradeNo,order.getAccount(),SHOP_ORDER_SERVICE_ID, SHOP_ORDER_PACK_NAME,order.getRightsId(),order.getChannel());
                }
            }
            updateShopStatusAdFeedback(order.getMobile(), outTradeNo,isPaySucc);
        } catch (Exception e) {
            e.printStackTrace();
            return failRespXml;
        }
        return succRespXml;
    }



    //微信用户支付通知(jsapi)
    @PostMapping(value = "/wxpayJsAPIResultNotify")
    @ResponseBody
    public FebsResponse wxpayJsAPIResultNotify(HttpServletRequest request) {
        try {
            WechatPayDecodeNotifyParam notifyParam=wxpayService.jsApiPayResultNotify(request);
            log.info("微信支付通知notifyParam:{}",notifyParam);
            final String outTradeNo = notifyParam.getOutTradeNo();
            OrderPay order= orderPayService.lambdaQuery().eq(OrderPay::getOrderId,outTradeNo).eq(OrderPay::getStatus,-1).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
            if(order==null){
                log.error("微信支付通知未找到支付日志或者订单已支付,订单号:{}",outTradeNo);
                return new FebsResponse().code("SUCCESS").message("成功");
            }
            final String resultCode = notifyParam.getTradeState();
            boolean isPaySucc=WXPayConstants.SUCCESS.equals(resultCode);
            //更新支付日志
            order.setStatus(isPaySucc?1:0);
            order.setOutTradeNo(notifyParam.getTransactionId());
            order.setPayTime(DateUtil.parseStrToDate(notifyParam.getSuccessTime()));
            String remark="{\"totalFee\":\""+notifyParam.getAmount().getTotal()+"\",\"appId\":\""+notifyParam.getAppid()+"\",\"mchId\":\""+notifyParam.getMchid()+"\",\"openId\":\"\""+notifyParam.getPayer().getOpenid()+"\"}";
            order.setRemark(remark);
            orderPayService.updateById(order);
            if(isPaySucc){
                //校验权益是否包月以及领取
                IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(ShopRechargeRightsServiceImpl.class);
                FebsResponse memberVerifyFebs = businessRightsSubService.memberVerify(outTradeNo, SHOP_ORDER_SERVICE_ID);
                FebsResponse rechargRecordVerifyFebs = businessRightsSubService.rechargRecordVerify(outTradeNo, SHOP_ORDER_SERVICE_ID);
                if(memberVerifyFebs.isOK() && rechargRecordVerifyFebs.isOK()) {
                    businessRightsSubService.createScheduledRecharge(outTradeNo,order.getAccount(),SHOP_ORDER_SERVICE_ID, SHOP_ORDER_PACK_NAME,order.getRightsId(),order.getChannel());
                }
            }
            updateShopStatusAdFeedback(order.getMobile(), outTradeNo,isPaySucc);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new FebsResponse().code("SUCCESS").message("成功");
    }

  private void updateShopStatusAdFeedback(String mobile,String outTradeNo, boolean isPaySucc) {
        try{
            //修改渠道订单表状态
            Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo,outTradeNo).last("limit 1").one();
            if(subscribe!=null){
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setStatus(isPaySucc?BizConstant.SUBSCRIBE_STATUS_SUCCESS:BizConstant.SUBSCRIBE_STATUS_FAIL);
                upd.setResult("付费成功");
                subscribeService.updateSubscribeDbAndEs(upd);

                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe,upd.getStatus());
            }
        }catch (Exception e){
            log.error("微信商城用户支付回传出错,手机号:{},订单号:{}",mobile,outTradeNo,e);
        }
    }


    /**
     * 商城订单列表查询
     * @return
     */
    @PostMapping(value = "/shop/orderList")
    @ResponseBody
    @RightsLogin(menu = "商城订单列表查询")
    public FebsResponse queryShopOrdertList(HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("商城产品列表查询=>手机号:{},referer:{}",mobile,referer);
        return orderPayService.queryShopOrderList(mobile);
    }



    /**
     * 商城产品订单状态查询
     * @return
     */
    @PostMapping(value = "/query/shoporder")
    @ResponseBody
    public Result<?> queryShopOrder(@RequestParam("orderId")String orderId,HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("商城产品订单状态查询=>订单号:{},referer:{}",orderId,referer);
        if(StringUtils.isBlank(orderId)){
            return Result.error("系统错误");
        }
        return orderPayService.queryShopOrder(orderId);
    }


    /**
     * 根据id查询通用权益包
     * @param
     * @return
     */
    @PostMapping(value = "/query/rightsPackList/id")
    @ResponseBody
    public FebsResponse queryRightsListById(@RequestParam("id") String id, HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("根据id查询通用权益包=>渠道号:{},referer:{}",id,referer);
        if (Strings.isNullOrEmpty(id)) {
            return new FebsResponse().fail().message("id错误");
        }
        return rightsRechargeService.queryRightsListById(id);
    }



    /**
     * 联联分销查询通用权益包
     * @param channel
     * @return
     */
    @PostMapping(value = "/query/lianLianRightsPackList/channel")
    @ResponseBody
    @RightsLogin(menu = "联联分销查询通用权益包")
    public FebsResponse queryLianLianRightsListByChannel(@RequestParam(value = "channel", required = false, defaultValue ="") String channel,
                                                         @RequestParam(value = "orderId", required = false, defaultValue ="") String orderId, HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("联联分销根据渠道号查询通用权益包=>渠道号:{},订单号:{},referer:{}",channel,orderId,referer);
        if (!Strings.isNullOrEmpty(orderId)){
            CommonCoupon commonCoupon=commonCouponService.lambdaQuery().select(CommonCoupon::getChannel).eq(CommonCoupon::getCouponCode,orderId).orderByDesc(CommonCoupon::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(commonCoupon==null){
                return new FebsResponse().fail().message("激活码错误");
            }else{
                channel=commonCoupon.getChannel();
            }
        }
        if (Strings.isNullOrEmpty(channel)) {
            return new FebsResponse().fail().message("渠道号错误");
        }
        return rightsRechargeService.queryLianLianRightsListByChannel(channel,request);
    }

    /**
     * 联联分销通用权益领取
     * @param serviceId
     * @param packName
     * @param productId
     * @param itemId
     * @param idCard
     * @param customerName
     * @param address
     * @param memo
     * @param channel
     * @param request
     * @return
     */
    @PostMapping(value = "/lianLianScheduled/recharge")
    @ResponseBody
    @RightsLogin(menu = "联联分销通用权益领取")
    public FebsResponse createLianLianScheduledRecharge(@RequestParam("serviceId")String serviceId,
                                                        @RequestParam("packName")String packName,
                                                        @RequestParam("productId") String productId,
                                                        @RequestParam("itemId")String itemId,
                                                        @RequestParam(name ="orderId",   required = false, defaultValue ="")String orderId,
                                                        @RequestParam(name ="idCard",   required = false, defaultValue ="")String idCard,
                                                        @RequestParam(name ="customerName",   required = false, defaultValue ="")String customerName,
                                                        @RequestParam(name ="address",   required = false, defaultValue ="")String address,
                                                        @RequestParam(name ="memo",   required = false, defaultValue ="")String memo,
                                                        @RequestParam(name = "channel",   required = false, defaultValue ="") String channel,
                                                        @RequestParam(name = "travelDate",   required = false, defaultValue ="") String travelDate,
                                                        HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("联联分销通用权益领取=>手机号:{},权益领取业务ID:{},权益包:{},产品ID:{},套餐ID:{},渠道号:{},referer:{}",mobile,serviceId,packName,productId,itemId,channel,referer);
        if (Strings.isNullOrEmpty(serviceId) || Strings.isNullOrEmpty(packName)) {
            return new FebsResponse().fail().message("业务参数错误");
        }
        if (Strings.isNullOrEmpty(itemId) || Strings.isNullOrEmpty(productId)) {
            return new FebsResponse().fail().message("产品配置错误");
        }
        if (Strings.isNullOrEmpty(channel)) {
            CommonCoupon commonCoupon=commonCouponService.lambdaQuery().select(CommonCoupon::getChannel).eq(CommonCoupon::getCouponCode,orderId).orderByDesc(CommonCoupon::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(commonCoupon==null){
                return new FebsResponse().fail().message("激活码错误");
            }else{
                channel=commonCoupon.getChannel();
            }
        }
        if (Strings.isNullOrEmpty(channel)) {
            return new FebsResponse().fail().message("渠道号错误");
        }
        return rightsRechargeService.createLianLianScheduledRecharge(orderId,serviceId,packName,channel,mobile,productId,itemId,idCard,customerName,address,memo,travelDate,request);
    }
    /**
     * 校验激活码是否过期
     * @param orderId
     * @return
     */
    @PostMapping(value = "/check/order")
    @ResponseBody
    public FebsResponse checkOrder(@RequestParam(value = "orderId", required = false, defaultValue ="") String orderId, HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        final String mobile = request.getHeader("mobile");
        log.info("联联分销根据渠道号查询通用权益包=>订单号:{},referer:{}",orderId,referer);
        if (Strings.isNullOrEmpty(orderId)){
            return new FebsResponse().fail().message("激活码错误");
        }
        CommonCoupon commonCoupon=commonCouponService.lambdaQuery().select(CommonCoupon::getMobile).eq(CommonCoupon::getCouponCode,orderId).in(CommonCoupon::getStatus,BizConstant.NOT_USE,BizConstant.RECHARGE_FAIL).ge(CommonCoupon::getInvalidTime, DateUtil.formatSplitTime(LocalDateTime.now())).orderByDesc(CommonCoupon::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(commonCoupon==null){
            return new FebsResponse().fail().message("激活码错误");
        }
        if(!commonCoupon.getMobile().equals(mobile)){
            return new FebsResponse().fail("手机号错误");
        }
        return new FebsResponse().success();
    }
}

