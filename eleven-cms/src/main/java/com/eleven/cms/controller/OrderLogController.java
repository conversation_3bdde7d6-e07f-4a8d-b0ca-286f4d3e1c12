package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.OrderLog;
import com.eleven.cms.service.IOrderLogService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 订购日志
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
@Api(tags="订购日志")
@RestController
@RequestMapping("/cms/orderLog")
@Slf4j
public class OrderLogController extends JeecgController<OrderLog, IOrderLogService> {
	@Autowired
	private IOrderLogService orderLogService;
	
	/**
	 * 分页列表查询
	 *
	 * @param orderLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "订购日志-分页列表查询")
	@ApiOperation(value="订购日志-分页列表查询", notes="订购日志-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(OrderLog orderLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<OrderLog> queryWrapper = QueryGenerator.initQueryWrapper(orderLog, req.getParameterMap());
		Page<OrderLog> page = new Page<OrderLog>(pageNo, pageSize);
		IPage<OrderLog> pageList = orderLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param orderLog
	 * @return
	 */
	//@AutoLog(value = "订购日志-添加")
	@ApiOperation(value="订购日志-添加", notes="订购日志-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody OrderLog orderLog) {
		orderLogService.save(orderLog);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param orderLog
	 * @return
	 */
	//@AutoLog(value = "订购日志-编辑")
	@ApiOperation(value="订购日志-编辑", notes="订购日志-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody OrderLog orderLog) {
		orderLogService.updateById(orderLog);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "订购日志-通过id删除")
	@ApiOperation(value="订购日志-通过id删除", notes="订购日志-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		orderLogService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "订购日志-批量删除")
	@ApiOperation(value="订购日志-批量删除", notes="订购日志-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.orderLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "订购日志-通过id查询")
	@ApiOperation(value="订购日志-通过id查询", notes="订购日志-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		OrderLog orderLog = orderLogService.getById(id);
		if(orderLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(orderLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param orderLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrderLog orderLog) {
        return super.exportXls(request, orderLog, OrderLog.class, "订购日志");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OrderLog.class);
    }

}
