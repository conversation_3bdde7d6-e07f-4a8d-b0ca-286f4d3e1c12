package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.TelecomOrder;
import com.eleven.cms.service.ITelecomOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: cai lei
 * @create: 2021-08-27 10:42
 */
@Api(tags="天翼空间订单")
@RestController
@RequestMapping("/cms/telecomOrder")
@Slf4j
public class TelecomOrderController extends JeecgController<TelecomOrder, ITelecomOrderService> {


    @Autowired
    ITelecomOrderService telecomOrderService;


    @GetMapping(value = "/list")
    //@AutoLog(value = "天翼空间订单-分页列表查询")
    @ApiOperation(value="天翼空间订单-分页列表查询", notes="天翼空间订单-分页列表查询")
    public Result<?> telecomOrderList (HttpServletRequest req,
                                       @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                       @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                       TelecomOrder telecomOrder){
        QueryWrapper<TelecomOrder> queryWrapper = QueryGenerator.initQueryWrapper(telecomOrder, req.getParameterMap());
        Page<TelecomOrder> page = new Page<TelecomOrder>(pageNo, pageSize);
        IPage<TelecomOrder> pageList = telecomOrderService.page(page, queryWrapper);
        return Result.ok(pageList);
    }


    /**
     * 导出excel
     *
     * @param request
     * @param telecomOrder
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TelecomOrder telecomOrder) {
        return super.exportXls(request, telecomOrder, TelecomOrder.class, "天翼空间订单");
    }


}
