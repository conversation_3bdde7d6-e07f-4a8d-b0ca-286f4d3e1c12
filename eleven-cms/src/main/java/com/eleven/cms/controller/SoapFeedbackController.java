package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.SoapFeedback;
import com.eleven.cms.service.ISoapFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 订购日志
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Api(tags="订购日志")
@RestController
@RequestMapping("/cms/soapFeedback")
@Slf4j
public class SoapFeedbackController extends JeecgController<SoapFeedback, ISoapFeedbackService> {
//	@Autowired
//	private ISoapFeedbackService soapFeedbackService;
//
//	/**
//	 * 分页列表查询
//	 *
//	 * @param soapFeedback
//	 * @param pageNo
//	 * @param pageSize
//	 * @param req
//	 * @return
//	 */
//	//@AutoLog(value = "订购日志-分页列表查询")
//	@ApiOperation(value="订购日志-分页列表查询", notes="订购日志-分页列表查询")
//	@GetMapping(value = "/list")
//	public Result<?> queryPageList(SoapFeedback soapFeedback,
//								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//								   HttpServletRequest req) {
//		QueryWrapper<SoapFeedback> queryWrapper = QueryGenerator.initQueryWrapper(soapFeedback, req.getParameterMap());
//		Page<SoapFeedback> page = new Page<SoapFeedback>(pageNo, pageSize);
//		IPage<SoapFeedback> pageList = soapFeedbackService.page(page, queryWrapper);
//		return Result.ok(pageList);
//	}
//
//	/**
//	 *   添加
//	 *
//	 * @param soapFeedback
//	 * @return
//	 */
//	//@AutoLog(value = "订购日志-添加")
//	@ApiOperation(value="订购日志-添加", notes="订购日志-添加")
//	@PostMapping(value = "/add")
//	public Result<?> add(@RequestBody SoapFeedback soapFeedback) {
//		soapFeedbackService.save(soapFeedback);
//		return Result.ok("添加成功！");
//	}
//
//	/**
//	 *  编辑
//	 *
//	 * @param soapFeedback
//	 * @return
//	 */
//	//@AutoLog(value = "订购日志-编辑")
//	@ApiOperation(value="订购日志-编辑", notes="订购日志-编辑")
//	@PutMapping(value = "/edit")
//	public Result<?> edit(@RequestBody SoapFeedback soapFeedback) {
//		soapFeedbackService.updateById(soapFeedback);
//		return Result.ok("编辑成功!");
//	}
//
//	/**
//	 *   通过id删除
//	 *
//	 * @param id
//	 * @return
//	 */
//	//@AutoLog(value = "订购日志-通过id删除")
//	@ApiOperation(value="订购日志-通过id删除", notes="订购日志-通过id删除")
//	@DeleteMapping(value = "/delete")
//	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
//		soapFeedbackService.removeById(id);
//		return Result.ok("删除成功!");
//	}
//
//	/**
//	 *  批量删除
//	 *
//	 * @param ids
//	 * @return
//	 */
//	//@AutoLog(value = "订购日志-批量删除")
//	@ApiOperation(value="订购日志-批量删除", notes="订购日志-批量删除")
//	@DeleteMapping(value = "/deleteBatch")
//	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
//		this.soapFeedbackService.removeByIds(Arrays.asList(ids.split(",")));
//		return Result.ok("批量删除成功!");
//	}
//
//	/**
//	 * 通过id查询
//	 *
//	 * @param id
//	 * @return
//	 */
//	//@AutoLog(value = "订购日志-通过id查询")
//	@ApiOperation(value="订购日志-通过id查询", notes="订购日志-通过id查询")
//	@GetMapping(value = "/queryById")
//	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
//		SoapFeedback soapFeedback = soapFeedbackService.getById(id);
//		if(soapFeedback==null) {
//			return Result.error("未找到对应数据");
//		}
//		return Result.ok(soapFeedback);
//	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param soapFeedback
//    */
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, SoapFeedback soapFeedback) {
//        return super.exportXls(request, soapFeedback, SoapFeedback.class, "订购日志");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, SoapFeedback.class);
//    }

}
