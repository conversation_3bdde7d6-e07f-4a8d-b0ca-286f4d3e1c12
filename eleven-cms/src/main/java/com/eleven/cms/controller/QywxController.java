package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.Qywx;
import com.eleven.cms.service.IQywxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
* @Description: cms_qywx
* @Author: jeecg-boot
* @Date:   2023-04-17
* @Version: V1.0
*/
@Api(tags="cms_qywx")
@RestController
@RequestMapping("/cms/Qywx")
@Slf4j
public class QywxController extends JeecgController<Qywx, IQywxService> {
   @Autowired
   private IQywxService qywxService;

   /**
    * 分页列表查询
    *
    * @param Qywx
    * @param pageNo
    * @param pageSize
    * @param req
    * @return
    */
   //@AutoLog(value = "cms_qywx-分页列表查询")
   @ApiOperation(value="cms_qywx-分页列表查询", notes="cms_qywx-分页列表查询")
   @GetMapping(value = "/list")
   public Result<?> queryPageList(Qywx Qywx,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  HttpServletRequest req) {
       QueryWrapper<Qywx> queryWrapper = QueryGenerator.initQueryWrapper(Qywx, req.getParameterMap());
       Page<Qywx> page = new Page<Qywx>(pageNo, pageSize);
       IPage<Qywx> pageList = qywxService.page(page, queryWrapper);
       return Result.ok(pageList);
   }

   /**
    *   添加
    *
    * @param Qywx
    * @return
    */
   //@AutoLog(value = "cms_qywx-添加")
   @ApiOperation(value="cms_qywx-添加", notes="cms_qywx-添加")
   @PostMapping(value = "/add")
   public Result<?> add(@RequestBody Qywx Qywx) {
       qywxService.save(Qywx);
       return Result.ok("添加成功！");
   }

   /**
    *  编辑
    *
    * @param Qywx
    * @return
    */
   //@AutoLog(value = "cms_qywx-编辑")
   @ApiOperation(value="cms_qywx-编辑", notes="cms_qywx-编辑")
   @PutMapping(value = "/edit")
   public Result<?> edit(@RequestBody Qywx Qywx) {
       qywxService.updateById(Qywx);
       return Result.ok("编辑成功!");
   }

   /**
    *   通过id删除
    *
    * @param id
    * @return
    */
   //@AutoLog(value = "cms_qywx-通过id删除")
   @ApiOperation(value="cms_qywx-通过id删除", notes="cms_qywx-通过id删除")
   @DeleteMapping(value = "/delete")
   public Result<?> delete(@RequestParam(name="id",required=true) String id) {
       qywxService.removeById(id);
       return Result.ok("删除成功!");
   }

   /**
    *  批量删除
    *
    * @param ids
    * @return
    */
   //@AutoLog(value = "cms_qywx-批量删除")
   @ApiOperation(value="cms_qywx-批量删除", notes="cms_qywx-批量删除")
   @DeleteMapping(value = "/deleteBatch")
   public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
       this.qywxService.removeByIds(Arrays.asList(ids.split(",")));
       return Result.ok("批量删除成功!");
   }

   /**
    * 通过id查询
    *
    * @param id
    * @return
    */
   //@AutoLog(value = "cms_qywx-通过id查询")
   @ApiOperation(value="cms_qywx-通过id查询", notes="cms_qywx-通过id查询")
   @GetMapping(value = "/queryById")
   public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
       Qywx Qywx = qywxService.getById(id);
       if(Qywx==null) {
           return Result.error("未找到对应数据");
       }
       return Result.ok(Qywx);
   }

   /**
   * 导出excel
   *
   * @param request
   * @param Qywx
   */
   @RequestMapping(value = "/exportXls")
   public ModelAndView exportXls(HttpServletRequest request, Qywx Qywx) {
       return super.exportXls(request, Qywx, Qywx.class, "cms_qywx");
   }

}
