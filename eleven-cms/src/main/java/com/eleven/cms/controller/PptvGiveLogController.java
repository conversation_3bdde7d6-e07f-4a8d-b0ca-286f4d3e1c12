package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.PptvGiveLog;
import com.eleven.cms.service.IPptvGiveLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: cms_pptv_give_log
 * @Author: jeecg-boot
 * @Date:   2021-06-10
 * @Version: V1.0
 */
@Api(tags="cms_pptv_give_log")
@RestController
@RequestMapping("/cms/pptvGiveLog")
@Slf4j
public class PptvGiveLogController extends JeecgController<PptvGiveLog, IPptvGiveLogService> {
	@Autowired
	private IPptvGiveLogService pptvGiveLogService;

	/**
	 * 分页列表查询
	 *
	 * @param pptvGiveLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_pptv_give_log-分页列表查询")
	@ApiOperation(value="cms_pptv_give_log-分页列表查询", notes="cms_pptv_give_log-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(PptvGiveLog pptvGiveLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PptvGiveLog> queryWrapper = QueryGenerator.initQueryWrapper(pptvGiveLog, req.getParameterMap());
		Page<PptvGiveLog> page = new Page<PptvGiveLog>(pageNo, pageSize);
		IPage<PptvGiveLog> pageList = pptvGiveLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param pptvGiveLog
	 * @return
	 */
	//@AutoLog(value = "cms_pptv_give_log-添加")
	@ApiOperation(value="cms_pptv_give_log-添加", notes="cms_pptv_give_log-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody PptvGiveLog pptvGiveLog) {
		pptvGiveLogService.save(pptvGiveLog);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param pptvGiveLog
	 * @return
	 */
	//@AutoLog(value = "cms_pptv_give_log-编辑")
	@ApiOperation(value="cms_pptv_give_log-编辑", notes="cms_pptv_give_log-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody PptvGiveLog pptvGiveLog) {
		pptvGiveLogService.updateById(pptvGiveLog);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_pptv_give_log-通过id删除")
	@ApiOperation(value="cms_pptv_give_log-通过id删除", notes="cms_pptv_give_log-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		pptvGiveLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_pptv_give_log-批量删除")
	@ApiOperation(value="cms_pptv_give_log-批量删除", notes="cms_pptv_give_log-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pptvGiveLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_pptv_give_log-通过id查询")
	@ApiOperation(value="cms_pptv_give_log-通过id查询", notes="cms_pptv_give_log-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		PptvGiveLog pptvGiveLog = pptvGiveLogService.getById(id);
		if(pptvGiveLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(pptvGiveLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pptvGiveLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PptvGiveLog pptvGiveLog) {
        return super.exportXls(request, pptvGiveLog, PptvGiveLog.class, "cms_pptv_give_log");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PptvGiveLog.class);
    }

}
