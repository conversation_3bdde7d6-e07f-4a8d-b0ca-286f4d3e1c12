package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.WechatCouponConfig;
import com.eleven.cms.service.IWechatCouponConfigService;
import com.eleven.cms.service.IWxpayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 微信代金券配置
 * @Author: jeecg-boot
 * @Date:   2024-08-09
 * @Version: V1.0
 */
@Api(tags="微信代金券配置")
@RestController
@RequestMapping("/cms/wechatCouponConfig")
@Slf4j
public class WechatCouponConfigController extends JeecgController<WechatCouponConfig, IWechatCouponConfigService> {
	@Autowired
	private IWechatCouponConfigService wechatCouponConfigService;
	 @Autowired
	 IWxpayService wxpayService;
	/**
	 * 分页列表查询
	 *
	 * @param wechatCouponConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "微信代金券配置-分页列表查询")
	@ApiOperation(value="微信代金券配置-分页列表查询", notes="微信代金券配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(WechatCouponConfig wechatCouponConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WechatCouponConfig> queryWrapper = QueryGenerator.initQueryWrapper(wechatCouponConfig, req.getParameterMap());
		Page<WechatCouponConfig> page = new Page<WechatCouponConfig>(pageNo, pageSize);
		IPage<WechatCouponConfig> pageList = wechatCouponConfigService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param wechatCouponConfig
	 * @return
	 */
	//@AutoLog(value = "微信代金券配置-添加")
	@ApiOperation(value="微信代金券配置-添加", notes="微信代金券配置-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody WechatCouponConfig wechatCouponConfig) {
		WechatCouponConfig config=wechatCouponConfigService.lambdaQuery().eq(WechatCouponConfig::getIsValid, 1).orderByDesc(WechatCouponConfig::getCreateTime).last("limit 1").one();
		if(config!=null){
			wechatCouponConfig.setAppId(config.getAppId());
			wechatCouponConfig.setMchId(config.getMchId());
			wechatCouponConfig.setApiKey(config.getApiKey());
			wechatCouponConfig.setMchSerialNo(config.getMchSerialNo());
		}
		wechatCouponConfigService.save(wechatCouponConfig);
		if(wechatCouponConfig.getIsValid().intValue()>0){
			Result<?> result=wxpayService.weiXinActivateCode(wechatCouponConfig);
			if(result.isOK()){
				wechatCouponConfigService.lambdaUpdate().eq(WechatCouponConfig::getId,wechatCouponConfig.getId()).set(WechatCouponConfig::getIsValid,1).set(WechatCouponConfig::getRemark,result.getMessage()).update();
			}else{
				wechatCouponConfigService.lambdaUpdate().eq(WechatCouponConfig::getId,wechatCouponConfig.getId()).set(WechatCouponConfig::getIsValid,0).set(WechatCouponConfig::getRemark,result.getMessage()).update();
			}
		}
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param wechatCouponConfig
	 * @return
	 */
	//@AutoLog(value = "微信代金券配置-编辑")
	@ApiOperation(value="微信代金券配置-编辑", notes="微信代金券配置-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody WechatCouponConfig wechatCouponConfig) {
		WechatCouponConfig config=wechatCouponConfigService.lambdaQuery().eq(WechatCouponConfig::getIsValid, 1).orderByDesc(WechatCouponConfig::getCreateTime).last("limit 1").one();
		if(config!=null){
			wechatCouponConfig.setAppId(config.getAppId());
			wechatCouponConfig.setMchId(config.getMchId());
			wechatCouponConfig.setApiKey(config.getApiKey());
			wechatCouponConfig.setMchSerialNo(config.getMchSerialNo());
		}
		wechatCouponConfigService.updateById(wechatCouponConfig);
		if(wechatCouponConfig.getIsValid().intValue()>0){
			Result<?> result=wxpayService.weiXinActivateCode(wechatCouponConfig);
			if(result.isOK()){
				wechatCouponConfigService.lambdaUpdate().eq(WechatCouponConfig::getId,wechatCouponConfig.getId()).set(WechatCouponConfig::getIsValid,1).set(WechatCouponConfig::getRemark,result.getMessage()).update();
			}else{
				wechatCouponConfigService.lambdaUpdate().eq(WechatCouponConfig::getId,wechatCouponConfig.getId()).set(WechatCouponConfig::getIsValid,0).set(WechatCouponConfig::getRemark,result.getMessage()).update();
			}
		}
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "微信代金券配置-通过id删除")
	@ApiOperation(value="微信代金券配置-通过id删除", notes="微信代金券配置-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		wechatCouponConfigService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "微信代金券配置-批量删除")
	@ApiOperation(value="微信代金券配置-批量删除", notes="微信代金券配置-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wechatCouponConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "微信代金券配置-通过id查询")
	@ApiOperation(value="微信代金券配置-通过id查询", notes="微信代金券配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		WechatCouponConfig wechatCouponConfig = wechatCouponConfigService.getById(id);
		if(wechatCouponConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(wechatCouponConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wechatCouponConfig
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WechatCouponConfig wechatCouponConfig) {
        return super.exportXls(request, wechatCouponConfig, WechatCouponConfig.class, "微信代金券配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WechatCouponConfig.class);
    }

}
