package com.eleven.cms.controller;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.entity.OutsideBusinessConfig;
import com.eleven.cms.service.IOutsideBusinessConfigService;
import com.eleven.cms.vo.OutsideConfigPage;
import com.eleven.cms.vo.UserInfo;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.OutsideConfig;
import com.eleven.cms.service.IOutsideConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: cms_outside_config
 * @Author: jeecg-boot
 * @Date:   2023-04-26
 * @Version: V1.0
 */
@Api(tags="cms_outside_config")
@RestController
@RequestMapping("/cms/outsideConfig")
@Slf4j
public class OutsideConfigController extends JeecgController<OutsideConfig, IOutsideConfigService> {
    @Autowired
    private IOutsideConfigService outsideConfigService;
    @Autowired
    private IOutsideBusinessConfigService outsideBusinessConfigService;

    /**
     * 分页列表查询
     *
     * @param outsideConfig
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "cms_outside_config-分页列表查询")
    @ApiOperation(value="cms_outside_config-分页列表查询", notes="cms_outside_config-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(OutsideConfig outsideConfig,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<OutsideConfig> queryWrapper = QueryGenerator.initQueryWrapper(outsideConfig, req.getParameterMap());
        Page<OutsideConfig> page = new Page<OutsideConfig>(pageNo, pageSize);
        IPage<OutsideConfig> pageList = outsideConfigService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     *   添加
     *
     * @param outsideConfigPage
     * @return
     */
    //@AutoLog(value = "cms_outside_config-添加")
    @ApiOperation(value="cms_outside_config-添加", notes="cms_outside_config-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody OutsideConfigPage outsideConfigPage) {
        OutsideConfig outsideConfig = new OutsideConfig();
        BeanUtils.copyProperties(outsideConfigPage, outsideConfig);
        outsideConfigService.saveMain(outsideConfig, outsideConfigPage.getOutsideBusinessConfigList());
        return Result.ok("添加成功！");
    }

    /**
     *  编辑
     *
     * @param outsideConfigPage
     * @return
     */
    //@AutoLog(value = "cms_outside_config-编辑")
    @ApiOperation(value="cms_outside_config-编辑", notes="cms_outside_config-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody OutsideConfigPage outsideConfigPage) {
        OutsideConfig outsideConfig = new OutsideConfig();
        BeanUtils.copyProperties(outsideConfigPage, outsideConfig);
        OutsideConfig outsideConfigEntity = outsideConfigService.getById(outsideConfig.getId());
        if(outsideConfigEntity==null) {
            return Result.error("未找到对应数据");
        }
        outsideConfigService.updateMain(outsideConfig, outsideConfigPage.getOutsideBusinessConfigList());
        return Result.ok("编辑成功!");
    }

    /**
     *   通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_outside_config-通过id删除")
    @ApiOperation(value="cms_outside_config-通过id删除", notes="cms_outside_config-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        outsideConfigService.delMain(id);
        return Result.ok("删除成功!");
    }

    /**
     *  批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "cms_outside_config-批量删除")
    @ApiOperation(value="cms_outside_config-批量删除", notes="cms_outside_config-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.outsideConfigService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_outside_config-通过id查询")
    @ApiOperation(value="cms_outside_config-通过id查询", notes="cms_outside_config-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        OutsideConfig outsideConfig = outsideConfigService.getById(id);
        if(outsideConfig==null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(outsideConfig);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_outside_business_config通过主表ID查询")
    @ApiOperation(value="cms_outside_business_config主表ID查询", notes="cms_outside_business_config-通主表ID查询")
    @GetMapping(value = "/queryOutsideBusinessConfigByMainId")
    public Result<?> queryOutsideBusinessConfigListByMainId(@RequestParam(name="id",required=true) String id) {
        List<OutsideBusinessConfig> outsideBusinessConfigList = outsideBusinessConfigService.selectByMainId(id);
        return Result.ok(outsideBusinessConfigList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param outsideConfig
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OutsideConfig outsideConfig) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<OutsideConfig> queryWrapper = QueryGenerator.initQueryWrapper(outsideConfig, request.getParameterMap());
        LoginUser sysUser = HttpUtil.getCurrUser();

        //Step.2 获取导出数据
        List<OutsideConfig> queryList = outsideConfigService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<OutsideConfig> outsideConfigList = new ArrayList<OutsideConfig>();
        if(oConvertUtils.isEmpty(selections)) {
            outsideConfigList = queryList;
        }else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            outsideConfigList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }

        // Step.3 组装pageList
        List<OutsideConfigPage> pageList = new ArrayList<OutsideConfigPage>();
        for (OutsideConfig main : outsideConfigList) {
            OutsideConfigPage vo = new OutsideConfigPage();
            BeanUtils.copyProperties(main, vo);
            List<OutsideBusinessConfig> outsideBusinessConfigList = outsideBusinessConfigService.selectByMainId(main.getId());
            vo.setOutsideBusinessConfigList(outsideBusinessConfigList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "cms_outside_config列表");
        mv.addObject(NormalExcelConstants.CLASS, OutsideConfigPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("cms_outside_config数据", "导出人:"+sysUser.getRealname(), "cms_outside_config"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<OutsideConfigPage> list = ExcelImportUtil.importExcel(file.getInputStream(), OutsideConfigPage.class, params);
                for (OutsideConfigPage page : list) {
                    OutsideConfig po = new OutsideConfig();
                    BeanUtils.copyProperties(page, po);
                    outsideConfigService.saveMain(po, page.getOutsideBusinessConfigList());
                }
                return Result.ok("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }

}
