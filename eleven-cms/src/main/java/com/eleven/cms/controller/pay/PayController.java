package com.eleven.cms.controller.pay;


import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.eleven.cms.aiunion.entity.PayOrders;
import com.eleven.cms.aiunion.pojo.dto.DigitalRightDTO;
import com.eleven.cms.aiunion.service.IPayOrdersService;
import com.eleven.cms.dto.AlipayNotifyParam;
import com.eleven.cms.dto.WechatpayNotify;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.exception.BusinessException;
import com.eleven.cms.service.IAlipayService;
import com.eleven.cms.service.pay.AlipayTradeWapPay;
import com.eleven.cms.service.pay.IWechatPayAppService;
import com.eleven.cms.service.pay.PayNotifyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;

@RestController
@RequestMapping("/cms/pay/alipay")
@Slf4j
public class PayController {

    @Resource
    private AlipayTradeWapPay alipayTradeWapPay;

    @Resource
    private IPayOrdersService payOrdersService;

    @Resource
    private List<PayNotifyService> payNotifyServiceList;

    @Autowired
    private IAlipayService alipayService;

    @Resource
    IWechatPayAppService wechatPayAppService;


    @ApiOperation(value = "阿里支付查询支付状态")
    @PostMapping(value = "/queryPayResult")
    public Result<?> queryPayResult(@RequestBody DigitalRightDTO.QueryPayReq rightReq) {
        try {
            AlipayTradeQueryResponse payResult = alipayTradeWapPay.queryPayResult(rightReq.getOrderNo(),rightReq.getTradeNo());
            String tradeStatus = payResult.getTradeStatus();
            return Result.ok(tradeStatus);
        } catch (AlipayApiException e) {
            throw new BusinessException("查询支付状态失败");
        }
    }

    @ApiOperation(value = "全部退款")
    @PostMapping(value = "/allRefund")
    public Result<?> refund(@RequestBody DigitalRightDTO.QueryPayReq rightReq) {
        try {
            PayOrders payOrders = payOrdersService.getPayOrdersByOrderNo(rightReq.getOrderNo());
            AlipayTradeRefundResponse payResult = alipayTradeWapPay.allRefund(rightReq.getOrderNo(), payOrders.getAmount().toString(), rightReq.getRefundReason());
            String tradeStatus = payResult.getTradeNo();
            return Result.ok(tradeStatus);
        } catch (AlipayApiException e) {
            throw new BusinessException("退款失败");
        }
    }

    //支付宝用户支付通知
    @RequestMapping(value = "alipayResultNotify",produces="text/plain")
    @ResponseBody
    public String alipayResultNotify(@RequestParam Map<String,String> requestParams) {
        String succRespMsg = "success";
        String failRespMsg = "failure";
        try {
            log.info("支付宝支付通知notifyParam:{} ", JSONObject.toJSONString(requestParams));
            AlipayNotifyParam notifyParam = alipayService.payResultNotify(requestParams);
            if(notifyParam.getTradeStatus().equals("TRADE_SUCCESS")) {
                final String outTradeNo = notifyParam.getOutTradeNo();
                PayOrders payOrders = payOrdersService.getPayOrdersByOrderNo(outTradeNo);
                if (payOrders == null) {
                    return failRespMsg;
                }
                payOrders.setPayStatus(PayStatueEnum.PAID.getPayType());
                payOrders.setOutTradeNo(notifyParam.getTradeNo());
                payOrdersService.updateById(payOrders);
                for (PayNotifyService notifyService : payNotifyServiceList) {
                    if(Objects.equals(notifyService.getBusinessType().getType(),payOrders.getBusinessType())){
                        notifyService.handleNotify(payOrders.getOrderNo(),requestParams);
                    }
                }
            }
        } catch (Exception e) {
            log.error("支付宝支付通知出错：{}",requestParams,e);
            return failRespMsg;
        }
        return succRespMsg;
    }
    @RequestMapping(value = "/wechat/notify", produces = "text/plain")
    @ResponseBody
    public String wechatNotify(HttpServletRequest request) {
        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()) {
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            WechatpayNotify notifyParam = wechatPayAppService.payResultNotify(notifyXml);
            Map<String, String> notityMap = conVerMap(notifyParam);
            //支付成功
            final boolean paySuccess = notifyParam.getTradeState().equals("SUCCESS");
            log.info("微信支付通知notifyParam = " + notifyParam);
            if (paySuccess) {
                final String outTradeNo = notifyParam.getOutTradeNo();
                PayOrders payOrders = payOrdersService.getPayOrdersByOrderNo(outTradeNo);
                if (payOrders == null) {
                    return "查询订单失败";
                }
                payOrders.setPayStatus(PayStatueEnum.PAID.getPayType());
                payOrders.setOutTradeNo(outTradeNo);
                payOrdersService.updateById(payOrders);
                for (PayNotifyService notifyService : payNotifyServiceList) {
                    if(Objects.equals(notifyService.getBusinessType().getType(),payOrders.getBusinessType())){
                        notifyService.handleNotify(payOrders.getOrderNo(),notityMap);
                    }
                }
            }
        } catch (Exception e) {
            log.info("微信支付回调失败",e);
            return failRespXml;
        }
        return succRespXml;
    }

    private Map<String, String> conVerMap(WechatpayNotify notifyParam) {
        Map<String, String> map = new HashMap<>();
        if (notifyParam != null) {
            map.put("out_trade_no", notifyParam.getOutTradeNo());
            map.put("transaction_id", notifyParam.getTransactionId());
            map.put("trade_type", notifyParam.getTradeType());
            map.put("trade_state", notifyParam.getTradeState());
            map.put("trade_state_desc", notifyParam.getTradeStateDesc());
            map.put("bank_type", notifyParam.getBankType());
            map.put("success_time", notifyParam.getSuccessTime());
            map.put("payer_openid", Optional.ofNullable(notifyParam.getPayer()).map(WechatpayNotify.Payer::getOpenid).orElse(null));
            map.put("amount_total", String.valueOf(Optional.ofNullable(notifyParam.getAmount()).map(WechatpayNotify.Amount::getTotal).orElse(0)));
            map.put("amount_currency", Optional.ofNullable(notifyParam.getAmount()).map(WechatpayNotify.Amount::getCurrency).orElse(null));
        }
        return map;
    }

}
