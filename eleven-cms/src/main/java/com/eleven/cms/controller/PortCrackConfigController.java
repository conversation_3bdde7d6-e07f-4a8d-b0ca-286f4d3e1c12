package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.entity.TuniuCouponCodeChargeLog;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.util.BizConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: cms_port_crack_config
 * @Author: jeecg-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
@Api(tags = "cms_port_crack_config")
@RestController
@RequestMapping("/cms/portCrackConfig")
@Slf4j
public class PortCrackConfigController extends JeecgController<PortCrackConfig, IPortCrackConfigService> {
    @Autowired
    private IPortCrackConfigService portCrackConfigService;

    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    /**
     * 分页列表查询
     *
     * @param portCrackConfig
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "cms_port_crack_config-分页列表查询", notes = "cms_port_crack_config-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(PortCrackConfig portCrackConfig,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<PortCrackConfig> queryWrapper = QueryGenerator.initQueryWrapper(portCrackConfig, req.getParameterMap());
        Page<PortCrackConfig> page = new Page<PortCrackConfig>(pageNo, pageSize);
        IPage<PortCrackConfig> pageList = portCrackConfigService.page(page, queryWrapper);
        return Result.ok(pageList);
    }


    @ApiOperation(value = "cms_port_crack_config-详细查询", notes = "cms_port_crack_config-详细查询")
    @PostMapping(value = "/details")
    @ResponseBody
    public Result<?> queryDetails(@RequestParam(value = "channel", required = false, defaultValue ="")String channel) {
        if(StringUtils.isEmpty(channel)){
            return Result.error("渠道号不能为空");
        }
        CmsCrackConfig crackConfig=cmsCrackConfigService.lambdaQuery().select(CmsCrackConfig::getBizType).eq(CmsCrackConfig::getChannel,channel).orderByAsc(CmsCrackConfig::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(crackConfig==null){
            return Result.ok();
        }
        CmsCrackConfig config=cmsCrackConfigService.lambdaQuery().select(CmsCrackConfig::getChannel).eq(CmsCrackConfig::getBizType,crackConfig.getBizType()).orderByAsc(CmsCrackConfig::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(crackConfig==null){
            return Result.ok();
        }
        PortCrackConfig portCrack=portCrackConfigService.lambdaQuery().eq(PortCrackConfig::getChannel,config.getChannel()).orderByAsc(PortCrackConfig::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(portCrack==null){
            return Result.ok();
        }
        return Result.ok(portCrack);
    }

    /**
     * 添加
     *
     * @param portCrackConfig
     * @return
     */
    //@AutoLog(value = "cms_port_crack_config-添加")
    @ApiOperation(value = "cms_port_crack_config-添加", notes = "cms_port_crack_config-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody PortCrackConfig portCrackConfig) {
        portCrackConfigService.save(portCrackConfig);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param portCrackConfig
     * @return
     */
    //@AutoLog(value = "cms_port_crack_config-编辑")
    @ApiOperation(value = "cms_port_crack_config-编辑", notes = "cms_port_crack_config-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody PortCrackConfig portCrackConfig) {
        portCrackConfigService.updateCrackConfig(portCrackConfig);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_port_crack_config-通过id删除")
    @ApiOperation(value = "cms_port_crack_config-通过id删除", notes = "cms_port_crack_config-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        portCrackConfigService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "cms_port_crack_config-批量删除")
    @ApiOperation(value = "cms_port_crack_config-批量删除", notes = "cms_port_crack_config-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.portCrackConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }


    /**
     * 导出excel
     *
     * @param request
     * @param portCrackConfig
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PortCrackConfig portCrackConfig) {
        return super.exportXls(request, portCrackConfig, PortCrackConfig.class, "cms_port_crack_config");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PortCrackConfig.class);
    }
}
