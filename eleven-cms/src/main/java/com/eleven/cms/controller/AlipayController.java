package com.eleven.cms.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.service.IAlipayConfigService;
import com.eleven.cms.util.BizConstant;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.Alipay;
import com.eleven.cms.service.IAlipayService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 支付宝支付配置
 * @Author: jeecg-boot
 * @Date:   2022-12-29
 * @Version: V1.0
 */
@Api(tags="支付宝支付配置")
@RestController
@RequestMapping("/cms/alipay")
@Slf4j
public class AlipayController extends JeecgController<Alipay, IAlipayConfigService> {
	@Autowired
	private IAlipayConfigService alipayService;
	 @Autowired
	 private RedisUtil redisUtil;

	/**
	 * 分页列表查询
	 *
	 * @param alipay
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "支付宝支付配置-分页列表查询")
	@ApiOperation(value="支付宝支付配置-分页列表查询", notes="支付宝支付配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(Alipay alipay,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<Alipay> queryWrapper = QueryGenerator.initQueryWrapper(alipay, req.getParameterMap());
		Page<Alipay> page = new Page<Alipay>(pageNo, pageSize);
		IPage<Alipay> pageList = alipayService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param alipay
	 * @return
	 */
	//@AutoLog(value = "支付宝支付配置-添加")
	@ApiOperation(value="支付宝支付配置-添加", notes="支付宝支付配置-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody Alipay alipay) {
		alipayService.save(alipay);

		String alipaySwitchCacheWarnRedisKey = "alipay::cache::warn::switch:" + alipay.getBusinessType();
		redisUtil.set(alipaySwitchCacheWarnRedisKey,alipay.getIsSwitch());
		alipayService.destroyAll();
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param alipay
	 * @return
	 */
	//@AutoLog(value = "支付宝支付配置-编辑")
	@ApiOperation(value="支付宝支付配置-编辑", notes="支付宝支付配置-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody Alipay alipay) {
		alipayService.updateById(alipay);
		String alipaySwitchCacheWarnRedisKey = "alipay::cache::warn::switch:" + alipay.getBusinessType();
		redisUtil.set(alipaySwitchCacheWarnRedisKey,alipay.getIsSwitch());
		alipayService.destroyAll();
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "支付宝支付配置-通过id删除")
	@ApiOperation(value="支付宝支付配置-通过id删除", notes="支付宝支付配置-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		alipayService.removeById(id);
		alipayService.destroyAll();
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "支付宝支付配置-批量删除")
	@ApiOperation(value="支付宝支付配置-批量删除", notes="支付宝支付配置-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.alipayService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "支付宝支付配置-通过id查询")
	@ApiOperation(value="支付宝支付配置-通过id查询", notes="支付宝支付配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		Alipay alipay = alipayService.getById(id);
		if(alipay==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(alipay);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param alipay
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Alipay alipay) {
        return super.exportXls(request, alipay, Alipay.class, "支付宝支付配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Alipay.class);
    }

	 /**
	  * 查询渠道号配置
	  *
	  * @return
	  */
	 @GetMapping(value = "/query/channel/list")
	 public Result<?> queryChannelList() {
		 List<String> result = new ArrayList<>();
		 List<Alipay> list = alipayService.lambdaQuery().eq(Alipay::getIsValid, 1).list();
		 for (Alipay alipay : list) {
			 result.add(alipay.getBusinessType());
		 }
		 return Result.ok(result);
	 }


 }
