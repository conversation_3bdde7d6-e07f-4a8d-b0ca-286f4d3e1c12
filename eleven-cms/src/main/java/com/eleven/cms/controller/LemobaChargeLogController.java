package com.eleven.cms.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.entity.LemobaChargeLog;
import com.eleven.cms.service.ILemobaChargeLogService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_lemoba_charge_log
 * @Author: jeecg-boot
 * @Date:   2021-12-27
 * @Version: V1.0
 */
@Api(tags="乐摩吧充值记录")
@RestController
@RequestMapping("/cms/lemobaChargeLog")
@Slf4j
public class LemobaChargeLogController extends JeecgController<LemobaChargeLog, ILemobaChargeLogService> {
	@Autowired
	private ILemobaChargeLogService cmsLemobaChargeLogService;

	/**
	 * 分页列表查询
	 *
	 * @param cmsLemobaChargeLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_lemoba_charge_log-分页列表查询")
	@ApiOperation(value="cms_lemoba_charge_log-分页列表查询", notes="cms_lemoba_charge_log-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(LemobaChargeLog cmsLemobaChargeLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<LemobaChargeLog> queryWrapper = QueryGenerator.initQueryWrapper(cmsLemobaChargeLog, req.getParameterMap());
		Page<LemobaChargeLog> page = new Page<LemobaChargeLog>(pageNo, pageSize);
		IPage<LemobaChargeLog> pageList = cmsLemobaChargeLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param cmsLemobaChargeLog
	 * @return
	 */
	//@AutoLog(value = "cms_lemoba_charge_log-添加")
	@ApiOperation(value="cms_lemoba_charge_log-添加", notes="cms_lemoba_charge_log-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody LemobaChargeLog cmsLemobaChargeLog) {
		cmsLemobaChargeLogService.save(cmsLemobaChargeLog);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param cmsLemobaChargeLog
	 * @return
	 */
	//@AutoLog(value = "cms_lemoba_charge_log-编辑")
	@ApiOperation(value="cms_lemoba_charge_log-编辑", notes="cms_lemoba_charge_log-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody LemobaChargeLog cmsLemobaChargeLog) {
		cmsLemobaChargeLogService.updateById(cmsLemobaChargeLog);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_lemoba_charge_log-通过id删除")
	@ApiOperation(value="cms_lemoba_charge_log-通过id删除", notes="cms_lemoba_charge_log-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		cmsLemobaChargeLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_lemoba_charge_log-批量删除")
	@ApiOperation(value="cms_lemoba_charge_log-批量删除", notes="cms_lemoba_charge_log-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cmsLemobaChargeLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_lemoba_charge_log-通过id查询")
	@ApiOperation(value="cms_lemoba_charge_log-通过id查询", notes="cms_lemoba_charge_log-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		LemobaChargeLog cmsLemobaChargeLog = cmsLemobaChargeLogService.getById(id);
		if(cmsLemobaChargeLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(cmsLemobaChargeLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmsLemobaChargeLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LemobaChargeLog cmsLemobaChargeLog) {
        return super.exportXls(request, cmsLemobaChargeLog, LemobaChargeLog.class, "cms_lemoba_charge_log");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LemobaChargeLog.class);
    }

}
