package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.ad.BeilehuApiProperties;
import com.eleven.cms.dto.CouponCodeImport;
import com.eleven.cms.dto.CouponCodeTuNiuImport;
import com.eleven.cms.entity.CouponCode;
import com.eleven.cms.service.ICouponCodeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: cms_coupon_code
 * @Author: jeecg-boot
 * @Date:   2022-03-22
 * @Version: V1.0
 */
@Api(tags="cms_coupon_code")
@RestController
@RequestMapping("/cms/couponCode")
@Slf4j
public class CouponCodeController extends JeecgController<CouponCode, ICouponCodeService> {
	@Autowired
	private ICouponCodeService couponCodeService;
	@Autowired
	BeilehuApiProperties beilehuApiProperties;

	/**
	 * 分页列表查询
	 *
	 * @param couponCode
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_coupon_code-分页列表查询")
	@ApiOperation(value="cms_coupon_code-分页列表查询", notes="cms_coupon_code-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CouponCode couponCode,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CouponCode> queryWrapper = QueryGenerator.initQueryWrapper(couponCode, req.getParameterMap());
		Page<CouponCode> page = new Page<CouponCode>(pageNo, pageSize);
		IPage<CouponCode> pageList = couponCodeService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param couponCode
	 * @return
	 */
	//@AutoLog(value = "cms_coupon_code-添加")
	@ApiOperation(value="cms_coupon_code-添加", notes="cms_coupon_code-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CouponCode couponCode) {
		couponCodeService.save(couponCode);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param couponCode
	 * @return
	 */
	//@AutoLog(value = "cms_coupon_code-编辑")
	@ApiOperation(value="cms_coupon_code-编辑", notes="cms_coupon_code-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody CouponCode couponCode) {
		couponCodeService.updateById(couponCode);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_coupon_code-通过id删除")
	@ApiOperation(value="cms_coupon_code-通过id删除", notes="cms_coupon_code-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		couponCodeService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_coupon_code-批量删除")
	@ApiOperation(value="cms_coupon_code-批量删除", notes="cms_coupon_code-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.couponCodeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_coupon_code-通过id查询")
	@ApiOperation(value="cms_coupon_code-通过id查询", notes="cms_coupon_code-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CouponCode couponCode = couponCodeService.getById(id);
		if(couponCode==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(couponCode);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param couponCode
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CouponCode couponCode) {
        return super.exportXls(request, couponCode, CouponCode.class, "cms_coupon_code");
    }


	/**
	 * 生成卡密
	 * @param request
	 * @param coupon
	 * @return
	 */
	@RequestMapping(value = "/exportXls/template")
	public ModelAndView generateImportTemplate(HttpServletRequest request, CouponCode coupon) {
		List<CouponCode> couponCodeList = Lists.newArrayList();
		Date time=new Date();
		if(Integer.valueOf(coupon.getRightsCount())>0){
			for (int i = 0; i < Integer.valueOf(coupon.getRightsCount()); i++) {
				String sid = RandomStringUtils.randomAlphanumeric(10);
				CouponCode couponCode = new CouponCode();
				couponCode.setCouponCode(sid);
				couponCode.setRightsId(coupon.getRightsId());
				couponCode.setStatus(0);
				couponCode.setInvalidTime(coupon.getInvalidTime());
				couponCode.setCreateTime(time);
				couponCode.setUpdateTime(time);
				couponCodeList.add(couponCode);
			}
			if (couponCodeList !=null && couponCodeList.size()>0) {
				couponCodeService.saveBatch(couponCodeList);
			}
		}
		// 构建模板
		return super.downXlsx(CouponCode.class, "卡密生成",couponCodeList);

	}



	/**
	 * 通过excel导入价格数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importPriceExcel(HttpServletRequest request, HttpServletResponse response) {
		return this.importExcel(request);
	}
	/**
	 * 通过excel导入数据
	 * @param request
	 * @return
	 */
	public Result<?> importExcel(HttpServletRequest request) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			ImportParams params = new ImportParams();
			params.setTitleRows(0);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<CouponCodeImport> list = ExcelImportUtil.importExcel(file.getInputStream(), CouponCodeImport.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				List<CouponCode> couponCodeList= Lists.newArrayList();
				Date time=new Date();
				Date invalidTim=DateUtil.stringToDate("2050-12-31 23:59:59");
				for(CouponCodeImport item:list){
					if(StringUtils.isNotBlank(item.getCouponCode())){
						CouponCode couponCode=new CouponCode();
						couponCode.setStatus(0);
						couponCode.setCouponCode(item.getCouponCode());
						couponCode.setRightsId(item.getRightsId());
						couponCode.setInvalidTime(invalidTim);
						couponCode.setCreateTime(time);
						couponCode.setUpdateTime(time);
						couponCodeList.add(couponCode);
					}
				}
				if(couponCodeList!=null && couponCodeList.size()>0){
					couponCodeService.saveBatch(couponCodeList);
				}
				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				return Result.error("文件导入失败:" + e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
	}
	/**
	 * 激活码状态同步
	 * @param rightsId
	 * @return
	 */
	@PostMapping(value = "/sync")
	public Result<?> couponCodeSync(@RequestParam(name = "rightsId", required = false, defaultValue = "" ) String rightsId) {
		List<String> productList = beilehuApiProperties.getBeilehuProductMap().keySet().stream().collect(Collectors.toList());
		if(StringUtils.isNotBlank(rightsId)){
			productList=Lists.newArrayList(rightsId);
		}
		List<CouponCode> list=couponCodeService.lambdaQuery().in(CouponCode::getRightsId,productList).eq(CouponCode::getStatus, BizConstant.RECHARGE_WAIT).orderByDesc(CouponCode::getCreateTime).list();
		list.forEach(item -> {
			couponCodeService.updateCouponCodeStatusByMobile(item.getCouponCode(),item.getOrderId());
		});
		return Result.ok();

	}




	/**
	 * 通过excel导入价格数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importExcelTuNiu", method = RequestMethod.POST)
	public Result<?> importPriceExcelTuNiu(HttpServletRequest request, HttpServletResponse response) {
		return this.importExcelTuNiu(request);
	}
	/**
	 * 通过excel导入数据
	 * @param request
	 * @return
	 */
	public Result<?> importExcelTuNiu(HttpServletRequest request) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			ImportParams params = new ImportParams();
			params.setTitleRows(0);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<CouponCodeTuNiuImport> list = ExcelImportUtil.importExcel(file.getInputStream(), CouponCodeTuNiuImport.class, params);
				//update-begin-author:taoyan date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				List<CouponCode> couponCodeList= Lists.newArrayList();
				for(CouponCodeTuNiuImport item:list){
					if(StringUtils.isNotBlank(item.getCouponCode())){
						CouponCode coupon=couponCodeService.lambdaQuery().eq(CouponCode::getCouponCode,item.getCouponCode()).orderByDesc(CouponCode::getCreateTime).last("limit 1").one();
						if(coupon !=null){
							coupon.setInvalidTime(DateUtil.stringToDate(item.getInvalidTime()));
							coupon.setCreateTime(DateUtil.stringToDate(item.getCreateTime()));
							coupon.setCreateBy(item.getCreateBy());
							couponCodeList.add(coupon);
						}
					}
				}
				if(couponCodeList!=null && couponCodeList.size()>0){
					couponCodeService.saveOrUpdateBatch(couponCodeList);
				}
				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:taoyan date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.size());
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				return Result.error("文件导入失败:" + e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
	}
}
