package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.DataNotifyLog;
import com.eleven.cms.service.IDataNotifyLogService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 数据回执记录
 * @Author: jeecg-boot
 * @Date:   2023-04-23
 * @Version: V1.0
 */
@Api(tags="数据回执记录")
@RestController
@RequestMapping("/cms/dataNotifyLog")
@Slf4j
public class DataNotifyLogController extends JeecgController<DataNotifyLog, IDataNotifyLogService> {
	@Autowired
	private IDataNotifyLogService dataNotifyLogService;
	
	/**
	 * 分页列表查询
	 *
	 * @param dataNotifyLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "数据回执记录-分页列表查询")
	@ApiOperation(value="数据回执记录-分页列表查询", notes="数据回执记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DataNotifyLog dataNotifyLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DataNotifyLog> queryWrapper = QueryGenerator.initQueryWrapper(dataNotifyLog, req.getParameterMap());
		Page<DataNotifyLog> page = new Page<DataNotifyLog>(pageNo, pageSize);
		IPage<DataNotifyLog> pageList = dataNotifyLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param dataNotifyLog
	 * @return
	 */
	//@AutoLog(value = "数据回执记录-添加")
	@ApiOperation(value="数据回执记录-添加", notes="数据回执记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody DataNotifyLog dataNotifyLog) {
		dataNotifyLogService.save(dataNotifyLog);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param dataNotifyLog
	 * @return
	 */
	//@AutoLog(value = "数据回执记录-编辑")
	@ApiOperation(value="数据回执记录-编辑", notes="数据回执记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody DataNotifyLog dataNotifyLog) {
		dataNotifyLogService.updateById(dataNotifyLog);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "数据回执记录-通过id删除")
	@ApiOperation(value="数据回执记录-通过id删除", notes="数据回执记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		dataNotifyLogService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "数据回执记录-批量删除")
	@ApiOperation(value="数据回执记录-批量删除", notes="数据回执记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.dataNotifyLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "数据回执记录-通过id查询")
	@ApiOperation(value="数据回执记录-通过id查询", notes="数据回执记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		DataNotifyLog dataNotifyLog = dataNotifyLogService.getById(id);
		if(dataNotifyLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(dataNotifyLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param dataNotifyLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DataNotifyLog dataNotifyLog) {
        return super.exportXls(request, dataNotifyLog, DataNotifyLog.class, "数据回执记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DataNotifyLog.class);
    }

}
