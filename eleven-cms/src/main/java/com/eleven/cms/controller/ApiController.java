package com.eleven.cms.controller;

//import com.aliyun.dypnsapi20170525.models.*;
//import com.aliyun.tea.TeaException;
//import com.aliyun.teautil.models.RuntimeOptions;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.ad.AdReportService;
import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.client.UnifyRightsFeignClient;
import com.eleven.cms.config.*;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.*;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.log.BizLogConstants;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.*;
import com.eleven.cms.remote.CommonSecurityService;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.ReportPageService;
import com.eleven.cms.shanghaimobile.properties.ShanghaiMobileBusiness;
import com.eleven.cms.shanghaimobile.service.IShanghaiMobileService;
import com.eleven.cms.shanghaimobile.util.ShanghaiMobileConstant;
import com.eleven.cms.util.*;
import com.eleven.cms.util.junbo.Receiver;
import com.eleven.cms.vo.*;
import com.eleven.qycl.entity.QyclOrderPay;
import com.eleven.qycl.entity.QyclOrderPayLog;
import com.eleven.qycl.service.IQyclOrderPayLogService;
import com.eleven.qycl.service.IQyclOrderPayService;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.wxpay.sdk.WXPayConstants;
import com.google.api.client.util.Lists;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.Consts;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.WeChatLogin;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.ColumnClassEnum;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Size;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;

import static com.eleven.cms.util.BizConstant.*;

/**
 *   对外的api接口
 */
@Api(tags = "api")
@RestController
@RequestMapping("/api")
@Slf4j
@Validated
public class ApiController {
    private static final Interner<String> interner = Interners.newWeakInterner();
    private final ObjectMapper mapper = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    public static String NOTIFY_URL="https://crbt.cdyrjygs.com/cms-vrbt/api/mall/wechat/pay/notify";
    //退款失败
    private static final Integer REFUND_FAIL=2;
    //退款成功
    private static final Integer REFUND_SUCCESS=1;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private XunfeiJingxianVrbtService xunfeiJingxianVrbtService;
    @Autowired
    private LiantongVrbtService liantongVrbtService;
    @Autowired
    private IColumnService columnService;
    @Autowired
    private IMusicService musicService;
    @Autowired
    private ISmsValidateService smsValidateService;
    @Autowired
    private IColumnMusicService columnMusicService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IVisitLogService visitLogService;
    @Autowired
    private SubscribeLogService subscribeLogService;
    @Autowired
    private MobileRegionService mobileRegionService;
    @Autowired
    private IVrbtChannelProvinceConfigService vrbtChannelProvinceConfigService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private IAdPlatformService adPlatformService;
    @Autowired
    private IOrderVrbtService orderVrbtService;
    @Autowired
    private JunboApiProperties junboApiProperties;
    @Autowired
    private IMiguPackService miguPackService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IRightsPackService rightsPackService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IPPTVApiService pptvApiService;
    @Autowired
    private LemobaProperties lemobaProperties;
    @Autowired
    private DianxinVrbtService dianxinVrbtService;
    @Autowired
    private AdReportService adReportService;

    @Autowired
    private BizProperties bizProperties;
    @Autowired
    private IShanghaiMobileService shanghaiMobileService;
    @Autowired
    TianyiSpaceService tianyiSpaceService;
    @Autowired
    OutsideProperties outsideProperties;
    @Autowired
    IProvinceLimitService provinceLimitService;
    @Autowired
    private ICmsMusicTelecomService cmsMusicTelecomService;
    @Autowired
    LemobaApiService lemobaApiService;
    @Autowired
    ILemobaChargeLogService cmsLemobaChargeLogService;
    @Autowired
    DouyinAppService douyinAppService;
    @Autowired
    private ChongqingYidongVrbtService chongqingYidongVrbtService;
    @Autowired
    private BlackListService blackListService;
    @Autowired
    private ChongqingYidongVrbtProperties chongqingYidongVrbtProperties;
    @Autowired
    private SichuanMobileFlowPacketService sichuanMobileFlowPacketService;
    @Autowired
    private SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    SiChuanMobileApiService siChuanMobileApiService;
    @Autowired
    private  ICouponCodeService couponCodeService;
    @Autowired
    private IAdSiteService adSiteService;

    @Autowired
    private WXPayPropertiesConfig wxPayPropertiesConfig;

    @Autowired
    private SelfPhoneValidateProperties selfPhoneValidateProperties;

    @Autowired
    private IWxpayService wxpayService;

    @Autowired
    private IAlipayService alipayService;

    @Autowired
    private ISpclPayLogService spclPayLogService;


    @Autowired
    private UnifyRightsFeignClient unifyRightsFeignClient;
    @Autowired
    private AliVerifyPhoneProperties aliVerifyPhoneProperties;
    @Autowired
    IEsDataService esDataService;
    @Autowired
    ComplaintService complaintService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    InnerUnionMemberService innerUnionMemberService;
    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private ISmsMoLogService smsMoLogService;
    @Autowired
    private IMemberRightsService memberRightsService;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IQyclOrderPayService qyclOrderPayService;
    @Autowired
    private IQyclWxpayService qyclWxpayService;
    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    @Autowired
    private ICmsAdvInfoService cmsAdvInfoService;
    @Autowired
    private KugouApiService kugouApiService;
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    private AliSignUpConfig aliSignUpConfig;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    private IProvinceChannelPhoneConfigService provinceChannelPhoneConfigService;
    @Autowired
    private ICityBlackConfigService cityBlackConfigService;
    @Autowired
    private TxFeedbackProperties txFeedbackProperties;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    ChangShaAliPayNotifyService changShaAliPayNotifyService;
    @Autowired
    private IWoReadOrderService woReadOrderService;
    @Autowired
    private IKuaiShouService kuaiShouService;
    @Autowired
    IVrbtDiyRingService vrbtDiyRingService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    private ISmsSendLogService smsSendLogService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private IQyclOrderPayLogService orderPayLogService;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private ReportPageService reportPageService;
    @Autowired
    private CommonSecurityService commonSecurityService;
    @Autowired
    private ICpOrderResultService cpOrderResultService;

    @ApiOperation(value = "获取所有栏目的歌曲列表", notes = "获取所有栏目的歌曲列表")
    @RequestMapping(value = "/vrbtList")
    public Result<?> vrbtList() {

        columnService.lambdaQuery()
                .orderByAsc(Column::getPriority)
                .list();
        List<ColumnDetail> columnDetails = columnService.listColumnDetail();

        return Result.ok(columnDetails);
    }

    ////@AutoLog(value = "获取指定视频彩铃产品ID的详情")
    @ApiOperation(value = "获取指定视频彩铃产品ID的详情", notes = "获取指定视频彩铃产品ID的详情")
    @RequestMapping(value = "/vrbtInfo")
    public Result<?> vrbtInfo(String vrbtId) {

        MusicVo musicVo = musicService.findVrbtInfoByVrbtId(vrbtId);

        return Result.ok(musicVo);
    }

    ////@AutoLog(value = "获取指定栏目id的歌曲列表")
    @ApiOperation(value = "获取指定栏目id的歌曲列表", notes = "获取指定栏目id的歌曲列表")
    @RequestMapping(value = "/vrbtListPage")
    public Result<?> vrbtListPage(String columnId,
                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                  @RequestParam(name = "pageSize", defaultValue = "6") Integer pageSize,
                                  HttpServletRequest req) {

        Page<MusicVo> page = new Page<MusicVo>(pageNo, pageSize);
        IPage<MusicVo> columnMusicVoIPage = columnMusicService.selectColumnMusicVoPage(page, columnId);

        return Result.ok(columnMusicVoIPage);
    }

    @ApiOperation(value = "获取指定栏目id的歌曲列表(讯飞)", notes = "获取指定栏目id的歌曲列表")
    @RequestMapping(value = "/xf/vrbtListPage")
    public String xunfeiVrbtListPage(String columnId,
                                  @RequestParam(name = "pageNo", defaultValue = "1",required = false) Integer pageNo,
                                  HttpServletRequest req) {

        return xunfeiJingxianVrbtService.queryVrbtRing(columnId, pageNo);
    }

    /**
     * 发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getCaptcha")
    @ResponseBody
    public Result<?> getCaptcha(@RequestParam("mobile") String mobile, @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.create(mobile, channelCode);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }

        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }

    /**
     * 发送短信验证码(带省份限制)
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getLimitCode")
    @ResponseBody
    public Result<?> getLimitCode(@RequestParam("mobile") String mobile, @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if(mobileRegionResult==null || !mobileRegionResult.isIspYidong()){
            return Result.noauth("暂时未开放,敬请期待");
        }
        boolean allow = vrbtChannelProvinceConfigService.allow(channelCode, mobileRegionResult.getProvince());
        if(!allow){
            return Result.noauth("暂时未开放,敬请期待");
        }
        boolean result = smsValidateService.create(mobile, channelCode);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }

        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }

    /**
     * 乐摩吧咪咕直接登录
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/limitDirectLogin")
    @ResponseBody
    public Result<?> limitDirectLogin(@RequestParam("mobile") String mobile, @RequestParam(name = "channelCode") String channelCode,@RequestParam(name = "subChannel") String subChannel,HttpServletRequest request) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if(mobileRegionResult==null || !mobileRegionResult.isIspYidong()){
            return Result.noauth("暂时未开放,敬请期待");
        }
        boolean allow = vrbtChannelProvinceConfigService.allow(channelCode, mobileRegionResult.getProvince());
        if(!allow){
            return Result.noauth("暂时未开放,敬请期待");
        }
        RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
        if(!result.isOK()){
            return Result.noauth("登录失败");
        }
        final String ipAddr = IPUtils.getIpAddr(request);
        //新增彩铃中心订阅包数据
        com.eleven.cms.log.OrderLog orderLog=new com.eleven.cms.log.OrderLog();
        orderLog.setMobile(mobile);
        orderLog.setMiguChannel(channelCode);
        orderLog.setSubChannel(subChannel);
        Subscribe subscribe=subscribeService.saveBjhyLemobaSubscribe(orderLog,ipAddr);

        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        //查询本月是否发送乐摩吧权益
        Integer count = cmsLemobaChargeLogService.lambdaQuery().eq(LemobaChargeLog::getMobile, mobile)
                .eq(LemobaChargeLog::getRightsMonth, rightsMonth)
                .eq(LemobaChargeLog::getChannelCode, channelCode)
                .count();
        Result resp = Result.ok("登录成功");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("token", result.getToken());
        dataNode.put("orderId", subscribe.getId());
        dataNode.put("state", count);
        resp.setResult(dataNode);
        return resp;
    }



    ////@AutoLog(value = "咪咕登录")
    @ApiOperation(value = "咪咕登录", notes = "咪咕登录")
    @PostMapping(value = "/miguLogin")
    public Result<?> miguLogin(String mobile, String captcha, String channelCode,HttpServletRequest request) {

        if (Strings.isNullOrEmpty(mobile) || Strings.isNullOrEmpty(captcha)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!captcha.matches("^\\d{6}$")) {
            return Result.noauth("验证码错误");
        }
        //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.noauth(e.getMessage());
        }

        RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
        if (result != null && result.isOK()) {
            Result resp = Result.ok("登录成功");
            resp.setResult(result.getToken());
            return resp;
        } else {
            return Result.noauth("登录失败");
        }
    }

    /**
     * @param mobile
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "咪咕直接登录")
    @ApiOperation(value = "咪咕直接登录", notes = "咪咕直接登录")
    @PostMapping(value = "/directLogin")
    public Result<?> directLogin(String mobile, String channelCode, HttpServletRequest request) {

        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        final String ipAddr = IPUtils.getIpAddr(request);

        log.info("咪咕直接登录=>手机号:{},渠道号:{},ip:{}", mobile, channelCode, ipAddr);

        RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
        if (result.isOK()) {
            Result resp = Result.ok("登录成功");
            resp.setResult(result.getToken());
            return resp;
        } else {
            return Result.noauth("登录失败");
        }
    }

    /**
     * @param msisdn
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "解密咪咕AES加密手机号", notes = "解密咪咕AES加密手机号")
    @PostMapping(value = "/transMsisdn")
    public Result<?> transMsisdn(String msisdn, String channelCode, HttpServletRequest request) {

        if (Strings.isNullOrEmpty(msisdn)) {
            return Result.noauth("参数错误");
        }
        final String ipAddr = IPUtils.getIpAddr(request);
        String  mobile = miguApiService.decryptMobile(msisdn, channelCode);
        log.info("解密咪咕AES加密手机号=>encryptedMsisdn:{},渠道号:{},mobile:{},ip:{}", msisdn, channelCode, mobile, ipAddr);

        return Strings.isNullOrEmpty(mobile) ? Result.error("操作失败") : Result.okAndSetData(mobile);
    }

    /**
     * 短信登录,只验证短信验证码,不去咪咕服务端登录,前端会在js登录
     *
     * @param mobile
     * @param captcha
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "短信登录")
    @ApiOperation(value = "短信登录", notes = "短信登录")
    @PostMapping(value = "/smsLogin")
    public Result<?> smsLogin(String mobile, String captcha, String channelCode) {

        if (Strings.isNullOrEmpty(mobile) || Strings.isNullOrEmpty(captcha)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!captcha.matches("^\\d{6}$")) {
            return Result.noauth("验证码错误");
        }
        //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.noauth(e.getMessage());
        }

        return Result.ok("登录成功");

    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "开通视频彩铃功能")
    @ApiOperation(value = "开通视频彩铃功能", notes = "开通视频彩铃功能")
    @PostMapping(value = "/vrbtOpen")
    @JsonView(RemoteResult.BasicView.class)
    public RemoteResult vrbtOpen(@RequestParam(name = "token") String mobileOrToken, @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {
        //return RemoteResult.fail("参数错误");

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.vrbtOpen(mobileOrToken, channelCode);
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "退订视频彩铃功能")
    @ApiOperation(value = "退订视频彩铃功能", notes = "退订视频彩铃功能")
    @PostMapping(value = "/vrbtCancel")
    @JsonView(RemoteResult.BasicView.class)
    public RemoteResult vrbtCancel(@RequestParam(name = "token") String mobileOrToken, @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {
        //return RemoteResult.fail("参数错误");

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.vrbtCancel(mobileOrToken, channelCode);
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "查询视频彩铃包月状态")
    @ApiOperation(value = "查询视频彩铃包月状态", notes = "查询视频彩铃包月状态")
    @PostMapping(value = "/vrbtMonthStatusQuery")
    @JsonView(RemoteResult.StatusQueryView.class)
    public RemoteResult vrbtMonthStatusQuery(@RequestParam(name = "token") String mobileOrToken, String channelCode) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.vrbtMonthStatusQuery(mobileOrToken, channelCode, true);
    }


    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode   //@param contentId
     * @param copyRightID
     * @param setFlag
     * @return
     */
    ////@AutoLog(value = "视频彩铃包月内内容免费订购")
    @ApiOperation(value = "视频彩铃包月内内容免费订购", notes = "视频彩铃包月内内容免费订购")
    @PostMapping(value = "/vrbtToneFreeMonthOrder")
    @JsonView(RemoteResult.ToneFreeMonthOrderView.class)
    public RemoteResult vrbtToneFreeMonthOrder(@RequestParam(name = "mobile") String mobileOrToken, String channelCode, String copyRightID, String setFlag) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.vrbtToneFreeMonthOrder(mobileOrToken, channelCode, copyRightID, setFlag);
    }

    /**
     * 主叫视频彩铃内容免费订购接口P23A6
     * @param mobileOrToken 手机号或者token
     * @param channelCode   渠道号
     * @param vrbtId 11位版权id或者18位产品id
     * @param setFlag 是否添加到个人默认铃音播放设置：默认为1 ；
     *      * 0：不添加设置 ；
     *      * 1订购且设置加入当前个人主叫默认铃音播放设置中；
     *      * 2订购且设置为当前唯一个人主叫视频默认铃音播放设置铃音；
     * @param ifSendSMS 是否需要发送内容订购提醒短信，默认为0
     *      * 0：发送 1：不发送
     * @return
     */
    ////@AutoLog(value = "主叫视频彩铃内容免费订购")
    @ApiOperation(value = "主叫视频彩铃内容免费订购", notes = "主叫视频彩铃内容免费订购")
    @PostMapping(value = "/activeVrbtFreeOrder")
    @JsonView(RemoteResult.ToneFreeMonthOrderView.class)
    public RemoteResult activeVrbtFreeOrder(@RequestParam(name = "mobile") String mobileOrToken,
                                            String channelCode,
                                            String vrbtId,
                                            @RequestParam(name = "channelCode", required = false, defaultValue = "1")String setFlag,
                                            @RequestParam(name = "channelCode", required = false, defaultValue = "0")String ifSendSMS) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.activeVrbtFreeOrder(mobileOrToken, channelCode, vrbtId, setFlag, ifSendSMS);
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode   //@param contentId
     * @param copyRightID
     * @param settingType 都不选:"0",被叫:"1",主叫:"2",都选:"3"
     * @return
     */
    ////@AutoLog(value = "视频彩铃包月内内容免费订购(主被叫)")
    //@ApiOperation(value = "视频彩铃包月内内容免费订购(主被叫)", notes = "视频彩铃包月内内容免费订购(主被叫)")
    //@PostMapping(value = "/vrbtToneOrderAndSetting")
    //@JsonView(RemoteResult.ToneFreeMonthOrderView.class)
    //public RemoteResult vrbtToneOrderAndSetting(@RequestParam(name = "mobile") String mobileOrToken, String channelCode, String copyRightID, String settingType) {
    //    if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
    //        return RemoteResult.fail("参数错误");
    //    }
    //    if (org.apache.commons.lang3.StringUtils.equalsAny(settingType, "0", "1")) {
    //        return miguApiService.vrbtToneFreeMonthOrder(mobileOrToken, channelCode, copyRightID, settingType);
    //    } else if (org.apache.commons.lang3.StringUtils.equals(settingType, "2")) {
    //        return miguApiService.vrbtInitiativeSetting(mobileOrToken, channelCode, copyRightID);
    //    } else {
    //        RemoteResult vrbtToneFreeMonthOrder = miguApiService.vrbtToneFreeMonthOrder(mobileOrToken, channelCode, copyRightID, "1");
    //        RemoteResult vrbtInitiativeSetting = miguApiService.vrbtInitiativeSetting(mobileOrToken, channelCode, copyRightID);
    //        return vrbtToneFreeMonthOrder.isOK() ? vrbtInitiativeSetting : vrbtToneFreeMonthOrder;
    //    }
    //}

    /**
     * @param mobile      手机号,不能传token,因为要根据手机号更新数据
     * @param channelCode //@param contentId
     * @param copyRightID
     * @param setFlag
     * @return
     */
    ////@AutoLog(value = "视频彩铃包月内内容免费订购(延迟)")
    @ApiOperation(value = "视频彩铃包月内内容免费订购(延迟)", notes = "视频彩铃包月内内容免费订购(延迟)")
    @PostMapping(value = "/vrbtToneFreeMonthOrderDelay")
    @JsonView(RemoteResult.ToneFreeMonthOrderView.class)
    public RemoteResult vrbtToneFreeMonthOrderDelay(@RequestParam(name = "mobile") String mobile, String channelCode, String copyRightID, String setFlag) {

        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }
        //异步延迟订购视频彩铃
        orderVrbtService.vrbtToneFreeOrderDelay(mobile, channelCode, copyRightID, setFlag);

        return RemoteResult.success();
    }

    /**
     * 查询视频彩铃功能状态
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "查询视频彩铃功能状态")
    @ApiOperation(value = "查询视频彩铃功能状态", notes = "查询视频彩铃功能状态")
    @PostMapping(value = "/vrbtStatusQuery")
    @JsonView(RemoteResult.StatusQueryView.class)
    public RemoteResult vrbtStatusQuery(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.vrbtStatusQuery(mobileOrToken, channelCode);
    }

    /**
     * 主叫视频彩铃功能查询接口P23A5
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "主叫视频彩铃功能查询")
    @ApiOperation(value = "主叫视频彩铃功能查询", notes = "主叫视频彩铃功能查询")
    @PostMapping(value = "/activeVrbtQuery")
    @JsonView(RemoteResult.StatusQueryView.class)
    public RemoteResult activeVrbtQuery(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.activeVrbtQuery(mobileOrToken, channelCode);
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "查询视频彩铃VoLTE功能状态")
    @ApiOperation(value = "查询视频彩铃VoLTE功能状态", notes = "查询视频彩铃VoLTE功能状态")
    @PostMapping(value = "/vrbtVoLTEStatusQuery")
    @JsonView(RemoteResult.StatusQueryView.class)
    public RemoteResult vrbtVoLTEStatusQuery(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.vrbtVoLTEStatusQuery(mobileOrToken, channelCode);
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "查询人铃音库视频彩铃内容")
    @ApiOperation(value = "查询人铃音库视频彩铃内容", notes = "查询人铃音库视频彩铃内容")
    @PostMapping(value = "/vrbtToneQuery", produces = MediaType.APPLICATION_JSON_VALUE)
    public String vrbtToneQuery(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode,
                                @RequestParam(name = "startRecordNum", required = false) String startRecordNum,
                                @RequestParam(name = "endRecordNum", required = false) String endRecordNum,
                                @RequestParam(name = "queryType", required = false) String queryType,
                                @RequestParam(name = "toneType", required = false) String toneType) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.failJson("参数错误");
        }

        try {
            return miguApiService.vrbtToneQuery(mobileOrToken, channelCode, startRecordNum, endRecordNum, queryType, toneType);
        } catch (Exception e) {
            return RemoteResult.failJson(e.getMessage());
        }
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "视频彩铃内容删除")
    @ApiOperation(value = "视频彩铃内容删除", notes = "视频彩铃内容删除")
    @PostMapping(value = "/vrbtToneDelete")
    @JsonView(RemoteResult.BasicView.class)
    public RemoteResult vrbtToneDelete(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode, String toneID) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(toneID)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.vrbtToneDelete(mobileOrToken, channelCode, toneID);
    }

    @ApiOperation(value = "个人铃音库清理", notes = "个人铃音库清理")
    @PostMapping(value = "/clearUserTone")
    @JsonView(RemoteResult.BasicView.class)
    public RemoteResult clearUserTone(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }
        miguApiService.clearUserTone(mobileOrToken,channelCode);

        return RemoteResult.success();
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "获取视频彩铃试看地址")
    @ApiOperation(value = "获取视频彩铃试看地址", notes = "获取视频彩铃试看地址")
    @PostMapping(value = "/vrbtTryToSee", produces = MediaType.APPLICATION_JSON_VALUE)
    public String vrbtTryToSee(@RequestParam(name = "mobileOrToken", required = false) String mobileOrToken, String channelCode, String vrbtId) {

        if (/*StringUtils.isEmpty(mobileOrToken)|| */StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(vrbtId)) {
            return RemoteResult.failJson("参数错误");
        }

        try {
            return miguApiService.vrbtTryToSee(mobileOrToken, channelCode, vrbtId);
        } catch (Exception e) {
            return RemoteResult.failJson(e.getMessage());
        }
    }

    /**
     * @param mobileOrToken 手机号或者token  可不传
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "获取歌曲在线听地址", notes = "获取歌曲在线听地址")
    @PostMapping(value = "/streamQuery", produces = MediaType.APPLICATION_JSON_VALUE)
    public String streamQuery(@RequestParam(name = "mobileOrToken", required = false) String mobileOrToken, String channelCode, String contentId) {

        if (/*StringUtils.isEmpty(mobileOrToken)|| */StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(contentId)) {
            return RemoteResult.failJson("参数错误");
        }
        try {
            return miguApiService.streamQuery(mobileOrToken, channelCode, contentId);
        } catch (Exception e) {
            return RemoteResult.failJson(e.getMessage());
        }
    }

    /**
     *  copyrightId 18位内容ID,11位版权ID,12位版权ID
     * 使用咪咕服务端安全接口来抓取图片地址  查找分别率为9:16或者接近这个分辨率的图片
     * @return
     */
    @ApiOperation(value = "查询视频彩铃产品信息", notes = "查询视频彩铃产品信息")
    @PostMapping(value = "/fetchVrbtProduct", produces = MediaType.APPLICATION_JSON_VALUE)
    public VrbtProduct fetchVrbtProduct(String vrbtId) {
        return miguApiService.fetchVrbtProduct(vrbtId);
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "查询视频彩铃信息")
    @ApiOperation(value = "查询视频彩铃信息", notes = "查询视频彩铃信息")
    @PostMapping(value = "/vrbtProductQuery", produces = MediaType.APPLICATION_JSON_VALUE)
    public String vrbtProductQuery(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode, String vrbtId) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(vrbtId)) {
            return RemoteResult.failJson("参数错误");
        }

        try {
            return miguApiService.vrbtProductQuery(mobileOrToken, channelCode, vrbtId);
        } catch (Exception e) {
            return RemoteResult.failJson(e.getMessage());
        }
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "视频彩铃功能状态和视频彩铃包月状态二合一查询接口")
    @ApiOperation(value = "视频彩铃功能状态和视频彩铃包月状态二合一查询接口", notes = "视频彩铃功能状态和视频彩铃包月状态二合一查询接口")
    @PostMapping(value = "/vrbtFunAndMonthStatusQuery", produces = MediaType.APPLICATION_JSON_VALUE)
    public VrbtCombinResult vrbtFunAndMonthStatusQuery(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return VrbtCombinResult.fail("参数错误");
        }
        return miguApiService.vrbtFunAndMonthStatusQuery(mobileOrToken, channelCode);
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "白金会员获取试听地址")
    @ApiOperation(value = "白金会员获取试听地址", notes = "白金会员获取试听地址")
    @PostMapping(value = "/bjhyAudition", produces = MediaType.APPLICATION_JSON_VALUE)
    public String bjhyAudition(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode, String contentId) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(contentId)) {
            return RemoteResult.failJson("参数错误");
        }

        try {
            return miguApiService.bjhyAudition(mobileOrToken, channelCode, contentId);
        } catch (Exception e) {
            return RemoteResult.failJson(e.getMessage());
        }
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "白金会员获取下载地址")
    @ApiOperation(value = "白金会员获取下载地址", notes = "白金会员获取下载地址")
    @PostMapping(value = "/bjhyDownlink", produces = MediaType.APPLICATION_JSON_VALUE)
    public String bjhyDownlink(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode, String contentId) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(contentId)) {
            return RemoteResult.failJson("参数错误");
        }

        try {
            return miguApiService.bjhyDownlink(mobileOrToken, channelCode, contentId);
        } catch (Exception e) {
            return RemoteResult.failJson(e.getMessage());
        }
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "5元白金会员获取试听地址")
    @ApiOperation(value = "5元白金会员获取试听地址", notes = "5元白金会员获取试听地址")
    @PostMapping(value = "/bjhy5Audition", produces = MediaType.APPLICATION_JSON_VALUE)
    public String bjhy5Audition(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode, String contentId) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(contentId)) {
            return RemoteResult.failJson("参数错误");
        }

        try {
            return miguApiService.bjhy5Audition(mobileOrToken, channelCode, contentId);
        } catch (Exception e) {
            return RemoteResult.failJson(e.getMessage());
        }
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "5元白金会员获取下载地址")
    @ApiOperation(value = "5元白金会员获取下载地址", notes = "5元白金会员获取下载地址")
    @PostMapping(value = "/bjhy5Downlink", produces = MediaType.APPLICATION_JSON_VALUE)
    public String bjhy5Downlink(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode, String contentId) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(contentId)) {
            return RemoteResult.failJson("参数错误");
        }

        try {
            return miguApiService.bjhy5Downlink(mobileOrToken, channelCode, contentId);
        } catch (Exception e) {
            return RemoteResult.failJson(e.getMessage());
        }
    }

    /**
     * @param mobile 手机号
     * @return
     */
    ////@AutoLog(value = "视频彩铃功能及包月状态复合查询")
    @ApiOperation(value = "视频彩铃功能及包月状态复合查询", notes = "视频彩铃功能及包月状态复合查询")
    @PostMapping(value = "/vrbtCombinQuery")
    public VrbtCombinResult vrbtCombinQuery(@RequestParam(name = "mobile") String mobile,
                                            @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {

        if (StringUtils.isEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return VrbtCombinResult.fail("请输入正确格式手机号");
        }
        return miguApiService.vrbtCombinQuery(mobile, channelCode);
    }

    /**
     * @param mobile 手机号
     * @channelCode 渠道号
     * @return
     */
    @ApiOperation(value = "渠道包复合查询", notes = "渠道包复合查询")
    @PostMapping(value = "/cpmbCombinQuery")
    public CpmbCombinResult cpmbCombinQuery(@RequestParam(name = "mobile") String mobile,
                                            @RequestParam(name = "channelCode") String channelCode) {

        if (StringUtils.isEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return CpmbCombinResult.fail("请输入正确格式手机号");
        }
        return miguApiService.cpmbCombinQuery(mobile, channelCode);
    }

    /**
     * @param mobileOrToken
     *         手机号或者token
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "查询白金会员/音乐包/视彩号包月订购关系", notes = "查询白金会员/音乐包/视彩号包月订购关系")
    @PostMapping(value = {"/schQuery", "/bjhyAndCpmbQuery"})
    public RemoteResult bjhyAndCpmbQuery(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode,@RequestParam(name = "serviceType",required = false) String serviceType) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode) ) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.bjhyAndCpmbQuery(mobileOrToken, channelCode, serviceType);
    }

    /**
     * 视频彩铃功能及包月状态复合查询(加密)
     * @param encryptData 加密的json数据,包含字段:mobil=手机号,channelCode=咪咕渠道号,subChannel=推广子渠道号
     * @return
     */
    @ApiOperation(value = "视频彩铃功能及包月状态复合查询", notes = "视频彩铃功能及包月状态复合查询")
    @PostMapping(value = "/vrbtIntentQuery")
    public VrbtCombinResult vrbtIntentQuery(@RequestBody String encryptData) throws JsonProcessingException {
        //System.out.println("encryptData = " + encryptData);
        String json = Cryptos.aesDecrypt(Base64.getDecoder().decode(encryptData), AES_KEY_FOR_DOUYIN_VRBT.getBytes(StandardCharsets.UTF_8));
        JsonNode tree = mapper.readTree(json);
        String mobile = tree.at("/mobile").asText();
        String channelCode = tree.at("/channelCode").asText();
        String subChannel = tree.at("/subChannel").asText();
        if (StringUtils.isEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return VrbtCombinResult.fail("请输入正确格式手机号");
        }
        if (StringUtils.isEmpty(channelCode) || StringUtils.isEmpty(subChannel)) {
            return VrbtCombinResult.fail("参数缺失");
        }
        return  miguApiService.vrbtCombinQuery(mobile, channelCode);
    }


    @ApiOperation(value = "咪咕登录并查询视频彩铃功能及包月状态", notes = "咪咕登录并查询视频彩铃功能及包月状态")
    @PostMapping(value = "/loginWithVrbtCombinQuery")
    public VrbtCombinResult loginWithVrbtCombinQuery(String mobile, String captcha, String channelCode) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return VrbtCombinResult.fail("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return VrbtCombinResult.fail("验证码错误");
        }
        //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return VrbtCombinResult.fail(e.getMessage());
        }

        return miguApiService.vrbtCombinQuery(mobile, channelCode);
    }

    /**
     * @param mobile 手机号
     * @return
     */
    ////@AutoLog(value = "视频彩铃功能及包月状态复合查询")
    @ApiOperation(value = "视频彩铃功能及包月状态复合查询", notes = "视频彩铃功能及包月状态复合查询")
    @PostMapping(value = "/vrbtLimitQuery")
    public VrbtCombinResult vrbtLimitQuery(@RequestParam(name = "mobile") String mobile,
                                            @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {

        if (StringUtils.isEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return VrbtCombinResult.fail("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if(mobileRegionResult==null || !mobileRegionResult.isIspYidong()){
            return VrbtCombinResult.fail("暂时未开放,敬请期待");
        }
        boolean allow = vrbtChannelProvinceConfigService.allow(channelCode, mobileRegionResult.getProvince());
        if(!allow){
            return VrbtCombinResult.fail("暂时未开放,敬请期待");
        }
        return miguApiService.vrbtCombinQuery(mobile, channelCode);
    }

    /**
     * @param mobileOrToken 手机号或者token
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "视频彩铃包月包退订", notes = "视频彩铃包月包退订")
    @PostMapping(value = "/vrbtUnsubscribe")
    @JsonView(RemoteResult.BasicView.class)
    public RemoteResult vrbtUnsubscribe(@RequestParam(name = "mobileOrToken") String mobileOrToken, String channelCode) {

        if (StringUtils.isEmpty(mobileOrToken) || StringUtils.isEmpty(channelCode)) {
            return RemoteResult.fail("参数错误");
        }

        return miguApiService.vrbtUnsubscribe(mobileOrToken, channelCode);
    }

    /**
     * 渠道订阅
     *
     * @return
     */
    @ApiOperation(value = "渠道订阅", notes = "渠道订阅")
    @PostMapping("/sub")
    public Result<?> channelSubscribe(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {

        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String app = request.getHeader("x-requested-with");
        //System.out.println("userAgent = " + userAgent);
        //String referer = request.getHeader("Referer");
        //System.out.println("referer = " + referer);
        String ipAddr = IPUtils.getIpAddr(request);
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            log.info("渠道订阅请求=>ip:{},app:{},json:{},ua:{}", ipAddr, app, jsonData,userAgent);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            if (adSiteBusinessConfigService.isGlobalBlackApp(app)) {
                log.error("手机号:{},渠道号:{},app:{}已被全局屏蔽", subscribe.getMobile(), subscribe.getChannel(), app);
                return Result.error("暂无资格，敬请期待！");
            }
            //不保存ua,仅在日志输出
            //subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            //final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            //referer字段存储广告app的包名
            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            subscribe.setCreateTime(new Date());
            subscribe.setQyclCompanyOwner(getCompanyOwner(request));
            return subscribeService.receiveOrder(subscribe);

        } catch (JeecgBootException e) {
            log.warn("渠道订阅请求,处理异常",e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.warn("渠道订阅请求,处理异常",e);
            return Result.error("请求参数错误");
        }
    }

    /**
     * 报备页-获取短信验证码
     *
     * @return Result<?>
     */
    @ApiOperation("报备页-获取短信验证码")
    @PostMapping("/getSmsCode")
    public Result<?> getSmsCode(@RequestBody JsonNode jsonNode, HttpServletRequest request) {
        log.info("报备页-获取短信验证码-入参:{}", jsonNode);
        return reportPageService.getSmsCode(jsonNode, request);
    }

    /**
     * 报备页-提交短信验证码订购
     *
     * @return Result<?>
     */
    @ApiOperation("报备页-提交短信验证码订购")
    @PostMapping("/order")
    public Result<?> order(@RequestBody JsonNode jsonNode) {
        log.info("报备页-提交短信验证码订购-入参:{}", jsonNode);
        return reportPageService.order(jsonNode);
//        rabbitMQMsgSender.reportPageOrderMessage(jsonNode);
    }

    /**
     * 创建订单
     *
     * @param request
     * @return Subscribe
     */
    private Subscribe createSubScribe(HttpServletRequest request) {
        Subscribe subscribe = new Subscribe();
        subscribe.setReferer(request.getHeader("x-requested-with"));
        subscribe.setIp(IPUtils.getIpAddr(request));
        subscribe.setCreateTime(new Date());
        subscribe.setQyclCompanyOwner(getCompanyOwner(request));
        subscribe.setBizType(getBizTypeByMiguChannel(subscribe.getChannel()));
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        subscribe.setProvince(mobileRegionResult.getProvince());
        subscribe.setCity(mobileRegionResult.getCity());
        subscribe.setIsp(mobileRegionResult.getOperator());
        subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
        subscribeService.updateSubscribeDbAndEs(subscribe);
        return subscribe;
    }

    /**
     * 即时开通短信验证码接收
     */
    @PostMapping("/submitSmsCode")
    @ResponseBody
    public Result<?> submitSmsCode(HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String raw = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("渠道短信验证码提交,ip:{},json内容=>{}", ipAddr, raw);
            final JsonNode tree = mapper.readTree(raw);
            //String subChannel = tree.at("/subChannel").asText();
            String transactionId = tree.at("/transactionId").asText();
            String smsCode = tree.at("/smsCode").asText();
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error("请求参数错误");
            }
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }

            PushSubscribeService pushSubscribeService = SpringContextUtils.getBean(PushSubscribeService.class);

            return pushSubscribeService.pushSmsCodeWithRaw(raw, "");

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("请求参数错误");
        }
    }

    /**
     * 渠道订阅pv
     *
     * @return
     */
    @PostMapping("/visit")
    public Result<?> visit(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String referer = request.getHeader("Referer");
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            //log.info("页面访问日志请求=>{}", jsonData);
            com.eleven.cms.log.VisitLog visitLog = null;
            try {
                visitLog = mapper.readValue(jsonData, com.eleven.cms.log.VisitLog.class);
            } catch (Exception e) {
                log.warn("访问日志接口=>json解析失败,json:{}", jsonData, e);
                return Result.error("json解析失败");
            }
            //visitLog.setBizType(BizLogConstants.BIZ_TYPE_VRBT);
            //根据渠道号确定业务类型
            String bizType = BizConstant.getBizTypeByMiguChannel(visitLog.getMiguChannel());
            final String finger = visitLog.getFinger() + ipAddr;
            visitLog.setBizType(bizType);
            visitLog.setFinger(finger);
            visitLog.setIp(ipAddr);
            visitLog.setUa(userAgent);
            visitLog.setReferer(referer);
            //写业务日志
            BizLogUtils.logVisit(visitLog);

            //数据库日志
            VisitLog vlEntity = new VisitLog();
            vlEntity.setFinger(finger);
            vlEntity.setIp(ipAddr);
            vlEntity.setPage(visitLog.getPageName());
            vlEntity.setChannel(visitLog.getMiguChannel());
            vlEntity.setSubChannel(visitLog.getSubChannel());
            vlEntity.setBizType(visitLog.getBizType());
            vlEntity.setCopyrightId(visitLog.getExtra());
            vlEntity.setExtra(visitLog.getAction());
            vlEntity.setCreateTime(new Date());
            visitLogService.save(vlEntity);
            //添加记录
            if(visitLog.getAction().equals("inputMobile")){
                subscribeLogService.subscribeLog(visitLog.getMiguChannel(),visitLog.getAction());
            }
            return Result.ok();

        } catch (JeecgBootException e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("请求参数错误");
        }
    }

    /**
     * 渠道订阅
     *
     * @return
     */
    @PostMapping("/orderLog")
    public Result<?> orderLog(/*HttpEntity<String> httpEntity, */HttpServletRequest request) {
        String ipAddr = IPUtils.getIpAddr(request);
        //System.out.println("ipAddr = " + ipAddr);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            //log.info("渠道订阅请求,加密的内容=>{}",raw);
            //String jsonData = Sm2Utils.decrypt(raw);
            //log.info("页面访问日志请求=>{}", jsonData);
            com.eleven.cms.log.OrderLog orderLog = mapper.readValue(jsonData, com.eleven.cms.log.OrderLog.class);
            orderLog.setBizType(BizLogConstants.BIZ_TYPE_VRBT);
            final String finger = orderLog.getFinger() + ipAddr;
            orderLog.setFinger(finger);
            MobileRegionResult mobileRegionResult = mobileRegionService.query(orderLog.getMobile());
            if (mobileRegionResult != null) {
                orderLog.setProvince(mobileRegionResult.getProvince());
                orderLog.setCity(mobileRegionResult.getCity());
            }
            //写业务日志
            BizLogUtils.logOrder(orderLog);
            if (BizConstant.MIGU_RES_CODE_SUCCEED.equals(orderLog.getMiguResCode()) && BizLogConstants.BIZ_TYPE_VRBT.equals(BizConstant.getBizTypeByMiguChannel(orderLog.getMiguChannel()))) {
                String serviceId = cmsCrackConfigService.getCrackConfigByChannel(orderLog.getMiguChannel()).getServiceId();
                //报备视频彩铃
                pptvApiService.pptvRightsRecharge(orderLog.getMobile(), orderLog.getFinger(), serviceId,orderLog.getMiguChannel());
//                //新增彩铃中心订阅包数据
//                subscribeService.transformSubscribeFromOrderLog(orderLog,ipAddr);
            }
            return Result.ok();

        } catch (JeecgBootException e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("请求参数错误");
        }
    }

    /**
     * @param subChannel 渠道号
     * @return
     */
    ////@AutoLog(value = "查询渠道号对应的广告参数配置")
    @ApiOperation(value = "查询渠道号对应的广告参数配置", notes = "查询渠道号对应的广告参数配置")
    @PostMapping(value = "/chAdCfg")
    public Result<?> chAdCfg(@RequestParam(name = "subChannel", required = false) String subChannel) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(subChannel)) {
            log.warn("查询渠道号对应的广告参数配置=>参数[subChannel]未传递");
            return Result.error("参数[subChannel]未传递");
        }
        FebsResponse febsResponse = adReportService.getAdPlatform(subChannel);
        return Result.ok(febsResponse.get("data"));
    }


    /**
     * @param subChannel 推广渠道号
     * @param startDate  开始日期  格式如:2020-10-01
     * @param endDate    结束日期  格式如:2020-10-01
     * @return
     */
    //@AutoLog(value = "渠道数据实时同步")
    @ApiOperation(value = "渠道数据实时同步", notes = "渠道数据实时同步")
    @RequestMapping(value = "/channel/report")
    public List<ChannelReportItem> channelReport(@RequestParam(name = "code") String subChannel,
                                                 @RequestParam(name = "start_date") String startDate,
                                                 @RequestParam(name = "end_date") String endDate) {
        LocalDate start;
        LocalDate end;
        if (StringUtils.isEmpty(subChannel) || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw new JeecgBootException("请求参数不全");
        }
        try {
            start = LocalDate.parse(startDate);
            end = LocalDate.parse(endDate);
        } catch (Exception e) {
            //e.printStackTrace();
            throw new JeecgBootException("请求参数日期格式错误");
        }

        return subscribeService.channelReport(subChannel, start, end);
    }

    @RequestMapping(value = "/setPercent")
    public Result<?> setPercent(Integer percent) {
        if (percent < 1 || percent > 100) {
            return Result.error("参数值必须在1-100");
        }
        miguApiService.setChannelSwtichPercent(percent);

        return Result.ok("设置成功");
    }

    @RequestMapping(value = "/getPercent")
    public Result<?> getPercent() {

        final Integer percent = miguApiService.getChannelSwtichPercent();

        return Result.ok(percent);
    }


    /**
     * 发送联通登录验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getLtCaptcha")
    @ResponseBody
    public Result<?> getLtCaptcha(@RequestParam("mobile") String mobile) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.createLt(mobile);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }

        return Result.ok("验证码已发送至你的手机,1分钟内有效");
    }

    ////@AutoLog(value = "咪咕登录")
    @ApiOperation(value = "联通登录", notes = "联通登录")
    @PostMapping(value = "/ltLogin")
    public Result<?> ltLogin(String mobile, String captcha,@RequestParam(defaultValue = BizConstant.BIZ_LT_CHANNEL_DEFAULT) String vrbtLtChannel) {

        if (Strings.isNullOrEmpty(mobile) || Strings.isNullOrEmpty(captcha)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!captcha.matches("^\\d{6}$")) {
            return Result.noauth("验证码错误");
        }
        //验证短信验证码
        try {
            final boolean loginResult = liantongVrbtService.codeLogin(mobile, captcha,vrbtLtChannel);
            if (!loginResult) {
                return Result.noauth("短信验证码错误或过期");
            }
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.noauth(e.getMessage());
        }

        return Result.ok("登录成功");
    }

    @ApiOperation(value = "联通开通包月或订购铃音", notes = "联通开通包月或订购铃音")
    @PostMapping(value = "/ltOrderMonOrRing")
    public Result<?> ltOrderMonOrRing(String mobile,
                                      @RequestParam(value = "ltRingId", required = false) String ltRingId,
                                      @RequestParam(value = "channel", defaultValue = MiguApiService.CH_DYB_DEFAULT) String channel,
                                      @RequestParam(value = "subChannel", defaultValue = BizConstant.SUB_CHANNEL_DEFAULT) String subChannel,
                                      @RequestParam(value = "vrbtLtChannel", defaultValue = BizConstant.BIZ_LT_CHANNEL_DEFAULT) String vrbtLtChannel,
                                      HttpServletRequest request) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }

        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult != null) {
            if (!mobileRegionResult.isIspLiantong()) {
                return Result.error("当前业务只支持联通用户!");
            }
        } else {
            return Result.error("当前业务只支持联通用户!");
        }
        //查询是否已开通了包月
        final boolean isSubedMon = liantongVrbtService.isSubedMon(mobile,vrbtLtChannel);
        //如果有包月
        if (isSubedMon) {
            //如果还需要订购铃音,就单独单独订购铃音
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(ltRingId)) {
                LiantongResp liantongResp = liantongVrbtService.settingRingOnePointMon(mobile, ltRingId,vrbtLtChannel);
                return Result.ok(liantongResp.getDescription());
            } else {
                return Result.ok("你已订购包月,请勿重复订购");
            }
        }
        //final LiantongResp liantongResp = liantongVrbtService.onePointProductMon(mobile,ltRingId);
        //未包月,需要生成订单,并传入铃音id(因为开包月同时订已存在的铃音不会包月成功,收到的回调也无法判定包月结果,所以这里单开包月)
        //String redirectUrl = "https://crbt.cdyrjygs.com/vrbt_lt/#/";
        String redirectUrl = request.getHeader("Referer") + "#/";
        final LiantongResp liantongResp = liantongVrbtService.onePointProductMon(mobile, null, redirectUrl,vrbtLtChannel);
        if (!liantongResp.isOK()) {
            log.warn("联通视频彩铃包月业务订购失败,手机号:{},resCode:{},resMessage:{}", mobile, liantongResp.getReturnCode(), liantongResp.getDescription());
            return Result.error("订购包月失败,请稍后再试");
        }
        String ispOrderNo = liantongResp.getOrderId();
        Subscribe subscribe = new Subscribe();
        subscribe.setChannel(channel);
        subscribe.setSubChannel(subChannel);
        subscribe.setCopyrightId(ltRingId);
        subscribe.setIsp(mobileRegionResult.getOperator());
        subscribe.setProvince(mobileRegionResult.getProvince());
        subscribe.setCity(mobileRegionResult.getCity());
        subscribe.setMobile(mobile);
        subscribe.setIspOrderNo(ispOrderNo);
        subscribeService.liantongOrder(subscribe);
        return Result.bizConfirm(liantongResp.getUrl());
    }

    @ApiOperation(value = "联通发送退订短信", notes = "联通发送退订短信")
    @PostMapping(value = "/ltSendUnSubCode")
    public Result<?> ltSendUnSubCode(String mobile,@RequestParam(defaultValue = BizConstant.BIZ_LT_CHANNEL_DEFAULT) String vrbtLtChannel) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }

        return liantongVrbtService.sendVerifyCode(mobile,vrbtLtChannel).toResult();
    }

    @ApiOperation(value = "联通产品退订", notes = "联通产品退订")
    @PostMapping(value = "/ltUnSubProduct")
    public Result<?> ltUnSubProduct(String mobile, String code,@RequestParam(defaultValue = BizConstant.BIZ_LT_CHANNEL_DEFAULT) String vrbtLtChannel) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }

        if (Strings.isNullOrEmpty(code) || !code.matches("^\\d{6}$")) {
            return Result.error("请输入正确的短信验证码");
        }

        if (!liantongVrbtService.isSubedMon(mobile,vrbtLtChannel)) {
            return Result.error("您未订购酷炫彩铃包月业务,无需退订");
        }

        return liantongVrbtService.unSubProductWithVCode(mobile, code,vrbtLtChannel).toResult();
    }

    @ApiOperation(value = "联通视频彩铃包月关系查询", notes = "联通视频彩铃产品包月关系查询")
    @PostMapping(value = "/ltSubedMonQuery")
    public Result<?> ltSubedMonQuery(String mobile,@RequestParam(defaultValue = BizConstant.BIZ_LT_CHANNEL_DEFAULT) String vrbtLtChannel) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }

        boolean isSubedMon = liantongVrbtService.isSubedMon(mobile, vrbtLtChannel);

        return Result.ok(isSubedMon ? "您已订购视频彩铃包月业务" : "您未订购视频彩铃包月业务");
    }

    @ApiOperation(value = "联通视频彩铃包月退订-免用户鉴权", notes = "联通视频彩铃包月退订-免用户鉴权")
    @PostMapping(value = "/ltUnSubProductNoToken")
    public Result<?> ltUnSubProductNoToken(String mobile,@RequestParam(defaultValue = BizConstant.BIZ_LT_CHANNEL_DEFAULT) String vrbtLtChannel) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }

        if (!liantongVrbtService.isSubedMon(mobile,vrbtLtChannel)) {
            return Result.error("您未订购视频彩铃包月业务,无需退订");
        }

        return liantongVrbtService.unSubProductNoToken(mobile,vrbtLtChannel).toResult();
    }

    /**
     * 发送短信验证码(订阅和联通)
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getUniteCaptcha")
    @ResponseBody
    public Result<?> getUniteCaptcha(@RequestParam("mobile") String mobile, @RequestParam(name = "channelCode", required = false, defaultValue = "014X04C") String channelCode) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        final MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return Result.noauth("系统繁忙,请稍后再试");
        }
        if (regionResult.isIspDianxin()) {
            return Result.noauth("暂不支持电信用户,敬请期待");
        }
        boolean result = regionResult.isIspYidong() ? smsValidateService.create(mobile, channelCode) : smsValidateService.createLt(mobile);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }

        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }

    /**
     * 登录(订阅和联通)
     *
     * @param mobile 手机号
     * @return
     */
    @ApiOperation(value = "登录", notes = "登录")
    @PostMapping(value = "/uniteLogin")
    public VrbtCombinResult uniteLogin(String mobile, String captcha, String channelCode,@RequestParam(defaultValue = BizConstant.BIZ_LT_CHANNEL_HONGSHENG) String vrbtLtChannel) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return VrbtCombinResult.fail("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return VrbtCombinResult.fail("验证码错误");
        }

        final MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return VrbtCombinResult.fail("系统繁忙,请稍后再试");
        }
        if (regionResult.isIspDianxin()) {
            return VrbtCombinResult.fail("暂不支持电信用户,敬请期待");
        }

        if (regionResult.isIspYidong()) {
            //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
            try {
                smsValidateService.check(mobile, captcha);
            } catch (JeecgBootException e) {
                //e.printStackTrace();
                return VrbtCombinResult.fail(e.getMessage());
            }

            final VrbtCombinResult combinResult = miguApiService.vrbtCombinQuery(mobile, channelCode);
            combinResult.setIsp(MobileRegionResult.ISP_YIDONG);
            return combinResult;

        } else {
            //验证短信验证码
            try {
                final boolean loginResult = liantongVrbtService.codeLogin(mobile, captcha,vrbtLtChannel);
                if (!loginResult) {
                    return VrbtCombinResult.fail("短信验证码错误或过期");
                }
            } catch (JeecgBootException e) {
                //e.printStackTrace();
                return VrbtCombinResult.fail(e.getMessage());
            }

            final VrbtCombinResult combinResult = VrbtCombinResult.success();
            combinResult.setIsp(MobileRegionResult.ISP_LIANTONG);
            return combinResult;
        }

    }

    /**
     * 登录并查询功能和包月状态(订阅,直接登录接口暂不支持联通)
     *
     * @param mobile 手机号
     * @return
     */
    @ApiOperation(value = "登录", notes = "登录")
    @PostMapping(value = "/uniteVrbtCombinQuery")
    public VrbtCombinResult uniteVrbtCombinQuery(String mobile, String channelCode,@RequestParam(defaultValue = BizConstant.BIZ_LT_CHANNEL_HONGSHENG) String vrbtLtChannel) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return VrbtCombinResult.fail("请输入正确格式手机号");
        }
        final MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return VrbtCombinResult.fail("系统繁忙,请稍后再试");
        }
        if (!regionResult.isIspYidong()) {
            return VrbtCombinResult.fail("暂不支持电信和联通用户,敬请期待");
        }

        final VrbtCombinResult combinResult = miguApiService.vrbtCombinQuery(mobile, channelCode);
        combinResult.setIsp(MobileRegionResult.ISP_YIDONG);
        return combinResult;
    }

    /**
     * 骏伯多会员直充结果回调
     *
     * @return
     */
    @RequestMapping(value = "/junboRechargeResult")
    public String notifyJunboRechargeResult(@RequestBody String requestBody) {
        log.info("骏伯多会员直充结果回调=>requestBody:{}", requestBody);
        try {
//            System.out.println("======>rsaPrivateKeyPkcs8:" + junboApiProperties.getRsaPrivateKeyPkcs8());
            final String decryptData = new Receiver().receiveByPublic(junboApiProperties.getRsaPrivateKeyPkcs8(), requestBody);
            log.info("骏伯多会员直充结果回调=>decryptData:{}", decryptData);
            final JunboResult junboResult = new ObjectMapper().readValue(decryptData, JunboResult.class);
            junboChargeLogService.receiveRechargeNotify(junboResult);
        } catch (Exception e) {
            log.info("骏伯多会员直充结果回调异常", e);
        }
        return "1";
    }

    /**
     * 会员微信登录
     *
     * @param mobile  手机号
     * @param captcha 验证码
     * @return
     */
    @ApiOperation(value = "会员微信登录", notes = "会员微信登录")
    @PostMapping(value = "/wechatLogin")
    @ResponseBody
    public Result<?> wechatLogin(@RequestParam("mobile") String mobile, @RequestParam("captcha") String captcha) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return Result.error("验证码错误");
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.error(e.getMessage());
        }
        String token = new TokenUtil().setUserLoginTime(mobile);
        return Result.ok(token);
    }

    /**
     * 咪咕业务列表查询
     *
     * @return
     */
    @ApiOperation(value = "咪咕业务列表查询", notes = "咪咕业务列表查询")
    @PostMapping(value = "/migupacklist")
    @ResponseBody
    public Result<?> queryPackList(@RequestParam(name = "orderKey", required = false, defaultValue = "CPBY,BJHY,BKST") String orderKey) {
        List<MiguPack> list = miguPackService.selectMain(orderKey);
        return Result.ok(list);
    }

    /**
     * 会员权益列表查询
     *
     * @return
     */
    @ApiOperation(value = "会员权益列表查询", notes = "会员权益列表查询")
    @PostMapping(value = "/memberRightslist")
    @ResponseBody
    @WeChatLogin(menu = "会员权益列表查询")
    public Result<?> queryRightslist(@RequestParam("packId") String packId) {
        List<RightsPack> list = rightsPackService.selectRightsPackList(packId);
        return Result.ok(list);
    }

    /**
     * 会员权益(全部)列表查询
     *
     * @return
     */
    @ApiOperation(value = "会员权益(全部)列表查询", notes = "会员权益(全部)列表查询")
    @PostMapping(value = "/wholeMemberRightsList")
    @ResponseBody
    @WeChatLogin(menu = "会员权益(全部)列表查询")
    public Result<?> wholeMemberRightsList(@RequestParam(name = "serviceId", required = false, defaultValue = "698039020108689345") String serviceId) {
        List<RightsPackDto> list = rightsPackService.wholeMemberRightsList(serviceId);
        return Result.ok(list);
    }

    /**
     * 会员权益领取
     *
     * @return
     */
    @ApiOperation(value = "会员权益领取", notes = "会员权益领取")
    @PostMapping(value = "/memberRightsObtain")
    @ResponseBody
    @WeChatLogin(menu = "会员权益领取")
    public Result<?> memberRightsObtain(@RequestParam("serviceId") String serviceId, @RequestParam("rightsId") String rightsId, @RequestParam("packName") String packName, HttpServletRequest request,@RequestParam(value = "account", required = false, defaultValue = "")String account) {
        String msisdn = (String) redisUtil.get(request.getHeader("token"));
        synchronized (interner.intern(msisdn)) {
            account=StringUtils.isEmpty(account)?msisdn:account;
            return memberService.obtainMember(msisdn,account, serviceId, rightsId, packName);
        }
    }

    /**
     * 领取记录列表查询
     *
     * @return
     */
    @ApiOperation(value = "领取记录列表查询", notes = "领取记录列表查询")
    @PostMapping(value = "/junboChargeList")
    @ResponseBody
    @WeChatLogin(menu = "领取记录列表查询")
    public Result<?> queryJunboChargeList(@RequestParam(value = "rightsMonth", required = false, defaultValue = "")String rightsMonth,@RequestParam(value = "sourse", required = false, defaultValue =BizConstant.SOURSE_PUBLIC)String sourse,HttpServletRequest request) {
        String phone = (String) redisUtil.get(request.getHeader("token"));
        List<JunboChargeLog> list = junboChargeLogService.selectMain(phone,rightsMonth,sourse);
        return Result.ok(list);
    }


    /**
     * pptv视频赠送
     *
     * @return
     */
    @ApiOperation(value = "soap回执-pptv视频赠送", notes = "soap回执-pptv视频赠送")
    @PostMapping(value = "/pptv/send")
    @ResponseBody
    public Result<?> sendPPTV(@RequestBody ObjectNode dataNode) {
        pptvApiService.pptvRightsByRechargeData(dataNode);
        return Result.ok();
    }


    /**
     * 电信视频彩铃退订
     *
     * @return
     */
    @ApiOperation(value = "电信视频彩铃退订", notes = "电信视频彩铃退订")
    @PostMapping(value = "/dianxinUnsubscribe")
    @ResponseBody
    public Result<?> dianxinUnsubscribe(@RequestParam("mobile") String mobile,@RequestParam(value = "company",defaultValue = BIZ_DIANXIN_CHANNEL_DEFAULT) String company) {
        log.info("电信视频彩铃退订=>手机号:{}", mobile);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        String content = dianxinVrbtService.unSubscribeByemp(mobile, company);
        return Result.ok(content);
    }

    /**
     * 电信视频彩铃业务订购查询
     *
     * @return
     */
    @ApiOperation(value = "电信视频彩铃业务订购查询", notes = "电信视频彩铃业务订购查询")
    @PostMapping(value = "/dianxinPackageExists")
    @ResponseBody
    public Result<?> dianxinPackageExists(@RequestParam("mobile") String mobile,@RequestParam(value = "company",defaultValue = BIZ_DIANXIN_CHANNEL_MAIHE_6) String company) {
        log.info("电信视频彩铃业务订购查询=>手机号:{}", mobile);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        boolean flag = dianxinVrbtService.queryPackageExist(mobile,company);
        return Result.ok(flag);
    }

    /**
     * 栏目分类列表查询
     *
     * @return
     */
    @ApiOperation(value = "栏目分类列表查询", notes = "栏目分类列表查询")
    @PostMapping(value = "/columnClassList")
    @ResponseBody
    public Result<?> queryColumnClassList() {
        List<ColumnClassEnum> list = ColumnClassEnum.getColumnClassList();
        List<ColumnClass> classsList = Lists.newArrayList();
        list.forEach(item -> {
            ColumnClass columnClass = new ColumnClass();
            columnClass.setColumnClass(item.getColumnClass());
            columnClass.setColumnClassName(item.getColumnClassName());
            classsList.add(columnClass);
        });
        return Result.ok(classsList);
    }


    /**
     * 栏目分类列表查询
     *
     * @return
     */
    @ApiOperation(value = "栏目列表查询", notes = "栏目列表查询")
    @PostMapping(value = "/columnList")
    @ResponseBody
    public Result<?> queryColumnList(@RequestParam("columnClass") String columnClass, @RequestParam(name = "isCarousel", required = false, defaultValue = "0") String isCarousel) {
        List<Column> list = columnService.lambdaQuery().eq(Column::getColumnClassName, columnClass).orderByAsc(Column::getPriority).list();
        return Result.ok(list);
    }

    /**
     * 栏目分类列表查询(讯飞)
     *
     * @return
     */
    @ApiOperation(value = "栏目列表查询", notes = "栏目列表查询")
    @PostMapping(value = "/xf/ColumnList")
    @ResponseBody
    public Result<?> queryXunfeiColumnList() {
        return Result.ok(xunfeiJingxianVrbtService.queryVrbtColumn());
    }


    /**
     * 电信报备提交验证码生成订单
     *
     * @param mobile
     * @param captcha
     * @return
     */
    @ApiOperation(value = "电信报备提交验证码生成订单", notes = "电信报备提交验证码生成订单")
    @PostMapping(value = "/submitSmsAndGenerateOrder")
    @ResponseBody
    public Result submitSmsAndGenerateOrder(@RequestParam("mobile") String mobile, @RequestParam(value = "captcha",defaultValue = "") String captcha,@RequestParam(value = "company",defaultValue = BIZ_DIANXIN_CHANNEL_DEFAULT) String company) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return Result.error("验证码错误");
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.error(e.getMessage());
        }
        final boolean packageExist = dianxinVrbtService.queryPackageExist(mobile,company);
        if (packageExist) {
            return Result.error("已有包月");
        }
        final DianxinResp dianxinResp = dianxinVrbtService.confirmOpenOrderLaunchedEx(mobile, "1", null, company);
        if (!dianxinResp.isOK()) {
            return Result.error("电信视频彩铃包月业务订购失败:" + dianxinResp.getResMessage());
        }
        if(!dianxinResp.getFeeUrl().contains(bizProperties.getSubscribeDianxinVrbtOrderPage())){
            log.error("电信视频彩铃计费地址变更");
        }
        //验证短信验证码
        String orderPage = bizProperties.getSubscribeDianxinVrbtOrderPage() + "?order_no=" + dianxinResp.getOrderNo();
        return Result.okAndSetData(orderPage);
    }

    /**
     * 电信视频彩铃创建订单(无需短信认证)
     *
     * @param mobile
     * @return
     */
    @ApiOperation(value = "电信视频彩铃创建订单(无需短信认证)", notes = "电信视频彩铃创建订单(无需短信认证)")
    @PostMapping(value = "/telecomGenOrderNoneAuth")
    @ResponseBody
    public Result telecomGenOrderNoneAuth(@RequestParam("mobile") String mobile,
                                          @RequestParam(value = "company",defaultValue = BIZ_DIANXIN_CHANNEL_MAIHE_6) String company) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if(BIZ_CHANNEL_AYY_AISPCL_HS.equals(company)){
            final DianxinResp dianxinResp=dianxinVrbtService.newConfirmOpenOrderLaunchedEx(mobile,"1", null,company);
            if (!dianxinResp.isOK()) {
                return Result.error("电信视频彩铃包月业务下单失败:" + dianxinResp.getResMessage());
            }
            return Result.okAndSetData(dianxinResp.getFeeUrl());
        }

        final DianxinResp dianxinResp = dianxinVrbtService.confirmOpenOrderLaunchedEx(mobile, "0",company, null);
        if (!dianxinResp.isOK()) {
            return Result.error("电信视频彩铃包月业务下单失败:" + dianxinResp.getResMessage());
        }

        return Result.okAndSetData(dianxinResp.getFeeUrl());
    }

    /**
     * 电信使用包月产品权益免费订购视频铃音
     * 3.8.1 接口描述在订购了合作方 APP 所属的视频彩铃合作包后，用户可以 0 元的价格订购该合作方预先入库到彩铃平台中的铃音。订购成功后将自动为用户设置为默认铃音
     *
     * @param mobile
     * @param toneCode
     * @return
     */
    @ApiOperation(value = "电信使用包月产品权益免费订购视频铃音", notes = "电信使用包月产品权益免费订购视频铃音")
    @PostMapping(value = "/telecomAddToneFreeOnProduct")
    @ResponseBody
    public Result telecomAddToneFreeOnProduct(@RequestParam("mobile") String mobile,
                                              @RequestParam("toneCode") String toneCode,
                                              @RequestParam(value = "company",required = false,defaultValue = BIZ_DIANXIN_CHANNEL_MAIHE_6) String company) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }

        final boolean packageExist = dianxinVrbtService.queryPackageExist(mobile,company);
        if (!packageExist) {
            return Result.error("请先开通视频彩铃包月再来免费订购视频铃音");
        }
        final DianxinResp dianxinResp = dianxinVrbtService.addToneFreeOnProduct(mobile, toneCode,company);
        return Result.okAndSetData(dianxinResp);
    }


    /**
     * 天翼空间发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getTyCaptcha")
    @ResponseBody
    public Result<?> getTyCaptcha(@RequestParam("mobile") String mobile, @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.createTy(mobile, channelCode);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }

        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }


    /**
     * 天翼空间验证码校验
     *
     * @param phone
     * @param code
     * @return
     */
    @PostMapping("/dianxinValidateCode")
    @ResponseBody
    public Result<?> dianxinValidateCode(@RequestParam("phone") String phone, @RequestParam("code") String code) throws Exception {
        log.info("渠道短信验证码发送,phone:{},code:{}", phone, code);
        if (!phone.matches(Regexp.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!code.matches("^\\d{6}$")) {
            return Result.error("验证码错误");
        }
        //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
        try {
            smsValidateService.check(phone, code);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.error(e.getMessage());
        }
        return Result.okAndSetData(tianyiSpaceService.generatePayUrl(phone));
    }


    /**
     * 天翼空间查询订购关系
     *
     * @param phone
     * @return
     * @throws Exception
     */
    @PostMapping("/tianyiRelationShip")
    @ResponseBody
    public FebsResponse queryRelationShip(@RequestParam("phone") String phone) throws Exception {
        if (!phone.matches(Regexp.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        try {
            boolean result = tianyiSpaceService.queryRelationShip(phone);
            if (result) {
                return new FebsResponse().success().message("已开通");
            } else {
                return new FebsResponse().fail().message("未开通");
            }
        } catch (Exception e) {
            log.error("天翼空间退订关系查询错误:{}", e);
            return new FebsResponse().fail().message(e.getMessage());
        }

    }

    /**
     * 天翼空间退订
     *
     * @param phone
     * @return
     * @throws Exception
     */
    @PostMapping("/tianyiUnsubscribe")
    @ResponseBody
    public FebsResponse unsubscribe(@RequestParam("phone") String phone) throws Exception {
        if (!phone.matches(Regexp.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        try {
            boolean result = tianyiSpaceService.unsubscribe(phone);
            if (result) {
                return new FebsResponse().success().message("退订成功");
            } else {
                return new FebsResponse().fail().message("退订失败");
            }
        } catch (Exception e) {
            log.error("天翼空间退订错误:{}", e);
            return new FebsResponse().fail().message(e.getMessage());
        }

    }

    /**
     * 上海移动视频彩铃订阅
     *
     * @param mobile
     * @param captcha
     * @return
     */
    @PostMapping("/shanghaiMobile/spcl/subscribe")
    @ResponseBody
    public Result<?> shanghaiMobileSPCLSubscribe(@RequestParam("mobile") String mobile,
                                                 @RequestParam(name = "captcha", required = false, defaultValue = "") String captcha,
                                                 @RequestParam(name = "business", required = false, defaultValue = ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTDY) String business,
                                                 @RequestParam(name = "orderId", required = false, defaultValue = "") String orderId,
                                                 @RequestParam(name = "channel", required = false, defaultValue = "") String channel,
                                                 HttpServletRequest request) {
        if (!mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
//        if (!captcha.matches("^\\d{6}$")) {
//            return Result.error("验证码错误");
//        }
//        try {
//            smsValidateService.check(mobile, captcha);
//        } catch (JeecgBootException e) {
//            //e.printStackTrace();
//            return Result.noauth(e.getMessage());
//        }
        String ip = IPUtils.getIpAddr(request);
//        if(ShanghaiMobileBusiness.SHANGHAI_MOBILE_5GTHB.contains(business)){
//            return shanghaiMobileService.businessLiuLiangBao(mobile, ip, ShanghaiMobileConstant.IS_YES_RIGHT,ShanghaiMobileBusiness.SHANGHAI_MOBILE_5GTHB);
//        }
//        return shanghaiMobileService.shanghaiMobileDOUYIN(mobile, ip, ShanghaiMobileConstant.IS_YES_RIGHT,business);
        if(!StringUtils.isEmpty(channel)){
            String service=ShanghaiMobileBusiness.productIdMap.get(channel);
            if(!StringUtils.isEmpty(service)){
                business=service;
            }
        }

        return shanghaiMobileService.shangHaiMobileBusinessOrder(mobile,ip,ShanghaiMobileConstant.IS_YES_RIGHT, business,captcha,orderId);
    }

    /**
     * 发送短信验证码 2021-9-24 14:43:41
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/sendSms")
    @ResponseBody
    public Result<?> sendSms(@RequestParam("mobile") String mobile) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.create(mobile, MiguApiService.BIZ_CPMB_10_CHANNEL_CODE);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }

        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }


    @PostMapping("/sichuanMobileRandom")
    @ResponseBody
    public Result getSichuanMobileRandom(@RequestParam(value = "phone") String phone,
                                         @RequestParam(value = "bizCode", defaultValue = "AZ440217") String bizCode) {
        return subscribeService.getSichuanMobileRandom(phone, bizCode);
    }

    @PostMapping("/sichuanMobileOrder")
    @ResponseBody
    public Result sichuanMobileOrder(@RequestParam(value = "phone") String phone,
                                     @RequestParam(value = "code") String code,
                                     @RequestParam(value = "bizCode", defaultValue = "AZ440217") String bizCode) {

        return subscribeService.sichuanMobileOrder(phone, code, bizCode);
    }


    @PostMapping("/sichuanMobileVrbtOrder")
    @ResponseBody
    public Result sichuanMobileVrbtOrder(@RequestParam(value = "phone") String phone,
                                         @RequestParam(value = "channel", defaultValue = BIZ_CHANNEL_SCYD_XSVRBTYL) String channelCode,
                                         @RequestParam(value = "company", defaultValue = BIZ_SCMCC_CHANNEL_YRJY,required = false) String company) {
        SichuanMobilePrepareOrderResult result = siChuanMobileApiService.prepareOrder(phone,channelCode);
        if (result.isOK()) {
            String jsonStr = siChuanMobileApiService.decryptResult(result.getResult(), channelCode);
            JsonNode jsonNode = null;
            try {
                jsonNode = mapper.readTree(jsonStr);
            } catch (JsonProcessingException e) {
                log.error("四川移动预下单-系统异常-jsonStr:{}",jsonStr, e);
                return Result.error("四川移动预下单-系统异常");
            }
            String serialNumber = jsonNode.get("serial_number").asText("");
            return Result.ok("预下单成功", serialNumber);
        }else {
            return Result.error("下单失败");
        }
//        String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(channelCode);
//        try {
//            SichuanMobilePrepareOrderResult sichuanMobileResult = sichuanMobileFlowPacketService.prepareOrder(phone, bizCode, company);
//            if (sichuanMobileResult.isOK()) {
//                String jsonStr = sichuanMobileFlowPacketService.decryptResult(sichuanMobileResult.getResult(), company);
//                JsonNode jsonNode = mapper.readTree(jsonStr);
//                String serialNumber = jsonNode.get("serial_number").asText();
//                return Result.ok("下单成功", serialNumber);
//            } else {
//                return Result.error("下单失败");
//            }
//        } catch (Exception e) {
//            return Result.error("下单失败");
//        }
    }


    /**
     * 短信登录,只验证短信验证码,不去咪咕服务端登录,前端会在js登录 2021-9-24 14:55:36
     *
     * @param mobile
     * @param captcha
     * @return
     */
    ////@AutoLog(value = "短信登录")
    @ApiOperation(value = "短信登录", notes = "短信登录")
    @PostMapping(value = "/smsValid")
    public Result<?> smsValid(String mobile, String captcha) {

        if (Strings.isNullOrEmpty(mobile) || Strings.isNullOrEmpty(captcha)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!captcha.matches("^\\d{6}$")) {
            return Result.noauth("验证码错误");
        }
        //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.noauth(e.getMessage());
        }

        return Result.ok("登录成功");

    }

    /**
     * 发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/shanghai/getCaptcha")
    @ResponseBody
    public Result<?> getShangHaiYDCaptcha(@RequestParam("mobile") String mobile, @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return Result.noauth("系统繁忙,请稍后再试");
        } else {
            if (!ShanghaiMobileConstant.PROVINCE_SHANGHAI.equals(regionResult.getProvince())) {
                return Result.noauth("只支持上海移动用户办理业务,其他地区敬请期待");
            }
        }
        if (regionResult.isIspDianxin()) {
            return Result.noauth("暂不支持电信用户,敬请期待");
        }
        boolean result = smsValidateService.create(mobile, channelCode);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }

        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }


    /**
     * 咪咕是否包月（渠道包月红包领取）
     *
     * @param mobile
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "咪咕是否包月（渠道包月红包领取）", notes = "咪咕是否包月（渠道包月红包领取）")
    @PostMapping(value = "/redpack/cpmbQuery")
    public Result<?> redpackDirectLogin(@RequestParam("mobile") String mobile, @RequestParam("channelCode") String channelCode, HttpServletRequest request) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        final String ipAddr = IPUtils.getIpAddr(request);
        log.info("咪咕直接登录=>手机号:{},渠道号:{},ip:{}", mobile, channelCode, ipAddr);
        RemoteResult result = miguApiService.cpmbQuery(mobile, channelCode);
        if (result.isOK()) {
            Result resp = Result.ok("登录成功");
            resp.setResult(result.getToken());
            return resp;
        } else {
            return Result.noauth("登录失败");
        }
    }

    /**
     * 外部视频彩铃发送短信
     *
     * @return
     */
    @PostMapping("/sichuanMobileCallback")
    public Result sichuanMobileCallback(@RequestParam("mobile") String mobile,
                                 @RequestParam("orderNo") String orderNo,
                                 @RequestParam("status") Integer status,
                                 @RequestParam("result") String result,
                                 @RequestParam(value="fullResult",required = false) String fullResult) {
        log.info("四川移动订单开通结果数据,mobile:{},orderNo:{},status:{},result:{},fullResult:{}", mobile, orderNo, status, result,fullResult);
        return subscribeService.receiveOrderSichuanMobileCallback(mobile, result, status, orderNo, fullResult);

    }


    /**
     * 外部视频彩铃发送短信
     *
     * @return
     */
    @PostMapping("/outside/getCaptcha")
    public Result outsideSendMsg(@RequestParam("mobile") String mobile,
                                 @RequestParam(value = "subChannel",defaultValue = "OSVRBT02") String subChannel,
                                 @RequestParam(value = "userAgent",required = false) String userAgent,
                                 @RequestParam(value = "appPackage",required = false) String appPackage,
                                 @RequestParam(value = "ip",required = false) String ip,
                                 HttpServletRequest request) {
        log.info("第三方渠道获取验证码请求 mobile:{},渠道号:{},ip:{},app:{},ua:{}", mobile, subChannel, ip, appPackage, userAgent);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if(blackListService.isBlackList(mobile)){
            return Result.msgBlackLimit();
        }
        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setSubChannel(subChannel);
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subChannel);
        if (outsideConfig == null) {
            log.error("无效的外部渠道号:{}", subChannel);
            return Result.error("无效的渠道号!");
        }
        String channelCode = outsideConfig.getChannel();
//        String channelCode = outsideProperties.getChannelCode(subChannel);
        subscribe.setChannel(channelCode);
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        subscribe.setUserAgent(userAgent);
        subscribe.setIp(ip);
        subscribe.setReferer(appPackage);
        subscribe.setCreateTime(new Date());
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if(mobileRegionResult!=null){
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if(mobileRegionResult.isIspYidong()){
//                    //T7切换百分之50到H0 && RandomUtils.isInRatio(50)
//                    if (MiguApiService.BIZ_BJHYDY_CHANNEL_CODE.equals(channelCode) && RandomUtils.isInRatio(50)) {
//                        subscribe.setChannel(MiguApiService.BIZ_BJHYYS_CHANNEL_CODE);
//                    }
                    String bizType = BizConstant.getBizTypeByMiguChannel(channelCode);
                    String provinceBusiness = outsideConfig.getProvinceBusiness();
                    //分省业务不做省份限制
                    boolean notLimit = org.apache.commons.lang3.StringUtils.isNotBlank(provinceBusiness) && org.apache.commons.lang3.StringUtils.contains(provinceBusiness, subscribe.getProvince());
                    if (BIZ_TYPE_BJHY.equals(bizType)) {
                        if (MiguApiService.BIZ_BJHYDY_CHANNEL_CODE.equals(channelCode) && !provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                            subscribe.setChannel(MiguApiService.BIZ_BJHYYS_CHANNEL_CODE);
                        }
                        //OSVRBT28切换50到宜搜
                        if ("OSVRBT28".equals(subChannel) && RandomUtils.isInRatio(20) && provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_BJHYYS_CHANNEL_CODE_TZ, mobileRegionResult.getProvince())) {
                            subscribe.setChannel(MiguApiService.BIZ_BJHYYS_CHANNEL_CODE_TZ);
                        }
                        if (BizConstant.isBjhyZhbChannel(subscribe.getChannel()) && org.apache.commons.lang3.StringUtils.equalsAny(mobileRegionResult.getProvince(), IBusinessCommonService.GUANGXI_PROVINCE, IBusinessCommonService.SHANGHAI_PROVINCE)) {
                            subscribe.setChannel(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W6);
                        }
                        if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince()) && !notLimit) {
                            log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                            return Result.error("暂未开放,敬请期待!");
                        }
                    } else {
                        if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince()) && !notLimit) {
                            log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                            return Result.error("暂未开放,敬请期待!");
                        }
                    }
                } else {
                    return Result.error("该业务只支持移动用户");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统繁忙,请稍后再试!");
        }



        return subscribeService.getOutsideCodeCrack(subscribe);
    }


    /**
     * 外部视频彩铃发送短信
     *
     * @return
     */
    @PostMapping("/outside/getTelecomCaptcha")
    public Result outsideTelecomSendMsg(@RequestParam( value = "channel",defaultValue = "DXVRBT") String channel,
                                        @RequestParam("mobile") String mobile,
                                        @RequestParam(value = "subChannel", defaultValue = "OSVRBT03") String subChannel,
                                        HttpServletRequest request) {
        log.info("第三方渠道获取验证码请求 mobile:{},渠道号:{}", mobile, subChannel);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if(blackListService.isBlackList(mobile)){
            return Result.msgBlackLimit();
        }

        boolean isMonth = dianxinVrbtService.queryPackageExist(mobile, BIZ_DIANXIN_CHANNEL_MAIHE);
        if (isMonth) {
            return Result.error("已有包月，请勿重复开通!");
        }
        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setSubChannel(subChannel);
        subscribe.setChannel(channel);
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        subscribe.setIp(ipAddr);
        subscribe.setUserAgent(userAgent);
        subscribe.setCreateTime(new Date());
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if(mobileRegionResult!=null){
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if(mobileRegionResult.isIspDianxin()){
                    //只开放电信
                    if (!provinceLimitService.isAvailableTelecom(mobileRegionResult.getProvinceId())) {
                        return Result.error("暂未开放,敬请期待!");
                    }
                }else {
                    return Result.error("该业务只支持电信用户");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统繁忙,请稍后再试!");
        }
        return subscribeService.getOutsideCodeCrack(subscribe);
    }

    /**
     * 外部视频彩铃发送短信（融合）
     *
     * @return
     */
    @PostMapping("/outside/getFuseCaptcha")
    public Result outsideFuseSendMsg(@RequestParam("mobile") String mobile,
                                 @RequestParam(value = "subChannel",defaultValue = "OSVRBT13") String subChannel,
                                 HttpServletRequest request) {
        log.info("第三方融合渠道获取验证码请求 mobile:{},渠道号:{}", mobile, subChannel);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if(blackListService.isBlackList(mobile)){
            return Result.msgBlackLimit();
        }
        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setSubChannel(subChannel);
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subChannel);
        if (outsideConfig == null) {
            log.error("无效的外部渠道号:{}", subChannel);
            return Result.error("无效的渠道号!");
        }
        String channelCode = outsideConfig.getChannel();
//        String channelCode = outsideProperties.getChannelCode(subChannel);
        subscribe.setChannel(channelCode);
        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        subscribe.setIp(ipAddr);
        subscribe.setUserAgent(userAgent);
        subscribe.setCreateTime(new Date());
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if(mobileRegionResult!=null){
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if(mobileRegionResult.isIspYidong()){
                    //优先查询骏伯动漫的省份
                    if (!provinceBusinessChannelConfigService.allow("COMIC_JUNBO", mobileRegionResult.getProvince())) {
                        log.warn("融合接口移动省份限制,渠道号:{},手机号:{},省份:{},将尝试为你开通20元音乐包", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                    } else {
                        //开通骏伯咪咕动漫
                        return innerUnionMemberService.outsideForword(mobile, subChannel);
                    }
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("融合接口移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                }else {
                    return Result.error("该业务只支持移动用户");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统繁忙,请稍后再试!");
        }
        return subscribeService.getOutsideCodeCrack(subscribe);
    }


    /**
     * 外部视频彩铃订购
     *
     * @return
     */
    @PostMapping("/outside/order")
    public Result outsideOrder(@RequestParam("mobile") String mobile,
                               @RequestParam("code") String code,
                               @RequestParam(value = "subChannel",defaultValue = "OSVRBT02") String subChannel,
                               @RequestParam(value = "copyrightId", required = false) String copyrightId,
                               @Validated @RequestParam("orderNo") @Size(max = 32, message = "订单号长度最大为32") String osOrderId,
                               HttpServletRequest request) {

        log.info("第三方渠道订阅请求,提交短信验证码 mobile:{},code:{},subChannel:{},copyrightId:{},osOrderId:{}", mobile, code, subChannel, copyrightId, osOrderId);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!code.matches(Regexp.MIGU_SMS_CODE_REG)) {
            return Result.error("验证码错误");
        }
//        subscribe.setCopyrightId(copyrightId);
        return subscribeService.receiveOrderOutsideCrack(osOrderId, code);
    }

    /**
     * 外部视频彩铃订购
     *
     * @return
     */
    @PostMapping("/outside/getTelecomOrder")
    public Result outsideTelecomOrder(@RequestParam("mobile") String mobile,
                                      @RequestParam("code") String code,
                                      @RequestParam(value = "subChannel", defaultValue = "OSVRBT03") String subChannel,
                                      @Validated @RequestParam("orderNo") @Size(max = 32, message = "订单号长度最大为32") String osOrderId,
                                      HttpServletRequest request) {

        log.info("第三方渠道订阅请求,提交短信验证码 mobile:{},code:{},subChannel:{},osOrderId:{}", mobile, code, subChannel, osOrderId);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!code.matches("^\\d{4}$")) {
            return Result.error("验证码错误");
        }
        return subscribeService.receiveOrderOutsideCrack(osOrderId, code);
    }

    /**
     * 外部视频彩铃订购
     *
     * @return
     */
    @PostMapping("/outside/fuseOrder")
    public Result outsideFuseOrder(@RequestParam("mobile") String mobile,
                                   @RequestParam("code") String code,
                                   @RequestParam(value = "subChannel", defaultValue = "OSVRBT13") String subChannel,
                                   @RequestParam(value = "copyrightId", required = false) String copyrightId,
                                   @Validated @RequestParam("orderNo") @Size(max = 32, message = "订单号长度最大为32") String osOrderId,
                                   HttpServletRequest request) {

        log.info("第三方渠道订阅请求,提交短信验证码 mobile:{},code:{},subChannel:{},copyrightId:{},osOrderId:{}", mobile, code, subChannel, copyrightId, osOrderId);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (!code.matches("^\\d{6}$")) {
            return Result.error("验证码错误");
        }
        if (osOrderId.length() < 18) {
            Result comicResult = innerUnionMemberService.outsideForwordOrder(mobile, code, osOrderId, subChannel);
            comicResult.setResult(outsideProperties.getComicJunboRightsUrl() + mobile);
            return comicResult;
        }
//        subscribe.setCopyrightId(copyrightId);
        Result result = subscribeService.receiveOrderOutsideCrack(osOrderId, code);
        result.setResult(outsideProperties.getCpmb20RightsUrl() + mobile);
        return result;
    }




    @ApiOperation(value = "电信铃音分页查询", notes = "电信铃音分页查询")
    @RequestMapping(value = "/telecomVrbtListPage")
    public Result<?> telecomVrbtListPage(CmsMusicTelecom cmsMusicTelecom,
                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                         @RequestParam(name = "pageSize", defaultValue = "6") Integer pageSize,
                                         HttpServletRequest req) {

        QueryWrapper<CmsMusicTelecom> queryWrapper = QueryGenerator.initQueryWrapper(cmsMusicTelecom, req.getParameterMap());
        queryWrapper.orderByAsc("create_time");

        // 过滤vrtb_img为空的数据
        queryWrapper.isNotNull("vrbt_img");
        queryWrapper.ne("vrbt_img", "");

        Page<CmsMusicTelecom> page = new Page<CmsMusicTelecom>(pageNo, pageSize);
        return Result.ok(cmsMusicTelecomService.page(page, queryWrapper));
    }

    @ApiOperation(value = "域名配置", notes = "域名配置")
    @PostMapping(value = "/domainConfig")
    public Result domainConfig(@RequestParam("file") MultipartFile file){
        if (file.isEmpty()) {
            return Result.error("请上传文件");
        }
        String domain = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf("."));
        //验证域名是否有效
        if (!Pattern.matches(BizConstant.DOMAIN_REG, domain)) {
            return Result.error("上传的文件文件名格式错误,请使用正确的域名作为文件名");
        }
//        //获取域名后缀
//        String domianSuffix = domain.substring(domain.lastIndexOf(".") + 1);
        try {
            File target = new File(BizConstant.NGINX_DIR + File.separator + "conf" + File.separator + "ssl" + File.separator + file.getOriginalFilename());
            //文件上传到 nginx conf/ssl目录下
            file.transferTo(target);
            //解压文件并获取需要替换的文件内容
            List<String> replaceList = UnzipUtil.unzip(target);
            final String pemFilename = replaceList.stream().filter(item -> item.endsWith(".pem")).findAny().orElseThrow(IllegalArgumentException::new);
            final String keyFilename = replaceList.stream().filter(item -> item.endsWith(".key")).findAny().orElseThrow(IllegalArgumentException::new);
            File sourceConfigFile = new File(BizConstant.NGINX_SOURCE_CONF_PATH);
            File targetConfigFile = new File(sourceConfigFile.getParent() + File.separator + "ssl_" + domain + ".conf");
            FileUtils.copyFile(sourceConfigFile, targetConfigFile);
            String content = FileUtil.readFile(targetConfigFile);
            content = content.replaceFirst("/data/nginx/conf/ssl/.+?\\.com\\.pem", "/data/nginx/conf/ssl/"+pemFilename);
            content = content.replaceFirst("/data/nginx/conf/ssl/.+?\\.com\\.key", "/data/nginx/conf/ssl/"+keyFilename);
            content = content.replaceFirst("server_name.+?\\.com", "server_name " + domain);
            boolean flag = FileUtil.writeFile(content, targetConfigFile);
            if(!flag){
                return Result.error("域名配置失败");
            }
            log.info("nginx替换后文件内容为:{}", content);
            String result = ExecUtils.execCmd("/usr/bin/nginx", new String[]{"-t"});
            if (!(result.indexOf("successful") > 0)) {
                return Result.error("域名配置失败");
            }
            ExecUtils.execCmd("/usr/bin/nginx", new String[]{"-s", "reload"});
            return Result.ok("域名配置成功");
        } catch (Exception e) {
            return Result.error("域名配置错误:" + e.getMessage());
        }
    }

    @ApiOperation(value = "电信环球网订购消息回调", notes = "电信ISMP订购退订消息回调")
    @GetMapping("/dianxinVrbt/huanqiuwangNotify")
    public String huanqiuwangDianxinVrbtIsmpNotify(@RequestParam(name = "orderid", required = false) String orderid,
                                                   @RequestParam(name = "reqtime", required = false) String reqtime,
                                                   @RequestParam(name = "phoneno", required = false) String phoneno,
                                                   @RequestParam(name = "port", required = false) String port,
                                                   @RequestParam(name = "cmd", required = false) String cmd,
                                                   @RequestParam(name = "status", required = false) String status,
                                                   @RequestParam(name = "extdata", required = false) String extdata) {

        log.info("电信环球网订购消息回调,orderid:{},reqtime:{},phoneno:{},port:{},cmd:{},status:{},extdata:{}",
                orderid, reqtime, phoneno, port, cmd, status, extdata);
        Date actionTime = null;
        try {
            actionTime = DateUtils.yyyymmddhhmmss.get().parse(reqtime);
        } catch (Exception e) {
            log.error("日期转换错误，错误日期：{}", reqtime);
        }
        subscribeService.receiveDianxinHuanqiuwangNotify(phoneno, orderid, status, actionTime);
        return "ok";
    }


    /**
     * 咪咕白金会员回调
     * @param result
     * @return
     */
    @ApiOperation(value = "咪咕白金会员回调", notes = "咪咕白金会员回调")
    @RequestMapping(value = "/bjhyResultCallback", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Result<?> bjhyResultCallback(@RequestBody Result result) {
        String content=SMUtil.sm2Decrypt(result.getResult().toString());
        log.info("咪咕白金会员回调,解密数据->:content:{}", content);
        ObjectNode dataNode= null;
        try {
            dataNode = mapper.readValue(content, ObjectNode.class);
        } catch (Exception e) {
            log.error("咪咕白金会员回调,数据转换异常:",e);
            return Result.error("请求错误！");
        }
        String resCode = dataNode.at("/resCode").asText();
        String resMsg = dataNode.at("/resMsg").asText();
        String mobile = dataNode.at("/mobile").asText();
        String orderId = dataNode.at("/orderId").asText();
        String channelCode = dataNode.at("/channelCode").asText();

        final RemoteResult bjhyResult =miguApiService.bjhyQuery(mobile,channelCode);
        log.info("咪咕白金会员回调，查询是否开通会员->:mobile:{},bjhyResult:{}", mobile,bjhyResult);
        if(bjhyResult!=null){
            Integer status=bjhyResult.isBjhyMember()?BizConstant.SUBSCRIBE_STATUS_SUCCESS:BizConstant.SUBSCRIBE_STATUS_FAIL;
            String  resultStr=new StringBuilder().append("{\"resCode\":\"").append(resCode).append("\",\"resMsg\":\"").append(resMsg).append("\"}").toString();
            Subscribe subscribe=new Subscribe(orderId,mobile,channelCode,status,resultStr);
            Boolean updateStatus=subscribeService.updateBjhy(subscribe);
            if(bjhyResult.isBjhyMember() && updateStatus){
                //包月状态校验60分钟和次日异步校验
                SpringContextUtils.getBean(RabbitMQMsgSender.class).addDelayedVerifyMessage(subscribe);
                return lemobaApiService.buildUserCenterUrl(orderId, mobile, channelCode);
            }
            return Result.error(resMsg);
        }
        return Result.error(resMsg);
    }


    /**
     * 咪咕白金会员（续订）乐摩吧权益领取
     * @param request
     * @return
     */
    @ApiOperation(value = "咪咕白金会员（续订）乐摩吧权益领取", notes = "咪咕白金会员（续订）乐摩吧权益领取")
    @PostMapping(value = "/bjhyRenewRightsCharge")
    @ResponseBody
    @WeChatLogin(menu = "咪咕白金会员（续订）乐摩吧权益领取")
    public Result<?> bjhyRenewRightsCharge(@RequestParam("mobile") String mobile, HttpServletRequest request) {
        log.info("咪咕白金会员（续订）乐摩吧权益领取=>mobile:{},request:{}",mobile,request);
        RemoteResult bjhyResultQX= miguApiService.bjhyQuery(mobile,BizConstant.BJHY_CHANNEL_CODE_QX);
        RemoteResult bjhyResultPP= miguApiService.bjhyQuery(mobile,BizConstant.BJHY_CHANNEL_CODE_PP);
        if(!bjhyResultQX.isBjhyMember() && !bjhyResultPP.isBjhyMember()){
            return Result.error(HttpStatus.INTERNAL_SERVER_ERROR.value(),BizConstant.MEMBER_MSG);
        }
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        //5元本月领取记录
        Integer chargeQX = cmsLemobaChargeLogService.lambdaQuery()
                .eq(LemobaChargeLog::getMobile, mobile)
                .eq(LemobaChargeLog::getChannelCode, BizConstant.BJHY_CHANNEL_CODE_QX)
                .eq(LemobaChargeLog::getRightsMonth, rightsMonth)
                .count();
        //10元本月领取记录
        Integer chargePP = cmsLemobaChargeLogService.lambdaQuery()
                .eq(LemobaChargeLog::getMobile, mobile)
                .eq(LemobaChargeLog::getChannelCode, BizConstant.BJHY_CHANNEL_CODE_PP)
                .eq(LemobaChargeLog::getRightsMonth, rightsMonth)
                .count();
        if(chargeQX>0 && chargePP>0){
            return Result.error(HttpStatus.INTERNAL_SERVER_ERROR.value(),BizConstant.RECEIVE_MSG);
        }
        //是否已经达到领取上限
        Integer bjhyQX = cmsLemobaChargeLogService.lambdaQuery().eq(LemobaChargeLog::getMobile, mobile)
                .eq(LemobaChargeLog::getChannelCode, BizConstant.BJHY_CHANNEL_CODE_QX)
                .count();
        //是否已经达到领取上限
        Integer bjhyPP = cmsLemobaChargeLogService.lambdaQuery().eq(LemobaChargeLog::getMobile, mobile)
                .eq(LemobaChargeLog::getChannelCode, BizConstant.BJHY_CHANNEL_CODE_PP)
                .count();
        if(bjhyQX>BizConstant.BJHY_CHANNEL_COUNT && bjhyPP>BizConstant.BJHY_CHANNEL_COUNT){
            return Result.error(HttpStatus.INTERNAL_SERVER_ERROR.value(),BizConstant.BJHY_RIGHTS_CHARGE_MSG);
        }
        //上个月
        String upRightsMonth=DateUtil.formatYearMonth(LocalDateTime.now().minusMonths(1));
        String orderId = IdWorker.get32UUID();
        if(bjhyResultQX.isBjhyMember() && chargeQX<=0 && bjhyQX<=BizConstant.BJHY_CHANNEL_COUNT){
            //上个月领取记录
            Integer upChargeQX = cmsLemobaChargeLogService.lambdaQuery()
                    .eq(LemobaChargeLog::getMobile, mobile)
                    .eq(LemobaChargeLog::getChannelCode, BizConstant.BJHY_CHANNEL_CODE_QX)
                    .eq(LemobaChargeLog::getRightsMonth, upRightsMonth)
                    .count();
            if(upChargeQX>=1){
                return lemobaApiService.buildUserCenterUrl(orderId, mobile, BizConstant.BJHY_CHANNEL_CODE_QX);
            }
        }
        if(bjhyResultPP.isBjhyMember() && chargePP<=0 && bjhyPP<=BizConstant.BJHY_CHANNEL_COUNT){
            //上个月领取记录
            Integer upChargePP = cmsLemobaChargeLogService.lambdaQuery()
                    .eq(LemobaChargeLog::getMobile, mobile)
                    .eq(LemobaChargeLog::getChannelCode, BizConstant.BJHY_CHANNEL_CODE_PP)
                    .eq(LemobaChargeLog::getRightsMonth, upRightsMonth)
                    .count();
            if(upChargePP>=1){
                return lemobaApiService.buildUserCenterUrl(orderId, mobile, BizConstant.BJHY_CHANNEL_CODE_PP);
            }
        }
        return Result.ok(BizConstant.NOT_RIGHTS_MSG);
    }


    @ApiOperation(value = "咪咕白金会员验证", notes = "咪咕白金会员验证")
    @PostMapping(value = "/check/migubjhy")
    @ResponseBody
    public Result<?> checkMigubjhy(@RequestParam("mobile") String mobile,@RequestParam("channelCode") String channelCode) {
        log.info("咪咕白金会员验证=>mobile:{},channelCode:{}",mobile,channelCode);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        RemoteResult bjhyResult= miguApiService.bjhyQuery(mobile,channelCode);
        log.info("咪咕白金会员回调，查询是否开通会员->:mobile:{},bjhyResult:{}",mobile,bjhyResult);
        if(bjhyResult.isBjhyMember()){
            return Result.ok(true);
        }else{
            return Result.ok(false);
        }
    }

    @ApiOperation(value = "解密抖音小程序手机号", notes = "解密抖音小程序手机号")
    @PostMapping(value = "/douyinApp/phoneNumber")
    @ResponseBody
    public Result<?> douyinAppPhoneNunmber(@RequestParam("data") String data,@RequestParam("code") String code,@RequestParam("iv") String iv) {
        return douyinAppService.phoneNumber(data, code, iv);
    }

    @ApiOperation(value = "联通沃音乐订购查询", notes = "联通沃音乐订购查询")
    @PostMapping(value = "/womusicSubscribeQuery")
    public Result<?> womusicSubscribeQuery(String mobile,@RequestParam(defaultValue = BizConstant.WO_MUSIC_PRODUCT_ID) String serviceId) {

        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        FebsResponse febsResponse = memberService.isMemberAndIsReceiveRights(mobile, serviceId,null,false,0L);
        if(febsResponse.isOK()){
            return Result.ok("您已订购联通沃音乐包月业务");
        }else{
            return Result.ok("您未订购联通沃音乐包月业务");
        }


    }

    @ApiOperation(value = "重庆移动获取短信报备", notes = "重庆移动获取短信报备")
    @PostMapping(value = "/chongqing/getCaptcha")
    public Result getSmsCode(@RequestParam String mobile,@RequestParam(defaultValue = BIZ_TYPE_CQYD_VRBT_DX) String channelCode) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return Result.noauth("系统繁忙,请稍后再试");
        } else {
            if (!(BizConstant.CHONGQING_PROVINCE.equals(regionResult.getProvince()) && regionResult.isIspYidong())) {
                return Result.noauth("暂只支持重庆移动用户");
            }
        }
        String bizCode = "";
        if (BIZ_TYPE_CQYD_VRBT_DX.equals(channelCode)) {
            bizCode = chongqingYidongVrbtProperties.getVrbtDxCode();
        } else if (BIZ_TYPE_CQYD_VRBT_DX.equals(channelCode)) {
            bizCode = chongqingYidongVrbtProperties.getFgQybCode();
        } else if (BIZ_TYPE_CQYD_MGHYHYB.equals(channelCode)) {
            bizCode = chongqingYidongVrbtProperties.getMghyHybCode();
        }
        ChongqingMobileResult chongqingMobileResult = chongqingYidongVrbtService.getSms(mobile,bizCode);
        if (chongqingMobileResult.isOK()) {
            return Result.noauth("验证码已发送", chongqingMobileResult.getData().getData());
        } else {
            return Result.error("获取验证码失败");
        }
    }

    @ApiOperation(value = "重庆移动提交短信报备", notes = "重庆移动提交短信报备")
    @PostMapping(value = "/chongqing/submitCaptcha")
    public Result smsCode(@RequestParam String mobile,@RequestParam String code,@RequestParam String orderNo,@RequestParam(defaultValue = BIZ_TYPE_CQYD_VRBT_DX) String channelCode) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return Result.noauth("系统繁忙,请稍后再试");
        } else {
            if (!(BizConstant.CHONGQING_PROVINCE.equals(regionResult.getProvince()) && regionResult.isIspYidong())) {
                return Result.noauth("暂只支持重庆移动用户");
            }
        }
        //验证短信验证码是否合法
        if (!code.matches(Regexp.MIGU_SMS_CODE_REG)) {
            return Result.error("验证码错误");
        }
        String bizCode = "";
        if(BIZ_TYPE_CQYD_VRBT_DX.equals(channelCode)){
            bizCode = chongqingYidongVrbtProperties.getVrbtDxCode();
        }else if(BIZ_TYPE_CQYD_VRBT_DX.equals(channelCode)){
            bizCode = chongqingYidongVrbtProperties.getFgQybCode();
        }
        ChongqingMobileResult chongqingMobileResult = chongqingYidongVrbtService.smsCode(mobile, code, orderNo, bizCode);
        if (chongqingMobileResult.isOK()) {
            return Result.ok("订阅成功");
        } else {
            return Result.error(chongqingMobileResult.getData().getRespMsg());
        }
    }


    /**
     * 联通喜马拉雅破解计费通知
     * @param cporderid
     * @param sid
     * @param time
     * @param fee
     * @param tel
     * @param code
     * @param vaccode
     * @param msg
     * @return
     */
    @GetMapping(value = "/xdcl/notify")
    @ResponseBody
    public String xdclCrackNotify(@RequestParam(value = "cporderid", required = false, defaultValue = "") String cporderid,
                                  @RequestParam(value = "sid", required = false, defaultValue = "") String sid,
                                  @RequestParam(value = "time", required = false, defaultValue = "") String time,
                                  @RequestParam(value = "fee", required = false, defaultValue = "") String fee,
                                  @RequestParam(value = "tel", required = false, defaultValue = "") String tel,
                                  @RequestParam(value = "code", required = false, defaultValue = "") String code,
                                  @RequestParam(value = "vaccode", required = false, defaultValue = "") String vaccode,
                                  @RequestParam(value = "msg", required = false, defaultValue = "") String msg) {
        log.info("联通心动彩铃计费通知,异步通知=>cporderid:{},sid:{},time:{},fee:{},tel:{},code:{},vaccode:{},msg:{}", cporderid, sid, time, fee, tel, code, vaccode, msg);
        return "ok";
    }



    /**
     * 查询有效期内未使用的券码
     * @return
     */
    @ApiOperation(value = "查询有效期内未使用的券码", notes = "查询有效期内未使用的券码")
    @PostMapping(value = "/getEffectiveDateCouponCode")
    @ResponseBody
    public FebsResponse getEffectiveDateCouponCode(@RequestParam("couponId") String couponId,HttpServletRequest request) {
        log.info("查询有效期内未使用的券码=>couponId:{}",couponId);
        CouponCode couponCode=couponCodeService.getEffectiveDateCouponCode(couponId);
        if(couponCode!=null){
            return new FebsResponse().success().data(couponCode);
        }
        return new FebsResponse().fail();
    }
    /**
     * 更新券码状态
     * @return
     */
    @ApiOperation(value = "更新券码状态", notes = "更新券码状态")
    @PostMapping(value = "/updateCouponCodeStatus")
    @ResponseBody
    public FebsResponse updateCouponCodeStatus(@RequestParam("id") String id,
                                               @RequestParam("orderId") String orderId,
                                               @RequestParam(name = "status", required = false, defaultValue ="1") String status) {
        log.info("更新券码状态=>主键:{},订单号:{},激活码状态:{}",id,orderId,status);
        couponCodeService.updateCouponCodeStatus(id,orderId,Integer.valueOf(status));
        return new FebsResponse().success();
    }
    /**
     * 获取广告位配置
     *
     * @return
     */
    //@AutoLog(value = "广告位")
    @ApiOperation(value="广告位", notes="广告位")
    @GetMapping(value = "/getAdSiteConfig")
    public Result<?> getConfig(@RequestParam(name = "category", required = false, defaultValue ="DOUYING_CENTER")  String category) {
        LambdaQueryWrapper<AdSite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdSite::getCategory,category);
        queryWrapper.eq(AdSite::getStatus,1);
        List<AdSite> list = adSiteService.list(queryWrapper);
        return Result.ok(list);
    }

    /**
     * 本机校验
     *
     * @return
     */
    //@AutoLog(value = "本机校验")
    @ApiOperation(value="本机校验", notes="本机校验")
    @GetMapping(value = "/selfPhoneValidate")
    public Result<?> selfPhoneValidate(@RequestParam("accessToken") String accessToken,
                                        @RequestParam("token") String token,
                                        @RequestParam("phone") String phone) throws JsonProcessingException {
        Map<String, Object> map = new HashMap<>();
        /** 产品密钥ID，产品标识 */
        String secretId = selfPhoneValidateProperties.getSecretId();
        /** 产品私有密钥，服务端生成签名信息使用，请严格保管，避免泄露 */
        String secretKey = selfPhoneValidateProperties.getSecretKey();
        /** 业务ID，易盾根据产品业务特点分配 */
        String businessId = selfPhoneValidateProperties.getBusinessId();
        /** 本机认证服务身份证实人认证在线检测接口地址 */
        String selfPhoneValidateUrl = selfPhoneValidateProperties.getSelfPhoneValidateUrl();

        Map<String, String> params = new HashMap<String, String>();
        // 1.设置公共参数
        params.put("secretId", secretId);
        params.put("businessId", businessId);
        params.put("version", "v1");
        // 格式为时间戳格式, 与当前时间差值不能超过6s
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        // 32随机字符串
        params.put("nonce", UUID.randomUUID().toString().replace("-", "")); // 32随机字符串
        // 2.设置私有参数d
        params.put("accessToken", accessToken); // 运营商预取号获取到的token
        params.put("token", token); // 易盾返回的token
        params.put("phone", phone);

        // 3.生成签名信息
        String signature = null;
        try {
            signature = com.eleven.cms.util.SignatureUtils.genSignature(secretKey,params);
        } catch (UnsupportedEncodingException e) {
            log.error("本机校验生成签名出错：" + e.getMessage());
        }
        params.put("signature", signature);
        // 4.发送HTTP请求
        String response = HttpClient4Utils.sendPost(selfPhoneValidateUrl, params, Consts.UTF_8);
        log.info("本机校验结果：" + response);

        final JsonNode resultJson = mapper.readTree(response);
        if (resultJson.at("/code").asInt()==200) {
            Integer result = resultJson.at("/data/result").asInt();
            if (result == 1) {
                map.put("result","1001");
                map.put("message","校验成功");
            } else if (result == 2) {
                map.put("result","1002");
                map.put("message","校验失败");
            } else {
                map.put("result","1003");
                map.put("message","校验失败");
            }
        } else {
            map.put("result","1004");
            map.put("message","校验失败");
        }
        return Result.ok(map);
    }
    /**
     * 支付 - 微信支付、支付宝支付
     *
     * @return
     */
    //@AutoLog(value = "支付")
    @ApiOperation(value="支付", notes="支付")
    @GetMapping(value = "/payment")
    public void payment(@RequestParam("mobile") String mobile, HttpServletResponse response) {

        String payWay = "AliPay";
        String buyId = "spcl";
        String price = "6";
        String subject = "视频彩铃订阅-炫视专属6元包（月包）";
        String orderIdPrefix = payWay.substring(0,1);
        String buyIdPrefix = buyId.substring(0,1);
        String orderId = orderIdPrefix + buyIdPrefix+mobile+"_"+System.currentTimeMillis();

        SpclPayLog spclPayLog = new SpclPayLog();
        spclPayLog.setOutTradeNo(orderId);
        spclPayLog.setPayWay(payWay);
        spclPayLog.setMobile(mobile);
        spclPayLog.setBuyGoodsId(buyId);
        spclPayLog.setPrice(price.toString());
        spclPayLog.setSubject(subject);
        spclPayLog.setCreateTime(new Date());
        spclPayLogService.save(spclPayLog);
        //微信支付
        if("WXPAY".equals(payWay)){
            String totalAmount = price.toString();
            try {
                Map<String, String> resp = wxpayService.pay(orderId, totalAmount, subject);
                String  mwebUrl = resp.get("mweb_url");
                String returnUrl = wxPayPropertiesConfig.getReturnUrl();
                String returnUrlEncode = UriUtils.encode(returnUrl + "?orderId=" + orderId, StandardCharsets.UTF_8);
                String mwebUrlWithRedirect = mwebUrl + "&redirect_url=" + returnUrlEncode;
                //String mwebUrlWithRedirect = mwebUrl + "&redirect_url=" + returnUrl + "?orderId=" + orderId;
                log.info("微信支付支付跳转地址->mwebUrlWithRedirect:{}",mwebUrlWithRedirect);

            } catch (Exception e) {
                log.info("微信支付出错：" + e.getMessage());
            }
        }else{
            //支付宝金额是小数字符串
            String totalAmount = price;
            //String totalAmount = productEnum == BjydBaiduProductEnum.QUARTER ? "0.02" : "0.01";
            try {
                alipayService.wapPay(orderId,totalAmount,subject,subject,response);
            } catch (IOException e) {
                log.info("支付宝支付出错：" + e.getMessage());
            }
        }
    }


    //微信用户支付通知
    @RequestMapping(value="wxpayResultNotify",produces="text/plain")
    @ResponseBody
    public String wxpayResultNotify(HttpServletRequest request) {

        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()){
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);

            WxpayNotifyParam notifyParam = wxpayService.payResultNotify(notifyXml);
            log.info("微信支付通知notifyParam = " + notifyParam);
            final String outTradeNo = notifyParam.getOutTradeNo();
            SpclPayLog spclPayLog = spclPayLogService.findByOutTradeNo(outTradeNo);
            if(spclPayLog==null){
                log.error("微信支付通知未找到支付日志,订单号:{}",outTradeNo);
                return failRespXml;
            }
            final String resultCode = notifyParam.getResultCode();
            boolean isPaySucc = org.apache.commons.lang3.StringUtils.equals(resultCode, WXPayConstants.SUCCESS);
            //更新支付日志
            spclPayLog.setStatus(isPaySucc?1:2);
            spclPayLog.setTradeStatus(resultCode);
            Integer totalAmount = Integer.parseInt(notifyParam.getTotalFee());
            spclPayLog.setTotalAmount(totalAmount);
            spclPayLog.setTradeNo(notifyParam.getTransactionId());
            spclPayLog.setPayTime(DateUtil.localDateTimeToDate(notifyParam.getTimeEnd()));
            spclPayLog.setAppId(notifyParam.getAppId());
            spclPayLog.setMchId(notifyParam.getMchId());
            spclPayLog.setOpenId(notifyParam.getOpenId());
            spclPayLog.setModifyTime(new Date());
            spclPayLogService.updateById(spclPayLog);
        } catch (Exception e) {
            e.printStackTrace();
            return failRespXml;
        }
        return succRespXml;
    }

    //支付宝用户支付通知
    @RequestMapping(value = "alipayResultNotify",produces="text/plain")
    @ResponseBody
    public String alipayResultNotify(@RequestParam Map<String,String> requestParams) {
        String succRespMsg = "success";
        String failRespMsg = "failure";
        try {
            AlipayNotifyParam notifyParam = alipayService.payResultNotify(requestParams);
            log.info("支付宝支付通知notifyParam:{} ",notifyParam);
            final String outTradeNo = notifyParam.getOutTradeNo();
            SpclPayLog spclPayLog = spclPayLogService.findByOutTradeNo(outTradeNo);
            if(spclPayLog==null){
                log.error("支付宝支付通知未找到支付日志,订单号:{}",outTradeNo);
                return failRespMsg;
            }
            final String tradeStatus = notifyParam.getTradeStatus();
            // 我间隔一年收到了TRADE_FINISHED通知,又给充值了,等于是收到了TRADE_SUCCESS和TRADE_FINISHED两次通知
            if(AliPayConfig.AlipayTradeStatus.TRADE_FINISHED.equals(tradeStatus)){
                log.info("支付宝支付通知交易完成TRADE_FINISHED,此通知会在TRADE_SUCCESS后二次通知,所以忽略");
                return succRespMsg;
            }
            boolean isPaySucc = AliPayConfig.AlipayTradeStatus.TRADE_SUCCESS.equals(tradeStatus) /*|| AlipayTradeStatus.TRADE_FINISHED.equals(tradeStatus)*/;
            //更新支付日志
            spclPayLog.setStatus(isPaySucc?1:2);
            spclPayLog.setTradeStatus(tradeStatus);
            Integer totalAmount = notifyParam.getTotalAmount().multiply(BigDecimal.valueOf(100L)).intValue();
            spclPayLog.setTotalAmount(totalAmount);
            spclPayLog.setTradeNo(notifyParam.getTradeNo());
            spclPayLog.setPayTime(DateUtil.localDateTimeToDate(notifyParam.getGmtPayment()));
            spclPayLog.setBuyerId(notifyParam.getBuyerId());
            spclPayLog.setAppId(notifyParam.getAppId());
            spclPayLog.setModifyTime(new Date());
            if(isPaySucc) {
                RemoteResult remoteResult = miguApiService.vrbtZeroOrder(spclPayLog.getMobile(), "014X0A3", 1);
                spclPayLog.setRemark(remoteResult.getResCode() + "," + remoteResult.getResMsg());


                //支付成功发送短信
                Map<String,String> smsMap= Maps.newHashMap();
                smsMap.put("payTime",DateUtil.getDateFormat(new Date(),DateUtil.FULL_TIME_SPLIT_PATTERN));
                smsModelService.sendSms(spclPayLog.getMobile(),"014X0A3","VRBT_YD_ALIPAY_H5","2",smsMap);
            }
            spclPayLogService.updateById(spclPayLog);
        } catch (Exception e) {
            log.error("支付宝支付通知出错：",e);
            return failRespMsg;
        }
        return succRespMsg;
    }

    /**
     * 骏伯联通计费点回调通知
     * code=0 成功 data为订单号
     * @return
     */
    //@AutoLog(value = "骏伯联通计费点回调通知")
    @ApiOperation(value="骏伯联通计费点回调通知", notes="骏伯联通计费点回调通知")
    @PostMapping(value = "/unicom/vrbt/callback")
    public String unicomCallback(@RequestParam("code") String code,
                                 @RequestParam("msg") String msg,
                                 @RequestParam("data") String data) {
        log.info("骏伯联通计费点回调通知入参=>code:{},msg:{},data:{}", code, msg, data);
        subscribeService.receiveLiantongJunboNotify(data,code,msg);
        return "1";
    }
    /**
     * 抖音查询权益列表
     * @param mobile
     * @return
     */
    @ApiOperation(value = "抖音查询权益列表", notes = "抖音查询权益列表")
    @PostMapping(value = "/douyin/query/rightsList")
    @ResponseBody
    public FebsResponse douyinQueryRightsList(@RequestParam(value = "mobile", required = false, defaultValue = "") String mobile) {
        log.info("抖音查询权益列表=>mobile:{}",mobile);
        return unifyRightsFeignClient.douyinQueryRightsList(mobile);
    }

    /**
     * 快马回调通知
     * code=0 成功 data为订单号
     * @return
     */
    //@AutoLog(value = "快马回调通知")
    @ApiOperation(value="快马回调通知", notes="快马回调通知")
    @PostMapping(value = "/kuaima/callback")
    public String kuaimaCallback(@RequestBody JsonNode jsonNode) {
        log.info("快马回调通知参数:{}", jsonNode.toString());
        jsonNode.get("orderNo");
//        subscribeService.receiveLiantongJunboNotify(data,code,msg);
        return "success";
    }


    /**
     * 阿里云本机校验-获取H5认证授权Token
     *
     * @return
     */
    ////@AutoLog(value = "阿里云本机校验-获取H5认证授权Token")
    //@ApiOperation(value="阿里云本机校验-获取H5认证授权Token", notes="阿里云本机校验-获取H5认证授权Token")
    //@GetMapping(value = "/aliGetAuthToken")
    //public Result<?> aliGetAuthToken() throws Exception {
    //    GetAuthTokenResponseBody.GetAuthTokenResponseBodyTokenInfo tokenInfo = null;
    //
    //    try {
    //        com.aliyun.dypnsapi20170525.Client client = AliUtil.createClient(aliVerifyPhoneProperties.getAccessKeyId(),
    //                aliVerifyPhoneProperties.getAccessKeySecret(),
    //                aliVerifyPhoneProperties.getEndpoint());
    //        GetAuthTokenRequest getAuthTokenRequest = new GetAuthTokenRequest()
    //                .setUrl(aliVerifyPhoneProperties.getUrl())
    //                .setOrigin(aliVerifyPhoneProperties.getOrigin());
    //        RuntimeOptions runtime = new RuntimeOptions();
    //        GetAuthTokenResponse authTokenWithOptions = client.getAuthTokenWithOptions(getAuthTokenRequest, runtime);
    //        tokenInfo = authTokenWithOptions.getBody().getTokenInfo();
    //    } catch (TeaException error) {
    //        log.error("阿里云本机校验-获取H5认证授权Token出错：" + error.message);
    //    }
    //    return Result.ok(tokenInfo);
    //}

    /**
     * 阿里云本机校验-使用Token验证手机号码
     *
     * @return
     */
    ////@AutoLog(value = "阿里云本机校验-使用Token验证手机号码")
    //@ApiOperation(value="阿里云本机校验-使用Token验证手机号码", notes="阿里云本机校验-使用Token验证手机号码")
    //@GetMapping(value = "/aliVerifyPhoneWithToken")
    //public Result<?> aliVerifyPhoneWithToken(@RequestParam("phoneNumber") String phoneNumber,@RequestParam("spToken") String spToken){
    //
    //    log.info("阿里云本机校验-使用Token验证手机号码入参=>phoneNumber:{},spToken:{}", phoneNumber, spToken);
    //    Map<String, String> map = new HashMap<>();
    //    map.put("code","0");
    //    map.put("message","校验失败");
    //    try {
    //        com.aliyun.dypnsapi20170525.Client client = AliUtil.createClient(aliVerifyPhoneProperties.getAccessKeyId(),
    //                aliVerifyPhoneProperties.getAccessKeySecret(),
    //                aliVerifyPhoneProperties.getEndpoint());
    //        VerifyPhoneWithTokenRequest verifyPhoneWithTokenRequest = new VerifyPhoneWithTokenRequest()
    //                .setPhoneNumber(phoneNumber)
    //                .setSpToken(spToken);
    //        RuntimeOptions runtime = new RuntimeOptions();
    //        VerifyPhoneWithTokenResponse verifyPhoneWithTokenResponse = client.verifyPhoneWithTokenWithOptions(verifyPhoneWithTokenRequest, runtime);
    //        VerifyPhoneWithTokenResponseBody.VerifyPhoneWithTokenResponseBodyGateVerify gateVerify = verifyPhoneWithTokenResponse.getBody().getGateVerify();
    //        if(gateVerify != null){
    //            String verifyResult = gateVerify.getVerifyResult();
    //            log.info("阿里云本机校验-使用Token验证手机号码result=>phoneNumber:{},spToken:{},verifyResult:{}", phoneNumber, spToken, verifyResult);
    //            if("PASS".equals(verifyResult)){
    //                map.put("code","1");
    //                map.put("message","校验成功");
    //            }
    //        }else{
    //            log.info("阿里云本机校验-使用Token验证手机号码接口gateVerify为空,phoneNumber:{}",phoneNumber);
    //        }
    //    } catch (Exception e) {
    //        log.info("阿里云本机校验-使用Token验证手机号码出错=>phoneNumber:{},错误信息:{}", phoneNumber, e);
    //    }
    //    return Result.ok(map);
    //}

    @GetMapping("queryIsp")
    @ResponseBody
    public Result queryIsp(@RequestParam String mobile){
        MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return Result.error("系统繁忙,请稍后再试");
        } else {
            return Result.okAndSetData(regionResult.getOperator());
        }
    }

    @GetMapping("queryCity")
    @ResponseBody
    public Result queryCity(@RequestParam String mobile){
        MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return Result.error("系统繁忙,请稍后再试");
        } else {
            return Result.okAndSetData(regionResult.getCity());
        }
    }

    @GetMapping("queryProvince")
    @ResponseBody
    public Result queryProvince(@RequestParam String mobile){
        MobileRegionResult regionResult = mobileRegionService.query(mobile);
        if (regionResult == null) {
            return Result.error("系统繁忙,请稍后再试");
        } else {
            return Result.okAndSetData(regionResult.getProvince());
        }
    }

    /**
     * 号段信息查询
     * @param mobile
     * @return
     */
    @GetMapping("region")
    @ResponseBody
    public MobileRegionResult region(@RequestParam String mobile){
        return mobileRegionService.query(mobile);
    }


    /**
     * 北岸唐唱音乐包咪咕直接登录
     * @param mobile
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "北岸唐唱音乐包咪咕直接登录", notes = "北岸唐唱音乐包咪咕直接登录")
    @PostMapping(value = "/batc/directLogin")
    public Result<?> batcDirectLogin(String mobile, String channelCode, HttpServletRequest request) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        final String ipAddr = IPUtils.getIpAddr(request);

        log.info("咪咕直接登录=>手机号:{},渠道号:{},ip:{}", mobile, channelCode, ipAddr);
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult.isIspYidong() && !vrbtChannelProvinceConfigService.allow(channelCode, mobileRegionResult.getProvince())) {
            return Result.error(511,"to_comic");
        }
        RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
        if (result.isOK()) {
            Result resp = Result.ok("登录成功");
            resp.setResult(result.getToken());
            return resp;
        } else {
            return Result.noauth("登录失败");
        }
    }

    /**
     * 查询所有订购业务
     *
     * @param mobile
     * @return
     */
    @ApiOperation(value = "查询所有订购业务", notes = "查询所有订购业务")
    @GetMapping(value = "/queryAllBizByMobile")
    public Result<?> queryAllBizByMobile(String mobile) {
        return Result.ok(esDataService.queryAllBizByMobile(mobile));
    }

    /**
     * 退订所有订购业务
     *
     * @param mobile
     * @return
     */
    @ApiOperation(value = "退订所有订购业务", notes = "退订所有订购业务")
    @GetMapping(value = "/cancelBizByMobile")
    public Result<?> queryAllBizByMobile(@RequestParam(value = "mobile") String mobile,
                                         @RequestParam(value = "bizType") String bizType,
                                         @RequestParam(value = "channel") String channel,
                                         @RequestParam(value = "serviceId") String serviceId) {
        try {
            complaintService.cancelOrder(mobile, channel, bizType, serviceId);
            return Result.ok("业务退订完成");
        }catch (Exception e){
            return Result.error("业务退订失败，请稍后再试");
        }
    }

    /**
     * IVR轨迹事件通知
     * @return
     */
    ////@AutoLog(value = "IVR轨迹事件通知")
    @ApiOperation(value = "IVR轨迹事件通知", notes = "IVR轨迹事件通知")
    @RequestMapping(value = "/ivrTrackEventNotify")
    public Result<?> ivrTrackEventNotify(@RequestParam Map<String,Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        //{"channelCode":"00210Q6","msisdn":"a073kK15n1lWaujfzCC+xg==","bizCode":"698039020108689345","loginTime":"20220720151512","orderTime":"20220720151214"}
        //MiguAppLoginRecord miguAppLoginRecord = new MiguAppLoginRecord();
        try {
            log.info("IVR轨迹事件通知,参数map:{},json数据:{}", requestMap,jsonNode);
        } catch (Exception e) {
            log.info("IVR轨迹事件通知,参数map:{},json数据:{},异常!", requestMap,jsonNode,e);
            return Result.error("轨迹时间处理失败");
        }

        return Result.ok();
    }

    /**
     * 曲库内容一点下线同步通知
     *  0100864 成都市悠然荐音科技有限公司   https://crbt.cdyrjygs.com/cms-vrbt/api/miguMusicOfflineNotify
     *  0100537 成都麦禾文化传播有限公司    https://read.hongsengtech.com/mugu_music/clickoffCallback.do
     *  0100918 北京华岸工贸有限责任公司   https://crbt.cdyrjygs.com/cms-vrbt/api/huaanOfflineNotify
     *  0100510 北京虹软协创通讯技术有限公司   https://crbt.cdyrjygs.com/cms-vrbt/api/hongruanOfflineNotify
     * @return
     */
    ////@AutoLog(value = "曲库内容一点下线同步通知")
    @ApiOperation(value = "曲库内容一点下线同步通知", notes = "曲库内容一点下线同步通知")
    @RequestMapping(value = {"/miguMusicOfflineNotify", "/miguMusic/clickoffCallback","huaanOfflineNotify","hongruanOfflineNotify"} )
    public String miguMusicOfflineNotify(@RequestParam Map<String,Object> requestMap, @RequestBody(required = false) JsonNode jsonNode, HttpServletRequest request) {
        //{
        //   "timestamp": "20220801104229",
        //   "transactionId": "e1da89b1-9748-47ae-93bf-91e57585614400100D",
        //   "musicProduct": {
        //      "copyrightId": "69929501144",
        //      "copyrightPlusId": "699295011441",
        //      "contentId": "12345678901234567",
        //      "bizType": "1",
        //      "enCodeMusicId": "f8c5913fd7c57682dfd0f96a7d37a353",
        //      "coprHiddenType": "7"
        //   }
        //}

        final String reqUrl = request.getRequestURL().toString();
        String lastPath = org.apache.commons.lang3.StringUtils.substringAfterLast(reqUrl,"/");
        final ImmutableMap<String, Pair<String, String>> confMap = ImmutableMap.of(
                "miguMusicOfflineNotify", Pair.of("0100864", "4a7018e8e78049ff8c4dbcc1532a469b"),
                "clickoffCallback", Pair.of("0100537", "7fe76102949b42ae8331263fd01f4922"),
                "huaanOfflineNotify", Pair.of("0100918", "ecfbf83a2a584a7baf9775dbf868bd05"),
                "hongruanOfflineNotify", Pair.of("0100510", "c28723f2530b4af38a2c26561e5c16e3"));

        final Pair<String, String> conf = confMap.get(lastPath);
        String channelId = conf.getLeft();
        String signKey = conf.getRight();
        try {
            //log.info("曲库内容一点下线同步通知,reqUrl:{},参数map:{},json数据:{}", reqUrl,requestMap,jsonNode);
            final String copyrightId = jsonNode.at("/musicProduct/copyrightId").asText();
            final String transactionId = jsonNode.at("/transactionId").asText();
            musicService.offline(copyrightId);
            miguApiService.clickoffReceipt(channelId, signKey, transactionId);
            //转发彩铃项目下线
            Request req = new Request.Builder().url("http://127.0.0.1:9528/migu-crbt/api/miguMusicOfflineNotify")
                    .post(okhttp3.RequestBody.create(okhttp3.MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), jsonNode.toString()))
                    .build();
            try (Response response = OkHttpClientUtils.getSingletonInstance().newCall(req).execute()) {
                /*String content = */response.body().string();
                //log.info("曲库内容一点下线同步通知-转发到彩铃项目-响应:{}", content);
            }
        } catch (Exception e) {
            log.info("曲库内容一点下线同步通知,reqUrl:{},参数map:{},json数据:{},异常!", reqUrl,requestMap,jsonNode,e);
            return "{\"resCode\":\"999004\",\"resMsg\":\"系统繁忙\"}";
        }

        return "{\"resCode\":\"000000\",\"resMsg\":\"成功\"}";
    }

    public static void main(String[] args) throws Exception {
        String code = "{\\\"code\\\":200,\\\"message\\\":\\\"\\u4FDD\\u5B58\\u6210\\u529F\\\"}";
        String code2 = "{\"code\":200,\"message\":\"保存成功\"}";
        // unicode 转换成 中文
        System.out.println(StringEscapeUtils.unescapeJava(code));
        // 中文转换成 unicode
        System.out.println(StringEscapeUtils.escapeJava(code2));
    }

    /**
     * 使用短信模板发送短信
     * @param mobile 手机号
     * @param channel 渠道号
     * @param serviceId 业务ID
     * @param serviceType 短信类型
     * @param code
     * @return
     */
    @PostMapping("/send/sms")
    @ResponseBody
    public FebsResponse sendSms(@RequestParam("mobile") String mobile,
                                @RequestParam("channel") String channel,
                                @RequestParam("serviceId") String serviceId,
                                @RequestParam("serviceType") String serviceType,
                                @RequestParam(value = "code", required = false, defaultValue = "") String code) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().code(String.valueOf(CommonConstant.SC_JEECG_NO_AUTH)).message("手机号错误！");
        }
        if(StringUtil.isNotBlank(code)){
            boolean result = smsModelService.sendSms(mobile,channel,serviceId,serviceType,code);
            if (!result) {
                return new FebsResponse().code(String.valueOf(CommonConstant.SC_JEECG_NO_AUTH)).message("发送短信验证码失败,请稍后再试");
            }
            return new FebsResponse().success("验证码已发送至你的手机,5分钟内有效");
        }else{
            boolean result = smsModelService.sendSms(mobile,channel,serviceId,serviceType);
            if (!result) {
                return new FebsResponse().code(String.valueOf(CommonConstant.SC_JEECG_NO_AUTH)).message("短信发送失败");
            }
            return new FebsResponse().success("短信发送成功");
        }
    }

    /**
     *  feign发送短信
     * @param phoneNumber 手机号
     * @param smsContent 短信内容
     * @return
     */
    @PostMapping("/feign/forwardSendSms")
    @ResponseBody
    public FebsResponse feignForwardSendSms(@RequestParam("phoneNumber") String phoneNumber,
                                            @RequestParam("smsContent") String smsContent,
                                            HttpServletRequest request) {
        final String ipAddr = IPUtils.getIpAddr(request);
        log.info("feign发送短信=>phoneNumber:{},ip:{}", phoneNumber, ipAddr);
        if (Strings.isNullOrEmpty(phoneNumber) || !phoneNumber.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail("手机号错误!");
        }
        final boolean isSend = datangSmsService.sendSms(phoneNumber, smsContent);
        return isSend ? new FebsResponse().success("短信已发送") : new FebsResponse().fail("短信发送失败");
    }


    /**
     * 乐摩吧咪咕直接登录
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/bjhyLimitDirectLogin")
    @ResponseBody
    public Result<?> bjhyLimitDirectLogin(@RequestParam("mobile") String mobile, @RequestParam(name = "channelCode") String channelCode, @RequestParam(name = "subChannel") String subChannel, HttpServletRequest request) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult == null || !mobileRegionResult.isIspYidong()) {
            return Result.noauth("暂时未开放,敬请期待");
        }
        boolean allow = provinceBusinessChannelConfigService.allow(channelCode, mobileRegionResult.getProvince());
        ;
        if (!allow) {
            return Result.noauth("暂时未开放,敬请期待");
        }
        RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
        if (!result.isOK()) {
            return Result.noauth("登录失败");
        }
        final String ipAddr = IPUtils.getIpAddr(request);
        final String bizType = getBizTypeByMiguChannel(channelCode);
        Subscribe subscribe = new Subscribe();
        subscribe.setBizType(bizType);
        subscribe.setIp(ipAddr);
        subscribe.setProvince(mobileRegionResult.getProvince());
        subscribe.setCity(mobileRegionResult.getCity());
        subscribe.setMobile(mobile);
        String isp = mobileRegionResult.getOperator();
        subscribe.setIsp(isp);
        subscribe.setSubChannel(subChannel);
        subscribe.setChannel(channelCode);
        subscribeService.createSubscribeDbAndEs(subscribe);
        Result resp = Result.ok("登录成功");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("token", result.getToken());
        dataNode.put("orderId", subscribe.getId());
        resp.setResult(dataNode);
        return resp;
    }

    /**
     * 话费充值
     * @param state
     * @param request
     * @return
     */
    @PostMapping("/fee/recharge")
    @ResponseBody
    public FebsResponse feeRecharge(@RequestParam(name = "state", required = false, defaultValue = "") String state,@RequestParam(name = "date", required = false, defaultValue = "") String date,@RequestParam(name = "serviceId", required = false, defaultValue = "")String serviceId,HttpServletRequest request) {
        if("batc".equals(state)){
            log.info("话费充值表同步,20元北岸唐唱音乐包");
            memberService.feeRechargeAdd("00210Q6","698039020108689345",date);
        }else if("comic".equals(state)){
            log.info("话费充值表同步,骏伯咪咕动漫联合会员");
            memberService.feeRechargeAdd("","330000132",null);
        }else if("renew".equals(state) && StringUtil.isNotBlank(serviceId)){
            log.info("话费充值，续订同步");
            memberService.feeRenew(serviceId);
        }else if("recharg".equals(state) && StringUtil.isNotBlank(serviceId)){
            log.info("话费充值，到账");
            memberService.feeRecharge(serviceId);
        }else if("city".equals(state)){
            log.info("话费充值，城市");
            memberService.feeCity();
        }else{
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }


    /**
     * 途牛激活码充值
     * @param code
     * @param mobile
     * @return
     */
    @ApiOperation(value = "途牛激活码充值", notes = "途牛激活码充值")
    @PostMapping(value = "/tuniu/recharge/code")
    @ResponseBody
    public FebsResponse updateCouponCodeStatusByMobile(@RequestParam("code") String code,
                                                       @RequestParam("mobile") String mobile,
                                                       @RequestParam(name = "rightsId", required = false, defaultValue = "tuniu") String rightsId,
                                                       @RequestParam(name = "account", required = false, defaultValue = "") String account) {
        log.info("更新券码状态=>code:{},mobile:{},rightsId:{}",code,mobile,rightsId);
        CouponCode couponCode=couponCodeService.lambdaQuery().eq(CouponCode::getCouponCode,code).eq(CouponCode::getRightsId,rightsId).one();
        if(couponCode==null){
            return new FebsResponse().fail().message("激活码错误");
        }
        if(BizConstant.ALREADY_EXPIRED.equals(couponCode.getStatus())){
            return new FebsResponse().fail().message("激活码已激活使用");
        }
        if(BizConstant.ALREADY_TIME.equals(couponCode.getStatus())){
            return new FebsResponse().fail().message("激活码已过期");
        }
        if(!org.apache.commons.lang3.StringUtils.equalsAny(String.valueOf(couponCode.getStatus()),String.valueOf(BizConstant.NOT_USE),String.valueOf(BizConstant.RECHARGE_FAIL))){
            return new FebsResponse().fail().message("激活码已使用");
        }
        if(StringUtil.equals("tuniu",rightsId)){
            Integer isRecharge=couponCodeService.lambdaQuery().eq(CouponCode::getOrderId,mobile).eq(CouponCode::getRightsId,rightsId).count();
            if(isRecharge>0){
                return new FebsResponse().fail().message("账号已充值");
            }
            Integer couponCount=couponCodeService.lambdaQuery().eq(CouponCode::getStatus,BizConstant.IS_USE).eq(CouponCode::getRightsId,rightsId).count();
            if(couponCount>=100){
                return new FebsResponse().fail().message("激活码已使用");
            }
        }
        if(couponCode.getInvalidTime()!=null){
            LocalDateTime invalidTime=couponCode.getInvalidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            if(invalidTime.isBefore(LocalDateTime.now())){
                return new FebsResponse().fail().message("激活码已使用");
            }
        }
        synchronized (interner.intern(code)) {
            return couponCodeService.updateCouponCodeStatusByMobile(code,mobile,couponCode.getOrderId(),couponCode.getRightsId(),account);
        }
    }

    /**
     * 大唐短信上行通知
     * id 	唯一序列号 	String 	32
     * mobile 	用户上行手机号码，如：*********** 	String 	11
     * srcid 	接收号码，平台提供的接入号 	String 	21
     * msgcontent 	接收内容，用户上行内容信息 	String 	140
     * time 	接收时间，需要按 yyyyMMddHHmmss 格 式，如：**************) 	String 	14
     * @return
     * [{"id":"9d9d0f4d1b864c129e299cc400c26306","time":"**************","msgcontent":"测试上行短信","srcid":"*************","mobile":"***********"}]
     */
    @ApiOperation(value="大唐短信上行通知", notes="大唐短信上行通知")
    @PostMapping(value = "/datangSms/mo")
    public String datangSmsMo(@RequestBody JsonNode jsonNode) {
        log.info("大唐短信上行通知=>数据:{}", jsonNode);
        try {
            SmsMoLog smsMoLog =  new SmsMoLog();
            final boolean isArr = jsonNode.isArray();
            smsMoLog.setId(jsonNode.at(isArr?"/0/id":"/id").asText());
            smsMoLog.setMobile(jsonNode.at(isArr?"/0/mobile":"/mobile").asText());
            smsMoLog.setSrcId(jsonNode.at(isArr?"/0/srcid":"/srcid").asText());
            smsMoLog.setMsgContent(jsonNode.at(isArr?"/0/msgcontent":"/msgcontent").asText());
            final String time = jsonNode.at(isArr?"/0/time":"/time").asText();
            final Date date = DateUtils.str2Date(time, DateUtils.yyyymmddhhmmss.get());
            smsMoLog.setCreateTime(date);
            smsMoLogService.handleMo(smsMoLog);
        } catch (Exception e) {
            log.error("大唐短信上行通知=>数据:{},处理异常!",jsonNode,e);
        }
        return "SUCCESS";
    }

    /**
     * 大唐短信上行通知(新版)
     *
     * @return
     * {
     *   	"molist":[
     *        {
     *          "mobile":"13683355681",           ---发送上行的手机号码
     * 			"content":"退订",                 ---上行发送的内容
     * 			"subid":"3456"，                  ---上行发送扩展号码
     * 			"recvtime":"2014-05-02 15:07:11",      ---上行发送时间
     * 			"msgid":"01201302171123190011"    ---上行发送id，系统唯一
     *        },
     *        {
     * 			"mobile":"13683355682",
     * 			"content":"TD",
     * 			"subid":"1234"，
     * 			"recvtime":"2014-05-02 15:07:12",
     * 			"msgid":"01201302171123190012"
     *        }
     * 	    ]
     * }
     */
    @ApiOperation(value="大唐短信上行通知(新版)", notes="大唐短信上行通知(新版)")
    @PostMapping(value = "/datangSms/smsmo")
    public String datangSmsMoNew(@RequestBody JsonNode jsonNode) {
        log.info("大唐短信上行通知(新版)=>数据:{}", jsonNode);
        try {
            SmsMoLog smsMoLog =  new SmsMoLog();
            smsMoLog.setId(jsonNode.at("/molist/0/msgid").asText());
            smsMoLog.setMobile(jsonNode.at("/molist/0/mobile").asText());
            smsMoLog.setSrcId(jsonNode.at("/molist/0/subid").asText());
            smsMoLog.setMsgContent(jsonNode.at("/molist/0/content").asText());
            final String time = jsonNode.at("/molist/0/recvtime").asText();
            final Date date = DateUtils.str2Date(time, DateUtils.datetimeFormat.get());
            smsMoLog.setCreateTime(date);
            smsMoLogService.handleMo(smsMoLog);
        } catch (Exception e) {
            log.error("大唐短信上行通知(新版)=>数据:{},处理异常!",jsonNode,e);
        }
        return "{\"result\":\"1\",\"resultdesc\":\"succ\"}";
    }

    /**
     * 大唐短信推送状态报告通知(新版)
     *
     * @return
     * {
     *     "reportlist":[            ------状态报告list
     *                {
     *              "msgid":"01201302171123160015",   -----对应mt返回的msgid，系统唯一
     *              "mobile":"13800138000",           -----发送下行时对应的目的号码
     *              "subid":"2345",                   -----发送的扩展码
     *              "submittime":"2013-02-17 11:23:16",    -----提交时间
     *              "dontime":"2013-02-17 11:23:26",       -----接收时间
     *              "sendresult":"1",                 -----发送结果：1表示发送成功，其他发送失败
     *              "state":"DELIVRD",                  -----状态值,此值有运营商返回
     *              "id":""
     *        },
     *        {
     *              "msgid":"01201302171123160016",
     *              "mobile":"13800138001",
     *              "subid":"",
     *              "submittime":"2013-02-17 11:23:18",
     *              "dontime":"2013-02-17 11:23:24",
     *              "sendresult":"1"，
     *              "state:"DELIVRD"，
     *              "id":""
     *        }
     * 	]
     * }
     */
    @ApiOperation(value="大唐短信推送状态报告通知(新版)", notes="大唐短信推送状态报告通知(新版)")
    @PostMapping(value = "/datangSms/smsreport")
    public String datangSmsReportNew(@RequestBody JsonNode jsonNode) {
        log.info("大唐短信推送状态报告通知(新版)=>数据:{}", jsonNode);
        //try {
        //    SmsMoLog smsMoLog =  new SmsMoLog();
        //    final boolean isArr = jsonNode.isArray();
        //    smsMoLog.setId(jsonNode.at(isArr?"/0/id":"/id").asText());
        //    smsMoLog.setMobile(jsonNode.at(isArr?"/0/mobile":"/mobile").asText());
        //    smsMoLog.setSrcId(jsonNode.at(isArr?"/0/srcid":"/srcid").asText());
        //    smsMoLog.setMsgContent(jsonNode.at(isArr?"/0/msgcontent":"/msgcontent").asText());
        //    final String time = jsonNode.at(isArr?"/0/time":"/time").asText();
        //    final Date date = DateUtils.str2Date(time, DateUtils.yyyymmddhhmmss.get());
        //    smsMoLog.setCreateTime(date);
        //    smsMoLogService.handleMo(smsMoLog);
        //} catch (Exception e) {
        //    log.error("大唐短信推送状态报告通知(新版)=>数据:{},处理异常!",jsonNode,e);
        //}
        return "{\"result\":\"1\",\"resultdesc\":\"succ\"}";
    }

    /**
     * 大唐短信上行通知(新版)
     *
     * @return
     * [
     *     {
     *         "mobile": "18607714703",
     *         "fisp": "10655512445223",
     *         "time": 1695373943917,
     *         "msg": "TD5"
     *     },
     *     {
     *         "mobile": "18607714704",
     *         "fisp": "10655512445224",
     *         "time": 1695373943917,
     *         "msg": "TD5"
     *     }
     * ]
     */
    @ApiOperation(value="大唐短信上行通知(V3)", notes="大唐短信上行通知(V3)")
    @PostMapping(value = "/datangSms/v3/smsmo")
    public String datangSmsMoV3(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("大唐短信上行通知(V3)=>数据:{}", jsonData);
            final JsonNode arrayNode = mapper.readTree(jsonData);
            if(!arrayNode.isArray()){
                throw new IllegalArgumentException();
            }
            for(JsonNode itemNode:arrayNode){
                SmsMoLog smsMoLog =  new SmsMoLog();
                smsMoLog.setMobile(itemNode.at("/mobile").asText());
                smsMoLog.setSrcId(itemNode.at("/fisp").asText());
                smsMoLog.setMsgContent(itemNode.at("/msg").asText());
                //final String time = itemNode.at("/time").asText();
                //final Date date = DateUtils.str2Date(time, DateUtils.yyyymmddhhmmss.get());
                final long time = itemNode.at("/time").asLong();
                smsMoLog.setCreateTime(new Date(time));
                smsMoLogService.handleMo(smsMoLog);
            }
        } catch (Exception e) {
            log.error("大唐短信上行通知(V3)处理异常!",e);
        }
        return "{\"status\": 0}";
    }

    /**
     * 大唐短信推送状态报告通知(新版)
     *
     * @return
     *  [{"state":"0","mobile":"18513995055","msgid":"T9099118343978","sortid":"00","time":1590574116049},{"state":"0","mobile":"18513995055","msgid":"T9375233783151","sortid":"00","time":1590574116049},{"state":"0","mobile":"18513995055","msgid":"T8056470747762","sortid":"00","time":1590574116049}]
     */
    @ApiOperation(value="大唐短信推送状态报告通知(V3)", notes="大唐短信推送状态报告通知(V3)")
    @PostMapping(value = "/datangSms/v3/smsreport")
    public String datangSmsReportV3(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("大唐短信推送状态报告通知(V3)=>数据:{}", jsonData);
            final JsonNode arrayNode = mapper.readTree(jsonData);
            if(!arrayNode.isArray()){
                throw new IllegalArgumentException();
            }
            for(JsonNode itemNode:arrayNode){
                final String mobile = itemNode.at("/mobile").asText();
                final String msgId = itemNode.at("/msgid").asText();
                final String state = itemNode.at("/state").asText();
                final Date time = new Date(itemNode.at("/time").asLong());
                smsSendLogService.receiveSendStateNotify(mobile,msgId,state,time);
            }
        } catch (Exception e) {
            log.error("大唐短信推送状态报告通知(V3)处理异常!",e);
        }
        return "{\"status\": 0}";
    }

    /**
     * {"orderNum":"2022110315172127809","orderResult":"成功","orderStatus":"0","orderTime":"2022-11-03 15:26:43","phone":"***********","registerResult":"订购成功","registerStatus":"0","registerTime":"2022-11-03 15:20:47","timetemp":"1667460403518","sign":"C81E639DCC46B8CC17C40C77A5D41C16"}
     *
     * @param jsonNode
     * @return
     */
    //@AutoLog(value = "湖南豫讯电信回调通知")
    @ApiOperation(value = "湖南豫讯电信回调通知", notes = "湖南豫讯电信回调通知")
    @PostMapping(value = "/yixunDianxinCallBack")
    public String yixunDianxinCallBack(@RequestBody JsonNode jsonNode) {
        log.info("湖南豫讯电信回调通知数据:{}", jsonNode);
        String orderNum = jsonNode.at("/orderNum").asText();
        String mobile = jsonNode.at("/phone").asText();
        String state = jsonNode.at("/orderStatus").asText();
        String orderTimeStr = jsonNode.at("/orderTime").asText();
        String orderResult = jsonNode.at("/orderResult").asText();
        Date orderTime = null;
        try {
            orderTime = DateUtils.datetimeFormat.get().parse(orderTimeStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        subscribeService.receiveHunanDianxinVrbtNotify(mobile, orderNum, state, orderTime, orderResult);
        String result = "{\"code\":0,\"msg\":\"SUCCESS\"}";
        return result;
    }

    /**
     * 创建订单
     * @param openId
     * @param companyTitle
     * @return
     *
     */
    @ApiOperation(value = "创建订单", notes = "创建订单")
    @PostMapping(value = "/create/order")
    @ResponseBody
    public FebsResponse createOrder(@RequestParam(value = "openId", required = false, defaultValue = "") String openId,
                                    @RequestParam(value = "companyTitle", required = false, defaultValue = "") String companyTitle,
                                    HttpServletRequest request){
        String companyOwner = getCompanyOwner(request);
        synchronized (interner.intern(openId)) {
            if(Strings.isNullOrEmpty(openId) || Strings.isNullOrEmpty(companyTitle)) {
                return new FebsResponse().fail().message("参数错误");
            }
            try {
                String orderId=qyclOrderPayService.savePay(openId,companyTitle,null, companyOwner);
                return new FebsResponse().success().data(orderId);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return new FebsResponse().fail().message("创建订单失败");
        }
    }

    public String getCompanyOwner(HttpServletRequest request) {
        String companyOwner = request.getHeader(QyclConstant.QYCL_COMPANY_OWNER_HEAD);
        if (org.apache.commons.lang3.StringUtils.isEmpty(companyOwner)) {
            companyOwner = QyclConstant.QYCL_COMPANY_OWNER_YRJY;
        }
        return companyOwner;
    }
    /**
     * 微信支付
     * @param mobile
     * @param rightsId
     * @param returnUrl
     * @param serviceId
     * @param packName
     * @param subject
     * @param account
     * @return
     */
    @ApiOperation(value = "微信支付", notes = "微信支付")
    @PostMapping(value = "/mall/wechat/pay")
    @ResponseBody
    public FebsResponse wechatPay(@RequestParam(value = "mobile", required = false, defaultValue = "") String mobile,
                                  @RequestParam(value = "rightsId", required = false, defaultValue = "") String rightsId,
                                  @RequestParam(value = "returnUrl", required = false, defaultValue = "")String returnUrl,
                                  @RequestParam(value = "serviceId", required = false, defaultValue = "")String serviceId,
                                  @RequestParam(value = "packName", required = false, defaultValue = "")String packName,
                                  @RequestParam(value = "subject", required = false, defaultValue = "")String subject,
                                  @RequestParam(value = "account", required = false, defaultValue = "")String account,
                                  @RequestParam(value = "tradeType", required = false, defaultValue = BizConstant.TRADE_TYPE_WECHAT) String tradeType,
                                  @RequestParam(value = "orderId", required = false, defaultValue = "") String orderId,
                                  @RequestParam(value = "businessType", required = false, defaultValue = BizConstant.BUSINESS_TYPE_WX_QYCLZY) String businessType){
        if(Strings.isNullOrEmpty(subject)) {
            return new FebsResponse().fail().message("参数错误");
        }


        if(org.apache.commons.lang3.StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_HTML,BizConstant.TRADE_TYPE_WECHAT)){
            if(StringUtil.isNotBlank(serviceId)){
                synchronized (interner.intern(mobile)) {
                    //权益购买支付
                    return rightsShopOrder(mobile, rightsId, returnUrl, serviceId, packName, subject, account, tradeType, businessType);
                }
            }
            synchronized (interner.intern(orderId)) {
                if(Strings.isNullOrEmpty(orderId)) {
                    return new FebsResponse().fail().message("订单错误");
                }
                //查询视频彩铃订单
                QyclOrderPay order=qyclOrderPayService.queryOrder(orderId);
                if(order==null || order.getPayStatus()!=-1){
                    return new FebsResponse().fail().message("订单错误");
                }
                try {
                    Map<String, String> resp = qyclWxpayService.pay(orderId,null,subject,NOTIFY_URL,tradeType,order.getOpenId(),businessType);
                    if(org.apache.commons.lang3.StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_HTML)){
                        String  mwebUrl = resp.get("mweb_url");
                        String returnUrlEncode = UriUtils.encode(returnUrl + "?orderId=" + orderId, StandardCharsets.UTF_8);
                        String mwebUrlWithRedirect = mwebUrl + "&redirect_url=" + returnUrlEncode;
                        log.info("微信支付支付跳转地址->mwebUrlWithRedirect:{}",mwebUrlWithRedirect);
                        return new FebsResponse().success().data(mwebUrlWithRedirect);
                    }
                    return new FebsResponse().success().data(resp);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return new FebsResponse().fail().message("订单错误");
            }
        }else if(org.apache.commons.lang3.StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_TIKTOK)){
            synchronized (interner.intern(orderId)) {
                try {
                    if(Strings.isNullOrEmpty(orderId)) {
                        return new FebsResponse().fail().message("订单错误");
                    }
                    //查询视频彩铃订单
                    QyclOrderPay order=qyclOrderPayService.queryOrder(orderId);
                    if(order==null || order.getPayStatus()!=-1){
                        return new FebsResponse().fail().message("订单错误");
                    }
                    Map<String, String> resp = qyclWxpayService.dyPay(orderId,null,subject,tradeType,businessType);
                    return new FebsResponse().success().data(resp);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return new FebsResponse().fail().message("订单错误");
            }

        }else if(BizConstant.TRADE_TYPE_TIKTOK_TRADE.equals(tradeType)){
            synchronized (interner.intern(orderId)) {
                try {
                    if(Strings.isNullOrEmpty(orderId)) {
                        return new FebsResponse().fail().message("订单错误");
                    }
                    //查询视频彩铃订单
                    QyclOrderPayLog order=orderPayLogService.queryOrder(orderId);
                    if(order==null){
                        return new FebsResponse().fail().message("订单错误");
                    }
                    Map<String, String> resp = qyclWxpayService.douYinPay(orderId,null,subject,tradeType,businessType);
                    return new FebsResponse().success().data(resp);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return new FebsResponse().fail().message("订单错误");
            }

        }
        return new FebsResponse().fail().message("订单错误");
    }



   //权益订单支付
    private FebsResponse rightsShopOrder(String mobile, String rightsId, String returnUrl, String serviceId, String packName, String subject, String account, String tradeType, String businessType) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        if(Strings.isNullOrEmpty(returnUrl)){
            return new FebsResponse().fail().message("参数错误");
        }
        if(Strings.isNullOrEmpty(rightsId)) {
            return new FebsResponse().fail().message("请选择权益产品");
        }
        MemberRights memberRights=memberRightsService.lambdaQuery().eq(MemberRights::getRightsSwitchs,1).eq(MemberRights::getRightsId, rightsId).orderByAsc(MemberRights::getUpdateTime).one();
        if(memberRights==null){
            return new FebsResponse().fail().message("产品已下架");
        }
        if(memberRights.getIsAccount()==1){
            if(Strings.isNullOrEmpty(account)) {
                return new FebsResponse().fail().message("请输入账号");
            }
        }
        //查询业务包
        Optional<MiguPack> packOpt=miguPackService.queryMiguPackDetail(serviceId, packName);
        if(packOpt==null || !packOpt.isPresent()){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        //设置权益参数
        Rights rights=new Rights(memberRights.getCouponId(),memberRights.getRightsName(),memberRights.getProductPrice(),DateUtil.formatYearMonth(LocalDateTime.now()),memberRights.getIsAccount(),memberRights.getCompanyOwner());
        //创建预约充值记录
        JunboChargeLog junboChargeLog = junboChargeLogService.createScheduleRechargeLog(mobile, account, serviceId, rights,null, packName);
        String orderIds=junboChargeLog.getMiguOrderId();
        Double totalFee=Double.valueOf(memberRights.getProductPrice());
        //创建支付记录
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatConfig(tradeType,businessType);
        orderPayService.savePay(tradeType, orderIds, totalFee,wechatConfigLog.getAppId(),wechatConfigLog.getMchId());
        try {
            Map<String, String> resp = qyclWxpayService.pay(orderIds,String.valueOf(memberRights.getProductPrice()), subject,NOTIFY_URL, tradeType,"", businessType);
            if(org.apache.commons.lang3.StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_HTML)){
                String  mwebUrl = resp.get("mweb_url");
                String returnUrlEncode = UriUtils.encode(returnUrl + "?orderId=" + orderIds, StandardCharsets.UTF_8);
                String mwebUrlWithRedirect = mwebUrl + "&redirect_url=" + returnUrlEncode;
                log.info("微信支付支付跳转地址->mwebUrlWithRedirect:{}",mwebUrlWithRedirect);
                return new FebsResponse().success().data(mwebUrlWithRedirect);
            }
            return new FebsResponse().success().data(resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail().message("下单失败");
    }


    //微信用户支付通知
    @RequestMapping(value="/mall/wechat/pay/notify",produces="text/plain")
    @ResponseBody
    public String mallResultNotify(HttpServletRequest request) {
        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()){
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            QyclWxpayNotifyParam notifyParam = qyclWxpayService.payResultNotify(notifyXml);
            log.info("微信支付通知notifyParam = " + notifyParam);
            final String resultCode = notifyParam.getResultCode();
            boolean isPaySucc =StringUtil.equals(resultCode, WXPayConstants.SUCCESS);
            QyclOrderPay order=qyclOrderPayService.queryOrder(notifyParam.getOutTradeNo());
            if(order!=null){
                if(isPaySucc){
                    qyclOrderPayService.updatePayStatus(notifyParam.getOutTradeNo(),notifyParam.getTransactionId(),1,order);
                }else{
                    qyclOrderPayService.updatePayStatus(notifyParam.getOutTradeNo(),notifyParam.getTransactionId(),0,order);
                }
            }else{
                if(isPaySucc){
                    orderPayService.updatePayStatus(notifyParam.getOutTradeNo(),notifyParam.getTransactionId(),1);
                }else{
                    orderPayService.updatePayStatus(notifyParam.getOutTradeNo(),notifyParam.getTransactionId(),0);
                }
            }
            return succRespXml;
        } catch (Exception e) {
            log.error("微信回调通知处理异常:", e);
            return failRespXml;
        }
    }
    /**
     * 抖音小程序担保支付回调
     *  抖音小程序担保付回调地址:
     * https://crbt.cdyrjygs.com/cms-vrbt/api/mall/payment/callback
     * 抖音小程序担保付token:
     * 3r17nt7v9tzmq331
     * @return
     */
    @RequestMapping("/mall/payment/callback")
    @ResponseBody
    public String paymentCallback(/*@RequestParam Map<String,String> allRequestParams,*//*HttpEntity<String> httpEntity, */HttpServletRequest request) {
        //log.info("抖音小程序担保支付回调,参数:{}", allRequestParams);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String raw = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("抖音小程序担保支付回调,raw:{}", raw);
            org.springframework.data.util.Pair<Boolean,DypayNotifyMsg> result =  qyclWxpayService.parsePaymentCallback(raw);
            if(result.getFirst()){
                final DypayNotifyMsg notifyMsg = result.getSecond();
                boolean isPaySucc =StringUtil.equals(notifyMsg.getStatus(), WXPayConstants.SUCCESS);
                QyclOrderPay order=qyclOrderPayService.queryOrder(notifyMsg.getCpOrderno());
                if(order!=null){
                    if(isPaySucc){
                        qyclOrderPayService.updatePayStatus(notifyMsg.getCpOrderno(),notifyMsg.getPaymentOrderNo(),1,order);
                    }else{
                        qyclOrderPayService.updatePayStatus(notifyMsg.getCpOrderno(),notifyMsg.getPaymentOrderNo(),0,order);
                    }
                }
            }else {
                //如果验签失败,就打告警日志
                log.error("抖音小程序担保支付回调,验签失败,raw:{}", raw);
            }

            return "{\"err_no\": 0, \"err_tips\": \"success\"}";

        } catch (Exception e) {
            log.warn("抖音小程序担保支付回调异常", e);
            return "{\"err_no\": 999, \"err_tips\": \"success\"}";
        }
    }
    //微信用户退款通知
    @RequestMapping(value="/mall/wechat/refund/notify",produces="text/plain")
    @ResponseBody
    public String wxrefundResultNotify(HttpServletRequest request) {

        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()){
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            WxInfoNotifyParam infoParam = qyclWxpayService.refundResultNotify(notifyXml);
            log.info("微信用户退款通知infoParam= " + infoParam);
            if(infoParam!=null){
                final String refundStatus = infoParam.getRefundStatus();
                boolean success =StringUtil.equals(refundStatus, WXPayConstants.SUCCESS);
                if(success){
                    qyclOrderPayService.upadateRefundStatus(infoParam.getOutRefundNo(),REFUND_SUCCESS,"退款成功！");
                }else{
                    qyclOrderPayService.upadateRefundStatus(infoParam.getOutRefundNo(),REFUND_FAIL,"退款失败！");
                }
            }
            return succRespXml;
        } catch (Exception e) {
            e.printStackTrace();
            return failRespXml;
        }
    }

    /**
     * 抖音小程序退款回调
     *  抖音小程序担保付回调地址:
     * https://crbt.cdyrjygs.com/cms-vrbt/api/mall/douyin/refund/notify
     * 抖音小程序担保付token:
     * 3r17nt7v9tzmq331
     * @return
     */
    @RequestMapping("/mall/douyin/refund/notify")
    @ResponseBody
    public String refundCallback(/*@RequestParam Map<String,String> allRequestParams,*//*HttpEntity<String> httpEntity, */HttpServletRequest request) {
        //log.info("抖音小程序担保支付回调,参数:{}", allRequestParams);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String raw = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("抖音小程序担保支付回调,raw:{}", raw);
            org.springframework.data.util.Pair<Boolean,DypayRefundMsg> result =  qyclWxpayService.parseRefundCallback(raw);
            if(result.getFirst()){
                final DypayRefundMsg notifyMsg = result.getSecond();
                boolean success =StringUtil.equals(notifyMsg.getStatus(), WXPayConstants.SUCCESS);
                if(success){
                    qyclOrderPayService.upadateRefundStatus(notifyMsg.getCpRefundno(),REFUND_SUCCESS,"退款成功！");
                }else{
                    qyclOrderPayService.upadateRefundStatus(notifyMsg.getCpRefundno(),REFUND_FAIL,"退款失败！");
                }
            }else {
                //如果验签失败,就打告警日志
                log.error("抖音小程序退款回调,验签失败,raw:{}", raw);
            }
            return "{\"err_no\": 0, \"err_tips\": \"success\"}";
        } catch (Exception e) {
            log.warn("抖音小程序退款回调异常", e);
            return "{\"err_no\": 999, \"err_tips\": \"success\"}";
        }
    }
    /**
     * 订单查询
     * @param outTradeNo
     * @return
     */
    @ApiOperation(value = "订单查询", notes = "订单查询")
    @PostMapping(value = "/mall/order/payState")
    @ResponseBody
    public FebsResponse queryOrderPayState(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo){
        if(StringUtil.isEmpty(outTradeNo)){
            return new FebsResponse().fail().message("订单号不能为空");
        }
        QyclOrderPay order=qyclOrderPayService.queryOrder(outTradeNo);
        if(order!=null){
            if(order.getPayStatus()==-1){
                return new FebsResponse().code(HttpStatus.BAD_GATEWAY).message("订单未支付");
            }else if(order.getPayStatus()==0){
                return new FebsResponse().code(HttpStatus.NOT_IMPLEMENTED).message("订单支付失败");
            }else if(order.getPayStatus()==1){
                return new FebsResponse().success().message("订单支付成功");
            }
        }
        return orderPayService.queryOrderPayState(outTradeNo);
    }

//    /**
//     * 获取Code
//     * @param redirectUrl
//     * @param businessType
//     * @param response
//     * @throws IOException
//     */
//    @ApiOperation(value = "获取Code", notes = "获取Code")
//    @GetMapping(value = "/mall/wechat/code")
//    @ResponseBody
//    public void wechatCode(@RequestParam(value = "redirectUrl", required = false, defaultValue ="") String redirectUrl,
//                           @RequestParam(value = "businessType", required = false, defaultValue = BizConstant.BUSINESS_TYPE_WX_QYCLZY) String businessType, HttpServletResponse response) throws IOException {
//        String fabsCode = qyclWxpayService.weChatCode(redirectUrl,businessType);
//        log.info("获取Code,请求数据=>地址:{}",fabsCode);
//        response.sendRedirect(fabsCode);
//    }

    /**
     * 获取微信用户openId接口
     * @param code
     * @return
     */
    @ApiOperation(value = "获取微信用户openId接口", notes = "获取微信用户openId接口")
    @PostMapping(value = "/mall/wechat/openId")
    @ResponseBody
    public FebsResponse wechatOpenId(@RequestParam(value = "code", required = false, defaultValue ="") String code,
                                     @RequestParam(value = "openId", required = false, defaultValue ="")String openId,
                                     @RequestParam(value = "tradeType", required = false, defaultValue = BizConstant.TRADE_TYPE_WECHAT) String tradeType,
                                     @RequestParam(value = "anonymousCode", required = false, defaultValue ="") String anonymousCode,
                                     @RequestParam(value = "businessType", required = false, defaultValue = BizConstant.BUSINESS_TYPE_WX_QYCLZY) String businessType){
        if(StringUtils.isEmpty(code)){
            return new FebsResponse().fail().message("code不能为空");
        }
        if(org.apache.commons.lang3.StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_HTML,BizConstant.TRADE_TYPE_WECHAT)){
            try {
                FebsResponse fabsOpenId = qyclWxpayService.weChatAppletsOpenId(openId,code,businessType);
                return fabsOpenId;
            } catch (Exception e) {
                e.printStackTrace();
            }

        }else if(org.apache.commons.lang3.StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_TIKTOK)){
            if(StringUtils.isEmpty(anonymousCode)){
                return new FebsResponse().fail().message("anonymousCode不能为空");
            }
            try {
                FebsResponse fabsOpenId = qyclWxpayService.tiktokAppletsOpenId(openId,code,anonymousCode,tradeType,businessType);
                return fabsOpenId;
            } catch (Exception e) {
                e.printStackTrace();
            }

        }else if(org.apache.commons.lang3.StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_KUAISHOU)){
            try {
                return kuaiShouService.getOpenId(code,tradeType,businessType);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else if(org.apache.commons.lang3.StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_ALIPAY)){
            try {
                return alipayService.getOpenId(code,tradeType,businessType);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return new FebsResponse().fail();
    }


    /**
     * 获取小程序scheme码
     * @return
     */
    @ApiOperation(value = "获取小程序scheme码", notes = "获取小程序scheme码")
    @PostMapping(value = "/mall/wechat/scheme")
    @ResponseBody
    public FebsResponse wechatScheme(@RequestParam(value = "query", required = false, defaultValue ="") String query,
                                     @RequestParam(value = "businessType", required = false, defaultValue = BizConstant.BUSINESS_TYPE_WX_QYCLZY) String businessType,HttpServletRequest request){
        try {
            FebsResponse fabsToken = qyclWxpayService.wechatToken(businessType);
            if(!fabsToken.isOK()){
                return fabsToken;
            }
            ObjectNode token =(ObjectNode)fabsToken.get("data");
            String accessToken=token.get("access_token").asText();
            String ipAddr = IPUtils.getIpAddr(request);
            log.info("获取小程序scheme码,请求数据=>ip:{}",ipAddr);
            FebsResponse fabsOpenId= qyclWxpayService.weChatScheme(accessToken,ipAddr,query);
            return fabsOpenId;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();
    }

    /**
     * 咪咕数据通知
     *
     * @return
     */
    @ApiOperation(value = "咪咕数据通知", notes = "咪咕数据通知")
    @PostMapping(value = "/adv/notify")
    @ResponseBody
    public String advNotify(@RequestBody JsonNode jsonNode) {
        log.info("咪咕数据通知接口数据:{}", jsonNode);
        String successJson = "{\"resCode\":\"000000\",\"resMsg\":\"接收成功\"}";
        String errorJson = "{\"resCode\":\"999999\",\"resMsg\":\"接收失败\"}";
        try {
            Long taskSeq = jsonNode.at("/taskSeq").asLong();
            JsonNode advArray = jsonNode.at("/advInfos");
            if (advArray.isArray()) {
                List<CmsAdvInfo> list = new ArrayList();
                for (JsonNode advNode : advArray) {
                    CmsAdvInfo cmsAdvInfo = new CmsAdvInfo();
                    cmsAdvInfo.setTaskSeq(taskSeq);
                    cmsAdvInfo.setAdvSeq(advNode.path("advSeq").longValue());
                    cmsAdvInfo.setAdvName(advNode.path("advName").textValue());
                    cmsAdvInfo.setPlanNum(advNode.path("planNum").longValue());
                    cmsAdvInfo.setNotifyType(advNode.path("notifyType").textValue());
                    cmsAdvInfo.setAdvUrl(advNode.path("advUrl").textValue());
                    cmsAdvInfo.setPicUrl(advNode.path("picUrl").textValue());
                    cmsAdvInfo.setRemark(advNode.path("remark").textValue());
                    try {
                        cmsAdvInfo.setStartTime(DateUtils.datetimeFormat.get().parse(advNode.path("startTime").textValue()));
                        cmsAdvInfo.setEndTime(DateUtils.datetimeFormat.get().parse(advNode.path("endTime").textValue()));
                    } catch (Exception e) {
                        log.error("咪咕数据通知接口数据:{},日期处理异常!", jsonNode, e);
                    }
                    list.add(cmsAdvInfo);
                }
                cmsAdvInfoService.saveBatch(list);
                return successJson;
            } else {
                return errorJson;
            }
        } catch (Exception e) {
            log.error("咪咕数据通知接口数据:{},处理异常!", jsonNode, e);
            return errorJson;
        }
    }
    /**
     * 酷狗二次校验回调
     * @param outTradeNo 订单号
     * @param phoneNo 手机号
     * @return
     */
    @GetMapping("/kugou/callback/doubleVerify")
    @ResponseBody
    public KugouDoubleVerifyDto doubleVerifyCallback(@RequestParam("out_trade_no")String outTradeNo,
                                                     @RequestParam("out_user_id")String phoneNo){

        return kugouApiService.doubleVerifyCallback(outTradeNo,phoneNo);
    }
    @GetMapping("/queryMonthStatus")
    @ResponseBody
    public Result queryMonthStatus(@RequestParam String mobile) {
        RemoteResult remoteResult = miguApiService.vrbtMonthStatusQuery(mobile, "014X057", false);
        if (remoteResult.isOK() && !"0".equals(remoteResult.getStatus())) {
            return Result.ok("已订购");
        }
        return Result.error("未订购");
    }

    /**
     * 酷狗登录
     * @param mobile
     * @param channelCode
     * @return
     */
    ////@AutoLog(value = "咪咕直接登录")
    @ApiOperation(value = "咪咕直接登录", notes = "咪咕直接登录")
    @PostMapping(value = "/kugou/directLogin")
    public Result<?> kuGouDirectLogin(String mobile, String channelCode, HttpServletRequest request) {
        final String ipAddr = IPUtils.getIpAddr(request);
        log.info("咪咕直接登录=>手机号:{},渠道号:{},ip:{}", mobile, channelCode, ipAddr);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult == null || !mobileRegionResult.isIspYidong()) {
            return Result.noauth("暂时未开放,敬请期待");
        }
        boolean allow = provinceBusinessChannelConfigService.allow(channelCode, mobileRegionResult.getProvince());
        if (!allow) {
            return Result.noauth("暂未开放，敬请期待");
        }
        RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
        if (result.isOK()) {
            Result resp = Result.ok("登录成功");
            resp.setResult(result.getToken());
            return resp;
        } else {
            return Result.noauth("登录失败");
        }
    }

    /**
     * 湖南豫讯电信智能回调通知
     * {"accNum":"***********","orderMsg":"成功","orderNo":"202308141603052597053","name":"YHBXX_01","timestamp":"20230814160527","orderState":"1"}
     */
    ////@AutoLog(value = "湖南豫讯电信智能回调通知")
    @ApiOperation(value = "湖南豫讯电信智能回调通知", notes = "湖南豫讯电信智能回调通知")
    @RequestMapping(value = "/yixunDianxinMindCallBack")
    public String yixunDianxinMindCallBack(@RequestParam Map<String,Object> requestMap,
                                           @RequestBody(required = false) JsonNode jsonNode/*,
                                           @RequestParam String name,
                                           @RequestParam String timestamp,
                                           @RequestParam String sign,
                                           @RequestParam String accNum,
                                           @RequestParam String orderNo,
                                           @RequestParam Integer orderState*/
    ) {
        log.info("湖南豫讯电信智能回调通知数据,参数map:{},json数据:{}", requestMap,jsonNode);
        try {
            //final String name = jsonNode.at("/name").asText();
            final String timestamp = jsonNode.at("/timestamp").asText();
            //final String sign = (String) requestMap.get("sign");
            final String accNum = jsonNode.at("/accNum").asText();
            final String orderNo = jsonNode.at("/orderNo").asText();
            final String orderState = jsonNode.at("/orderState").asText();
            //log.info("湖南豫讯电信智能回调通知数据,号码:{},订单号:{},订购状态:{},orderMsg:{}", accNum, orderNo, orderState,orderMsg);
            subscribeService.receiveHunanDianxinMindNotify(accNum, orderNo, Integer.parseInt(orderState), DateUtil.stringToDate(timestamp,DateUtil.FULL_TIME_PATTERN));
        } catch (Exception e) {
            log.info("湖南豫讯电信智能回调通知数据,参数map:{},json数据:{},处理异常!", requestMap,jsonNode,e);
        }
        return "ok";
    }

    /**
     * 支付宝签约
     *
     * @return
     */
    @ApiOperation(value="支付宝签约", notes="支付宝签约")
    @PostMapping(value = "/aliSignUp")
    public FebsResponse aliSignUp(HttpServletRequest request, HttpServletResponse response) {

        String userAgent = request.getHeader("User-Agent");
        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
            userAgent = userAgent.substring(0, 512);
        }
        String ipAddr = IPUtils.getIpAddr(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("渠道订阅请求=>ip:{},{}",ipAddr, jsonData);
            Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
            String app = request.getHeader("x-requested-with");
            subscribe.setUserAgent(userAgent);
            subscribe.setReferer(app);
            subscribe.setIp(ipAddr);
            String businessType=subscribe.getBusinessType();
            String mobile = subscribe.getMobile();
            if(org.springframework.util.StringUtils.isEmpty(businessType)){
                return new FebsResponse().fail().message("业务类型错误");
            }
            if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
                return new FebsResponse().fail().message("请正确填写手机号");
            }
            //黑名单直接返回
            if(blackListService.isBlackList(mobile)){
                return new FebsResponse().fail().message("暂时无法提供服务，请谅解!");
            }
            return alipayService.aliSignPay(subscribe);
        } catch (Exception e) {
            log.info("支付宝签约出错：", e);
        }
        return new FebsResponse().fail();
    }

    /**
     * 签约异步通知接口
     */
    @ResponseBody
    @PostMapping("/aliSignUpNotify")
    public String aliSignUpNotify(HttpServletRequest request) throws Exception{

        String succRespMsg = "success";
        String failRespMsg = "failure";
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        final String charset = request.getParameter("charset");
        final String notifyTime = request.getParameter("notify_time");
        final String alipayUserId = request.getParameter("alipay_user_id");
        final String sign = request.getParameter("sign");
        final String externalAgreementNo = request.getParameter("external_agreement_no");
        final String version = request.getParameter("version");
        final String signTime = request.getParameter("sign_time");
        final String notifyId = request.getParameter("notify_id");
        final String notifyType = request.getParameter("notify_type");
        final String agreementNo = request.getParameter("agreement_no");
        final String invalidTime = request.getParameter("invalid_time");
        final String authAppId = request.getParameter("auth_app_id");
        final String personalProductCode = request.getParameter("personal_product_code");
        final String validTime = request.getParameter("valid_time");
        final String loginToken = request.getParameter("login_token");
        final String appId = request.getParameter("app_id");
        final String signType = request.getParameter("sign_type");
        final String signScene = request.getParameter("sign_scene");
        final String status = request.getParameter("status");
        final String alipayLogonId = request.getParameter("alipay_logon_id");
        AliSignUpNotifyParam aliSignUpNotifyParam = new AliSignUpNotifyParam();
        aliSignUpNotifyParam.setCharset(charset);
        aliSignUpNotifyParam.setNotifyTime(notifyTime);
        aliSignUpNotifyParam.setAlipayUserId(alipayUserId);
        aliSignUpNotifyParam.setSign(sign);
        aliSignUpNotifyParam.setExternalAgreementNo(externalAgreementNo);
        aliSignUpNotifyParam.setVersion(version);
        aliSignUpNotifyParam.setSignTime(signTime);
        aliSignUpNotifyParam.setNotifyId(notifyId);
        aliSignUpNotifyParam.setNotifyType(notifyType);
        aliSignUpNotifyParam.setAgreementNo(agreementNo);
        aliSignUpNotifyParam.setInvalidTime(invalidTime);
        aliSignUpNotifyParam.setAuthAppId(authAppId);
        aliSignUpNotifyParam.setPersonalProductCode(personalProductCode);
        aliSignUpNotifyParam.setValidTime(validTime);
        aliSignUpNotifyParam.setLoginToken(loginToken);
        aliSignUpNotifyParam.setAppId(appId);
        aliSignUpNotifyParam.setSignType(signType);
        aliSignUpNotifyParam.setSignScene(signScene);
        aliSignUpNotifyParam.setStatus(status);
        aliSignUpNotifyParam.setAlipayLogonId(alipayLogonId);

        try{
            //获取支付宝POST过来反馈信息
            //AliSignUpNotifyParam aliSignUpNotifyParam = mapper.convertValue(requestParams, AliSignUpNotifyParam.class);
            log.info("支付宝签约通知接口,参数:{}",aliSignUpNotifyParam.toString());
            AliSignRecord aliSignRecord = aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo, aliSignUpNotifyParam.getExternalAgreementNo()).one();
            if(aliSignRecord == null || (aliSignRecord!=null && aliSignRecord.getSignStatus().equals(3))){
                log.info("支付宝签约通知,找不到订单信息或者已解约,订单号:{}",aliSignUpNotifyParam.getExternalAgreementNo());
                return succRespMsg;
            }
            String outTradeNo = "AliPay" + IdWorker.getIdStr();
            //更新签约表状态
            if("NORMAL".equals(aliSignUpNotifyParam.getStatus())){
                log.info("支付宝签约通知,签约成功,订单号:{}",aliSignUpNotifyParam.getExternalAgreementNo());
                //已收到过回调
                if(aliSignRecord.getSignStatus().equals(1)){
                    log.info("支付宝签约通知,二次回调直接返回,订单号:{}",aliSignUpNotifyParam.getExternalAgreementNo());
                    return succRespMsg;
                }
                aliSignRecord.setSignStatus(1);
                aliSignRecord.setAgreementNo(aliSignUpNotifyParam.getAgreementNo());
                aliSignRecord.setSignSuccessTime(new Date());
                aliSignRecord.setAlipayUserId(aliSignUpNotifyParam.getAlipayUserId());
                aliSignRecord.setInvalidTime(aliSignUpNotifyParam.getInvalidTime());
                //签约成功设置下次扣款时间 - 2023-5-16 10:40:37
                if(LocalDate.now().getDayOfMonth()>=6){
                    LocalDate localDate=LocalDate.now().plusMonths(1).plusDays(-5);
                    aliSignRecord.setNextDeductTime(Date.from(localDate.atStartOfDay().atZone( ZoneId.systemDefault()).toInstant()));
                }else{
                    LocalDate localDate=LocalDate.now().plusMonths(1).withDayOfMonth(1);
                    aliSignRecord.setNextDeductTime(Date.from(localDate.atStartOfDay().atZone( ZoneId.systemDefault()).toInstant()));
                }
                aliSignRecordService.updateStatus(aliSignRecord,outTradeNo);
                //发起第一次扣款
                Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getAppId, aliSignRecord.getAppId()).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
                String totalAmount = alipay.getSingleAmount();
                String subject = alipay.getBusinessName();
                Boolean isAliPay=alipayService.aliTradePay(aliSignRecord.getMobile(),
                        aliSignRecord.getExternalAgreementNo(),
                        aliSignUpNotifyParam.getAgreementNo(),
                        outTradeNo,
                        totalAmount,
                        subject,aliSignRecord.getAppId(),aliSignRecord.getBusinessType(),aliSignRecord.getBusinessName(),aliSignRecord.getSubChannel(),aliSignRecord.getBizType());

                //电信支付宝视频彩铃首次扣款失败自动解约
                if(!isAliPay && StringUtil.equals(aliSignRecord.getBizType(),BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY)){
                    alipayService.unSign(externalAgreementNo);
                }
            }else{
                aliSignRecord.setSignStatus(2);
                aliSignRecord.setAgreementNo(aliSignUpNotifyParam.getAgreementNo());
                aliSignRecordService.updateStatus(aliSignRecord,outTradeNo);
            }
        }catch (Exception e) {
            log.error("支付宝签约通知出错,参数:{}",aliSignUpNotifyParam.toString(),e);
            return failRespMsg;
        }
        return succRespMsg;
    }

    /**
     * 解约异步通知接口
     */
    @ResponseBody
    @PostMapping("/aliTerminationNotify")
    public String aliTerminationNotify(@RequestParam Map<String,String> requestParams) throws Exception{
        String succRespMsg = "success";
        String failRespMsg = "failure";
        try{
            //获取支付宝POST过来反馈信息
            AliUnSignNotifyParam aliUnSignNotifyParam = mapper.convertValue(requestParams, AliUnSignNotifyParam.class);
            log.info("支付宝解约通知接口,参数:{}",aliUnSignNotifyParam.toString());
            AliSignRecord aliSignRecord = aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo, aliUnSignNotifyParam.getExternalAgreementNo()).one();
            if(aliSignRecord == null){
                log.info("支付宝解约通知接口,找不到订单信息,订单号:{}",aliUnSignNotifyParam.getExternalAgreementNo());
                return failRespMsg;
            }
            if("UNSIGN".equals(aliUnSignNotifyParam.getStatus())){
                log.info("支付宝解约通知接口,解约成功,订单号:{}",aliUnSignNotifyParam.getExternalAgreementNo());
                //更新签约表状态
                aliSignRecord.setSignStatus(3);
                aliSignRecord.setUnsignTime(DateUtils.parseDate(aliUnSignNotifyParam.getUnsignTime(),"yyyy-MM-dd HH:mm:ss"));
            }else{
                log.info("支付宝解约通知接口,解约失败,订单号:{}",aliUnSignNotifyParam.getExternalAgreementNo());
                aliSignRecord.setSignStatus(4);
            }
            aliSignRecordService.updateStatus(aliSignRecord,null);

            //电信支付宝视频彩铃用户解约并退订电信支付宝视频彩铃权益
            if("UNSIGN".equals(aliUnSignNotifyParam.getStatus()) && StringUtil.equals(aliSignRecord.getBizType(),BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY)){
                dianxinVrbtService.unSubscribeByemp(aliSignRecord.getMobile(),BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
            }
            //长沙支付宝解约通知接口
//            changShaAliPayNotifyService.unSignNotify(aliUnSignNotifyParam.getExternalAgreementNo(),aliSignRecord.getBusinessType(),true);
        }catch (Exception e) {
            log.error("支付宝解约通知出错:",e);
            return failRespMsg;
        }
        return succRespMsg;
    }

    //支付宝签约扣款通知
    @RequestMapping(value = "aliTradePayResultNotify",produces="text/plain")
    @ResponseBody
    public String aliTradePayResultNotify(@RequestParam Map<String,String> requestParams) {
        String succRespMsg = "success";
        String failRespMsg = "failure";
        try {
            //log.info("支付宝签约扣款通知requestParams:{} ",mapper.writeValueAsString(requestParams));
            //接受参数sign + 变成了空格，变回来
            requestParams.put("sign",(requestParams.get("sign") == null ? "" : requestParams.get("sign")).replace(" ","+"));
            requestParams.put("fund_bill_list",(requestParams.get("fund_bill_list") == null ? "" : requestParams.get("fund_bill_list")).replace("\"","").replace(":","="));
            AlipayNotifyParam notifyParam = alipayService.aliTradePayResultNotify(requestParams);
            log.info("支付宝签约扣款通知notifyParam:{} ",notifyParam);
            final String outTradeNo = notifyParam.getOutTradeNo();
            AliSignChargingOrder aliSignChargingOrder = aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getOrderNo,outTradeNo).one();
            if(aliSignChargingOrder==null){
                log.error("支付宝签约扣款通知未找到支付日志,订单号:{}",outTradeNo);
                return failRespMsg;
            }
            final String tradeStatus = notifyParam.getTradeStatus();
            // 我间隔一年收到了TRADE_FINISHED通知,又给充值了,等于是收到了TRADE_SUCCESS和TRADE_FINISHED两次通知
            if(AliPayConfig.AlipayTradeStatus.TRADE_FINISHED.equals(tradeStatus)){
                log.info("支付宝签约扣款通知交易完成TRADE_FINISHED,此通知会在TRADE_SUCCESS后二次通知,所以忽略");
                return succRespMsg;
            }
            boolean isPaySucc = AliPayConfig.AlipayTradeStatus.TRADE_SUCCESS.equals(tradeStatus) /*|| AlipayTradeStatus.TRADE_FINISHED.equals(tradeStatus)*/;

            //已收到过支付成功通过，直接返回
            if(aliSignChargingOrder.getOrderStatus() == 1){
                log.info("支付宝签约扣款通知,二次回调直接返回,订单号:{}",outTradeNo);
                return succRespMsg;
            }
            //更新支付日志
            aliSignChargingOrder.setOrderStatus(isPaySucc?1:2);
            aliSignChargingOrder.setUpdateTime(new Date());
            aliSignChargingOrder.setPayTime(new Date());
            aliSignChargingOrder.setPayMonth(DateUtil.formatYearMonth(LocalDateTime.now()));
            aliSignChargingOrder.setAlipayUserId(notifyParam.getBuyerId());
            aliSignChargingOrder.setTradeNo(notifyParam.getTradeNo());
            aliSignChargingOrder.setBuyerLogonId(notifyParam.getBuyerLogonId());
            //设置签约状态
            AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo,aliSignChargingOrder.getExternalAgreementNo()).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
            if(aliSignRecord!=null){
                aliSignChargingOrder.setSignStatus(aliSignRecord.getSignStatus());
            }
            if(isPaySucc && BizConstant.BIZ_TYPE_VRBT_YD_ALIPAY.equals(aliSignChargingOrder.getBizType())){
                //移动支付宝视频彩铃充值
                couponCodeService.addAliPayVrbtQueue(outTradeNo);
                aliSignChargingOrder.setRemark("支付宝移动视频彩铃权益充值中");
            }else if(isPaySucc && BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY.equals(aliSignChargingOrder.getBizType())){
                //电信支付宝视频彩铃首次扣款成功充值电信支付宝视频彩铃权益
                AliSignChargingOrder orderPay=aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getExternalAgreementNo, aliSignChargingOrder.getExternalAgreementNo()).eq(AliSignChargingOrder::getBizType, BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY).orderByAsc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
                if(orderPay!=null && orderPay.getOrderNo().equals(outTradeNo)){
                    couponCodeService.addAliPayVrbtQueue(outTradeNo);
                    aliSignChargingOrder.setRemark("支付宝电信视频彩铃权益充值中");
                }
            }else if(isPaySucc && BizConstant.BIZ_TYPE_VRBT_LT_ALIPAY.equals(aliSignChargingOrder.getBizType())){
                //联通支付宝视频彩铃充值
                couponCodeService.addAliPayVrbtQueue(outTradeNo);
                aliSignChargingOrder.setRemark("支付宝联通视频彩铃权益充值中");
            }else{
                aliSignChargingOrder.setRemark(isPaySucc?"支付成功":"支付失败");
            }
            aliSignChargingOrderService.updateById(aliSignChargingOrder);


            //电信支付宝视频彩铃首次扣款失败自动解约
            if(!isPaySucc && StringUtil.equals(aliSignChargingOrder.getBizType(),BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY)){
                AliSignChargingOrder orderPay=aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getExternalAgreementNo, aliSignChargingOrder.getExternalAgreementNo()).eq(AliSignChargingOrder::getBizType, BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY).orderByAsc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
                if(orderPay!=null && orderPay.getOrderNo().equals(outTradeNo)){
                    alipayService.unSign(aliSignChargingOrder.getExternalAgreementNo());
                }
            }
            if(isPaySucc){
                //初始进单
                String alipayCacheWarnRedisKey = "alipay::cache::warn:" +aliSignChargingOrder.getBusinessType();
                redisUtil.set(alipayCacheWarnRedisKey,"success",BizConstant.ALIPAY_TIME);
                //首次支付修改渠道订单状态、上报广告平台
                aliSignChargingOrderService.updateStatusAdFeedback(aliSignChargingOrder,aliSignChargingOrder.getBusinessType(),outTradeNo);
//                if(StringUtil.isNotBlank(aliSignChargingOrder.getExternalAgreementNo())){
//                    //修改下次扣款时间
//                    AliSignRecord aliSignRecord =aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo, aliSignChargingOrder.getExternalAgreementNo()).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
//                    if(aliSignRecord != null){
//                        aliSignRecord.setNextDeductTime(DateUtil.nextDeductTime(new Date()));
//                        aliSignRecordService.updateById(aliSignRecord);
//                    }
//                }
                //支付成功发送短信
                smsModelService.sendSms(aliSignChargingOrder.getMobile(),"ALIPAY",aliSignChargingOrder.getBizType(),"2");
            }

            //长沙支付宝支付通知接口
//            changShaAliPayNotifyService.payNotify(outTradeNo,aliSignChargingOrder.getBusinessType(),true);


            //支付宝通知回传
            aliSignChargingOrderService.aliPayCallback(outTradeNo,aliSignChargingOrder.getMobile(),aliSignChargingOrder.getBusinessType());
        } catch (Exception e) {
            log.error("支付宝签约扣款通知出错：",e);
            return failRespMsg;
        }
        return succRespMsg;
    }

    /**
     * 号段省份验证
     * @param mobile
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "号段省份验证", notes = "号段省份验证")
    @RequestMapping(value = "/provinceChannelPhoneValidate")
    public Result<?> provinceChannelPhoneValidate(String mobile, String channelCode) {
        log.info("号段省份验证=>手机号:{},渠道号:{}", mobile, channelCode);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        //号段验证
        boolean phoneValidateAllow = provinceChannelPhoneConfigService.phoneValidate(channelCode, mobile);
        if(!phoneValidateAllow){
            return Result.noauth("号段未通过验证");
        }
        //渠道省份验证
        boolean provinceValidateAllow = provinceChannelPhoneConfigService.provinceValidate(channelCode, mobileRegionResult.getProvince());
        if(!provinceValidateAllow){
            return Result.bizConfirm("省份未通过验证");
        }
        return Result.ok("验证成功");
    }
    /**
     * 号段城市验证
     * @param mobile
     * @param channelCode
     * @return
     */
    @ApiOperation(value = "号段省份验证", notes = "号段省份验证")
    @RequestMapping(value = "/cityChannelPhoneValidate")
    public Result<?> cityChannelPhoneValidate(String mobile, String channelCode) {
        log.info("号段省份验证=>手机号:{},渠道号:{}", mobile, channelCode);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        //号段验证
        boolean phoneValidateAllow = cityBlackConfigService.phoneValidate(channelCode, mobile);
        if(!phoneValidateAllow){
            return Result.noauth("号段未通过验证");
        }
        return Result.ok("验证成功");
    }

    /**
     * 腾讯授权回调接口
     * @param authorizationCode
     * @return
     */
    @ApiOperation(value = "腾讯授权回调接口", notes = "腾讯授权回调接口")
    @RequestMapping(value = "/txFeedback")
    public Result<?> txFeedback(@RequestParam("authorization_code") String authorizationCode,String state) {
        final HttpUrl httpUrl = HttpUrl.parse(txFeedbackProperties.getUrl())
                .newBuilder()
                .addQueryParameter("authorizationCode", authorizationCode)
                .addQueryParameter("state", state)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = OkHttpClientUtils.getSingletonInstance().newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("腾讯授权回调接口,异常:", e);
        }
        return Result.ok("OK");
    }


    /**
     * 获取产品价格
     * @param businessType
     * @param tradeType
     * @return
     */
    @ApiOperation(value = "获取产品价格", notes = "获取产品价格")
    @PostMapping(value = "/mall/product/price")
    @ResponseBody
    public FebsResponse wechatProduct(@RequestParam(value = "businessType", required = false, defaultValue = BizConstant.BUSINESS_TYPE_WX_QYCLZY) String businessType,
                                      @RequestParam(value = "tradeType", required = false, defaultValue = BizConstant.TRADE_TYPE_WECHAT) String tradeType){
        WechatConfigLog wechatConfigLog = wechatConfigLogService.getWechatConfig(tradeType,businessType);
        if(wechatConfigLog!=null){
            return new FebsResponse().success().data(wechatConfigLog.getTotalAmount());
        }
        return new FebsResponse().fail();
    }


    /**
     * 电信h5订单查询接口
     * @param mobile
     * @param orderNo
     * @param company
     * @return
     */
    @ApiOperation(value = "电信h5订单查询接口", notes = "电信h5订单查询接口")
    @PostMapping(value = "/queryH5Order")
    @ResponseBody
    public Result queryH5Order(@RequestParam("mobile") String mobile,
                                              @RequestParam("orderNo") String orderNo,
                                              @RequestParam(value = "company",required = false,defaultValue = BIZ_DIANXIN_CHANNEL_MAIHE_6) String company) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        DianxinResp dianxinResp=dianxinVrbtService.queryH5Order(mobile,orderNo,company);
        if(dianxinResp.isOK()){
            return Result.ok("订购成功");
        }
        return Result.error("未订购");
    }

    @ApiOperation("图片安审")
    @GetMapping("/check/picSecurity")
    public Result<Object> picSecurity(@RequestParam("picUrl") String picUrl
            , @RequestParam(value = "securityDetection", defaultValue = "true") Boolean securityDetection,
                                      @RequestParam(value = "faceDetection", required = false, defaultValue = "false") Boolean faceDetection) {
        return Result.ok(commonSecurityService.picSecurity(securityDetection, faceDetection, picUrl));
    }

    @ApiOperation("图片送审")
    @GetMapping("/check/picSecurity/push")
    public Result<Object> picSecurityPush(@RequestParam("picUrl") String picUrl
            , @RequestParam(value = "securityDetection", defaultValue = "true") Boolean securityDetection,
                                      @RequestParam(value = "faceDetection", required = false, defaultValue = "false") Boolean faceDetection) {
        return Result.ok(commonSecurityService.picSecurityPush(securityDetection, faceDetection, picUrl));
    }

    @ApiOperation("查询图片送审结果")
    @GetMapping("/query/picSecurityResult")
    public Result<Object> picSecurityPushResult(@RequestParam("dataId") String dataId) {
        return Result.ok(commonSecurityService.securityResult(dataId));
    }

    @ApiOperation("CP权益合作-获取订购结果通知")
    @PostMapping("/cp/resultSync")
    public Result<Object> cpResultSync(@RequestBody JsonNode jsonNode) {
        String moduleId = jsonNode.at("/moduleId").asText();
        String productId = jsonNode.at("/productId").asText();
        String province = jsonNode.at("/province").asText();
        String orderId = jsonNode.at("/orderId").asText();
        String submitTime = jsonNode.at("/submitTime").asText();
        String mobile = jsonNode.at("/mobile").asText();
        String sourceOrderId = jsonNode.at("/sourceOrderId").asText();

        if (StringUtils.isEmpty(moduleId)) {
            return Result.error("moduleId不能为空!");
        }
        if (StringUtils.isEmpty(productId)) {
            return Result.error("productId不能为空!");
        }
        if (StringUtils.isEmpty(province)) {
            return Result.error("province不能为空!");
        }
        if (StringUtils.isEmpty(orderId)) {
            return Result.error("orderId不能为空!");
        }
        if (StringUtils.isEmpty(submitTime)) {
            return Result.error("submitTime不能为空!");
        }
        if (StringUtils.isEmpty(mobile)) {
            return Result.error("mobile不能为空!");
        }
        if (StringUtils.isEmpty(sourceOrderId)) {
            return Result.error("sourceOrderId不能为空!");
        }

        CpOrderResult cpOrderResult = new CpOrderResult();
        cpOrderResult.setModuleId(moduleId);
        cpOrderResult.setProductId(productId);
        cpOrderResult.setProvince(province);
        cpOrderResult.setOrderId(orderId);

        Date parse;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            parse = sdf.parse(submitTime);
        } catch (ParseException e) {
            return Result.error("时间格式错误！正确格式：yyyy-MM-dd HH:mm:ss");
        }
        cpOrderResult.setSubmitTime(parse);
        cpOrderResult.setMobile(mobile);
        cpOrderResult.setSourceOrderId(sourceOrderId);
        cpOrderResultService.save(cpOrderResult);
        return Result.ok();
    }
}

