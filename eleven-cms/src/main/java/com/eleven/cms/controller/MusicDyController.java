package com.eleven.cms.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.entity.MusicDy;
import com.eleven.cms.service.IMusicDyService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 订阅包歌曲
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
@Api(tags="订阅包歌曲")
@RestController
@RequestMapping("/cms/musicDy")
@Slf4j
public class MusicDyController extends JeecgController<MusicDy, IMusicDyService> {
	@Autowired
	private IMusicDyService musicDyService;

	/**
	 * 分页列表查询
	 *
	 * @param musicDy
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "订阅包歌曲-分页列表查询")
	@ApiOperation(value="订阅包歌曲-分页列表查询", notes="订阅包歌曲-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(MusicDy musicDy,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<MusicDy> queryWrapper = QueryGenerator.initQueryWrapper(musicDy, req.getParameterMap());
		Page<MusicDy> page = new Page<MusicDy>(pageNo, pageSize);
		IPage<MusicDy> pageList = musicDyService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param musicDy
	 * @return
	 */
	//@AutoLog(value = "订阅包歌曲-添加")
	@ApiOperation(value="订阅包歌曲-添加", notes="订阅包歌曲-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody MusicDy musicDy) {
		musicDyService.save(musicDy);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param musicDy
	 * @return
	 */
	//@AutoLog(value = "订阅包歌曲-编辑")
	@ApiOperation(value="订阅包歌曲-编辑", notes="订阅包歌曲-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody MusicDy musicDy) {
		musicDyService.updateById(musicDy);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "订阅包歌曲-通过id删除")
	@ApiOperation(value="订阅包歌曲-通过id删除", notes="订阅包歌曲-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		musicDyService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "订阅包歌曲-批量删除")
	@ApiOperation(value="订阅包歌曲-批量删除", notes="订阅包歌曲-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.musicDyService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "订阅包歌曲-通过id查询")
	@ApiOperation(value="订阅包歌曲-通过id查询", notes="订阅包歌曲-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		MusicDy musicDy = musicDyService.getById(id);
		if(musicDy==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(musicDy);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param musicDy
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MusicDy musicDy) {
        return super.exportXls(request, musicDy, MusicDy.class, "订阅包歌曲");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return musicDyService.importExcel(request, response, MusicDy.class);
    }
	 /**
	  *   通过id删除
	  *
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "歌曲-通过id抓取图片")
	 @ApiOperation(value="歌曲-通过id抓取图片", notes="歌曲-通过id抓取图片")
	 @GetMapping(value = "/fetchImg")
	 public Result<?> fetchImg(@RequestParam(name="id",required=true) String id) {
		 musicDyService.updateProductById(id);
		 return Result.ok("已抓取图片!");
	 }

	 /**
	  *  批量删除
	  *
	  * @param ids
	  * @return
	  */
	 //@AutoLog(value = "歌曲-批量抓取图片")
	 @ApiOperation(value="歌曲-批量抓取图片", notes="歌曲-批量抓取图片")
	 @GetMapping(value = "/fetchImgBatch")
	 public Result<?> fetchImgBatch(@RequestParam(name="ids",required=true) String ids) {
		 this.musicDyService.updateProductByIdList(Arrays.asList(ids.split(",")));
		 return Result.ok("已批量抓取图片!");
	 }
}
