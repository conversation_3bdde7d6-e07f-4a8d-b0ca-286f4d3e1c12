package com.eleven.cms.controller;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/15 14:03
 **/

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.RemoteResult;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.VrbtAppLogin;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 新的ApiController,注意访问路径不要和旧的冲突
 */
@Api(tags = "api")
@RestController
@RequestMapping("/api/vrbt/app")
@Slf4j
@Validated
public class ApiVrbtAppCommonController {
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private ISmsValidateService smsValidateService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    private IVrbtAppUserService vrbtAppUserService;
    @Autowired
    private IVrbtAppUserPointsService vrbtAppUserPointsService;
    @Autowired
    private IVrbtAppUserOrderService vrbtAppUserOrderService;
    /**
     * 视频彩铃app登录发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getCaptcha")
    @ResponseBody
    public Result<?> getCaptcha(@RequestParam("mobile") String mobile,
                                @RequestParam(name = "channelCode", required = false, defaultValue =MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174) String channelCode,
                                @RequestParam(name = "serviceId",   required = false, defaultValue =BizConstant.SMS_MODEL_COMMON_SERVICE_ID) String serviceId) {
        log.info("视频彩铃app登录-发送短信验证码=>手机号:{},渠道号:{},业务ID:{}",mobile,channelCode,serviceId);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.rightsCreate(mobile,channelCode,serviceId);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }
        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }
    /**
     * 视频彩铃app登录接口
     * @param mobile
     * @param captcha
     * @return
     */
    @PostMapping(value = "/login")
    @ResponseBody
    public Result vrbtAppLogin(@RequestParam("mobile") String mobile,@RequestParam("captcha")String captcha,HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("视频彩铃app登录=>手机号:{},短信验证码:{},referer:{}",mobile,captcha,referer);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号",false);
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return Result.error("验证码错误",false);
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult == null || !mobileRegionResult.isIspYidong()) {
            return Result.error("暂只支持移动用户",false);
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage(),false);
        }
        return vrbtAppUserService.vrbtAppLogin(mobile);
    }

    /**
     * 视频彩铃app查询用户是否包月
     * @param mobile
     * @return
     */
    @PostMapping(value = "/sub")
    @ResponseBody
    @VrbtAppLogin(methodDesc = "查询用户是否包月")
    public Result sub(@RequestParam("mobile") String mobile) {
        final RemoteResult remoteResult =miguApiService.asMemberQuery(mobile,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        boolean sub=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getChannel,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174).eq(Subscribe::getStatus,BizConstant.SUBSCRIBE_STATUS_SUCCESS).count()>0;
        if(remoteResult.isAsMember() && sub){
            return Result.ok("查询成功",true);
        }
        return Result.ok("查询成功",false);
    }

    /**
     * 视频彩铃app获取积分
     * @param mobile
     * @return
     */
    @PostMapping(value = "/points")
    @ResponseBody
    @VrbtAppLogin(methodDesc = "获取积分")
    public Result points(@RequestParam("mobile") String mobile,@RequestParam("pointsType") String pointsType) {
        if(StringUtil.isEmpty(pointsType) || !StringUtils.equalsAny(pointsType,BizConstant.INITB_POINTS,BizConstant.SIGN_IN_POINTS)){
            return Result.error("系统错误",0);
        }
        return vrbtAppUserPointsService.points(mobile,pointsType);
    }



    /**
     * 视频彩铃app获取签到列表
     * @param mobile
     * @return
     */
    @PostMapping(value = "/pointsList")
    @ResponseBody
    @VrbtAppLogin(methodDesc = "获取签到列表")
    public Result pointsList(@RequestParam("mobile") String mobile) {
        return vrbtAppUserPointsService.pointsList(mobile);
    }
    /**
     * 视频彩铃app积分兑换
     * @param mobile
     * @return
     */
    @PostMapping(value = "/pointsUse")
    @ResponseBody
    @VrbtAppLogin(methodDesc = "积分兑换")
    public Result pointsUse(@RequestParam("mobile") String mobile,@RequestParam("consigneeMobile") String consigneeMobile,@RequestParam("consigneeName") String consigneeName,@RequestParam("address") String address,@RequestParam("productName") String productName) {
        if (Strings.isNullOrEmpty(consigneeMobile) || !consigneeMobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入收货人手机号",0);
        }
        if (Strings.isNullOrEmpty(consigneeName)){
            return Result.error("请输入收货人名称",0);
        }
        if (Strings.isNullOrEmpty(address)){
            return Result.error("请输入收货人地址",0);
        }
        if (Strings.isNullOrEmpty(productName)){
            return Result.error("请输入产品",0);
        }
        return vrbtAppUserOrderService.pointsUse(mobile,consigneeMobile,consigneeName,address,productName);
    }


    /**
     * 视频彩铃app查询兑换记录
     * @param mobile
     * @return
     */
    @PostMapping(value = "/orderList")
    @ResponseBody
    @VrbtAppLogin(methodDesc = "兑换记录")
    public Result orderList(@RequestParam("mobile") String mobile) {
        return vrbtAppUserOrderService.orderList(mobile);
    }

    /**
     * 视频彩铃app查询领取记录
     * @param mobile
     * @return
     */
    @PostMapping(value = "/rechargeList")
    @ResponseBody
    @VrbtAppLogin(methodDesc = "领取记录")
    public Result rechargeList(@RequestParam("mobile") String mobile) {
        return vrbtAppUserOrderService.rechargeList(mobile);
    }
    /**
     * 视频彩铃app领取视频彩铃
     * @param mobile
     * @return
     */
    @PostMapping(value = "/rechargeVrbt")
    @ResponseBody
    @VrbtAppLogin(methodDesc = "领取视频彩铃")
    public Result rechargeVrbt(@RequestParam("mobile") String mobile) {
        return vrbtAppUserOrderService.rechargeVrbt(mobile);
    }
    /**
     * 视频彩铃app查询视频彩铃
     * @param mobile
     * @return
     */
    @PostMapping(value = "/queryVrbt")
    @ResponseBody
    @VrbtAppLogin(methodDesc = "查询视频彩铃")
    public Result queryVrbt(@RequestParam("mobile") String mobile) {
        return vrbtAppUserOrderService.queryVrbt(mobile);
    }
}
