package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CmsNotifyXwfcs;
import com.eleven.cms.service.ICmsNotifyXwfcsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: cms_notify_xwfcs
 * @Author: jeecg-boot
 * @Date:   2025-02-12
 * @Version: V1.0
 */
@Api(tags="cms_notify_xwfcs")
@RestController
@RequestMapping("/cms.eleven.cms/cmsNotifyXwfcs")
@Slf4j
public class CmsNotifyXwfcsController extends JeecgController<CmsNotifyXwfcs, ICmsNotifyXwfcsService> {
	@Autowired
	private ICmsNotifyXwfcsService cmsNotifyXwfcsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cmsNotifyXwfcs
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "cms_notify_xwfcs-分页列表查询")
	@ApiOperation(value="cms_notify_xwfcs-分页列表查询", notes="cms_notify_xwfcs-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CmsNotifyXwfcs cmsNotifyXwfcs,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CmsNotifyXwfcs> queryWrapper = QueryGenerator.initQueryWrapper(cmsNotifyXwfcs, req.getParameterMap());
		Page<CmsNotifyXwfcs> page = new Page<CmsNotifyXwfcs>(pageNo, pageSize);
		IPage<CmsNotifyXwfcs> pageList = cmsNotifyXwfcsService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cmsNotifyXwfcs
	 * @return
	 */
	@AutoLog(value = "cms_notify_xwfcs-添加")
	@ApiOperation(value="cms_notify_xwfcs-添加", notes="cms_notify_xwfcs-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CmsNotifyXwfcs cmsNotifyXwfcs) {
		cmsNotifyXwfcsService.save(cmsNotifyXwfcs);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cmsNotifyXwfcs
	 * @return
	 */
	@AutoLog(value = "cms_notify_xwfcs-编辑")
	@ApiOperation(value="cms_notify_xwfcs-编辑", notes="cms_notify_xwfcs-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody CmsNotifyXwfcs cmsNotifyXwfcs) {
		cmsNotifyXwfcsService.updateById(cmsNotifyXwfcs);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "cms_notify_xwfcs-通过id删除")
	@ApiOperation(value="cms_notify_xwfcs-通过id删除", notes="cms_notify_xwfcs-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		cmsNotifyXwfcsService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "cms_notify_xwfcs-批量删除")
	@ApiOperation(value="cms_notify_xwfcs-批量删除", notes="cms_notify_xwfcs-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cmsNotifyXwfcsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "cms_notify_xwfcs-通过id查询")
	@ApiOperation(value="cms_notify_xwfcs-通过id查询", notes="cms_notify_xwfcs-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CmsNotifyXwfcs cmsNotifyXwfcs = cmsNotifyXwfcsService.getById(id);
		if(cmsNotifyXwfcs==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(cmsNotifyXwfcs);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmsNotifyXwfcs
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsNotifyXwfcs cmsNotifyXwfcs) {
        return super.exportXls(request, cmsNotifyXwfcs, CmsNotifyXwfcs.class, "cms_notify_xwfcs");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CmsNotifyXwfcs.class);
    }

}
