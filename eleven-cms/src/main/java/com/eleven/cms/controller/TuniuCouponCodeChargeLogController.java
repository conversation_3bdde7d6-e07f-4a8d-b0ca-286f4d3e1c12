package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.TuniuCouponCodeChargeLog;
import com.eleven.cms.service.ITuniuCouponCodeChargeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 途牛券码充值记录
 * @Author: jeecg-boot
 * @Date:   2024-03-29
 * @Version: V1.0
 */
@Api(tags="途牛券码充值记录")
@RestController
@RequestMapping("/cms/tuniuCouponCodeChargeLog")
@Slf4j
public class TuniuCouponCodeChargeLogController extends JeecgController<TuniuCouponCodeChargeLog, ITuniuCouponCodeChargeLogService> {
	@Autowired
	private ITuniuCouponCodeChargeLogService tuniuCouponCodeChargeLogService;

	/**
	 * 分页列表查询
	 *
	 * @param tuniuCouponCodeChargeLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "途牛券码充值记录-分页列表查询")
	@ApiOperation(value="途牛券码充值记录-分页列表查询", notes="途牛券码充值记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<TuniuCouponCodeChargeLog> queryWrapper = QueryGenerator.initQueryWrapper(tuniuCouponCodeChargeLog, req.getParameterMap());
		Page<TuniuCouponCodeChargeLog> page = new Page<TuniuCouponCodeChargeLog>(pageNo, pageSize);
		IPage<TuniuCouponCodeChargeLog> pageList = tuniuCouponCodeChargeLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param tuniuCouponCodeChargeLog
	 * @return
	 */
	//@AutoLog(value = "途牛券码充值记录-添加")
	@ApiOperation(value="途牛券码充值记录-添加", notes="途牛券码充值记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog) {
		tuniuCouponCodeChargeLogService.save(tuniuCouponCodeChargeLog);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param tuniuCouponCodeChargeLog
	 * @return
	 */
	//@AutoLog(value = "途牛券码充值记录-编辑")
	@ApiOperation(value="途牛券码充值记录-编辑", notes="途牛券码充值记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog) {
		tuniuCouponCodeChargeLogService.updateById(tuniuCouponCodeChargeLog);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "途牛券码充值记录-通过id删除")
	@ApiOperation(value="途牛券码充值记录-通过id删除", notes="途牛券码充值记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		tuniuCouponCodeChargeLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "途牛券码充值记录-批量删除")
	@ApiOperation(value="途牛券码充值记录-批量删除", notes="途牛券码充值记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.tuniuCouponCodeChargeLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "途牛券码充值记录-通过id查询")
	@ApiOperation(value="途牛券码充值记录-通过id查询", notes="途牛券码充值记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog = tuniuCouponCodeChargeLogService.getById(id);
		if(tuniuCouponCodeChargeLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(tuniuCouponCodeChargeLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param tuniuCouponCodeChargeLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog) {
        return super.exportXls(request, tuniuCouponCodeChargeLog, TuniuCouponCodeChargeLog.class, "途牛券码充值记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TuniuCouponCodeChargeLog.class);
    }

}
