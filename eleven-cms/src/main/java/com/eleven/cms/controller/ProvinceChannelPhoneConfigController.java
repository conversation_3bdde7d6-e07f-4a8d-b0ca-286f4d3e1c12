package com.eleven.cms.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.entity.ProvinceBusinessChannelConfig;
import com.eleven.cms.entity.ProvinceBusinessChannelInfo;
import com.eleven.cms.entity.ProvinceChannelPhoneInfo;
import com.eleven.cms.vo.ProvinceBusinessChannelConfigPage;
import com.eleven.cms.vo.ProvinceChannelPhoneConfigPage;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.springframework.beans.BeanUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.ProvinceChannelPhoneConfig;
import com.eleven.cms.service.IProvinceChannelPhoneConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_province_channel_phone_config
 * @Author: jeecg-boot
 * @Date:   2023-02-06
 * @Version: V1.0
 */
@Api(tags="cms_province_channel_phone_config")
@RestController
@RequestMapping("/cms/provinceChannelPhoneConfig")
@Slf4j
public class ProvinceChannelPhoneConfigController extends JeecgController<ProvinceChannelPhoneConfig, IProvinceChannelPhoneConfigService> {
	@Autowired
	private IProvinceChannelPhoneConfigService provinceChannelPhoneConfigService;
	
	/**
	 * 分页列表查询
	 *
	 * @param provinceChannelPhoneConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_province_channel_phone_config-分页列表查询")
	@ApiOperation(value="cms_province_channel_phone_config-分页列表查询", notes="cms_province_channel_phone_config-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ProvinceChannelPhoneConfig provinceChannelPhoneConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ProvinceChannelPhoneConfig> queryWrapper = QueryGenerator.initQueryWrapper(provinceChannelPhoneConfig, req.getParameterMap());
		Page<ProvinceChannelPhoneConfig> page = new Page<ProvinceChannelPhoneConfig>(pageNo, pageSize);
		IPage<ProvinceChannelPhoneConfig> pageList = provinceChannelPhoneConfigService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param provinceChannelPhoneConfigPage
	 * @return
	 */
	//@AutoLog(value = "cms_province_channel_phone_config-添加")
	@ApiOperation(value="cms_province_channel_phone_config-添加", notes="cms_province_channel_phone_config-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProvinceChannelPhoneConfigPage provinceChannelPhoneConfigPage) {
		ProvinceChannelPhoneConfig provinceChannelPhoneConfig = new ProvinceChannelPhoneConfig();
		org.springframework.beans.BeanUtils.copyProperties(provinceChannelPhoneConfigPage, provinceChannelPhoneConfig);
		provinceChannelPhoneConfigService.saveMain(provinceChannelPhoneConfig, provinceChannelPhoneConfigPage.getProvinceChannelPhoneInfoList());
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param provinceChannelPhoneConfigPage
	 * @return
	 */
	//@AutoLog(value = "cms_province_channel_phone_config-编辑")
	@ApiOperation(value="cms_province_channel_phone_config-编辑", notes="cms_province_channel_phone_config-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProvinceChannelPhoneConfigPage provinceChannelPhoneConfigPage) throws Exception{
		ProvinceChannelPhoneConfig provinceChannelPhoneConfig = new ProvinceChannelPhoneConfig();
		BeanUtils.copyProperties(provinceChannelPhoneConfigPage, provinceChannelPhoneConfig);
		ProvinceChannelPhoneConfig provinceChannelPhoneConfigEntity = provinceChannelPhoneConfigService.getById(provinceChannelPhoneConfig.getId());
		if(provinceChannelPhoneConfigEntity==null) {
			return Result.error("未找到对应数据");
		}
		provinceChannelPhoneConfigService.updateMain(provinceChannelPhoneConfig, provinceChannelPhoneConfigPage.getProvinceChannelPhoneInfoList());
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_province_channel_phone_config-通过id删除")
	@ApiOperation(value="cms_province_channel_phone_config-通过id删除", notes="cms_province_channel_phone_config-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		provinceChannelPhoneConfigService.deleteById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_province_channel_phone_config-批量删除")
	@ApiOperation(value="cms_province_channel_phone_config-批量删除", notes="cms_province_channel_phone_config-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.provinceChannelPhoneConfigService.deleteByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_province_channel_phone_config-通过id查询")
	@ApiOperation(value="cms_province_channel_phone_config-通过id查询", notes="cms_province_channel_phone_config-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProvinceChannelPhoneConfig provinceChannelPhoneConfig = provinceChannelPhoneConfigService.getById(id);
		if(provinceChannelPhoneConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(provinceChannelPhoneConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param provinceChannelPhoneConfig
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProvinceChannelPhoneConfig provinceChannelPhoneConfig) {
		// Step.1 组装查询条件查询数据
		QueryWrapper<ProvinceChannelPhoneConfig> queryWrapper = QueryGenerator.initQueryWrapper(provinceChannelPhoneConfig, request.getParameterMap());
		LoginUser sysUser = HttpUtil.getCurrUser();

		//Step.2 获取导出数据
		List<ProvinceChannelPhoneConfig> queryList = provinceChannelPhoneConfigService.list(queryWrapper);
		// 过滤选中数据
		String selections = request.getParameter("selections");
		List<ProvinceChannelPhoneConfig> provinceChannelPhoneConfigList = new ArrayList<ProvinceChannelPhoneConfig>();
		if(oConvertUtils.isEmpty(selections)) {
			provinceChannelPhoneConfigList = queryList;
		}else {
			List<String> selectionList = Arrays.asList(selections.split(","));
			provinceChannelPhoneConfigList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
		}

		// Step.3 组装pageList
		List<ProvinceChannelPhoneConfigPage> pageList = new ArrayList<ProvinceChannelPhoneConfigPage>();
		for (ProvinceChannelPhoneConfig main : provinceChannelPhoneConfigList) {
			ProvinceChannelPhoneConfigPage vo = new ProvinceChannelPhoneConfigPage();
			BeanUtils.copyProperties(main, vo);
			List<ProvinceChannelPhoneInfo> provinceChannelPhoneInfoList = provinceChannelPhoneConfigService.selectByMainId(main.getId());
			vo.setProvinceChannelPhoneInfoList(provinceChannelPhoneInfoList);
			pageList.add(vo);
		}

		// Step.4 AutoPoi 导出Excel
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		mv.addObject(NormalExcelConstants.FILE_NAME, "cms_province_channel_phone_config列表");
		mv.addObject(NormalExcelConstants.CLASS, ProvinceChannelPhoneConfigPage.class);
		mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("cms_province_channel_phone_config数据", "导出人:"+sysUser.getRealname(), "cms_province_channel_phone_config"));
		mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
		return mv;
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			ImportParams params = new ImportParams();
			params.setTitleRows(2);
			params.setHeadRows(1);
			params.setNeedSave(true);
			try {
				List<ProvinceChannelPhoneConfigPage> list = ExcelImportUtil.importExcel(file.getInputStream(), ProvinceChannelPhoneConfigPage.class, params);
				for (ProvinceChannelPhoneConfigPage page : list) {
					ProvinceChannelPhoneConfig po = new ProvinceChannelPhoneConfig();
					BeanUtils.copyProperties(page, po);
					provinceChannelPhoneConfigService.saveMain(po, page.getProvinceChannelPhoneInfoList());
				}
				return Result.ok("文件导入成功！数据行数:" + list.size());
			} catch (Exception e) {
				log.error(e.getMessage(),e);
				return Result.error("文件导入失败:"+e.getMessage());
			} finally {
				try {
					file.getInputStream().close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.ok("文件导入失败！");
    }

	 /**
	  * 通过id查询
	  *
	  * @param id
	  * @return
	  */
	 @GetMapping(value = "/queryProvinceChannelPhoneInfoByMainId")
	 public Result<?> queryProvinceChannelPhoneInfoByMainId(@RequestParam(name="id",required=true) String id) {
		 List<ProvinceChannelPhoneInfo> list = provinceChannelPhoneConfigService.selectByMainId(id);
		 return Result.ok(list);
	 }

}
