package com.eleven.cms.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.util.HttpUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.CmsComicVideo;
import com.eleven.cms.entity.CmsColumnComic;
import com.eleven.cms.vo.CmsColumnComicPage;
import com.eleven.cms.service.ICmsColumnComicService;
import com.eleven.cms.service.ICmsComicVideoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: cms_column_comic
 * @Author: jeecg-boot
 * @Date: 2024-08-26
 * @Version: V1.0
 */
@Api(tags = "cms_column_comic")
@RestController
@RequestMapping("/cms/cmsColumnComic")
@Slf4j
public class CmsColumnComicController {
    @Autowired
    private ICmsColumnComicService cmsColumnComicService;
    @Autowired
    private ICmsComicVideoService cmsComicVideoService;

    /**
     * 分页列表查询
     *
     * @param cmsColumnComic
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "cms_column_comic-分页列表查询")
    @ApiOperation(value = "cms_column_comic-分页列表查询", notes = "cms_column_comic-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(CmsColumnComic cmsColumnComic,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<CmsColumnComic> queryWrapper = QueryGenerator.initQueryWrapper(cmsColumnComic, req.getParameterMap());
        Page<CmsColumnComic> page = new Page<CmsColumnComic>(pageNo, pageSize);
        IPage<CmsColumnComic> pageList = cmsColumnComicService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param cmsColumnComicPage
     * @return
     */
    //@AutoLog(value = "cms_column_comic-添加")
    @ApiOperation(value = "cms_column_comic-添加", notes = "cms_column_comic-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody CmsColumnComicPage cmsColumnComicPage) {
        CmsColumnComic cmsColumnComic = new CmsColumnComic();
        BeanUtils.copyProperties(cmsColumnComicPage, cmsColumnComic);
        cmsColumnComicService.saveMain(cmsColumnComic, cmsColumnComicPage.getCmsComicVideoList());
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param cmsColumnComicPage
     * @return
     */
    //@AutoLog(value = "cms_column_comic-编辑")
    @ApiOperation(value = "cms_column_comic-编辑", notes = "cms_column_comic-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody CmsColumnComicPage cmsColumnComicPage) {
        CmsColumnComic cmsColumnComic = new CmsColumnComic();
        BeanUtils.copyProperties(cmsColumnComicPage, cmsColumnComic);
        CmsColumnComic cmsColumnComicEntity = cmsColumnComicService.getById(cmsColumnComic.getId());
        if (cmsColumnComicEntity == null) {
            return Result.error("未找到对应数据");
        }
        cmsColumnComicService.updateMain(cmsColumnComic, cmsColumnComicPage.getCmsComicVideoList());
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_column_comic-通过id删除")
    @ApiOperation(value = "cms_column_comic-通过id删除", notes = "cms_column_comic-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        cmsColumnComicService.delMain(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "cms_column_comic-批量删除")
    @ApiOperation(value = "cms_column_comic-批量删除", notes = "cms_column_comic-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.cmsColumnComicService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_column_comic-通过id查询")
    @ApiOperation(value = "cms_column_comic-通过id查询", notes = "cms_column_comic-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        CmsColumnComic cmsColumnComic = cmsColumnComicService.getById(id);
        if (cmsColumnComic == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(cmsColumnComic);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_comic_video通过主表ID查询")
    @ApiOperation(value = "cms_comic_video主表ID查询", notes = "cms_comic_video-通主表ID查询")
    @GetMapping(value = "/queryCmsComicVideoByMainId")
    public Result<?> queryCmsComicVideoListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<CmsComicVideo> cmsComicVideoList = cmsComicVideoService.selectByMainId(id);
        return Result.ok(cmsComicVideoList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param cmsColumnComic
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsColumnComic cmsColumnComic) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<CmsColumnComic> queryWrapper = QueryGenerator.initQueryWrapper(cmsColumnComic, request.getParameterMap());
        LoginUser sysUser = HttpUtil.getCurrUser();

        //Step.2 获取导出数据
        List<CmsColumnComic> queryList = cmsColumnComicService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<CmsColumnComic> cmsColumnComicList = new ArrayList<CmsColumnComic>();
        if (oConvertUtils.isEmpty(selections)) {
            cmsColumnComicList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            cmsColumnComicList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }

        // Step.3 组装pageList
        List<CmsColumnComicPage> pageList = new ArrayList<CmsColumnComicPage>();
        for (CmsColumnComic main : cmsColumnComicList) {
            CmsColumnComicPage vo = new CmsColumnComicPage();
            BeanUtils.copyProperties(main, vo);
            List<CmsComicVideo> cmsComicVideoList = cmsComicVideoService.selectByMainId(main.getId());
            vo.setCmsComicVideoList(cmsComicVideoList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "cms_column_comic列表");
        mv.addObject(NormalExcelConstants.CLASS, CmsColumnComicPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("cms_column_comic数据", "导出人:" + sysUser.getRealname(), "cms_column_comic"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<CmsColumnComicPage> list = ExcelImportUtil.importExcel(file.getInputStream(), CmsColumnComicPage.class, params);
                for (CmsColumnComicPage page : list) {
                    CmsColumnComic po = new CmsColumnComic();
                    BeanUtils.copyProperties(page, po);
                    cmsColumnComicService.saveMain(po, page.getCmsComicVideoList());
                }
                return Result.ok("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }

}
