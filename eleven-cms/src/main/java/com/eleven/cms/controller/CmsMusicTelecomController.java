package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CmsMusicTelecom;
import com.eleven.cms.service.ICmsMusicTelecomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: cms_music_telecom
 * @Author: jeecg-boot
 * @Date:   2021-11-18
 * @Version: V1.0
 */
@Api(tags="cms_music_telecom")
@RestController
@RequestMapping("/com.eleven.cms/cmsMusicTelecom")
@Slf4j
public class CmsMusicTelecomController extends JeecgController<CmsMusicTelecom, ICmsMusicTelecomService> {
	@Autowired
	private ICmsMusicTelecomService cmsMusicTelecomService;

	/**
	 * 分页列表查询
	 *
	 * @param cmsMusicTelecom
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_music_telecom-分页列表查询")
	@ApiOperation(value="cms_music_telecom-分页列表查询", notes="cms_music_telecom-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CmsMusicTelecom cmsMusicTelecom,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CmsMusicTelecom> queryWrapper = QueryGenerator.initQueryWrapper(cmsMusicTelecom, req.getParameterMap());
		Page<CmsMusicTelecom> page = new Page<CmsMusicTelecom>(pageNo, pageSize);
		IPage<CmsMusicTelecom> pageList = cmsMusicTelecomService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cmsMusicTelecom
	 * @return
	 */
	//@AutoLog(value = "cms_music_telecom-添加")
	@ApiOperation(value="cms_music_telecom-添加", notes="cms_music_telecom-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CmsMusicTelecom cmsMusicTelecom) {
		cmsMusicTelecomService.save(cmsMusicTelecom);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cmsMusicTelecom
	 * @return
	 */
	//@AutoLog(value = "cms_music_telecom-编辑")
	@ApiOperation(value="cms_music_telecom-编辑", notes="cms_music_telecom-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody CmsMusicTelecom cmsMusicTelecom) {
		cmsMusicTelecomService.updateById(cmsMusicTelecom);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_music_telecom-通过id删除")
	@ApiOperation(value="cms_music_telecom-通过id删除", notes="cms_music_telecom-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		cmsMusicTelecomService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_music_telecom-批量删除")
	@ApiOperation(value="cms_music_telecom-批量删除", notes="cms_music_telecom-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cmsMusicTelecomService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_music_telecom-通过id查询")
	@ApiOperation(value="cms_music_telecom-通过id查询", notes="cms_music_telecom-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CmsMusicTelecom cmsMusicTelecom = cmsMusicTelecomService.getById(id);
		if(cmsMusicTelecom==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(cmsMusicTelecom);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmsMusicTelecom
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsMusicTelecom cmsMusicTelecom) {
        return super.exportXls(request, cmsMusicTelecom, CmsMusicTelecom.class, "cms_music_telecom");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CmsMusicTelecom.class);
    }

}
