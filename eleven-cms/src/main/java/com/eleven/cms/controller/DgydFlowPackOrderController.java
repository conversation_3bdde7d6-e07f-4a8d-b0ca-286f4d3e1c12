package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.DgydFlowPackOrder;
import com.eleven.cms.service.IDgydFlowPackOrderService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 东莞移动流量包订单
 * @Author: jeecg-boot
 * @Date:   2023-01-11
 * @Version: V1.0
 */
@Api(tags="东莞移动流量包订单")
@RestController
@RequestMapping("/cms/dgydFlowPackOrder")
@Slf4j
public class DgydFlowPackOrderController extends JeecgController<DgydFlowPackOrder, IDgydFlowPackOrderService> {
	@Autowired
	private IDgydFlowPackOrderService dgydFlowPackOrderService;
	
	/**
	 * 分页列表查询
	 *
	 * @param dgydFlowPackOrder
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "东莞移动流量包订单-分页列表查询")
	@ApiOperation(value="东莞移动流量包订单-分页列表查询", notes="东莞移动流量包订单-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DgydFlowPackOrder dgydFlowPackOrder,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DgydFlowPackOrder> queryWrapper = QueryGenerator.initQueryWrapper(dgydFlowPackOrder, req.getParameterMap());
		Page<DgydFlowPackOrder> page = new Page<DgydFlowPackOrder>(pageNo, pageSize);
		IPage<DgydFlowPackOrder> pageList = dgydFlowPackOrderService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param dgydFlowPackOrder
	 * @return
	 */
	//@AutoLog(value = "东莞移动流量包订单-添加")
	@ApiOperation(value="东莞移动流量包订单-添加", notes="东莞移动流量包订单-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody DgydFlowPackOrder dgydFlowPackOrder) {
		dgydFlowPackOrderService.save(dgydFlowPackOrder);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param dgydFlowPackOrder
	 * @return
	 */
	//@AutoLog(value = "东莞移动流量包订单-编辑")
	@ApiOperation(value="东莞移动流量包订单-编辑", notes="东莞移动流量包订单-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody DgydFlowPackOrder dgydFlowPackOrder) {
		dgydFlowPackOrderService.updateById(dgydFlowPackOrder);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "东莞移动流量包订单-通过id删除")
	@ApiOperation(value="东莞移动流量包订单-通过id删除", notes="东莞移动流量包订单-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		dgydFlowPackOrderService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "东莞移动流量包订单-批量删除")
	@ApiOperation(value="东莞移动流量包订单-批量删除", notes="东莞移动流量包订单-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.dgydFlowPackOrderService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "东莞移动流量包订单-通过id查询")
	@ApiOperation(value="东莞移动流量包订单-通过id查询", notes="东莞移动流量包订单-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		DgydFlowPackOrder dgydFlowPackOrder = dgydFlowPackOrderService.getById(id);
		if(dgydFlowPackOrder==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(dgydFlowPackOrder);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param dgydFlowPackOrder
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DgydFlowPackOrder dgydFlowPackOrder) {
        return super.exportXls(request, dgydFlowPackOrder, DgydFlowPackOrder.class, "东莞移动流量包订单");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DgydFlowPackOrder.class);
    }

}
