package com.eleven.cms.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eleven.cms.entity.ProvinceLimit;
import com.eleven.cms.model.TreeModel;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.ProvinceTree;
import com.eleven.cms.service.IProvinceTreeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_province_tree
 * @Author: jeecg-boot
 * @Date:   2021-08-13
 * @Version: V1.0
 */
@Api(tags="cms_province_tree")
@RestController
@RequestMapping("/cms/cmsProvinceTree")
@Slf4j
public class ProvinceTreeController extends JeecgController<ProvinceTree, IProvinceTreeService> {
	@Autowired
	private IProvinceTreeService provinceTreeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cmsProvinceTree
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_province_tree-分页列表查询")
	@ApiOperation(value="cms_province_tree-分页列表查询", notes="cms_province_tree-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ProvinceTree cmsProvinceTree,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ProvinceTree> queryWrapper = QueryGenerator.initQueryWrapper(cmsProvinceTree, req.getParameterMap());
		Page<ProvinceTree> page = new Page<ProvinceTree>(pageNo, pageSize);
		IPage<ProvinceTree> pageList = provinceTreeService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	 /**
	  * 查询全部
	  *
	  * @return
	  */
	 //@AutoLog(value = "cms_province_tree-查询全部")
	 @ApiOperation(value="cms_province_tree-查询全部", notes="cms_province_tree-查询全部")
	 @GetMapping(value = "/listNoPage")
	 public Result<?> queryPageList(ProvinceTree cmsProvinceTree) {
		 QueryWrapper<ProvinceTree> queryWrapper = new QueryWrapper<ProvinceTree>();
		 List<ProvinceTree> list = provinceTreeService.list(queryWrapper);
		 return Result.ok(list);
	 }
	
	/**
	 *   添加
	 *
	 * @param cmsProvinceTree
	 * @return
	 */
	//@AutoLog(value = "cms_province_tree-添加")
	@ApiOperation(value="cms_province_tree-添加", notes="cms_province_tree-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProvinceTree cmsProvinceTree) {
		provinceTreeService.save(cmsProvinceTree);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cmsProvinceTree
	 * @return
	 */
	//@AutoLog(value = "cms_province_tree-编辑")
	@ApiOperation(value="cms_province_tree-编辑", notes="cms_province_tree-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProvinceTree cmsProvinceTree) {
		provinceTreeService.updateById(cmsProvinceTree);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_province_tree-通过id删除")
	@ApiOperation(value="cms_province_tree-通过id删除", notes="cms_province_tree-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		provinceTreeService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_province_tree-批量删除")
	@ApiOperation(value="cms_province_tree-批量删除", notes="cms_province_tree-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.provinceTreeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_province_tree-通过id查询")
	@ApiOperation(value="cms_province_tree-通过id查询", notes="cms_province_tree-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProvinceTree cmsProvinceTree = provinceTreeService.getById(id);
		if(cmsProvinceTree==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(cmsProvinceTree);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmsProvinceTree
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProvinceTree cmsProvinceTree) {
        return super.exportXls(request, cmsProvinceTree, ProvinceTree.class, "cms_province_tree");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProvinceTree.class);
    }
	 /**
	  * 查询菜单省份树
	  * @param request
	  * @return
	  */
	 @RequestMapping(value = "/queryTreeList", method = RequestMethod.GET)
	 public Result<Map<String,Object>> queryTreeList(HttpServletRequest request) {
		 Result<Map<String,Object>> result = new Result<>();
		 //全部权限ids
		 List<String> ids = new ArrayList<>();
		 try {
			 LambdaQueryWrapper<ProvinceTree> query = new LambdaQueryWrapper<ProvinceTree>();
			 List<ProvinceTree> list = provinceTreeService.list(query);
			 for(ProvinceTree provinceTree : list) {
				 ids.add(provinceTree.getId());
			 }
			 List<TreeModel> treeList = new ArrayList<>();
			 getTreeModelList(treeList, list, null);
			 Map<String,Object> resMap = new HashMap<String,Object>();
			 resMap.put("treeList", treeList); //全部树节点数据
			 resMap.put("ids", ids);//全部树ids
			 result.setResult(resMap);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 log.error(e.getMessage(), e);
		 }
		 return result;
	 }
	 private void getTreeModelList(List<TreeModel> treeList,List<ProvinceTree> metaList,TreeModel temp) {
		 for (ProvinceTree provinceTree : metaList) {
			 String tempPid = provinceTree.getParentId();
			 TreeModel tree = new TreeModel(provinceTree.getId(), tempPid, provinceTree.getProvinceName(),1,true );
			 treeList.add(tree);
		 }
	 }
}
