package com.eleven.cms.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.entity.BusinessPack;
import com.eleven.cms.service.IBusinessPackService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 业务渠道权益关联
 * @Author: jeecg-boot
 * @Date:   2022-12-19
 * @Version: V1.0
 */
@Api(tags="业务渠道权益关联")
@RestController
@RequestMapping("/cms/businessPack")
@Slf4j
public class BusinessPackController extends JeecgController<BusinessPack, IBusinessPackService> {
	@Autowired
	private IBusinessPackService businessPackService;

	/**
	 * 分页列表查询
	 *
	 * @param businesslPack
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	////@AutoLog(value = "业务渠道权益关联-分页列表查询")
	@ApiOperation(value="业务渠道权益关联-分页列表查询", notes="业务渠道权益关联-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(BusinessPack businesslPack,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<BusinessPack> queryWrapper = QueryGenerator.initQueryWrapper(businesslPack, req.getParameterMap());
		Page<BusinessPack> page = new Page<BusinessPack>(pageNo, pageSize);
		IPage<BusinessPack> pageList = businessPackService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param businessPack
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益关联-添加")
	@ApiOperation(value="业务渠道权益关联-添加", notes="业务渠道权益关联-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody BusinessPack businessPack) {
		businessPackService.save(businessPack);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param businessPack
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益关联-编辑")
	@ApiOperation(value="业务渠道权益关联-编辑", notes="业务渠道权益关联-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody BusinessPack businessPack) {
		businessPackService.updateById(businessPack);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益关联-通过id删除")
	@ApiOperation(value="业务渠道权益关联-通过id删除", notes="业务渠道权益关联-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		businessPackService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益关联-批量删除")
	@ApiOperation(value="业务渠道权益关联-批量删除", notes="业务渠道权益关联-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.businessPackService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益关联-通过id查询")
	@ApiOperation(value="业务渠道权益关联-通过id查询", notes="业务渠道权益关联-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		BusinessPack businesslPack = businessPackService.getById(id);
		if(businesslPack==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(businesslPack);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param businesslPack
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BusinessPack businesslPack) {
        return super.exportXls(request, businesslPack, BusinessPack.class, "业务渠道权益关联");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BusinessPack.class);
    }

}
