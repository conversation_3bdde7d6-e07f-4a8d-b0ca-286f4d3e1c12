package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.dto.QyclWxpayNotifyParam;
import com.eleven.cms.dto.WxInfoNotifyParam;
import com.eleven.cms.entity.YinglouOrder;
import com.eleven.cms.entity.YinglouUser;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.TokenUtil;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.qycl.entity.QyclRingTemplate;
import com.eleven.qycl.service.IQyclRingTemplateService;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.Login;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriUtils;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import static com.eleven.cms.util.BizCommonConstant.*;

/**
 * 影楼相关接口
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/25 16:28
 **/
@Api(tags = "api")
@RestController
@RequestMapping("/api/yinglou")
@Slf4j
@Validated
public class ApiYinglouController {
    private static final String LOG_TAG = "影楼";
    @Autowired
    private IYinglouUserService yinglouUserService;
    @Autowired
    private IYinglouOrderService yinglouOrderService;
    @Autowired
    private IQyclWxpayService qyclWxpayService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private IQyclRingTemplateService qyclRingTemplateService;

    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private IYinglouRingService yinglouRingService;
    /**
     * 影楼登录接口
     * @param userName
     * @param passWord
     * @return
     */
    @PostMapping(value = "/login")
    @ResponseBody
    public  Result<?>  login(@RequestParam("userName") String userName,@RequestParam("passWord")String passWord,HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("{}-登录接口-登录账号:{},密码:{},referer:{}",LOG_TAG,userName,passWord,referer);
        if (Strings.isNullOrEmpty(userName) || Strings.isNullOrEmpty(passWord)) {
            return Result.error("账号错误或密码错误");
        }
        String sign = DigestUtils.md5DigestAsHex((userName+passWord).getBytes(StandardCharsets.UTF_8));
        YinglouUser user=yinglouUserService.lambdaQuery().select(YinglouUser::getNumberId).eq(YinglouUser::getUserName,userName).eq(YinglouUser::getPassWord,sign).orderByDesc(YinglouUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(user==null){
            return Result.error("账号错误或密码错误");
        }
        String token = TokenUtil.setLoginTime(userName);
        Result result=new Result();
        result.setResult(token);
        result.setMessage("登录成功");
        result.setNumberId(user.getNumberId());
        result.setCode(CommonConstant.SC_OK_200);
        return result;
    }
    /**
     * 影楼查询是否有未使用订单
     * @return
     */
    @PostMapping(value = "/isSub")
    @ResponseBody
    @Login
    public Result<?> isSub(HttpServletRequest request) {
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-查询是否有未使用订单-登录账号:{},referer:{}",LOG_TAG,userName,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        //查询已支付并且未使用的
        return yinglouOrderService.isSub(userName);
    }

    /**
     * 查询订单是否支付
     * @return
     */
    @PostMapping(value = "/isPay")
    @ResponseBody
    @Login
    public Result<?> isPay(@RequestParam("orderId") String orderId,HttpServletRequest request) {
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-查询订单是否支付-登录账号:{},订单号:{},referer:{}",LOG_TAG,userName,orderId,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        //查询订单是否支付
        return yinglouOrderService.isPay(orderId,userName);
    }


    /**
     * 影楼在线支付
     * @param tradeType
     * @param channel
     * @param subject
     * @param openId
     * @param returnUrl
     * @return
     */
    @PostMapping(value = "/jsapi/pay")
    @ResponseBody
    @Login
    public Result<?> pay(@RequestParam(value = "tradeType", required = false, defaultValue = BizConstant.TRADE_TYPE_WECHAT) String tradeType,
                         @RequestParam(name = "channel",   required = false, defaultValue ="002115U") String channel,
                         @RequestParam(value = "subject", required = false, defaultValue = "")String subject,
                         @RequestParam(value = "openId", required = false, defaultValue = "") String openId,
                         @RequestParam(value = "returnUrl", required = false, defaultValue = "") String returnUrl,
                         HttpServletRequest request){
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-在线支付-登录账号:{},支付类型:{},渠道号:{},产品名称:{},用户标识:{},h5支付成功跳转地址:{},referer:{}",LOG_TAG,userName,tradeType,channel,subject,openId,returnUrl,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        if(StringUtils.isEmpty(channel)){
            return Result.error("渠道号错误");
        }
        log.info("影楼在线支付=>支付方式:{},渠道号:{},产品名称:{},openId:{}",tradeType, channel,subject,openId);
        try {
            Map<String, String> resp  = yinglouOrderService.pay(tradeType,channel,subject,openId,userName);
            if(StringUtils.equalsAny(tradeType,BizConstant.TRADE_TYPE_HTML)){
                if(StringUtils.isBlank(returnUrl)){
                    return Result.error("重定向地址错误！");
                }
                String  mwebUrl = resp.get("mweb_url");
                String  orderId = resp.get("orderId");
                String returnUrlEncode = UriUtils.encode(returnUrl + "?orderId=" + orderId, StandardCharsets.UTF_8);
                String mwebUrlWithRedirect = mwebUrl + "&redirect_url=" + returnUrlEncode;
                log.info("微信支付支付跳转地址->mwebUrlWithRedirect:{}",mwebUrlWithRedirect);
                return Result.ok(mwebUrlWithRedirect);
            }
            return Result.ok(resp);
        } catch (Exception e) {
            log.error("影楼业务微信支付支付异常", e);
        }
        return Result.error("系统异常！");
    }


    //微信用户支付通知
    @RequestMapping(value="/jsapi/pay/notify",produces="text/plain")
    @ResponseBody
    public String jsapiWechatPayNotify(HttpServletRequest request) {
        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()){
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            QyclWxpayNotifyParam notifyParam = qyclWxpayService.payResultNotify(notifyXml);
            log.info("影楼微信支付通知notifyParam = " + notifyParam);
            yinglouOrderService.modifyPayStatus(notifyParam);
            return succRespXml;
        } catch (Exception e) {
            log.error("微信回调通知处理异常:", e);
            return failRespXml;
        }
    }


    /**
     * 分页列表查询铃音列表
     * @param columnName
     * @param pageNo
     * @param pageSize
     * @return
     */
    @PostMapping("/columnRingList")
    @ResponseBody
    public Result<?> queryColumnRingList(@RequestParam("columnName") String columnName,
                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                         HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("{}-分页列表查询铃音列表-栏目名称:{},页码:{},分页条数:{},referer:{}",LOG_TAG,columnName,pageNo,pageSize,referer);
        LambdaQueryWrapper<QyclRingTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(QyclRingTemplate::getOrderBy);
        queryWrapper.eq(QyclRingTemplate::getColumnName,columnName);
        Page<QyclRingTemplate> page = new Page<QyclRingTemplate>(pageNo, pageSize);
        IPage<QyclRingTemplate> pageList = qyclRingTemplateService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 影楼查询商户信息
     * @param mobile
     * @param channel
     * @return
     */
    @PostMapping(value = "/circle")
    @ResponseBody
    @Login
    public RemoteResult queryCircle(@RequestParam("mobile") String mobile,@RequestParam(name = "channel",   required = false, defaultValue ="002115U") String channel,
                                    HttpServletRequest request){

        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-查询商户信息-登录账号:{},商户手机号:{},渠道号:{},referer:{}",LOG_TAG,userName,mobile,channel,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return RemoteResult.fail("账号错误或密码错误");
        }
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return RemoteResult.fail("请输入正确格式手机号");
        }
        if(StringUtils.isEmpty(channel)){
            return RemoteResult.fail("渠道号错误");
        }
        return miguApiService.sxhGetCircleId(mobile,channel);
    }
    /**
     * 影楼查询商户成员
     * @param mobile
     * @param channel
     * @return
     */
    @PostMapping(value = "/memberList")
    @ResponseBody
    @Login
    public Result<?> queryMemberList(@RequestParam("mobile") String mobile,@RequestParam(name = "channel",   required = false, defaultValue ="002115U") String channel,
                                     HttpServletRequest request){

        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-查询商户成员-登录账号:{},商户手机号:{},渠道号:{},referer:{}",LOG_TAG,userName,mobile,channel,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if(StringUtils.isEmpty(channel)){
            return Result.error("渠道号错误");
        }
        return miguApiService.sxhQueryMemberList(mobile,channel);
    }






    /**
     * 发送开通短信
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/openSms")
    @ResponseBody
    @Login
    public Result<?> openSms(@RequestParam("mobile") String mobile,
                             @RequestParam(name = "channel",   required = false, defaultValue ="002115U") String channel,
                             @RequestParam(value = "circleId", required = false, defaultValue = "")String circleId,
                             @RequestParam(name = "serviceId",   required = false, defaultValue =BizConstant.SMS_MODEL_COMMON_SERVICE_ID) String serviceId,
                             HttpServletRequest request) {
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-发送开通短信-登录账号:{},开通手机号:{},渠道号:{},业务ID:{},referer:{}",LOG_TAG,userName,mobile,channel,serviceId,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if(StringUtils.isEmpty(channel)){
            return Result.error("渠道号错误");
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + channel + ":" +mobile;
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        boolean result = false;
        try {
            String aesMobile=yinglouRingService.aesEncryptMobile(mobile,channel);
            if(StringUtils.isBlank(aesMobile)){
                return Result.error("系统错误");
            }
            Map<String,String> smsMap= Maps.newHashMap();
            smsMap.put("value1",aesMobile);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            if(StringUtils.isNotBlank(circleId)){
                smsMap.put("value2",circleId);
                result =smsModelService.sendSms(mobile,channel,serviceId,BizConstant.BUSINESS_TYPE_MAIN,smsMap);
            }else{
                Result<?> resultOrder=yinglouOrderService.isSms(userName,mobile,channel);
                if(!resultOrder.isOK()){
                    return resultOrder;
                }
                result =smsModelService.sendSms(mobile,channel,serviceId,BizConstant.BUSINESS_TYPE_CODE,smsMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!result) {
            return Result.noauth("短信发送失败,请稍后再试");
        }
        return Result.ok("短信发送成功");
    }

    /**
     * 影楼查询手机号是否绑定订单
     * @param mobile
     * @return
     */
    @PostMapping(value = "/queryOrder")
    @ResponseBody
    public Result<?> bindOrderByMobile(@RequestParam("mobile") String mobile,
                                       @RequestParam(name = "channel",   required = false, defaultValue ="002115U") String channel,
                                       @RequestParam(value = "circleId", required = false, defaultValue = "")String circleId,
                                       HttpServletRequest request){
        String referer = request.getHeader("Referer");
        log.info("{}-查询手机号是否绑定订单-开通手机号:{},渠道号:{},referer:{}",LOG_TAG,mobile,channel,referer);
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if(StringUtils.isEmpty(channel)){
            return Result.error("渠道号错误");
        }
        return yinglouOrderService.bindOrderByMobile(mobile,channel,circleId);
    }

    /**
     * 影楼开通回执
     * @param mobile
     * @return
     */
    @PostMapping(value = "/openNotify")
    @ResponseBody
    public Result<?> openNotify(@RequestParam("mobile") String mobile,
                                       @RequestParam(value = "status", required = false, defaultValue = "")String status,
                                       HttpServletRequest request){
        String referer = request.getHeader("Referer");
        log.info("{}-开通回执-开通手机号:{},开通状态:{},referer:{}",LOG_TAG,mobile,status,referer);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if(StringUtils.isEmpty(status)){
            return Result.error("状态错误");
        }
        return yinglouOrderService.updateStatus(mobile,Integer.valueOf(status));
    }
    /**
     * 影楼铃音上传
     * @param mobile
     * @param circleId
     * @param channel
     * @param ringUrl
     * @return
     * @throws IOException
     */
    @PostMapping("/ringUpload")
    @ResponseBody
    @Login
    public Result<?> ringUpload(@RequestParam("mobile") String mobile,
                                   @RequestParam("circleId") String circleId,
                                   @RequestParam(name = "channel",   required = false, defaultValue ="002115U") String channel,
                                   @RequestParam("ringUrl") String ringUrl,
                                   HttpServletRequest request){
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-铃音上传-登录账号:{},开通手机号:{},渠道号:{},商户ID:{},铃音地址:{},referer:{}",LOG_TAG,userName,mobile,channel,circleId,ringUrl,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (StringUtils.isEmpty(ringUrl) || StringUtils.isEmpty(channel)  || StringUtils.isEmpty(circleId)) {
            return Result.error("参数错误");
        }
        //查询商户是否存在
        Result<Object> resultMember=miguApiService.sxhGetMemberList(circleId,mobile,channel);
        if(!resultMember.isOK()){
            return resultMember;
        }
        yinglouRingService.ringUpload(mobile,circleId,channel,ringUrl);
        return Result.ok("铃音正在上传");
    }

    /**
     * 影楼生成模板铃音
     * @param mobile
     * @param circleId
     * @param channel
     * @param templateId
     * @param clipsParam
     * @return
     */
    @PostMapping("/createTemplateRing")
    @ResponseBody
    @Login
    public Result<?> createTemplateRing(@RequestParam("mobile") String mobile,
                                        @RequestParam("circleId") String circleId,
                                        @RequestParam(name = "channel",   required = false, defaultValue ="002115U") String channel,
                                        @RequestParam("templateId") String templateId,
                                        @RequestParam("clipsParam") String clipsParam,
                                        HttpServletRequest request) {
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-生成模板铃音-登录账号:{},开通手机号:{},渠道号:{},商户ID:{},模板ID:{},图片地址:{},referer:{}",LOG_TAG,userName,mobile,channel,circleId,templateId,clipsParam,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        if (StringUtils.isEmpty(templateId) || StringUtils.isEmpty(channel)  || StringUtils.isEmpty(circleId) || StringUtils.isEmpty(clipsParam)) {
            return Result.error("参数错误");
        }
        String aliVideoJobId=yinglouRingService.createTemplateRing(mobile, circleId, templateId, clipsParam,channel);
        if(StringUtils.isNotBlank(aliVideoJobId)){
            return Result.ok("铃音制作成功",aliVideoJobId);
        }
        return Result.error("铃音制作失败");
    }



    /**
     * 影楼查询支付订单
     * @return
     */
    @PostMapping(value = "/orderList")
    @ResponseBody
    @Login
    public Result<?> queryPayOrderList(@RequestParam("payTime") String payTime,HttpServletRequest request) {
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-查询支付订单-登录账号:{},referer:{}",LOG_TAG,userName,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        return yinglouOrderService.queryPayOrderList(userName,payTime);
    }


    /**
     * 查询铃音是否制作成功
     * @return
     */
    @PostMapping(value = "/isAliVideoRing")
    @ResponseBody
    @Login
    public Result<?> isAliVideoRing(@RequestParam("aliVideoJobId") String aliVideoJobId,HttpServletRequest request) {
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-查询铃音是否制作成功-登录账号:{},任务ID:{},referer:{}",LOG_TAG,userName,aliVideoJobId,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        //查询铃音是否制作成功
        return yinglouRingService.isAliVideoRing(aliVideoJobId);
    }
    /**
     * 查询铃音是否审核通过
     * @return
     */
    @PostMapping(value = "/isCircleRing")
    @ResponseBody
    @Login
    public Result<?> isCircleRing(@RequestParam("circleId") String circleId,HttpServletRequest request) {
        String userName =String.valueOf(TokenUtil.getMobile());
        String referer = request.getHeader("Referer");
        log.info("{}-查询铃音是否制作成功-登录账号:{},商户ID:{},referer:{}",LOG_TAG,userName,circleId,referer);
        boolean user=yinglouUserService.lambdaQuery().eq(YinglouUser::getUserName,userName).count()<=0;
        if(user){
            return Result.error("账号错误或密码错误");
        }
        //查询铃音是否审核通过
        return yinglouRingService.isCircleRing(circleId);
    }

    //微信用户退款通知
    @RequestMapping(value = "/jsapi/wechat/refund/notify", produces = "text/plain")
    @ResponseBody
    public String wxrefundResultNotify(HttpServletRequest request) {
        String succRespXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String failRespXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
        try (ServletInputStream inputStream = request.getInputStream()) {
            String notifyXml = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            WxInfoNotifyParam infoParam = qyclWxpayService.refundResultNotify(notifyXml);
            log.info("影楼微信用户退款通知infoParam= " + infoParam);
            if (infoParam != null) {
                final String refundStatus = infoParam.getRefundStatus();
                YinglouOrder yinglouOrder = yinglouOrderService.queryNotRefundOrder(infoParam.getOutRefundNo());
                if (yinglouOrder == null) {
                    log.error("影楼微信用户退款通知重复通知-notifyParam:{}", infoParam);
                    return succRespXml;
                }
                yinglouOrderService.modifyRefundStatus(yinglouOrder.getOrderId(), infoParam.getOutRefundNo(), refundStatus);
            }
            return succRespXml;
        } catch (Exception e) {
            e.printStackTrace();
            return failRespXml;
        }
    }
}
