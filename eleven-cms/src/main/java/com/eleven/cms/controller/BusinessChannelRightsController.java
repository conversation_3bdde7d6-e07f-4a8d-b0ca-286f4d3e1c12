package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.BusinessChannelRights;
import com.eleven.cms.service.IBusinessChannelRightsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 业务渠道权益
 * @Author: jeecg-boot
 * @Date:   2022-12-19
 * @Version: V1.0
 */
@Api(tags="业务渠道权益")
@RestController
@RequestMapping("/cms/businessChannelRights")
@Slf4j
public class BusinessChannelRightsController extends JeecgController<BusinessChannelRights, IBusinessChannelRightsService> {
	@Autowired
	private IBusinessChannelRightsService businessChannelRightsService;

	/**
	 * 分页列表查询
	 *
	 * @param businessChannelRights
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益-分页列表查询")
	@ApiOperation(value="业务渠道权益-分页列表查询", notes="业务渠道权益-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(BusinessChannelRights businessChannelRights,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<BusinessChannelRights> queryWrapper = QueryGenerator.initQueryWrapper(businessChannelRights, req.getParameterMap());
		Page<BusinessChannelRights> page = new Page<BusinessChannelRights>(pageNo, pageSize);
		IPage<BusinessChannelRights> pageList = businessChannelRightsService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param businessChannelRights
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益-添加")
	@ApiOperation(value="业务渠道权益-添加", notes="业务渠道权益-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody BusinessChannelRights businessChannelRights) {
		businessChannelRightsService.save(businessChannelRights);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param businessChannelRights
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益-编辑")
	@ApiOperation(value="业务渠道权益-编辑", notes="业务渠道权益-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody BusinessChannelRights businessChannelRights) {
		businessChannelRightsService.updateById(businessChannelRights);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益-通过id删除")
	@ApiOperation(value="业务渠道权益-通过id删除", notes="业务渠道权益-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		businessChannelRightsService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益-批量删除")
	@ApiOperation(value="业务渠道权益-批量删除", notes="业务渠道权益-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.businessChannelRightsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "业务渠道权益-通过id查询")
	@ApiOperation(value="业务渠道权益-通过id查询", notes="业务渠道权益-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		BusinessChannelRights businessChannelRights = businessChannelRightsService.getById(id);
		if(businessChannelRights==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(businessChannelRights);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param businessChannelRights
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BusinessChannelRights businessChannelRights) {
        return super.exportXls(request, businessChannelRights, BusinessChannelRights.class, "业务渠道权益");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BusinessChannelRights.class);
    }

}
