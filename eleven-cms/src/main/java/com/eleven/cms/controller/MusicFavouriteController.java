package com.eleven.cms.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.entity.Music;
import com.eleven.cms.vo.ColumnDetail;
import com.eleven.cms.vo.MusicFavouriteVo;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.entity.MusicFavourite;
import com.eleven.cms.service.IMusicFavouriteService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: cms_music_favourite
 * @Author: jeecg-boot
 * @Date:   2022-03-01
 * @Version: V1.0
 */
@Api(tags="cms_music_favourite")
@RestController
@RequestMapping("/cms/musicFavourite")
@Slf4j
public class MusicFavouriteController extends JeecgController<MusicFavourite, IMusicFavouriteService> {
	@Autowired
	private IMusicFavouriteService musicFavouriteService;
	
	/**
	 * 分页列表查询
	 *
	 * @param musicFavourite
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_music_favourite-分页列表查询")
	@ApiOperation(value="cms_music_favourite-分页列表查询", notes="cms_music_favourite-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(MusicFavourite musicFavourite,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<MusicFavourite> queryWrapper = QueryGenerator.initQueryWrapper(musicFavourite, req.getParameterMap());
		Page<MusicFavourite> page = new Page<MusicFavourite>(pageNo, pageSize);
		IPage<MusicFavourite> pageList = musicFavouriteService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
//	/**
////	 *   添加
////	 *
////	 * @param musicFavourite
////	 * @return
////	 */
////	//@AutoLog(value = "cms_music_favourite-添加")
////	@ApiOperation(value="cms_music_favourite-添加", notes="cms_music_favourite-添加")
////	@PostMapping(value = "/add")
////	public Result<?> add(@RequestBody MusicFavourite musicFavourite) {
////		musicFavouriteService.save(musicFavourite);
////		return Result.ok("添加成功！");
////	}
	
	/**
	 *  编辑
	 *
	 * @param musicFavourite
	 * @return
	 */
	//@AutoLog(value = "cms_music_favourite-编辑")
	@ApiOperation(value="cms_music_favourite-编辑", notes="cms_music_favourite-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody MusicFavourite musicFavourite) {
		musicFavouriteService.updateById(musicFavourite);
		return Result.ok("编辑成功!");
	}
	
//	/**
//	 *   通过id删除
//	 *
//	 * @param id
//	 * @return
//	 */
//	//@AutoLog(value = "cms_music_favourite-通过id删除")
//	@ApiOperation(value="cms_music_favourite-通过id删除", notes="cms_music_favourite-通过id删除")
//	@DeleteMapping(value = "/delete")
//	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
//		musicFavouriteService.removeById(id);
//		return Result.ok("删除成功!");
//	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_music_favourite-批量删除")
	@ApiOperation(value="cms_music_favourite-批量删除", notes="cms_music_favourite-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.musicFavouriteService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_music_favourite-通过id查询")
	@ApiOperation(value="cms_music_favourite-通过id查询", notes="cms_music_favourite-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		MusicFavourite musicFavourite = musicFavouriteService.getById(id);
		if(musicFavourite==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(musicFavourite);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param musicFavourite
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MusicFavourite musicFavourite) {
        return super.exportXls(request, musicFavourite, MusicFavourite.class, "cms_music_favourite");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MusicFavourite.class);
    }

//	 /**
//	  * 匹配歌曲
//	  *
//	  * @param mobile 手机号
//	  * @return
//	  */
//	 //@AutoLog(value = "cms_music_favourite-匹配歌曲")
//	 @ApiOperation(value="cms_music_favourite-匹配歌曲", notes="cms_music_favourite-匹配歌曲")
//	 @GetMapping(value = "/matching")
//	 public Result<?> matching(@RequestParam(name="mobile",required=true) String mobile) {
//		 List<ColumnDetail> list = musicFavouriteService.matching(mobile);
//		 return Result.ok(list);
//	 }
//	 /**
//	  * 查询收藏记录
//	  *
//	  * @param mobile 手机号
//	  * @return
//	  */
//	 //@AutoLog(value = "cms_music_favourite-查询收藏记录")
//	 @ApiOperation(value="cms_music_favourite-查询收藏记录", notes="cms_music_favourite-查询收藏记录")
//	 @GetMapping(value = "/findByMobile")
//	 public Result<?> findByMobile(@RequestParam(name="mobile",required=true) String mobile) {
//		 List<MusicFavouriteVo> list = musicFavouriteService.findByMobile(mobile);
//		 return Result.ok(list);
//	 }

}
