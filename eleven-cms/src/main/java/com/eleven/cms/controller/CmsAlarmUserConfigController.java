package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CmsAlarmUserConfig;
import com.eleven.cms.entity.CmsAlarmUserConfigDetail;
import com.eleven.cms.service.ICmsAlarmUserConfigDetailService;
import com.eleven.cms.service.ICmsAlarmUserConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: cms_alarm_user_config
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
@Api(tags="cms_alarm_user_config")
@RestController
@RequestMapping("/cms/cmsAlarmUserConfig")
@Slf4j
public class CmsAlarmUserConfigController extends JeecgController<CmsAlarmUserConfig, ICmsAlarmUserConfigService> {
	@Autowired
	private ICmsAlarmUserConfigService cmsAlarmUserConfigService;
	@Autowired
	private ICmsAlarmUserConfigDetailService cmsAlarmUserConfigDetailService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cmsAlarmUserConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config-分页列表查询")
	@ApiOperation(value="cms_alarm_user_config-分页列表查询", notes="cms_alarm_user_config-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CmsAlarmUserConfig cmsAlarmUserConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CmsAlarmUserConfig> queryWrapper = QueryGenerator.initQueryWrapper(cmsAlarmUserConfig, req.getParameterMap());
		Page<CmsAlarmUserConfig> page = new Page<CmsAlarmUserConfig>(pageNo, pageSize);
		IPage<CmsAlarmUserConfig> pageList = cmsAlarmUserConfigService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cmsAlarmUserConfig
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config-添加")
	@ApiOperation(value="cms_alarm_user_config-添加", notes="cms_alarm_user_config-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody @Validated CmsAlarmUserConfig cmsAlarmUserConfig) {
		cmsAlarmUserConfigService.save(cmsAlarmUserConfig);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cmsAlarmUserConfig
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config-编辑")
	@ApiOperation(value="cms_alarm_user_config-编辑", notes="cms_alarm_user_config-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody @Validated CmsAlarmUserConfig cmsAlarmUserConfig) {
		cmsAlarmUserConfigService.updateById(cmsAlarmUserConfig);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config-通过id删除")
	@ApiOperation(value="cms_alarm_user_config-通过id删除", notes="cms_alarm_user_config-通过id删除")
	@DeleteMapping(value = "/delete")
	@Transactional(rollbackFor = Exception.class)
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		cmsAlarmUserConfigService.removeById(id);
		cmsAlarmUserConfigDetailService.remove(new LambdaQueryWrapper<CmsAlarmUserConfigDetail>()
				.eq(CmsAlarmUserConfigDetail::getAlarmUserConfigId, id));
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config-批量删除")
	@ApiOperation(value="cms_alarm_user_config-批量删除", notes="cms_alarm_user_config-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cmsAlarmUserConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_alarm_user_config-通过id查询")
	@ApiOperation(value="cms_alarm_user_config-通过id查询", notes="cms_alarm_user_config-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CmsAlarmUserConfig cmsAlarmUserConfig = cmsAlarmUserConfigService.getById(id);
		if(cmsAlarmUserConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(cmsAlarmUserConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmsAlarmUserConfig
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsAlarmUserConfig cmsAlarmUserConfig) {
        return super.exportXls(request, cmsAlarmUserConfig, CmsAlarmUserConfig.class, "cms_alarm_user_config");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CmsAlarmUserConfig.class);
    }

}
