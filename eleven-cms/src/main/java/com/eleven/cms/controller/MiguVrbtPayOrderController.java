package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.MiguVrbtPayOrder;
import com.eleven.cms.service.IMiguVrbtPayOrderService;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
* @Description: cms_migu_vrbt_pay_order
* @Author: jeecg-boot
* @Date:   2022-12-30
* @Version: V1.0
*/
@Api(tags="cms_migu_vrbt_pay_order")
@RestController
@RequestMapping("/cms/miguVrbtPayOrder")
@Slf4j
public class MiguVrbtPayOrderController extends JeecgController<MiguVrbtPayOrder, IMiguVrbtPayOrderService> {
    @Autowired
    private IMiguVrbtPayOrderService miguVrbtPayOrderService;
    private static final Interner<String> interner = Interners.newWeakInterner();


    /**
     * 分页列表查询
     *
     * @param miguVrbtPayOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
   //@AutoLog(value = "cms_ali_sign_charging_order-分页列表查询")
   @ApiOperation(value="cms_ali_sign_charging_order-分页列表查询", notes="cms_ali_sign_charging_order-分页列表查询")
   @GetMapping(value = "/list")
   public Result<?> queryPageList(MiguVrbtPayOrder miguVrbtPayOrder,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  HttpServletRequest req) {
       QueryWrapper<MiguVrbtPayOrder> queryWrapper = QueryGenerator.initQueryWrapper(miguVrbtPayOrder, req.getParameterMap());
       Page<MiguVrbtPayOrder> page = new Page<MiguVrbtPayOrder>(pageNo, pageSize);
       IPage<MiguVrbtPayOrder> pageList = miguVrbtPayOrderService.page(page, queryWrapper);
       return Result.ok(pageList);
   }




    /**
     * 支付宝退款
     * @param outTradeNo 订单号
     * @param refund
     * @return
     */
    @ApiOperation(value = "支付宝退款", notes = "支付宝退款")
    @PostMapping(value = "/aliPay/refund")
    @ResponseBody
    @RequiresPermissions("aliSignChargingOrder:refund")
    public Result aliPayRefund(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo,@RequestParam(value = "refund", required = false, defaultValue ="")String refund){
        if(StringUtils.isEmpty(outTradeNo)){
            return Result.error("订单号不能为空");
        }
        if(StringUtils.isEmpty(refund)){
            return Result.error("退款金额不能为空");
        }
        synchronized (interner.intern(outTradeNo)) {
            return miguVrbtPayOrderService.aliPayRefund(outTradeNo);
        }
    }
    /**
     * 支付宝查询退款
     * @param outTradeNo 订单号
     * @return
     */
    @ApiOperation(value = "支付宝查询退款", notes = "支付宝查询退款")
    @PostMapping(value = "/aliPay/query/refund")
    @ResponseBody
    public Result<?> aliPayQueryRefund(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo){
        if(StringUtils.isEmpty(outTradeNo)){
            return Result.error("订单号不能为空");
        }
        return miguVrbtPayOrderService.aliPayQueryRefund(outTradeNo);
    }

}
