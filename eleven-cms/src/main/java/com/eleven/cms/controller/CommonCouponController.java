package com.eleven.cms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.CommonCoupon;
import com.eleven.cms.service.ICommonCouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: cms_common_coupon
 * @Author: jeecg-boot
 * @Date:   2024-07-04
 * @Version: V1.0
 */
@Api(tags="cms_common_coupon")
@RestController
@RequestMapping("/cms/commonCoupon")
@Slf4j
public class CommonCouponController extends JeecgController<CommonCoupon, ICommonCouponService> {
	@Autowired
	private ICommonCouponService commonCouponService;

	/**
	 * 分页列表查询
	 *
	 * @param commonCoupon
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "cms_common_coupon-分页列表查询")
	@ApiOperation(value="cms_common_coupon-分页列表查询", notes="cms_common_coupon-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CommonCoupon commonCoupon,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CommonCoupon> queryWrapper = QueryGenerator.initQueryWrapper(commonCoupon, req.getParameterMap());
		Page<CommonCoupon> page = new Page<CommonCoupon>(pageNo, pageSize);
		IPage<CommonCoupon> pageList = commonCouponService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param commonCoupon
	 * @return
	 */
	//@AutoLog(value = "cms_common_coupon-添加")
	@ApiOperation(value="cms_common_coupon-添加", notes="cms_common_coupon-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CommonCoupon commonCoupon) {
		commonCouponService.save(commonCoupon);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param commonCoupon
	 * @return
	 */
	//@AutoLog(value = "cms_common_coupon-编辑")
	@ApiOperation(value="cms_common_coupon-编辑", notes="cms_common_coupon-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody CommonCoupon commonCoupon) {
		commonCouponService.updateById(commonCoupon);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_common_coupon-通过id删除")
	@ApiOperation(value="cms_common_coupon-通过id删除", notes="cms_common_coupon-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		commonCouponService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "cms_common_coupon-批量删除")
	@ApiOperation(value="cms_common_coupon-批量删除", notes="cms_common_coupon-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.commonCouponService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "cms_common_coupon-通过id查询")
	@ApiOperation(value="cms_common_coupon-通过id查询", notes="cms_common_coupon-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CommonCoupon commonCoupon = commonCouponService.getById(id);
		if(commonCoupon==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(commonCoupon);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param commonCoupon
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CommonCoupon commonCoupon) {
        return super.exportXls(request, commonCoupon, CommonCoupon.class, "通用券码");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CommonCoupon.class);
    }

}
