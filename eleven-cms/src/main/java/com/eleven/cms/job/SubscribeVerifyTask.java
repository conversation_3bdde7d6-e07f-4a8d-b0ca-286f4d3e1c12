package com.eleven.cms.job;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @author: eleven
 * @create: 
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class SubscribeVerifyTask {
    @Autowired
    SubscribeVerifyWorker subscribeVerifyWorker;

    /**
     * TODO 月初需要按渠道号放开
     * @throws Exception
     */
    @Scheduled(fixedRate = Long.MAX_VALUE,initialDelay=10*1000)
    public void startVerify() throws Exception {
        last6MonthToVerify();
    }

    /**
     * TODO 月初需要按渠道号放开
     * 包月核实-重置前6个月数据已重置过无需再重置
     *  每月2号凌晨1点重置
     * @throws Exception
     */
    @Scheduled(cron = "0 0 1 1 * ?")
    public void resetLast6MonthToVerify() throws Exception {
        last6MonthToVerify();
    }

    public void last6MonthToVerify() throws Exception {
        log.info("启动执行跑半年存量包月核实任务");
        if(subscribeVerifyWorker.isCurrentMonthVerfyStatusFinish()){
            log.info("当月已完成执行跑半年存量包月核实任务");
            return;
        }
        subscribeVerifyWorker.stop();
        //重置数据状态
        subscribeVerifyWorker.resetLast6MonthToVerify();
        subscribeVerifyWorker.start();
    }
}

