package com.eleven.cms.job;

import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.remote.SichuanMobileFlowPacketService;
import com.eleven.cms.service.ISmsValidateService;
import com.eleven.cms.service.impl.SmsNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: cai lei
 * @create: 2021-11-29 10:40
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class SichuanMobileNoticeTask {
    @Autowired
    SichuanMobileFlowPacketService sichuanMobileFlowPacketService;
    @Autowired
    SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    SmsNotifyService smsNotifyService;
    //每日9点拉取四川移动通知并发送短信
//    @Scheduled(cron = "0 0 9 * * ?")
//    public void queryNoticeForSchedule() throws Exception {
//        log.info("开始执行四川移动下发通知定时任务");
//        List<String> notices = sichuanMobileFlowPacketService.queryNotice();
//        List<String> mobiles = sichuanMobileFlowPacketProperties.getMobiles();
//        notices.forEach(notice -> {
//            mobiles.forEach(mobile -> {
//                smsNotifyService.sendNotify(mobile, notice + "——四川移动");
//            });
//        });
//        log.info("结束执行四川移动下发通知定时任务");
//    }
}
