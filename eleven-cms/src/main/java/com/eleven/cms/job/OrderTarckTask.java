package com.eleven.cms.job;

import com.eleven.cms.service.IOrderTarckService;
import com.eleven.qycl.service.IDataCollectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 添加渠道订购数据定时任务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/19 11:57
 **/
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class OrderTarckTask {
    @Autowired
    private IOrderTarckService orderTarckService;

    /**
     * 每日凌晨2点查014X04C渠道号2024年1月2日山东订购包月校验
     * @throws Exception
     */
    //@Scheduled(cron = "0 0 2 * * ?")
    //public void addTarck() throws Exception {
    //    log.info("开始执行添加渠道订购数据定时任务");
    //    orderTarckService.addTarck();
    //    log.info("结束执行添加渠道订购数据定时任务");
    //}
}
