package com.eleven.cms.job;

import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.service.IAliSignRecordService;
import com.eleven.cms.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 支付宝签约协议周期扣款(已修改为发送扣款消息到消息队列进行扣款,因为支付宝扣款接口时间开销大)
 *  jeecgboot集成的quartz定时任务有bug,跑着跑着就不执行了,所以使用spring自带的比较好
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class AliSignDeductTask {

    @Autowired
    private IAliSignRecordService aliSignRecordService;


//    @Scheduled(fixedDelay = 10*1000,initialDelay=5*1000)
    public void init() {
        LocalTime taskTime=LocalTime.of(10,30,0);
            if(LocalTime.now().isAfter(taskTime)){
                log.info("定时任务-支付宝签约协议周期扣款消息入队开始");
                boolean isFinish=this.monthlyDeductMoney();
                if(isFinish){
                    try {
                        TimeUnit.MINUTES.sleep(10L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }else {
                log.info("定时任务-支付宝签约协议周期扣款消息入队时间未到");
                try {
                    TimeUnit.MINUTES.sleep(1L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
    }
    private Boolean monthlyDeductMoney() {
        final String today = DateUtil.formatYearMonthDay(LocalDateTime.now());
        List<AliSignRecord> aliSignRecordList = aliSignRecordService.lambdaQuery()
                .select(AliSignRecord::getId,AliSignRecord::getMobile)
                .le(AliSignRecord::getNextDeductTime, new Date())
                .ne(AliSignRecord::getDeductDay, today)
                .eq(AliSignRecord::getSignStatus,1)
                .last("LIMIT 1000")
                .list();
        boolean isFinish = aliSignRecordList==null || aliSignRecordList.size()==0;
        if(isFinish){
            log.info("定时任务-支付宝签约协议周期扣款消息入队结束");
            return isFinish;
        }
        log.info("定时任务-支付宝签约协议周期扣款消息入队");
        aliSignRecordList.forEach(aliSignRecord -> {
            try {
                aliSignRecordService.updateDeductDayAndSendDeductMessage(aliSignRecord,today);
                //TimeUnit.MILLISECONDS.sleep(200L);
            } catch (Exception e) {
                log.error("id:{},手机号:{},支付宝签约协议周期扣款消息入队异常!",aliSignRecord.getId(),aliSignRecord.getMobile(),e);
            }
        });
        return false;
    }

}
