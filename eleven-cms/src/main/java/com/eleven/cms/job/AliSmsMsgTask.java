package com.eleven.cms.job;

import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.service.IAliSignRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 支付宝签约协议周期扣款
 *  jeecgboot集成的quartz定时任务有bug,跑着跑着就不执行了,所以使用spring自带的比较好
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class AliSmsMsgTask {

    @Autowired
    private IAliSignRecordService aliSignRecordService;

//    @Scheduled(fixedDelay = 10*1000,initialDelay=5*1000)
    public void initSendDeductionMsg() {
        LocalTime taskTime=LocalTime.of(10,0,0);
            if(LocalTime.now().isAfter(taskTime)){
                log.info("定时任务-支付宝周期扣款前三天发送扣款提醒短信开始");
                boolean isFinish=this.monthDeductionMsg();
                if(isFinish){
                    try {
                        TimeUnit.MINUTES.sleep(60L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }else {
                log.info("定时任务-支付宝周期扣款前三天发送扣款提醒短信时间未到");
                try {
                    TimeUnit.MINUTES.sleep(1L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
    }
    private Boolean monthDeductionMsg() {
        String sendDeductMsgDay=formatYearMonthDay(LocalDateTime.now());
        List<AliSignRecord> aliSignRecordList = aliSignRecordService.lambdaQuery()
                .select(AliSignRecord::getId, AliSignRecord::getMobile)
                .eq(AliSignRecord::getNextDeductTime, LocalDateTime.of(LocalDate.now().plusDays(2), LocalTime.MIN))
                .ne(AliSignRecord::getSendDeductMsgDay,sendDeductMsgDay)
                .eq(AliSignRecord::getSignStatus,"1")
                .last("LIMIT 1000")
                .list();
        boolean isFinish = aliSignRecordList==null || aliSignRecordList.size()==0;
        if(isFinish){
            log.info("定时任务-支付宝周期扣款前三天发送扣款提醒短信结束");
            return isFinish;
        }
        log.info("定时任务-支付宝周期扣款前三天发送扣款提醒短信");
        aliSignRecordList.forEach(aliSignRecord -> {
            try {
//                TimeUnit.SECONDS.sleep(1L);
//                Map<String,String> smsMap= Maps.newHashMap();
//                smsModelService.sendSms(aliSignRecord.getMobile(),"IVR_ALIPAY","IVR_ALIPAY","4",smsMap);
//                aliSignRecord.setSendDeductMsgDay(this.formatYearMonthDay(LocalDateTime.now()));
//                aliSignRecordService.updateById(aliSignRecord);
                aliSignRecordService.updateSendDeductMsgDay(aliSignRecord,sendDeductMsgDay);
            } catch (Exception e) {
                log.error("id:{},手机号:{},支付宝周期扣款前三天发送扣款提醒短信异常:{}",aliSignRecord.getId(),aliSignRecord.getMobile(),e.getMessage());
                e.printStackTrace();
            }
        });
        return false;
    }
    private String formatYearMonthDay(LocalDateTime localDateTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDateTime.format(dateTimeFormatter);
    }
}
