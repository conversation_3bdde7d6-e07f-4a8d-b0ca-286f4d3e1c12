package com.eleven.cms.job;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.SubscribeVerifyService;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.service.ISubscribeService;
import org.jeecg.common.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.*;
import java.util.List;
import java.util.concurrent.*;

/**
 * 临时包月校验,注意此数据只临时保存到content字段,不会保存到es,需要跑完后需要自己手动去查数据库
 */
@Component
public class SubscribeVerifyTempWorker {

    private static Logger logger = LoggerFactory.getLogger(SubscribeVerifyTempWorker.class);

    public static LocalDate fetchDay = LocalDate.of(2024,4,11);
    public static LocalDate endDay = LocalDate.of(2024, 2, 10);
    public static String[] verifyChannels = {"002112O","002112T","002112S"};

    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IDatangSmsService datangSmsService;
    @Lazy
    @Autowired
    SubscribeVerifyTempWorker subscribeVerifyWorker;

    @Value("${verify.thread.amount}")
    private int consumerThreadAmount;

    private static final String KEY_VERIFY_SWITCH = "temp_verifySwitch";

    /**
     * 开始多线程包月状态校验
     * @return
     */
    public boolean start(){
        boolean ifAbsent = redisUtil.setIfAbsent(KEY_VERIFY_SWITCH, KEY_VERIFY_SWITCH);
        if(ifAbsent){
            subscribeVerifyWorker.work();
        }
        return ifAbsent;
    }

    public void stop(){
        redisUtil.del(KEY_VERIFY_SWITCH);
    }

    private boolean isRunning(){
        return redisUtil.hasKey(KEY_VERIFY_SWITCH);
    }

    /**
     * 启动多线程下载
     */
    @Async
    public void work() {

        DeamonThreadFactory deamonThreadFactory = new DeamonThreadFactory();
        ArrayBlockingQueue<String> queue = new ArrayBlockingQueue<>(consumerThreadAmount);

        ExecutorService exec = Executors.newFixedThreadPool(consumerThreadAmount+1,deamonThreadFactory);
        exec.execute(new Producer(queue));
        for (int consumerId = 0; consumerId < consumerThreadAmount; consumerId++) {
            logger.info("临时包月核实Consumer-{}:-------------------started!", consumerId);
            try {
                //间隔5秒启动一个线程
                exec.execute(new Consumer(queue, consumerId));
                TimeUnit.SECONDS.sleep(3L);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        exec.shutdown();
    }



    /**
     * 完成后保存统计数据到es
     */
    public void smsNotifyFinish(){
        datangSmsService.sendSms("18080928200","【休闲集舍】临时存量核实已完成");
    }


    /**
     * 将所有创建的线程设置为后台守护线程,用以解决tomcat无法正常关闭的问题
     */
    private class DeamonThreadFactory implements ThreadFactory {

        @Override
        public Thread newThread(Runnable r) {
            Thread t = Executors.defaultThreadFactory().newThread(r);
            t.setDaemon(Boolean.TRUE);
            return t;
        }
    }


    private class Producer implements Runnable {

        private ArrayBlockingQueue<String> queue;

        public Producer(ArrayBlockingQueue<String> queue) {
            this.queue = queue;
        }

        @Override
        public void run() {

            while (!Thread.currentThread().isInterrupted() && isRunning() && !fetchDay.isBefore(endDay)) {

                try {
                    List<String> idList = subscribeVerifyService.findTempVerifyDataByDate(fetchDay,verifyChannels);
                    logger.warn("临时包月核实Producer: 校验日期{}数据条数:{}!",fetchDay.toString(),idList.size());
                    if (idList.isEmpty()) {
                        logger.warn("临时包月核实Producer:Fetch 0 taskId from server!");
                        try {
                            TimeUnit.SECONDS.sleep(1L);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    } else {
                        for (String id : idList) {
                            try {
                                //logger.warn("包月核实Producer:Put id {} to queue!", id);
                                queue.put(id);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    
                } catch (Exception e) {
                    logger.error("临时包月核实Producer:Fetch taskId from server fail:{}", e.getMessage());
                    try {
                        TimeUnit.SECONDS.sleep(10L);
                    } catch (InterruptedException ex) {
                        ex.printStackTrace();
                    }
                }
                fetchDay = fetchDay.minusDays(1L);
            }

            //如果已经到了尽头的那一天也没有数据了,就往redis设置状态
            if(fetchDay.isEqual(fetchDay)){
                smsNotifyFinish();
            }
            logger.warn("临时包月核实Producer已完成:Fetch OK!");
        }
    }

    private class Consumer implements Runnable {

        private ArrayBlockingQueue<String> queue;
        private int id;
        //private WebDriver webDriver;

        public Consumer(ArrayBlockingQueue<String> queue, int id) {
            this.queue = queue;
            this.id = id;
            //this.webDriver = webDriver;
        }

        @Override
        public void run() {

            while (!Thread.currentThread().isInterrupted() && isRunning()) {

                try {
                    String subscribeId = queue.take();
                    logger.warn("临时包月核实Consumer-{}执行任务id:{}", id, subscribeId);
                    Subscribe subscribe = subscribeService.getById(subscribeId);
                    subscribeVerifyService.verifyTempData(subscribe);
                } catch (Exception e) {
                    logger.warn("临时包月核实Consumer-{}: do work exception!", id, e);
                    //e.printStackTrace();
                    //try { webDriver.quit(); } catch (Exception driverException) { }
                }
            }
        }
    }

}
