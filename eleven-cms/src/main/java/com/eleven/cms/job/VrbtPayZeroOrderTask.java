package com.eleven.cms.job;

import com.eleven.cms.entity.VrbtZeroOrder;
import com.eleven.cms.service.IVrbtZeroOrderService;
import com.eleven.cms.util.RandomUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 视频彩铃三方支付刷量完成任务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class VrbtPayZeroOrderTask {

    @Autowired
    IVrbtZeroOrderService vrbtZeroOrderService;
    @Autowired
    RedisUtil redisUtil;

    public static final String CHANNEL_J6 = "014X0J6" ;
    final String CHANNEL_J6_COPY_RIGHT_ID="103375T0205";
    final String CHANNEL_J6_CONTENT_ID ="600926000013943348";
    public static final int MAX_INTERVAL_SECONDS_J6 = 120 ;
    public static final int DAY_LIMIT_J6 = 480 ;
    public static final String VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_J6 = "vrbtZeroOrder:dayCounter:changxiang:" ;
    public static final String VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_J6 = "vrbtZeroOrder:dayLimit:changxiang" ;
    public static final String VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_J6 = "vrbtZeroOrder:maxIntervalSeconds:changxiang" ;

    public static final String CHANNEL_J7 = "014X0J7";
    final String CHANNEL_J7_COPY_RIGHT_ID="120084T0069";
    final String CHANNEL_J7_CONTENT_ID ="600985A00005184036";
    public static final int MAX_INTERVAL_SECONDS_J7 = 120 ;
    public static final int DAY_LIMIT_J7 = 480 ;
    public static final String VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_J7 = "vrbtZeroOrder:dayCounter:chuangxiang:" ;
    public static final String VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_J7 = "vrbtZeroOrder:dayLimit:chuangxiang" ;
    public static final String VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_J7 = "vrbtZeroOrder:maxIntervalSeconds:chuangxiang" ;

    /**
     * 014X0J6 定时任务-三方支付视频彩铃0元订购。间隔1秒执行一次,延迟10秒启动
     */
//    @Scheduled(fixedDelay =1000, initialDelay = 10*1000)
    public void initChangXiang() {
        LocalTime taskTime=LocalTime.of(9,30,0);
            if(LocalTime.now().isAfter(taskTime)){
                log.info("定时任务-三方支付视频彩铃0元订购开始-畅想-"+CHANNEL_J6);
                boolean isFinish=this.vrbtZeroOrderChangXiang();
                if(isFinish){
                    try {
                        TimeUnit.MINUTES.sleep(10L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }else {
                log.info("定时任务-三方支付视频彩铃0元订购时间未到-畅想-"+CHANNEL_J6);
                try {
                    TimeUnit.MINUTES.sleep(1L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
    }
    private Boolean vrbtZeroOrderChangXiang() {
        long dayLimitYrjy = getRedisValueOrDefault(VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_J6,DAY_LIMIT_J6);
        if(getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_J6)>= dayLimitYrjy) {
            return Boolean.TRUE;
        }
        final VrbtZeroOrder order = vrbtZeroOrderService.lambdaQuery()
                .eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getChannel,CHANNEL_J6)
                .eq(VrbtZeroOrder::getBizType,"vrbt")
                .eq(VrbtZeroOrder::getStatus, -1)
                .orderByAsc(VrbtZeroOrder::getId)
                .last("LIMIT 1")
                .one();

        if(order==null){
            log.info("定时任务-三方支付视频彩铃0元订购结束-畅想-"+CHANNEL_J6);
            return Boolean.TRUE;
        }
        log.info("定时任务-三方支付视频彩铃0元订购-创响-"+CHANNEL_J6+",今日限量:{},当前处理第{}个,手机号:{}", dayLimitYrjy, getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_J6) + 1, order.getMobile());
        try {
            vrbtZeroOrderService.handleOrder(order.getMobile(),order.getChannel(),order.getId(),CHANNEL_J6_COPY_RIGHT_ID,CHANNEL_J6_CONTENT_ID,String.valueOf(order.getPrice()));
            if (incrDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_J6) >= dayLimitYrjy) {
                return Boolean.TRUE;
            }
            int maxIntervalSecondsYrjy = getRedisValueOrDefault(VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_J6,MAX_INTERVAL_SECONDS_J6);
            TimeUnit.SECONDS.sleep(RandomUtils.createRandom(0, maxIntervalSecondsYrjy));
        } catch (Exception e) {
            log.error("手机号:{},三方支付视频彩铃0元订购异常!-创响-"+CHANNEL_J6+"", order.getMobile(), e);
        }
        return Boolean.FALSE;
    }
    /**
     * 014X0J7 定时任务-三方支付视频彩铃0元订购。间隔1秒执行一次,延迟12秒启动
     */
//    @Scheduled(fixedDelay =1000, initialDelay = 12*1000)
    public void initChuangXiang() {
        LocalTime taskTime=LocalTime.of(9,30,0);
        if(LocalTime.now().isAfter(taskTime)){
            log.info("定时任务-三方支付视频彩铃0元订购开始-创响-"+CHANNEL_J7);
            boolean isFinish=this.vrbtZeroOrderChuangXiang();
            if(isFinish){
                try {
                    TimeUnit.MINUTES.sleep(10L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }else {
            log.info("定时任务-三方支付视频彩铃0元订购时间未到-创响-"+CHANNEL_J7);
            try {
                TimeUnit.MINUTES.sleep(1L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }



    private Boolean vrbtZeroOrderChuangXiang() {
        long dayLimitYrjy = getRedisValueOrDefault(VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_J7,DAY_LIMIT_J7);
        if(getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_J7)>= dayLimitYrjy) {
            return Boolean.TRUE;
        }
        final VrbtZeroOrder order = vrbtZeroOrderService.lambdaQuery()
                .eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getChannel,CHANNEL_J7)
                .eq(VrbtZeroOrder::getBizType,"vrbt")
                .eq(VrbtZeroOrder::getStatus, -1)
                .orderByAsc(VrbtZeroOrder::getId)
                .last("LIMIT 1")
                .one();
        if(order==null){
            log.info("定时任务-三方支付视频彩铃0元订购结束-创响-"+CHANNEL_J7);
            return Boolean.TRUE;
        }
        log.info("定时任务-三方支付视频彩铃0元订购-创响-"+CHANNEL_J7+",今日限量:{},当前处理第{}个,手机号:{}", dayLimitYrjy, getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_J7) + 1, order.getMobile());
        try {
            vrbtZeroOrderService.handleOrder(order.getMobile(),order.getChannel(),order.getId(),CHANNEL_J7_COPY_RIGHT_ID,CHANNEL_J7_CONTENT_ID,String.valueOf(order.getPrice()));
            if (incrDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_J7) >= dayLimitYrjy) {
                return Boolean.TRUE;
            }
            int maxIntervalSecondsYrjy = getRedisValueOrDefault(VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_J7,MAX_INTERVAL_SECONDS_J7);
            TimeUnit.SECONDS.sleep(RandomUtils.createRandom(0, maxIntervalSecondsYrjy));
        } catch (Exception e) {
            log.error("手机号:{},三方支付视频彩铃0元订购异常!-创响-"+CHANNEL_J7+"", order.getMobile(), e);
        }
        return Boolean.FALSE;
    }



    /**
     * 获取当天订购计数
     *
     */
    public long getDayCounter(String vrbtZeroOrderDayCounterRedisKeyPrefix) {
        final String todayCounterKey = vrbtZeroOrderDayCounterRedisKeyPrefix + LocalDate.now();
        if (!redisUtil.hasKey(todayCounterKey)) {
            return 0;
        } else {
            return (Integer) redisUtil.get(todayCounterKey);
        }
    }

    /**
     * 增加当天订购计数
     *
     */
    public long incrDayCounter(String vrbtZeroOrderDayCounterRedisKeyPrefix) {
        final String todayCounterKey = vrbtZeroOrderDayCounterRedisKeyPrefix + LocalDate.now();
        if (!redisUtil.hasKey(todayCounterKey)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(todayCounterKey, 1, expire);
            return 1;
        } else {
            return redisUtil.incr(todayCounterKey, 1);
        }
    }

    /**
     * 获取redis缓存值,如果没有就返回默认值(redis template在,所以强转Long都会报错)
     * @param redisKey
     * @param defaultValue
     * @return
     */
    public int getRedisValueOrDefault(String redisKey, int defaultValue) {
        final Object val = redisUtil.get(redisKey);

        if (Objects.isNull(val)) {
            redisUtil.set(redisKey, defaultValue);
            return defaultValue;
        }
        return ((Number) val).intValue();
    }
}
