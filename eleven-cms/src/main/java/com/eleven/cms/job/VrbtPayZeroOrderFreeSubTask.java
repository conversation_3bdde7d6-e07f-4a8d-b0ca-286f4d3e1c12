package com.eleven.cms.job;

import com.eleven.cms.entity.VrbtZeroOrder;
import com.eleven.cms.service.IVrbtZeroOrderService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 视频彩铃0元订购刷量完成任务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class VrbtPayZeroOrderFreeSubTask {
    @Autowired
    IVrbtZeroOrderService vrbtZeroOrderService;
    @Autowired
    RedisUtil redisUtil;
    /**
     * 视频彩铃0元订购。间隔10秒执行一次,延迟10秒启动
     */
//    @Scheduled(fixedDelay =10*1000, initialDelay = 10*1000)
    public void initChangXiang() {
        LocalTime taskTime=LocalTime.of(10,30,0);
        if(LocalTime.now().isAfter(taskTime)){
            log.info("定时任务-视频彩铃0元订购入队开始");
            boolean isFinish=this.vrbtZeroOrder();
            if(isFinish){
                try {
                    TimeUnit.MINUTES.sleep(10L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }else {
            log.info("定时任务-视频彩铃0元订购消息入队时间未到");
            try {
                TimeUnit.MINUTES.sleep(1L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
    private boolean vrbtZeroOrder() {
        List<VrbtZeroOrder> vrbtZeroOrderList = vrbtZeroOrderService.lambdaQuery().select(VrbtZeroOrder::getId,VrbtZeroOrder::getMobile)
                .eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getStatus,-2)
                .eq(VrbtZeroOrder::getBizType,"vrbt").orderByAsc(VrbtZeroOrder::getId).last("limit 10000").list();
        boolean isFinish = vrbtZeroOrderList==null || vrbtZeroOrderList.size()==0;
        if(isFinish){
            log.info("定时任务-视频彩铃0元订购结束");
            return isFinish;
        }
        vrbtZeroOrderList.parallelStream().forEach(vrbtZeroOrder -> {
            try {
                vrbtZeroOrderService.updateVrbtZeroOrderStatusMessage(vrbtZeroOrder);
            } catch (Exception e) {
                log.error("id:{},手机号:{},视频彩铃0元订购包月订购消息入队异常!",vrbtZeroOrder.getId(),vrbtZeroOrder.getMobile(),e);
            }
        });
        return false;
    }
}
