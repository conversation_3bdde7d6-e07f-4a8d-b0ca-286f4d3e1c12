package com.eleven.cms.job;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2024/11/8 15:08
 * Desc: 定时任务条件,只在node01的阿里云内网ip上运行
 */
@Slf4j
public class TaskCondition implements Condition {
    public static final String TASK_IP = "**************";
    public static final List<String> IP_LIST = getLocalHostAndPortIps();

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        //List<String> ipList = getLocalHostAndPortIps();
        //logger.info("ipList={}",String.join(",",ipList));
        //String ip = context.getEnvironment().getProperty("task.ip");
        //logger.info("ip={}",ip);
        if(IP_LIST.contains(TASK_IP)){
            return true;
        }
        return false;
    }

    /*
     * 获取本机ip+端口，可能有多个
     */
    public static List<String> getLocalHostAndPortIps() {
        String result = "";
        List<String> rtnList = new ArrayList<String>();
        Enumeration netInterfaces;
        try {
            netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = (NetworkInterface) netInterfaces.nextElement();
                Enumeration niips = ni.getInetAddresses();
                while (niips.hasMoreElements()) {
                    InetAddress ip = (InetAddress) niips.nextElement();

                    if (ip.isSiteLocalAddress() || ip.isLoopbackAddress()) {
                        rtnList.add(ip.getHostAddress());
                    }
                }
            }

        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return rtnList;
    }

}

