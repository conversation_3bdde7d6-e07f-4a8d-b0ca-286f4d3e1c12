package com.eleven.cms.job;

import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.ILianlianChargeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/14 16:47
 **/
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class LianLianRechargeRightsTask {

//    @Autowired
//    private ILianlianChargeLogService lianlianChargeLogService;
//    /**
//     * 渠道包月预约充值,间隔3分钟执行一次,延迟1分钟启动
//     */
//    @Scheduled(fixedDelay = 3 * 60 * 1000, initialDelay = 1 * 60 * 1000)
//    public void rechargeForSchedule() {
//        log.info("定时任务-联联分销会员权益预约充值开始");
//        lianlianChargeLogService.findTimeoutSchedule().forEach( lianlianChargeLog -> {
//            try {
//                //新权益充值定时任务
//                lianlianChargeLogService.rechargeForSchedule(lianlianChargeLog);
//            } catch (Exception e) {
//                log.error("id:{},手机号:{},联联分销会员权益预约充值异常:{}",lianlianChargeLog.getId(),lianlianChargeLog.getMobile(),e.getMessage());
//                e.printStackTrace();
//            }
//        });
//        log.info("定时任务-会员权益预约充值结束");
//    }
}
