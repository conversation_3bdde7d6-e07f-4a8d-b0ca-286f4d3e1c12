package com.eleven.cms.job;

import com.eleven.cms.service.IJunboChargeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 会员权益预约直充定时任务
 *  jeecgboot集成的quartz定时任务有bug,跑着跑着就不执行了,所以使用spring自带的比较好
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class RechargeRightsTask {

    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    /**
     * 渠道包月预约充值,间隔3分钟执行一次,延迟1分钟启动
     */
    @Scheduled(fixedDelay = 3 * 60 * 1000, initialDelay = 1 * 60 * 1000)
    public void rechargeForSchedule() {
		log.info("定时任务-会员权益预约充值开始");
        junboChargeLogService.findTimeoutSchedule().forEach( junboChargeLog -> {
            try {
                //新权益充值定时任务
                junboChargeLogService.newRechargeForSchedule(junboChargeLog);
            } catch (Exception e) {
                log.error("id:{},手机号:{},会员权益预约充值异常:{}",junboChargeLog.getId(),junboChargeLog.getAccount(),e.getMessage());
                e.printStackTrace();
            }
        });
        log.info("定时任务-会员权益预约充值结束");
	}

//    /**
//     * pptv视频发送短信,间隔3分钟执行一次,延迟1分钟启动
//     */
//    @Scheduled(fixedDelay = 3 * 60 * 1000, initialDelay = 1 * 60 * 1000)
//    public void pptvSendSms() {
//        log.info("定时任务-pptv视频发送短信开始");
//        pptvGiveLogService.findTimeoutSchedule().forEach( pptvGiveLog -> {
//            try {
//                datangSmsService.sendPPTVRightsNotice(pptvGiveLog.getMobile());
//                pptvGiveLogService.lambdaUpdate().ge(PptvGiveLog::getId,pptvGiveLog.getId()).set(PptvGiveLog::getSendSmsState, BizConstant.PPTV_STATUS_SUCCESS).update();
//            } catch (Exception e) {
//                log.error("id:{},手机号:{},pptv视频发送短信异常:{}",pptvGiveLog.getId(),pptvGiveLog.getMobile(),e.getMessage());
//                e.printStackTrace();
//            }
//        });
//        log.info("定时任务-pptv视频发送短信结束");
//    }

}
