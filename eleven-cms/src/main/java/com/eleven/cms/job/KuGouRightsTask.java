package com.eleven.cms.job;

import com.eleven.cms.entity.KugouOrder;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.IKugouOrderService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.RemoteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 酷狗续订权益发送
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class KuGouRightsTask {
    @Autowired
    private IKugouOrderService kugouOrderService;

    //@Scheduled(fixedDelay = 10*1000,initialDelay=5*1000)
    public void init() {
        LocalTime taskTime=LocalTime.of(10,30,0);
        if(LocalTime.now().isAfter(taskTime) && LocalDate.now().getDayOfMonth()>=5){
            log.info("定时任务-酷狗存量包月校验消息入队开始");
            boolean isFinish=this.kuGouStockCheck();
            if(isFinish){
                try {
                    TimeUnit.MINUTES.sleep(10L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }else {
            log.info("定时任务-酷狗存量包月校验消息入队时间未到");
            try {
                TimeUnit.MINUTES.sleep(1L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 更新酷狗续订
     */
    private Boolean kuGouStockCheck() {
        LocalDateTime start = LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        LocalDateTime end=    LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        List<KugouOrder> kugouList = kugouOrderService.lambdaQuery()
                .between(KugouOrder::getCreateTime,start,end)
                .eq(KugouOrder::getStatus,1)
                .eq(KugouOrder::getRechargeStatus,0)
                .groupBy(KugouOrder::getMobile)
                .orderByAsc(KugouOrder::getCreateTime)
                .last("LIMIT 1000")
                .list();
        boolean isFinish = kugouList==null || kugouList.size()==0;
        if(isFinish){
            log.info("定时任务-酷狗存量包月校验消息入队结束");
            return isFinish;
        }
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        kugouList.forEach(kugou -> {
            try {
                kugou.setRightsMonth(rightsMonth);
                kugou.setRechargeStatus(4);
                kugouOrderService.updateRightsMonthAndSendRightsRechargeMessage(kugou);
            } catch (Exception e) {
                log.error("id:{},手机号:{},酷狗存量包月校验消息入队异常!",kugou.getId(),kugou.getMobile(),e);
            }
        });
        return false;
    }
}
