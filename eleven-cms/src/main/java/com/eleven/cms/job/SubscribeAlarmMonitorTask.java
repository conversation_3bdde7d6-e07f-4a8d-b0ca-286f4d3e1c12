package com.eleven.cms.job;

import com.eleven.cms.service.impl.AlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 *
 * @author: eleven
 * @create: 
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class SubscribeAlarmMonitorTask {

    @Autowired
    AlarmService alarmService;
    @Scheduled(fixedRate = 10L*60L*1000L,initialDelay=30L*1000L)
    public void monitorSubscribe() throws Exception {
        alarmService.monitorSubscribe();
    }


}

