package com.eleven.cms.job;

import com.eleven.cms.remote.AlipayMsgService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @author: eleven
 * @create: 
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class AlipayMsgTask {
    @Autowired
    AlipayMsgService alipayMsgService;

    //已经迁移到cms-crbt项目中,因为启动时遇到无效的appid或导致有的线程池被spring shutdown---------------------------------------------------------
    //@Scheduled(fixedRate = Long.MAX_VALUE,initialDelay=5*1000)
    public void connetAlipayMsg() throws Exception {
        log.info("启动支付宝蚂蚁消息websocket连接建立任务");
        alipayMsgService.connetAll();
    }
}
