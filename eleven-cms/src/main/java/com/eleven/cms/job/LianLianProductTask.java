package com.eleven.cms.job;

import com.eleven.cms.entity.MemberRights;
import com.eleven.cms.service.ILianLianFenXiaoService;
import com.eleven.cms.service.IMemberRightsService;
import com.eleven.cms.vo.LianLianFenXiaoProductDetail;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 联联产品同步上架下架
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/14 16:47
 **/
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class LianLianProductTask {

    @Autowired
    private IMemberRightsService memberRightsService;
    @Autowired
    private ILianLianFenXiaoService lianLianFenXiaoService;
    /**
     * 每天2点联联产品同步上架下架
     */
    //@Scheduled(cron = "0 0 2 * * ?")
    public void lianLianProduct() {
        log.info("定时任务-联联分销产品同步上架下架开始");
        List<MemberRights> memberRightsList=memberRightsService.lambdaQuery().select(MemberRights::getId,MemberRights::getCouponId,MemberRights::getRightsId,MemberRights::getRightsName).eq(MemberRights::getCompanyOwner,"LIANLIAN").list();
        for(MemberRights memberRights:memberRightsList){
            try {
                Result<?> result=lianLianFenXiaoService.queryProductDetailByProductId(memberRights.getCouponId(), memberRights.getRightsId());
                if(!result.isOK()){
                    memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,0).update();
                    continue;
                }
                LianLianFenXiaoProductDetail productDetail=(LianLianFenXiaoProductDetail)result.getResult();
                if(productDetail==null){
                    memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,0).update();
                    continue;
                }
                if(productDetail.getItemStock().intValue()>0){
                    Optional<LianLianFenXiaoProductDetail.ItemList> itemListOptional = productDetail.getItemList().stream()
                            .filter(item -> memberRights.getRightsId().equals(String.valueOf(item.getItemId())))
                            .findAny();
                    if(itemListOptional.isPresent() && itemListOptional.get().getStock()!=null && itemListOptional.get().getStock().intValue()>0){
                        memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,1).update();
                    }else{
                        memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,0).update();
                    }
                    continue;
                }
                if(productDetail.getStockAmount()!=null && productDetail.getStockAmount().intValue()>0){
                    memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,1).update();
                }else{
                    memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,0).update();
                }
            } catch (Exception e) {
                log.error("产品ID:{},套餐ID:{},产品名称:{},联联分销产品同步上架下架异常:{}",memberRights.getCouponId(),memberRights.getRightsId(),memberRights.getRightsName(),e.getMessage());
                e.printStackTrace();
            }
        }
        log.info("定时任务-联联分销产品同步上架下架结束");
    }
}
