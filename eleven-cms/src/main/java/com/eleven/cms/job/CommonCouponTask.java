package com.eleven.cms.job;

import com.eleven.cms.service.ICommonCouponService;
import com.eleven.cms.service.IOrderTarckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 添加通用券码定时任务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024-7-5 15:30:50
 **/
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class CommonCouponTask {
    @Autowired
    private ICommonCouponService commonCouponService;
//
//    /**
//     * 每月1-5号9点生成上月未退订数据的本月券码
//     * @throws Exception
//     */
//    @Scheduled(cron = "0 0 9 1,2,3,4,5 * ? ")
//    public void addCouponTask(){
//        log.info("开始执行添加通用券码定时任务");
//        commonCouponService.addCouponTask();
//        log.info("结束执行添加通用券码定时任务");
//    }
}
