package com.eleven.cms.job;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.impl.EsDataServiceImpl;
import com.eleven.cms.queue.MiguRingFtpUploadMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.service.ISmsModelService;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.RemoteResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.core.query.SourceFilter;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;


/**
 * 每月续订短信
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/23 10:57
 **/
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class MonthRenewSubSmsTask {
//    private static final List<String> CHANNEL_LIST= Lists.newArrayList("00210U2","00210Q6","00210W5");
    private static final List<String> CHANNEL_LIST= Lists.newArrayList(/*MiguApiService.BIZ_SCH_CHANNEL_CODE_SZR,*/
        MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP,
        MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX,
        BizConstant.BIZ_CHANNEL_SXYD_JBAICL);
    @Autowired
    ElasticsearchRestTemplate restTemplate;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IDatangSmsService iDatangSmsService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 每月5号9点发送
     */
    @Scheduled(cron = "0 0 9 5 * ?")
    public void init() {
        log.info("定时任务-每月续订短信开始");
        List<EsSubscribe> esSubscribeList=findByChannel(CHANNEL_LIST);
        if(esSubscribeList==null || esSubscribeList.size()<=0){
            log.info("定时任务-每月续订短信es数据查询失败");
            return;
        }
        for (EsSubscribe esSubscribe : esSubscribeList) {
            smsModelService.monthRenewSubSmsMessage(esSubscribe.getChannel(),esSubscribe.getMobile(),esSubscribe.getId(),esSubscribe.getBizType());
        }
        log.info("定时任务-每月续订短信结束");
    }

    /**
     * AI视频彩铃每日活跃7000（00211DQ、00211GY）
     */
//    @Scheduled(cron = "0 0 10 * * ?")
    public void doActive7000() {
        try {
            log.info("定时任务-AI视频彩铃每日7000活跃任务开始");
            List<String> channels = Lists.newArrayList(
                    MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP,
                    MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX);
            List<EsSubscribe> esSubscribeList = findByChannel(channels);
            if (CollectionUtils.isEmpty(esSubscribeList)) {
                log.info("定时任务-AI视频彩铃每日7000活跃任务查询失败");
                return;
            }

            int count = 0;
            // 创建一个Random对象
            Random random = new Random();

            for (EsSubscribe esSubscribe : esSubscribeList) {
                if (count >= 7000) {
                    break;
                }
                String doactive7000Key = "aispcl:day:7000:" + esSubscribe.getChannel() + "-" + esSubscribe.getMobile();
                if (redisUtil.hasKey(doactive7000Key)) {
                    count++;
                    continue;
                }

                final RemoteResult schResult = miguApiService.schQuery(esSubscribe.getMobile(), esSubscribe.getChannel());
                if (schResult.isSchMember()) {
                    // 生成1到3000之间的随机数
                    int randomNumber = random.nextInt(3000) + 1;
                    // 随机延迟分布
                    Thread.sleep(randomNumber);

                    miguApiService.specialProductSub(esSubscribe.getMobile(), esSubscribe.getChannel());
                    rabbitMQMsgSender.sendMiguRingFtpUploadMessage(MiguRingFtpUploadMessage.builder()
                            .id(esSubscribe.getId())
                            .mobile(esSubscribe.getMobile())
                            .tag(esSubscribe.getChannel()).build());

                    redisUtil.set(doactive7000Key, doactive7000Key, TimeUtils.getRemainingSecondOfDay());
                    count++;
                    LogUtils.info(esSubscribe.getChannel(), "AI视频彩铃每日7000活跃任务-exec");
                }
            }

            // 任务执行完成后，给相关人员发送短信通知
            List<String> mobiles = Arrays.asList("13408608881", "13408669942");
            for (String mobile : mobiles) {
                iDatangSmsService.sendSms(mobile, "【休闲集舍】AI视频彩铃每日" + count + "活跃任务已完成");
            }
            log.info("定时任务-AI视频彩铃每日7000活跃任务结束");
        } catch (Exception e) {
            log.error("定时任务-AI视频彩铃每日7000活跃任务异常");
        }
    }


    /**
     * 根据渠道号查询es订购数据
     * @param channelList
     * @return
     */
    public List<EsSubscribe> findByChannel(List<String> channelList) {
        EsSubscribe esSubscribe=new EsSubscribe();
        esSubscribe.setStatus(1);
        esSubscribe.setVerifyStatus(1);
//        esSubscribe.setVerifyStatusDaily(1);
        SearchHits<EsSubscribe> searchHits = restTemplate.search(buildExportQuery(esSubscribe, channelList), EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
        if(searchHits==null || searchHits.isEmpty()){
            log.info("ES查询数据不存在=>渠道号:{}",channelList);
            return null;
        }
        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        return esSubscribeList;
    }

    /**
     * 构建Excel导出查询
     *
     * @param esSubscribe 查询参数
     * @return
     */
    public Query buildExportQuery(EsSubscribe esSubscribe, List<String> channelList) {
        //需要查询的字段
        String[] includes = {"id","mobile","bizType","channel","createTime"};
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        build(builder, esSubscribe, channelList);
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
                    @Override
                    public String[] getIncludes() {
                        return includes;
                    }

                    @Override
                    public String[] getExcludes() {
                        return new String[0];
                    }
                }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(10000000);
        return nativeSearchQuery;
    }


    /**
     * 构建查询
     *
     * @param builder
     */
    private void build(BoolQueryBuilder builder, EsSubscribe esSubscribe,List<String> channelList) {

        /**
         * 使用QueryBuilder
         * termQuery("key", obj) 完全匹配
         * termsQuery("key", obj1, obj2..)   一次匹配多个值
         * matchQuery("key", Obj) 单个匹配, field不支持通配符, 前缀具高级特性
         * multiMatchQuery("text", "field1", "field2"..);  匹配多个字段, field有通配符忒行
         * matchAllQuery();         匹配所有文件
         * wildcardQuery();         模糊查询
         * must(QueryBuilders)；   AND
         * mustNot(QueryBuilders)； NOT
         * should； OR
         */
        //渠道号列表查询
        if(channelList!=null && !channelList.isEmpty()){
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            channelList.forEach(channel -> {
                shouldQuery.should(QueryBuilders.termQuery("channel.keyword", channel));
            });
            builder.must(shouldQuery);
        }
        //开通状态
        if (esSubscribe.getStatus() != null) {
            builder.must(QueryBuilders.termQuery("status", esSubscribe.getStatus()));
        }
        if (esSubscribe.getVerifyStatus() != null) {
            builder.must(QueryBuilders.termQuery("verifyStatus", esSubscribe.getVerifyStatus()));
        }
        if (esSubscribe.getVerifyStatusDaily() != null) {
            builder.must(QueryBuilders.termQuery("verifyStatusDaily", esSubscribe.getVerifyStatusDaily()));
        }
        builder.must(QueryBuilders.rangeQuery("createTime").lt(DateUtil.localDateTimeToDate(DateUtil.getFirstDayOfMonthWithMinTime()).getTime()));
    }
}
