package com.eleven.cms.job;

import com.eleven.cms.entity.Alipay;
import com.eleven.cms.remote.RechargeAlertService;
import com.eleven.cms.service.IAlipayConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 *
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class AlipayCacheWarnTask {
    @Autowired
    private IAlipayConfigService alipayService;
    @Autowired
    private RechargeAlertService rechargeAlertService;
    /**
     * 支付宝缓存预警
     */
//    @Scheduled(fixedDelay = 3 * 60 * 1000, initialDelay = 1 * 60 * 1000)
    public void alipayCacheWarnSchedule() {
        log.info("定时任务-支付宝缓存预警开始");
        alipayService.lambdaQuery().eq(Alipay::getIsSwitch, "1").list().forEach(alipay -> {
            try {
                rechargeAlertService.alipayCacheWarn(alipay.getBusinessType(),alipay.getBusinessName(),alipay.getIsSwitch());
            } catch (Exception e) {
                log.error("业务类型:{},业务名称:{},支付宝缓存预警异常:{}",alipay.getBusinessType(),alipay.getBusinessName(),e.getMessage());
                e.printStackTrace();
            }
        });
        log.info("定时任务-支付宝缓存预警结束");
    }
}
