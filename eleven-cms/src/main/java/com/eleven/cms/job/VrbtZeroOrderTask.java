package com.eleven.cms.job;

import com.eleven.cms.entity.VrbtZeroOrder;
import com.eleven.cms.service.IVrbtZeroOrderService;
import com.eleven.cms.util.RandomUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 讯飞三方支付刷量完成任务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class VrbtZeroOrderTask {

    @Autowired
    IVrbtZeroOrderService vrbtZeroOrderService;
    @Autowired
    RedisUtil redisUtil;
    
    public static final String CHANNEL_YRJY = "014X0A3" ;
    //public static final int MAX_INTERVAL_SECONDS_YRJY = 180 ;
    //public static final int DAY_LIMIT_YRJY = 300 ;
    public static final int MAX_INTERVAL_SECONDS_YRJY = 120 ;
    public static final int DAY_LIMIT_YRJY = 480 ;
    public static final String VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_YRJY = "vrbtZeroOrder:dayCounter:yrjy:" ;
    public static final String VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_YRJY = "vrbtZeroOrder:dayLimit:yrjy" ;
    public static final String VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_YRJY = "vrbtZeroOrder:maxIntervalSeconds:yrjy" ;

    public static final String CHANNEL_XUNFEI = "014X09A" ;
    public static final int MAX_INTERVAL_SECONDS_XUNFEI = 240 ;
    public static final int DAY_LIMIT_XUNFEI = 200 ;
    public static final String VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_XUNFEI = "vrbtZeroOrder:dayCounter:xunfei:" ;
    public static final String VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_XUNFEI = "vrbtZeroOrder:dayLimit:xunfei" ;
    public static final String VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_XUNFEI = "vrbtZeroOrder:maxIntervalSeconds:xunfei" ;

    public static final String CHANNEL_XUNFEI_TEMP = "014X0F7" ;
    public static final int MAX_INTERVAL_SECONDS_XUNFEI_TEMP_MONTH = 10 ;
    public static final int DAY_LIMIT_XUNFEI_TEMP_MONTH_MIN = 5000 ;
    public static final int DAY_LIMIT_XUNFEI_TEMP_MONTH_MAX = 6000 ;
    public static final String VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_XUNFEI_TEMP = "vrbtZeroOrder:dayCounter:xunfeiTemp:" ;
    public static final String VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_XUNFEI_TEMP = "vrbtZeroOrder:dayLimit:xunfeiTemp" ;
    public static final String VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_XUNFEI_TEMP = "vrbtZeroOrder:maxIntervalSeconds:xunfeiTemp" ;

    public static final int PROVINCE_BJHY_ORDER_MONTH_MAX_INTERVAL_SECONDS = 6 ;
    public static final int PROVINCE_BJHY_ORDER_DAY_LIMIT_MONTH_MIN = 20000 ;
    public static final int PROVINCE_BJHY_ORDER_DAY_LIMIT_MONTH_MAX = 25000 ;
    public static final String PROVINCE_BJHY_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX = "provinceBjhyOrder:dayCounter:" ;
    public static final String PROVINCE_BJHY_ORDER_DAY_LIMIT_REDIS_KEY = "provinceBjhyOrder:dayLimit" ;
    public static final String PROVINCE_BJHY_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY = "provinceBjhyOrder:maxIntervalSeconds" ;

    //@Scheduled(fixedDelay = 1000,initialDelay=10*1000)
    public void init() {
        LocalTime taskTime=LocalTime.of(9,30,0);
            if(LocalTime.now().isAfter(taskTime)){
                log.info("定时任务-三方支付视频彩铃0元订购开始");
                boolean isFinish=this.vrbtZeroOrder();
                if(isFinish){
                    try {
                        TimeUnit.MINUTES.sleep(10L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }else {
                log.info("定时任务-三方支付视频彩铃0元订购时间未到");
                try {
                    TimeUnit.MINUTES.sleep(1L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
    }

    //@Scheduled(fixedDelay = 1000,initialDelay=12*1000)
    public void initXunfei() {
        LocalTime taskTime=LocalTime.of(9,30,0);
        if(LocalTime.now().isAfter(taskTime)){
            log.info("定时任务-三方支付视频彩铃0元订购(讯飞)开始");
            boolean isFinish=this.vrbtZeroOrderXunfei();
            if(isFinish){
                try {
                    TimeUnit.MINUTES.sleep(10L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }else {
            log.info("定时任务-三方支付视频彩铃0元订购(讯飞)时间未到");
            try {
                TimeUnit.MINUTES.sleep(1L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 讯飞临时刷量
     */
    //@Scheduled(fixedDelay = 10,initialDelay=12*1000)
    public void initXunfeiTemp() {
        LocalTime taskTimeStart=LocalTime.of(7,0,0);
        LocalTime taskTimeEnd=LocalTime.of(23,59,59);
        if(/*YearMonth.now().equals(YearMonth.of(2024,8)) &&*/ LocalTime.now().isAfter(taskTimeStart) && LocalTime.now().isBefore(taskTimeEnd)){
            log.info("定时任务-三方支付视频彩铃0元订购(讯飞临时)开始");
            boolean isFinish=this.vrbtZeroOrderXunfeiTemp();
            if(isFinish){
                try {
                    TimeUnit.MINUTES.sleep(10L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }else {
            log.info("定时任务-三方支付视频彩铃0元订购(讯飞临时)未在时段内");
            try {
                TimeUnit.MINUTES.sleep(1L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 音乐包四川存量用户且有功能的发开功能的三方支付权益
     */
    //@Scheduled(fixedDelay = 10,initialDelay=12*1000)
    public void initProvinceBjhyOrder() {
        LocalTime taskTimeStart=LocalTime.of(7,0,0);
        LocalTime taskTimeEnd=LocalTime.of(23,59,59);
        if(YearMonth.now().equals(YearMonth.of(2024,10))
            && LocalTime.now().isAfter(taskTimeStart) && LocalTime.now().isBefore(taskTimeEnd)){
            log.info("定时任务-三方支付音乐包四川开视频彩铃功能开始");
            boolean isFinish=this.provinceBjhyOrder();
            if(isFinish){
                try {
                    TimeUnit.MINUTES.sleep(10L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }else {
            log.info("定时任务-三方支付音乐包四川开视频彩铃功能未在时段内");
            try {
                TimeUnit.MINUTES.sleep(1L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }


    private Boolean vrbtZeroOrder() {
        long dayLimitYrjy = getRedisValueOrDefault(VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_YRJY,DAY_LIMIT_YRJY);
        if(getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_YRJY)>= dayLimitYrjy) {
            return Boolean.TRUE;
        }
        final VrbtZeroOrder order = vrbtZeroOrderService.lambdaQuery()
                .eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getChannel,CHANNEL_YRJY)
                .eq(VrbtZeroOrder::getBizType,"vrbt")
                .eq(VrbtZeroOrder::getStatus, -1)
                .orderByAsc(VrbtZeroOrder::getId)
                .last("LIMIT 1")
                .one();

        if(order==null){
            log.info("定时任务-三方支付视频彩铃0元订购结束");
            return Boolean.TRUE;
        }
        log.info("定时任务-三方支付视频彩铃0元订购,今日限量:{},当前处理第{}个,手机号:{}", dayLimitYrjy, getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_YRJY) + 1, order.getMobile());
        try {
            vrbtZeroOrderService.handleOrder(order);
            if (incrDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_YRJY) >= dayLimitYrjy) {
                return Boolean.TRUE;
            }
            int maxIntervalSecondsYrjy = getRedisValueOrDefault(VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_YRJY,MAX_INTERVAL_SECONDS_YRJY);
            TimeUnit.SECONDS.sleep(RandomUtils.createRandom(0, maxIntervalSecondsYrjy));
        } catch (Exception e) {
            log.error("手机号:{},三方支付视频彩铃0元订购异常!", order.getMobile(), e);
        }
        return Boolean.FALSE;
    }

    private Boolean vrbtZeroOrderXunfei() {
        long dayLimitXunfei = getRedisValueOrDefault(VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_XUNFEI,DAY_LIMIT_XUNFEI);
        if(getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_XUNFEI)>= dayLimitXunfei) {
            return Boolean.TRUE;
        }
        final VrbtZeroOrder order = vrbtZeroOrderService.lambdaQuery()
                .eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getChannel,CHANNEL_XUNFEI)
                .eq(VrbtZeroOrder::getBizType,"vrbt")
                .eq(VrbtZeroOrder::getStatus, -1)
                .orderByAsc(VrbtZeroOrder::getId)
                .last("LIMIT 1")
                .one();
        if(order==null){
            log.info("定时任务-三方支付视频彩铃0元订购(讯飞)结束");
            return Boolean.TRUE;
        }
        log.info("定时任务-三方支付视频彩铃0元订购(讯飞),今日限量:{},当前处理第{}个,手机号:{}", dayLimitXunfei, getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_XUNFEI) + 1, order.getMobile());
        try {
            vrbtZeroOrderService.handleOrderXunfei(order);
            if (incrDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_XUNFEI) >= dayLimitXunfei) {
                return Boolean.TRUE;
            }
            int maxIntervalSecondsXunfei = getRedisValueOrDefault(VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_XUNFEI,MAX_INTERVAL_SECONDS_XUNFEI);
            TimeUnit.SECONDS.sleep(RandomUtils.createRandom(0, maxIntervalSecondsXunfei));
        } catch (Exception e) {
            log.error("手机号:{},三方支付视频彩铃0元订购(讯飞)异常!", order.getMobile(), e);
        }
        return Boolean.FALSE;
    }

    private Boolean vrbtZeroOrderXunfeiTemp() {
        final LocalDate localDate = LocalDate.now();
        //final boolean isMonth11 = localDate.getMonthValue() == 11;
        //未报备通过,每天暂时只跑一百个
        //int dayLimitXunfeiTemp = 100;
        int dayLimitXunfeiTemp = /*isMonth11 ? DAY_LIMIT_XUNFEI_TEMP_MONTH_11 :*/ RandomUtils.createRandom(DAY_LIMIT_XUNFEI_TEMP_MONTH_MIN, DAY_LIMIT_XUNFEI_TEMP_MONTH_MAX);

        long dayLimitXunfei = getRedisValueOrDefault(VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_XUNFEI_TEMP + ":" + localDate,dayLimitXunfeiTemp);
        if(getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_XUNFEI_TEMP)>= dayLimitXunfei) {
            return Boolean.TRUE;
        }
        final VrbtZeroOrder order = vrbtZeroOrderService.lambdaQuery()
                //.eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getChannel,CHANNEL_XUNFEI_TEMP)
                .eq(VrbtZeroOrder::getBizType,"vrbt")
                .eq(VrbtZeroOrder::getStatus, -1)
                .orderByAsc(VrbtZeroOrder::getId)
                .last("LIMIT 1")
                .one();
        if(order==null){
            log.info("定时任务-三方支付视频彩铃0元订购(讯飞临时)结束");
            return Boolean.TRUE;
        }
        log.info("定时任务-三方支付视频彩铃0元订购(讯飞临时),今日限量:{},当前处理第{}个,手机号:{}", dayLimitXunfei, getDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_XUNFEI_TEMP) + 1, order.getMobile());
        try {
            vrbtZeroOrderService.handleOrderXunfeiTemp(order);
            if (incrDayCounter(VRBT_ZERO_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX_XUNFEI_TEMP) >= dayLimitXunfei) {
                return Boolean.TRUE;
            }
            int maxIntervalSecondsXunfeiTemp = /*isMonth11 ? MAX_INTERVAL_SECONDS_XUNFEI_TEMP_MONTH_11 : */MAX_INTERVAL_SECONDS_XUNFEI_TEMP_MONTH;
            int maxIntervalSecondsXunfei = getRedisValueOrDefault(VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_XUNFEI_TEMP + ":" + localDate, maxIntervalSecondsXunfeiTemp);
            //使用毫秒来随机
            TimeUnit.SECONDS.sleep(RandomUtils.createRandom(0, maxIntervalSecondsXunfei));
        } catch (Exception e) {
            log.error("手机号:{},三方支付视频彩铃0元订购(讯飞临时)异常!", order.getMobile(), e);
        }
        return Boolean.FALSE;
    }

    private Boolean provinceBjhyOrder() {
        final LocalDate localDate = LocalDate.now();
        //final boolean isMonth11 = localDate.getMonthValue() == 11;
        //未报备通过,每天暂时只跑一百个
        //int dayLimitXunfeiTemp = 100;
        int dayLimitXunfeiTemp = /*isMonth11 ? DAY_LIMIT_XUNFEI_TEMP_MONTH_11 :*/ RandomUtils.createRandom(
                PROVINCE_BJHY_ORDER_DAY_LIMIT_MONTH_MIN, PROVINCE_BJHY_ORDER_DAY_LIMIT_MONTH_MAX);

        long dayLimitXunfei = getRedisValueOrDefault(PROVINCE_BJHY_ORDER_DAY_LIMIT_REDIS_KEY + ":" + localDate,dayLimitXunfeiTemp);
        if(getDayCounter(PROVINCE_BJHY_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX)>= dayLimitXunfei) {
            return Boolean.TRUE;
        }
        final VrbtZeroOrder order = vrbtZeroOrderService.lambdaQuery()
                .eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getBizType,"cpmb")
                .eq(VrbtZeroOrder::getStatus, -1)
                .orderByAsc(VrbtZeroOrder::getId)
                .last("LIMIT 1")
                .one();
        if(order==null){
            log.info("定时任务-三方支付音乐包四川开视频彩铃功能结束");
            return Boolean.TRUE;
        }
        log.info("定时任务-三方支付音乐包四川开视频彩铃功能,今日限量:{},当前处理第{}个,手机号:{}", dayLimitXunfei, getDayCounter(
                PROVINCE_BJHY_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX) + 1, order.getMobile());
        try {
            vrbtZeroOrderService.handleOrderProvinceVrbtFun(order);
            if (incrDayCounter(PROVINCE_BJHY_ORDER_DAY_COUNTER_REDIS_KEY_PREFIX) >= dayLimitXunfei) {
                return Boolean.TRUE;
            }
            int maxIntervalSecondsXunfeiTemp = /*isMonth11 ? MAX_INTERVAL_SECONDS_XUNFEI_TEMP_MONTH_11 : */PROVINCE_BJHY_ORDER_MONTH_MAX_INTERVAL_SECONDS;
            int maxIntervalSecondsXunfei = getRedisValueOrDefault(PROVINCE_BJHY_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY + ":" + localDate, maxIntervalSecondsXunfeiTemp);
            //使用毫秒来随机
            TimeUnit.SECONDS.sleep(RandomUtils.createRandom(0, maxIntervalSecondsXunfei));
        } catch (Exception e) {
            log.error("手机号:{},三方支付音乐包四川开视频彩铃功能异常!", order.getMobile(), e);
        }
        return Boolean.FALSE;
    }

    /**
     * 获取当天订购计数
     *
     */
    public long getDayCounter(String vrbtZeroOrderDayCounterRedisKeyPrefix) {
        final String todayCounterKey = vrbtZeroOrderDayCounterRedisKeyPrefix + LocalDate.now();
        if (!redisUtil.hasKey(todayCounterKey)) {
            return 0;
        } else {
            return (Integer) redisUtil.get(todayCounterKey);
        }                                                                          
    }
    
    /**
     * 增加当天订购计数
     *
     */
    public long incrDayCounter(String vrbtZeroOrderDayCounterRedisKeyPrefix) {
        final String todayCounterKey = vrbtZeroOrderDayCounterRedisKeyPrefix + LocalDate.now();
        if (!redisUtil.hasKey(todayCounterKey)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(todayCounterKey, 1, expire);
            return 1;
        } else {
            return redisUtil.incr(todayCounterKey, 1);
        }
    }

    /**
     * 获取redis缓存值,如果没有就返回默认值(redis template在,所以强转Long都会报错)
     * @param redisKey
     * @param defaultValue
     * @return
     */
    public int getRedisValueOrDefault(String redisKey, int defaultValue) {
        final Object val = redisUtil.get(redisKey);
        
        if (Objects.isNull(val)) {
            redisUtil.set(redisKey, defaultValue);
            return defaultValue;
        }
        return ((Number) val).intValue();
    }

    public static void main(String[] args) {
        //System.out.println(VRBT_ZERO_ORDER_DAY_LIMIT_REDIS_KEY_XUNFEI_TEMP + ":" + LocalDate.now());
        //System.out.println(VRBT_ZERO_ORDER_MAX_INTERVAL_SECONDS_REDIS_KEY_XUNFEI_TEMP + ":" + LocalDate.now());
        //System.out.println(LocalDate.now().getMonthValue());
        //System.out.println(LocalDate.now().equals(LocalDate.of(2023,11,27)));

        System.out.println(YearMonth.now());
        System.out.println(YearMonth.now().equals(YearMonth.of(2023,12)));
    }

}
