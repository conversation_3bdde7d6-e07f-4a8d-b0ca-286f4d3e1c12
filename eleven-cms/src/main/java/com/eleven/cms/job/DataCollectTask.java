package com.eleven.cms.job;

import com.eleven.qycl.service.IDataCollectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 企业彩铃数据汇总定时任务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/19 11:57
 **/
@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class DataCollectTask {
    @Autowired
    IDataCollectService dataCollectService;
    //每日0点汇总
    //@Scheduled(cron = "0 0 0 * * ?")
    public void dataCollect() throws Exception {
        log.info("开始执行企业彩铃数据汇总定时任务");
        dataCollectService.dataCollect();
        log.info("结束执行企业彩铃数据汇总定时任务");
    }
}
