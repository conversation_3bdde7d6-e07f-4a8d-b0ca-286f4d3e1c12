package com.eleven.cms.zhmb.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: zhmb_ring
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Data
@TableName("zhmb_ring")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "zhmb_ring对象", description = "zhmb_ring")
public class ZhmbRing implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 封面图片
     */
    @Excel(name = "封面图片", width = 15)
    @ApiModelProperty(value = "封面图片")
    private String coverUrl;
    /**
     * 视频地址
     */
    @Excel(name = "视频地址", width = 15)
    @ApiModelProperty(value = "视频地址")
    private String videoUrl;
    /**
     * 铃声类型,1换脸2跳舞3动漫
     */
    @Excel(name = "铃声类型,1换脸2跳舞3动漫", width = 15)
    @ApiModelProperty(value = "铃声类型,1换脸2跳舞3动漫")
    private String ringType;
    /**
     * 任务id
     */
    @Excel(name = "任务id", width = 15)
    @ApiModelProperty(value = "任务id")
    private String jobId;
    /**
     * 0制作中,1制作成功,2制作失败
     */
    @Excel(name = "0制作中,1制作成功,2制作失败", width = 15)
    @ApiModelProperty(value = "0制作中,1制作成功,2制作失败")
    private String ringStatus;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
