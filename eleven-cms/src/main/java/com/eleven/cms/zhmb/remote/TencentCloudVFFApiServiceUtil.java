package com.eleven.cms.zhmb.remote;

import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * @author: cai lei
 * @create: 2024-10-16 19:58
 * 腾讯云AI换脸API
 */
@Slf4j
public class TencentCloudVFFApiServiceUtil {

    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    public static final String VIDEO_FACEFUSION_HOST = "facefusion.tencentcloudapi.com";
    public static final String CONTENT_TYPE = "application/json; charset=utf-8";
    public static final String VERSION = "2018-12-01";
    public static final String REGION = "ap-chengdu";
    public static final String LOG_TAG = "腾讯云AI换脸API";

    private static final OkHttpClient client = OkHttpClientUtils.getNewInstance().newBuilder().build();
    private static final ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    /**
     * {"Response":{"JobQueueLength":104,"EstimatedProcessTime":10,"ReviewResultSet":[],"RequestId":"9b421182-c81e-4969-a156-669a2154201f","JobId":"00434935A2978D1652D454D6104BC3180D4"}}
     * {"Response":{"RequestId":"7f87ea1c-3fcf-40cc-8127-31667fce144c","Error":{"Code":"FailedOperation.ImageSizeExceed","Message":"base64编码后的图片数据过大。"}}}
     *
     * @param projectId
     * @param modelId
     * @param imageUrl
     */
    @SneakyThrows
    public static String videoFaceFusion(String mobile, String projectId, String modelId, String imageUrl) {
        ObjectNode data = mapper.createObjectNode();
        data.put("ProjectId", projectId);
        data.put("ModelId", modelId);
        //对象存储预签名地址(PUT 方法)，
        //填值则最终融合的视频上传到此地址。
        //注意：如果此地址上传失败，则会返回腾讯侧默认的地址。
        //data.put("UserDesignatedUrl", GeneratePresignedUrlDemo.generateSimplePresignedDownloadUrl());
        ArrayNode mergeInfosNode = mapper.createArrayNode();
        ObjectNode imageNode = mapper.createObjectNode();
        imageNode.put("Url", imageUrl);
        mergeInfosNode.add(imageNode);
        data.put("MergeInfos", mergeInfosNode);

        //body参数{\"ProjectId\":\"at_1846485675052244992\",\"ModelId\":\"mt_1846503393558142976\",\"MergeInfos\":[{\"Url\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/test/1886d5ff-074e-4f99-8f3e-d48c3c77b75d.jpg\"}]}
        RequestBody body = RequestBody.create(JSON, data.toString());
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String auth = getAuth(VIDEO_FACEFUSION_HOST, CONTENT_TYPE, timestamp, data.toString());
        String requestUrl = "https://" + VIDEO_FACEFUSION_HOST;
        Request request = new Request.Builder().url(requestUrl).post(body)
            .header("Host", VIDEO_FACEFUSION_HOST)
            .header("X-TC-Timestamp", timestamp)
            .header("X-TC-Version", VERSION)
            .header("X-TC-Action", "SubmitVideoFaceFusionJob")
            .header("X-TC-Region", REGION)
            .header("X-TC-Token", "")
            .header("X-TC-RequestClient", "SDK_JAVA_BAREBONE")
            .header("Authorization", auth)
            .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交换脸任务-用户:{},projectId:{},modelId:{},响应:{}", LOG_TAG, mobile, projectId, modelId, content);
            JsonNode jsonNode = mapper.readTree(content);
            String jobId = jsonNode.at("/Response/JobId").asText();
            return jobId;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交换脸任务-用户:{},projectId:{},modelId:{},异常:", LOG_TAG, mobile, projectId, modelId, e);
            return "";
        }
    }

    /**
     * {"Response":{"JobErrorCode":"","JobErrorMsg":"","JobStatus":"处理完成","JobStatusCode":7,"RequestId":"c862206d-979e-47d7-bde2-294641a76a3b","VideoFaceFusionOutput":{"DurationInSec":9,"FPS":30,"Frame":273,"Height":1280,"VideoMD5":"9c6ae58daa962f07f0e49512f031d824","VideoUrl":"http://facefusion.ai.qcloud.com/face_fusion_prod/default/1308599495/20241018170524_6fcd39ba-754f-4c6a-a9a9-55bbce84e3b9_mt_1847164205092024320_v2_h264_result.mp4","Width":720}}}
     *
     * @param jobId
     */
    @SneakyThrows
    public static JsonNode queryVideoFaceFusionJobId(String jobId) {
        ObjectNode data = mapper.createObjectNode();
        data.put("JobId", jobId);
        //body参数{"JobId":"00441D783E3C803F9761BBF8E326028B756"}
        RequestBody body = RequestBody.create(JSON, data.toString());
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String auth = getAuth(VIDEO_FACEFUSION_HOST, CONTENT_TYPE, timestamp, data.toString());
        String requestUrl = "https://" + VIDEO_FACEFUSION_HOST;
        Request request = new Request.Builder().url(requestUrl).post(body)
            .header("Host", VIDEO_FACEFUSION_HOST)
            .header("X-TC-Timestamp", timestamp)
            .header("X-TC-Version", VERSION)
            .header("X-TC-Action", "QueryVideoFaceFusionJob")
            .header("X-TC-Region", REGION)
            .header("X-TC-Token", "")
            .header("X-TC-RequestClient", "SDK_JAVA_BAREBONE")
            .header("Authorization", auth)
            .build();
        log.info("{}-查询视频换脸任务-jobId:{}", LOG_TAG, jobId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            JsonNode jsonNode = mapper.readTree(content);
            log.info("{}-查询视频换脸任务-jobId:{},响应:{}", LOG_TAG, jobId, content);
            return jsonNode;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-查询视频换脸任务-jobId:{},异常:", LOG_TAG, jobId, e);
            return null;
        }
    }

    private static String getAuth(String host, String contentType, String timestamp, String body) throws NoSuchAlgorithmException, InvalidKeyException {
        String canonicalUri = "/";
        String canonicalQueryString = "";
        String canonicalHeaders = "content-type:" + contentType + "\nhost:" + host + "\n";
        String signedHeaders = "content-type;host";

        String hashedRequestPayload = sha256Hex(body.getBytes(StandardCharsets.UTF_8));
        String canonicalRequest = "POST"
            + "\n"
            + canonicalUri
            + "\n"
            + canonicalQueryString
            + "\n"
            + canonicalHeaders
            + "\n"
            + signedHeaders
            + "\n"
            + hashedRequestPayload;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        String date = sdf.format(new Date(Long.valueOf(timestamp + "000")));
        String service = host.split("\\.")[0];
        String credentialScope = date + "/" + service + "/" + "tc3_request";
        String hashedCanonicalRequest =
            sha256Hex(canonicalRequest.getBytes(StandardCharsets.UTF_8));
        String stringToSign =
            "TC3-HMAC-SHA256\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;

        byte[] secretDate = hmac256(("TC3" + TencentCloudLMServiceApiUtil.SecretKey).getBytes(StandardCharsets.UTF_8), date);
        byte[] secretService = hmac256(secretDate, service);
        byte[] secretSigning = hmac256(secretService, "tc3_request");
        String signature = printHexBinary(hmac256(secretSigning, stringToSign)).toLowerCase();
        return "TC3-HMAC-SHA256 "
            + "Credential="
            + TencentCloudLMServiceApiUtil.SecretId
            + "/"
            + credentialScope
            + ", "
            + "SignedHeaders="
            + signedHeaders
            + ", "
            + "Signature="
            + signature;
    }

    private static String sha256Hex(byte[] b) throws NoSuchAlgorithmException {
        MessageDigest md;
        md = MessageDigest.getInstance("SHA-256");
        byte[] d = md.digest(b);
        return printHexBinary(d).toLowerCase();
    }

    private static final char[] hexCode = "0123456789ABCDEF".toCharArray();

    private static String printHexBinary(byte[] data) {
        StringBuilder r = new StringBuilder(data.length * 2);
        for (byte b : data) {
            r.append(hexCode[(b >> 4) & 0xF]);
            r.append(hexCode[(b & 0xF)]);
        }
        return r.toString();
    }

    private static byte[] hmac256(byte[] key, String msg) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        mac.init(secretKeySpec);
        return mac.doFinal(msg.getBytes(StandardCharsets.UTF_8));
    }

    public static void main(String[] args) throws JsonProcessingException {
//        new TencentCloudVFFApiServiceUtil().videoFaceFusion("", "at_1846485675052244992", "mt_1846861687363448832", "https://static.cdyrjygs.com/test/dabfca91-bd8f-4acb-a4bd-d446792afa1d.jpg");
//        new TencentCloudVFFApiServiceUtil().queryVideoFaceFusionJobId("0045E4232A91EB4C05EBF066E81F40D2403");
    }
}
