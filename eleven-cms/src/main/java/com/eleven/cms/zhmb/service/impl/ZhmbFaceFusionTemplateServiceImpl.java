package com.eleven.cms.zhmb.service.impl;

import com.eleven.cms.zhmb.entity.ZhmbFaceFusionTemplate;
import com.eleven.cms.zhmb.mapper.ZhmbFaceFusionTemplateMapper;
import com.eleven.cms.zhmb.service.IZhmbFaceFusionTemplateService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: zhmb_face_fusion_template
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Service
public class ZhmbFaceFusionTemplateServiceImpl extends ServiceImpl<ZhmbFaceFusionTemplateMapper, ZhmbFaceFusionTemplate> implements IZhmbFaceFusionTemplateService {

}
