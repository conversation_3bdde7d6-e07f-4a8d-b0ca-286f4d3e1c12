package com.eleven.cms.zhmb.service.impl;

import com.eleven.cms.zhmb.entity.ZhmbImageToVideoTemplate;
import com.eleven.cms.zhmb.mapper.ZhmbImageToVideoTemplateMapper;
import com.eleven.cms.zhmb.service.IZhmbImageToVideoTemplateService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: zhmb_image_to_video_template
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Service
public class ZhmbImageToVideoTemplateServiceImpl extends ServiceImpl<ZhmbImageToVideoTemplateMapper, ZhmbImageToVideoTemplate> implements IZhmbImageToVideoTemplateService {

}
