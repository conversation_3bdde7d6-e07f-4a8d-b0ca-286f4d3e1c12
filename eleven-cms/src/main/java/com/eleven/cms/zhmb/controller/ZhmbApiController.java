package com.eleven.cms.zhmb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.zhmb.entity.ZhmbDanceTemplate;
import com.eleven.cms.zhmb.entity.ZhmbFaceFusionTemplate;
import com.eleven.cms.zhmb.entity.ZhmbImageToVideoTemplate;
import com.eleven.cms.zhmb.entity.ZhmbRing;
import com.eleven.cms.zhmb.remote.TencentCloudLMServiceApiUtil;
import com.eleven.cms.zhmb.remote.TencentCloudVFFApiServiceUtil;
import com.eleven.cms.zhmb.service.IZhmbDanceTemplateService;
import com.eleven.cms.zhmb.service.IZhmbFaceFusionTemplateService;
import com.eleven.cms.zhmb.service.IZhmbImageToVideoTemplateService;
import com.eleven.cms.zhmb.service.IZhmbRingService;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import com.tencentcloudapi.aiart.v20221229.models.ImageToImageResponse;
import com.tencentcloudapi.vclm.v20240523.models.SubmitImageAnimateJobResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * AI智绘模版
 *
 * @author: cai lei
 * @create: 2024-10-17 22:03
 */
@Api(tags = "zhmb_api")
@RestController
@RequestMapping("/api/zhmb")
@Slf4j
@RequiredArgsConstructor
public class ZhmbApiController {

    @Autowired
    private IZhmbFaceFusionTemplateService zhmbFaceFusionTemplateService;
    @Autowired
    private IZhmbImageToVideoTemplateService zhmbImageToVideoTemplateService;
    @Autowired
    private IZhmbDanceTemplateService zhmbDanceTemplateService;
    @Autowired
    private IZhmbRingService zhmbRingService;
    @Autowired
    private AliMediaService aliMediaService;


    @ApiOperation("换脸栏目模板")
    @GetMapping("/column/faceFusion/list")
    public Result queryColumnFaceFusionList(ZhmbFaceFusionTemplate zhmbFaceFusionTemplate,
                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                            HttpServletRequest req) {
        QueryWrapper<ZhmbFaceFusionTemplate> queryWrapper = QueryGenerator.initQueryWrapper(zhmbFaceFusionTemplate, req.getParameterMap());
        queryWrapper.orderByAsc("sort");
        Page<ZhmbFaceFusionTemplate> page = new Page<>(pageNo, pageSize);
        IPage<ZhmbFaceFusionTemplate> pageList = zhmbFaceFusionTemplateService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    @ApiOperation("动漫栏目模板")
    @GetMapping("/column/imageComic/list")
    public Result queryColumnImageComicList(ZhmbImageToVideoTemplate imageToVideoTemplate,
                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                            HttpServletRequest req) {
        QueryWrapper<ZhmbImageToVideoTemplate> queryWrapper = QueryGenerator.initQueryWrapper(imageToVideoTemplate, req.getParameterMap());
        queryWrapper.orderByAsc("sort");
        Page<ZhmbImageToVideoTemplate> page = new Page<>(pageNo, pageSize);
        IPage<ZhmbImageToVideoTemplate> pageList = zhmbImageToVideoTemplateService.page(page, queryWrapper);
        return Result.ok(pageList);
    }


    @ApiOperation("跳舞栏目模板")
    @GetMapping("/column/imageDance/list")
    public Result queryColumnImageDanceList(ZhmbDanceTemplate zhmbDanceTemplate,
                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                            HttpServletRequest req) {
        QueryWrapper<ZhmbDanceTemplate> queryWrapper = QueryGenerator.initQueryWrapper(zhmbDanceTemplate, req.getParameterMap());
        queryWrapper.orderByAsc("sort");
        Page<ZhmbDanceTemplate> page = new Page<>(pageNo, pageSize);
        IPage<ZhmbDanceTemplate> pageList = zhmbDanceTemplateService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    @ApiOperation("换脸")
    @GetMapping("/createRing/faceFusion")
    public Result faceFusion(@RequestParam String mobile,
                             @RequestParam String templateId,
                             @RequestParam String imageUrl) {
        try {
            ZhmbFaceFusionTemplate zhmbFaceFusionTemplate = zhmbFaceFusionTemplateService.getById(templateId);
            if (zhmbFaceFusionTemplate == null) {
                return Result.error("模板不存在");
            }
            String jobId = TencentCloudVFFApiServiceUtil.videoFaceFusion(mobile, zhmbFaceFusionTemplate.getProjectId(), zhmbFaceFusionTemplate.getModelId(), imageUrl);
            String ringId = zhmbRingService.createRing(jobId, mobile, imageUrl, "1");
            return Result.ok("铃声制作中", ringId);
        } catch (Exception e) {
            return Result.error("铃声制作失败");
        }
    }

    @ApiOperation("动漫")
    @GetMapping("/createRing/imageComic")
    public Result imageStyle(@RequestParam String mobile,
                             @RequestParam String templateId,
                             @RequestParam String imageUrl) {
        try {
            ZhmbImageToVideoTemplate zhmbImageToVideoTemplate = zhmbImageToVideoTemplateService.getById(templateId);
            if (zhmbImageToVideoTemplate == null) {
                return Result.error("模板不存在");
            }
            ImageToImageResponse imageToImageResponse = TencentCloudLMServiceApiUtil.imageStyle(mobile, imageUrl, zhmbImageToVideoTemplate.getImageStyle());
            String returnImageUrl = imageToImageResponse.getResultImage();
            String filePath = "tx" + "/" + IdWorker.get32UUID() + ".raw";
            String uploadUrl = aliMediaService.putObjectRemoteUrl(filePath, returnImageUrl);
            List<String> imageList = new ArrayList<>();
            imageList.add(imageUrl);
            imageList.add(uploadUrl);
            String jobId = aliMediaService.produceVideoForAi(AliMediaProperties.JOB_QUEUE_TAG_TXAI, imageList, zhmbImageToVideoTemplate);
            String ringId = zhmbRingService.createRing(jobId, mobile, imageUrl, "3");
            return Result.ok("铃声制作中", ringId);
        } catch (Exception e) {
            return Result.error("铃声制作失败");
        }
    }

    @ApiOperation("跳舞")
    @GetMapping("/createRing/imageDance")
    public Result imageDance(@RequestParam String mobile,
                             @RequestParam String templateId,
                             @RequestParam String imageUrl) {
        try {
            ZhmbDanceTemplate zhmbDanceTemplate = zhmbDanceTemplateService.getById(templateId);
            if (zhmbDanceTemplate == null) {
                return Result.error("模板不存在");
            }
            SubmitImageAnimateJobResponse submitImageAnimateJobResponse = TencentCloudLMServiceApiUtil.imageDance(mobile, imageUrl, zhmbDanceTemplate.getTemplateId());
            String jobId = submitImageAnimateJobResponse.getJobId();
            String ringId = zhmbRingService.createRing(jobId, mobile, imageUrl, "2");
            return Result.ok("铃声制作中", ringId);
        } catch (Exception e) {
            return Result.error("铃声制作失败");
        }
    }

    @ApiOperation("铃声状态查询")
    @GetMapping("/queryRingStatus")
    public Result queryRingStatus(@RequestParam String ringId) {
        try {
            ZhmbRing zhmbRing = zhmbRingService.getById(ringId);
            return Result.ok("", zhmbRing.getVideoUrl());
        } catch (Exception e) {
            return Result.error("查询铃声状态错误:{}", e);
        }
    }

    @ApiOperation("铃声列表")
    @GetMapping("/ring/list")
    public Result ringList(ZhmbRing zhmbRing,
                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                           HttpServletRequest req) {
        if (StringUtils.isBlank(zhmbRing.getMobile())) {
            return Result.ok();
        }
        QueryWrapper<ZhmbRing> queryWrapper = QueryGenerator.initQueryWrapper(zhmbRing, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        Page<ZhmbRing> page = new Page<>(pageNo, pageSize);
        IPage<ZhmbRing> pageList = zhmbRingService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    @ApiOperation("铃声详细")
    @GetMapping("/ring/info")
    public Result ringInfo(@RequestParam String ringId) {
        return Result.ok(zhmbRingService.getById(ringId));
    }


    @ApiOperation("删除铃声")
    @GetMapping("/ring/delete")
    public Result deleteRing(@RequestParam String ringId,
                             HttpServletRequest req) {
        ZhmbRing zhmbRing = zhmbRingService.getById(ringId);
        if (zhmbRing != null && "1".equals(zhmbRing.getRingStatus())) {
            zhmbRingService.removeById(ringId);
            return Result.ok("删除成功");
        }
        return Result.ok("删除失败");
    }

}
