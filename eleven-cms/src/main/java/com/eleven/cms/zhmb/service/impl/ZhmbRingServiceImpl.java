package com.eleven.cms.zhmb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.zhmb.entity.ZhmbRing;
import com.eleven.cms.zhmb.mapper.ZhmbRingMapper;
import com.eleven.cms.zhmb.service.IZhmbRingService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Description: zhmb_ring
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Service
public class ZhmbRingServiceImpl extends ServiceImpl<ZhmbRingMapper, ZhmbRing> implements IZhmbRingService {

    @Override
    public String createRing(String jobId, String mobile, String imageUrl, String ringType) {
        ZhmbRing zhmbRing = new ZhmbRing();
        zhmbRing.setMobile(mobile);
        zhmbRing.setRingStatus("0");
        zhmbRing.setCreateTime(new Date());
        zhmbRing.setJobId(jobId);
        zhmbRing.setRingType(ringType);
        zhmbRing.setCoverUrl(imageUrl);
        this.save(zhmbRing);
        return zhmbRing.getId();
    }

    @Override
    public void aliJobFinishHandle(String jobId, String mediaUrl) {
        this.lambdaUpdate().eq(ZhmbRing::getJobId, jobId).eq(ZhmbRing::getRingType, "3").set(ZhmbRing::getRingStatus, "1").set(ZhmbRing::getUpdateTime, new Date()).set(ZhmbRing::getVideoUrl, mediaUrl).update();
    }


}
