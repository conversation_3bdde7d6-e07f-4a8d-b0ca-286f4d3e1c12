package com.eleven.cms.zhmb.task;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.job.TaskCondition;
import com.eleven.cms.zhmb.entity.ZhmbRing;
import com.eleven.cms.zhmb.remote.TencentCloudLMServiceApiUtil;
import com.eleven.cms.zhmb.remote.TencentCloudVFFApiServiceUtil;
import com.eleven.cms.zhmb.service.IZhmbRingService;
import com.eleven.qycl.service.AliMediaService;
import com.fasterxml.jackson.databind.JsonNode;
import com.tencentcloudapi.vclm.v20240523.models.DescribeImageAnimateJobResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @author: cai lei
 * @create: 2024-10-18 16:00
 */

@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class FaceFusionTask {

    @Autowired
    private IZhmbRingService zhmbRingService;
    @Autowired
    private AliMediaService aliMediaService;


    //@Scheduled(fixedDelay = 20 * 1000, initialDelay = 30 * 1000)
    public void queryFaceFusionJob() throws Exception {
        //当前时间大于创建时间5分钟的数据并且状态为制作种才参与轮询
        List<ZhmbRing> ringList = zhmbRingService.lambdaQuery().lt(ZhmbRing::getCreateTime, LocalDateTime.now().minusSeconds(30)).eq(ZhmbRing::getRingType, "1").eq(ZhmbRing::getRingStatus, "0").list();
        ringList.forEach(ring -> {

            JsonNode faceFusionNode = TencentCloudVFFApiServiceUtil.queryVideoFaceFusionJobId(ring.getJobId());
            String jobStatusCode = faceFusionNode.at("/Response/JobStatusCode").asText();
            if ("7".equals(jobStatusCode)) {
                String returnVideoUrl = faceFusionNode.at("/Response/VideoFaceFusionOutput/VideoUrl").asText();
                ring.setRingStatus("1");
                try {
                    String filePath = "tx" + "/" + IdWorker.get32UUID() + ".mp4";
                    String videoUrl = aliMediaService.putObjectRemoteUrl(filePath, returnVideoUrl);
                    ring.setVideoUrl(videoUrl);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                ring.setUpdateTime(new Date());
                zhmbRingService.updateById(ring);
            } else if ("5".equals(jobStatusCode)) {
                ring.setRingStatus("2");
                ring.setUpdateTime(new Date());
                zhmbRingService.updateById(ring);
            }

        });
    }

}
