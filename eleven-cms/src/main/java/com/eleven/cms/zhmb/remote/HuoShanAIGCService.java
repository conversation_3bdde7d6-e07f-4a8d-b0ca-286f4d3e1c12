package com.eleven.cms.zhmb.remote;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import com.volcengine.service.visual.IVisualService;
import com.volcengine.service.visual.impl.VisualServiceImpl;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

/**
 * 火山AIGC服务
 *
 * @author: cai lei
 * @create: 2024-10-14 1
 * //@Service0:24
 * //
 */
@Service
public class HuoShanAIGCService {


    @Autowired
    AliMediaService aliMediaService;

    /**
     * {
     * "code": 10000,
     * "data": {
     * "1332073689": 1332073689,
     * "3Dpet_Disney01_strength_clip": 0.6,
     * "3Dpet_Disney01_strength_model": 0.6,
     * "algorithm_base_resp": {
     * "status_code": 0,
     * "status_message": "Success"
     * },
     * "apply_id_layer": "3,4,5,6,7,8,9,10",
     * "binary_data_base64": [
     * "/9xxx",  //重点关注此返回，转换后的图片Base64
     * ],
     * "clip_skip": -2,
     * "cn_mode": 0,
     * "comfyui_cost": 4,
     * "controlnet_weight": 0.4,
     * "ddim_steps": 20,
     * "i2t_tag_text": "",
     * "id_weight": 0,
     * "image_urls": [
     * "https://xxx"
     * ],
     * "long_resolution": 832,
     * "lora_map": {
     * "3Dpet_Disney01": {
     * "strength_clip": 0.6,
     * "strength_model": 0.6
     * }
     * },
     * "prompt": "",
     * "sampler_name": "dpmpp_2m",
     * "scale": 5,
     * "scheduler": "karras",
     * "seed": -1,
     * "strength": 0.6,
     * "sub_prompts": [
     * ],
     * "u_prompt": "(((embedding:EasyNegative))), (((wrong clothing))), (colorful hair), paintings, 2d, sketch, (nsfw), (nude), (messy hair), high skull, (big head), fluttering hair, (limb errors), wrong eyes, bad-artist, embedding:badhandv4, flag, text, logo, trademark, brand, nudity, bare, uncovered, naked, breasts, plump breasts, big breasts, nipples, butt, ass, bad hands, broken limbs, broken fingers, extra fingers, fewer fingers, strange fingers, bad hand, skin blemishes, (ugly:1.2), (low quality:2), (normal quality:2), ((open mouth))"
     * },
     * "message": "Success",
     * "request_id": "202405201722544E4A22904AB5C20B42D5",
     * "status": 10000,
     * "time_elapsed": "5.849820164s"
     * }
     *
     * @param reqJson
     * @param imageUrlArray
     */


    @SneakyThrows
    private List generateAigcImageStyle(String reqJson, String[] imageUrlArray) {
        IVisualService visualService = VisualServiceImpl.getInstance();
        // call below method if you dont set ak and sk in ～/.vcloud/config
        visualService.setAccessKey("AKLTNWQ0OTZlOGYyZjhiNGIzMGI3YTQ0NjM5MzdlNzZhNzk");
        visualService.setSecretKey("TkRWbE5UWTJOMlZtWWpOak5HUTNZMkZpWldFMFpqSTBZMkV4WW1RMllUSQ==");
        //请求Body(查看接口文档请求参数-请求示例，将请求参数内容复制到此)
        JSONObject req = (JSONObject) JSONObject.parse(reqJson);
        JSONArray imageArray = new JSONArray();
        for (String imageUrl : imageUrlArray) {
            imageArray.add(imageUrl);
        }
        req.put("image_urls", imageArray);
        JSONObject rep = (JSONObject) visualService.cvProcess(req);
        JSONObject data = rep.getJSONObject("data");
        List<String> generateImageList = new ArrayList<>();
        data.getJSONArray("binary_data_base64").forEach(base64Str -> generateImageList.add((String) base64Str));
        return generateImageList;
    }


    public void generateVideo(String[] imageUrl, String imageStyleType) {
//        List<String> imageList = new ArrayList<>();
//        ImageStyleEnum.getImageStyleList(imageStyleType).forEach(imageStyle ->
//            imageList.addAll(generateAigcImageStyle(imageStyle.json, imageUrl))
//        );
//        List<String> ossImageList = new ArrayList<>();
//        ossImageList.add(imageUrl[0]);
//        imageList.forEach(imageBase64Str -> {
//            String filePathUrl = "huoshan" + "/" + IdWorker.get32UUID() + ".raw";
//            String ossUrl = aliMediaService.putObjectBase64(filePathUrl, imageBase64Str);
//            ossImageList.add(ossUrl);
//        });
//        https://ims-media.oss-cn-beijing.aliyuncs.com/audio/mshx.mp3
//        aliMediaService.produceVideoForAi(AliMediaProperties.JOB_QUEUE_TAG_QYCL, Arrays.asList(imageUrl), "https://ims-media.oss-cn-beijing.aliyuncs.com/audio/ybwgkn.mp3", "", "", "",imageStyleType);
    }







    public static void main(String[] args) {
        IVisualService visualService = VisualServiceImpl.getInstance();
        // call below method if you dont set ak and sk in ～/.vcloud/config
        visualService.setAccessKey("AKLTNWQ0OTZlOGYyZjhiNGIzMGI3YTQ0NjM5MzdlNzZhNzk");
        visualService.setSecretKey("TkRWbE5UWTJOMlZtWWpOak5HUTNZMkZpWldFMFpqSTBZMkV4WW1RMllUSQ==");

        JSONObject req = new JSONObject();
        //请求Body(查看接口文档请求参数-请求示例，将请求参数内容复制到此)
        req.put("req_key", "img2img_water_ink_style");
        String loraMapStr = "{'0107inkman': {'strength_model': 0.7000000000000001, 'strength_clip': 0.7000000000000001},'1228ink': {'strength_model': 0.1, 'strength_clip': 0.1},'add_detail': {'strength_model':-0.5,'strength_clip':-0.5}}";
//        String loraMapStr = "{'Cateye_AT45': {'strength_model': 0.2, 'strength_clip': 0.2},'CATSEYEcp001': {'strength_model': 0.2, 'strength_clip': 0.2}}";
        req.put("lora_map", JSONObject.parse(loraMapStr));

        JSONArray imageArray = new JSONArray();
        imageArray.add("https://ims-media.oss-cn-beijing.aliyuncs.com/test/1886d5ff-074e-4f99-8f3e-d48c3c77b75d.jpg");
        req.put("image_urls", imageArray);


        String json = "{'req_key':'img2img_water_ink_style','image_urls':['https://ims-media.oss-cn-beijing.aliyuncs.com/test/1886d5ff-074e-4f99-8f3e-d48c3c77b75d.jpg'],'strength':0.6,'seed':-1,'scale':8,'ddim_steps':20,'lora_map':{'Cateye_AT45':{'strength_model':0.2,'strength_clip':0.2},'CATSEYEcp001':{'strength_model':0.2,'strength_clip':0.2}},'clip_skip':1,'controlnet_weight':1,'sampler_name':'dpmpp_2m','scheduler':'karras','long_resolution':832,'cn_mode':0,'id_weight':1.0,'apply_id_layer':'2,3,4,5,6,7,8,9,10,11,12','tagger_settings':{'switch':false},'vae_choice':1}";

        try {
            JSONObject rep = (JSONObject) visualService.cvProcess(JSONObject.parse(json));
            JSONObject data = rep.getJSONObject("data");
            String base64Str = (String) data.getJSONArray("binary_data_base64").get(0);
            FileUtils.writeByteArrayToFile(new File("D:\\huoshan\\1.png"), Base64.getDecoder().decode(base64Str));
        } catch (Exception e) {
            e.printStackTrace();
        }

//        try {
//            new HuoShanAIGCService().generateVideo(new String[]{"https://ims-media.oss-cn-beijing.aliyuncs.com/test/20241012095546.png"});
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
    }
}
