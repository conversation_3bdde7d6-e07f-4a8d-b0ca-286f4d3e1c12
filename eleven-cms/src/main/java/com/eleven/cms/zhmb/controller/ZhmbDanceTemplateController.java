package com.eleven.cms.zhmb.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.zhmb.entity.ZhmbDanceTemplate;
import com.eleven.cms.zhmb.service.IZhmbDanceTemplateService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: zhmb_dance_template
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Api(tags = "zhmb_dance_template")
@RestController
@RequestMapping("/cms.zhmb/zhmbDanceTemplate")
@Slf4j
public class ZhmbDanceTemplateController extends JeecgController<ZhmbDanceTemplate, IZhmbDanceTemplateService> {
    @Autowired
    private IZhmbDanceTemplateService zhmbDanceTemplateService;

    /**
     * 分页列表查询
     *
     * @param zhmbDanceTemplate
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "zhmb_dance_template-分页列表查询")
    @ApiOperation(value = "zhmb_dance_template-分页列表查询", notes = "zhmb_dance_template-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ZhmbDanceTemplate zhmbDanceTemplate,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ZhmbDanceTemplate> queryWrapper = QueryGenerator.initQueryWrapper(zhmbDanceTemplate, req.getParameterMap());
        Page<ZhmbDanceTemplate> page = new Page<ZhmbDanceTemplate>(pageNo, pageSize);
        IPage<ZhmbDanceTemplate> pageList = zhmbDanceTemplateService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param zhmbDanceTemplate
     * @return
     */
    @AutoLog(value = "zhmb_dance_template-添加")
    @ApiOperation(value = "zhmb_dance_template-添加", notes = "zhmb_dance_template-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ZhmbDanceTemplate zhmbDanceTemplate) {
        zhmbDanceTemplateService.save(zhmbDanceTemplate);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zhmbDanceTemplate
     * @return
     */
    @AutoLog(value = "zhmb_dance_template-编辑")
    @ApiOperation(value = "zhmb_dance_template-编辑", notes = "zhmb_dance_template-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ZhmbDanceTemplate zhmbDanceTemplate) {
        zhmbDanceTemplateService.updateById(zhmbDanceTemplate);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "zhmb_dance_template-通过id删除")
    @ApiOperation(value = "zhmb_dance_template-通过id删除", notes = "zhmb_dance_template-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        zhmbDanceTemplateService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "zhmb_dance_template-批量删除")
    @ApiOperation(value = "zhmb_dance_template-批量删除", notes = "zhmb_dance_template-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.zhmbDanceTemplateService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "zhmb_dance_template-通过id查询")
    @ApiOperation(value = "zhmb_dance_template-通过id查询", notes = "zhmb_dance_template-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ZhmbDanceTemplate zhmbDanceTemplate = zhmbDanceTemplateService.getById(id);
        if (zhmbDanceTemplate == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(zhmbDanceTemplate);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param zhmbDanceTemplate
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZhmbDanceTemplate zhmbDanceTemplate) {
        return super.exportXls(request, zhmbDanceTemplate, ZhmbDanceTemplate.class, "zhmb_dance_template");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZhmbDanceTemplate.class);
    }

}
