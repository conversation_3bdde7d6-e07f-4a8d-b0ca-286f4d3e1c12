package com.eleven.cms.zhmb.task;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.job.TaskCondition;
import com.eleven.cms.zhmb.entity.ZhmbRing;
import com.eleven.cms.zhmb.remote.TencentCloudLMServiceApiUtil;
import com.eleven.cms.zhmb.service.IZhmbRingService;
import com.eleven.qycl.service.AliMediaService;
import com.tencentcloudapi.vclm.v20240523.models.DescribeImageAnimateJobResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @author: cai lei
 * @create: 2024-10-18 16:00
 */

@Slf4j
@Service
@Profile("prod")
@Conditional({TaskCondition.class})
public class ImageDanceTask {

    @Autowired
    private IZhmbRingService zhmbRingService;
    @Autowired
    private AliMediaService aliMediaService;


    //@Scheduled(fixedDelay = 2 * 60 * 1000, initialDelay = 60 * 1000)
    public void queryImageDanceJob() throws Exception {
        //当前时间大于创建时间5分钟的数据并且状态为制作种才参与轮询
        List<ZhmbRing> ringList = zhmbRingService.lambdaQuery().lt(ZhmbRing::getCreateTime, LocalDateTime.now().minusMinutes(2)).eq(ZhmbRing::getRingType, "2").eq(ZhmbRing::getRingStatus, "0").list();
        ringList.forEach(ring -> {
            try {
                DescribeImageAnimateJobResponse describeImageAnimateJobResponse = TencentCloudLMServiceApiUtil.queryImageDanceJob(ring.getJobId());
                if ("DONE".equals(describeImageAnimateJobResponse.getStatus())) {
                    ring.setRingStatus("1");
                    String filePath = "tx" + "/" + IdWorker.get32UUID() + ".mp4";
                    String videoUrl = aliMediaService.putObjectRemoteUrl(filePath, describeImageAnimateJobResponse.getResultVideoUrl());
                    ring.setVideoUrl(videoUrl);
                    ring.setUpdateTime(new Date());
                    zhmbRingService.updateById(ring);
                } else if ("FAIL".equals(describeImageAnimateJobResponse.getStatus())) {
                    ring.setRingStatus("2");
                    ring.setUpdateTime(new Date());
                    zhmbRingService.updateById(ring);
                }
            } catch (Exception e) {
                ring.setRingStatus("2");
                ring.setUpdateTime(new Date());
                zhmbRingService.updateById(ring);
            }
        });
    }

}
