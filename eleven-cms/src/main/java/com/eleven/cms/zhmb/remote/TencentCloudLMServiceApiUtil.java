package com.eleven.cms.zhmb.remote;

import com.google.common.collect.ImmutableMap;
import com.tencentcloudapi.aiart.v20221229.AiartClient;
import com.tencentcloudapi.aiart.v20221229.models.ImageToImageRequest;
import com.tencentcloudapi.aiart.v20221229.models.ImageToImageResponse;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.vclm.v20240523.VclmClient;
import com.tencentcloudapi.vclm.v20240523.models.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;

import java.util.Map;

/**
 * @author: cai lei
 * @create: 2024-10-16 16:08
 * 腾讯云大模型创作service工具类
 */
@Slf4j
public class TencentCloudLMServiceApiUtil {

    public final static String SecretId = "AKID53oNjvuvtgFbksqb9WUItIUVKzRgswnW";
    public final static String SecretKey = "d7R0NXTZZSGwQisreFfx88xe9UkolYBd";
    private final static Credential credential = new Credential(SecretId, SecretKey);
    public static final String LOG_TAG = "腾讯云大模型创作API";

    /**
     * 风格ID，取值说明：
     * 2d_anime 2D动漫；
     * 3d_cartoon 3D卡通；
     * 3d_china 3D国潮；
     * pixel_art 像素风。
     *
     * @param videoUrl
     */
    @SneakyThrows
    public void videoStyle(String videoUrl) {
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("vclm.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        VclmClient client = new VclmClient(credential, "ap-guangzhou", clientProfile);
        // 实例化一个请求对象,每个接口都会对应一个request对象
        SubmitVideoStylizationJobRequest req = new SubmitVideoStylizationJobRequest();
        req.setStyleId("2d_anime");
        req.setStyleStrength("medium");
        req.setVideoUrl(videoUrl);
        // 返回的resp是一个SubmitVideoStylizationJobResponse的实例，与请求对象对应
        SubmitVideoStylizationJobResponse resp = client.SubmitVideoStylizationJob(req);
        String jobId = resp.getJobId();
        // 输出json格式的字符串回包
        System.out.println(AbstractModel.toJsonString(resp));
        System.out.println(jobId);
    }

    @SneakyThrows
    public void queryVideoStyleJob() {
        // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
        // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
        // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("vclm.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        VclmClient client = new VclmClient(credential, "ap-guangzhou", clientProfile);
        // 实例化一个请求对象,每个接口都会对应一个request对象
        DescribeVideoStylizationJobRequest req = new DescribeVideoStylizationJobRequest();
        // 返回的resp是一个DescribeVideoStylizationJobResponse的实例，与请求对象对应
        req.setJobId("b5850e6c6a4a4339b730f58c8dea9be4");
        DescribeVideoStylizationJobResponse resp = client.DescribeVideoStylizationJob(req);
        // 输出json格式的字符串回包
        System.out.println(AbstractModel.toJsonString(resp));
    }

    /**
     * 图片跳舞
     * TemplateId
     * ke3 科目三；
     * tuziwu 兔子舞；
     * huajiangwu 划桨舞
     *
     * @param imageUrl {"JobId":"1238720783082266624","RequestId":"9a6c93bc-4348-413f-8806-40a0e09af327"}
     */
    public static SubmitImageAnimateJobResponse imageDance(String mobile, String imageUrl, String templateId) {
        try {
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vclm.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VclmClient client = new VclmClient(credential, "ap-guangzhou", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            SubmitImageAnimateJobRequest req = new SubmitImageAnimateJobRequest();
            req.setImageUrl(imageUrl);
            req.setTemplateId(templateId);
            req.setEnableAudio(true);
            log.info("用户:{},提交图片跳舞任务,imageUrl:{},templateId:{}", mobile, imageUrl, templateId);
            // 返回的resp是一个SubmitImageAnimateJobResponse的实例，与请求对象对应
            SubmitImageAnimateJobResponse resp = client.SubmitImageAnimateJob(req);
            log.info("用户:{},提交图片跳舞任务,imageUrl:{},templateId:{},响应:{}", mobile, imageUrl, templateId, AbstractModel.toJsonString(resp));
            return resp;
        } catch (Exception e) {
            log.error("用户:{},提交图片跳舞任务,imageUrl:{},templateId:{},异常:", mobile, imageUrl, templateId, e);
            throw new JeecgBootException("图片跳舞错误");
        }
    }

    /**
     * 图片跳舞任务查询
     *
     * @param jobId {"Status":"DONE","ErrorCode":"","ErrorMessage":"","ResultVideoUrl":"https://vcg-prod-1258344699.cos.ap-guangzhou.myqcloud.com/image_animate/output/1308599495/93f0b9d0-2a38-4262-8e7b-07075febac0c.mp4?q-sign-algorithm=sha1&q-ak=AKID8PcPB70x2Ibr49A1vnkdSBcTJ6lMrLgp&q-sign-time=1729074497%3B1729160897&q-key-time=1729074497%3B1729160897&q-header-list=host&q-url-param-list=&q-signature=b7f70604a0a2e9834c5068fe7a497bb96a5e8d5b","MaskVideoUrl":"","RequestId":"29db27bc-a03b-422f-b8d6-7ed82559492c"}
     */
    @SneakyThrows
    public static DescribeImageAnimateJobResponse queryImageDanceJob(String jobId) {
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("vclm.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        VclmClient client = new VclmClient(credential, "ap-guangzhou", clientProfile);
        // 实例化一个请求对象,每个接口都会对应一个request对象
        DescribeImageAnimateJobRequest req = new DescribeImageAnimateJobRequest();
        req.setJobId(jobId);
        // 返回的resp是一个DescribeImageAnimateJobResponse的实例，与请求对象对应
        DescribeImageAnimateJobResponse resp = client.DescribeImageAnimateJob(req);
        System.out.println(AbstractModel.toJsonString(resp));
        return resp;
    }


    /**
     * 图像风格化
     *
     * @param imageUrl
     */
    public static ImageToImageResponse imageStyle(String mobile, String imageUrl, String styleType) {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("aiart.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            AiartClient client = new AiartClient(credential, "ap-guangzhou", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            ImageToImageRequest req = new ImageToImageRequest();
            req.setInputUrl(imageUrl);
            req.setRspImgType("url");
            req.setLogoAdd(0L);
            req.setStyles(new String[]{styleType});
            // 返回的resp是一个ImageToImageResponse的实例，与请求对象对应
            log.info("用户:{},图像风格化,imageUrl:{},styleType:{}请求", mobile, imageUrl, styleType);
            ImageToImageResponse resp = client.ImageToImage(req);
            log.info("用户:{},图像风格化,imageUrl:{},styleType:{},响应:{}", mobile, imageUrl, styleType, AbstractModel.toJsonString(resp));
            return resp;
        } catch (Exception e) {
            log.info("用户:{},图像风格化,imageUrl:{},styleType:{},异常:", mobile, imageUrl, styleType, e);
            throw new JeecgBootException("图像风格化错误");
        }
    }



    public static Map<String, String> IMAGE_STYLE_MAP = new ImmutableMap.Builder()
        .put("104", "水彩画")
        .put("107", "卡通插画")
        .put("116", "3D卡通")
        .put("201", "日系动漫")
        .put("203", "唯美古风")
        .put("210", "2.5D动画")
        .put("120", "木雕")
        .put("121", "黏土")
        .put("123", "清新日漫")
        .put("124", "小人书插画")
        .put("125", "国风工笔")
        .put("126", "玉石")
        .put("127", "瓷器")
        .put("135", "毛毡（亚洲版）")
        .put("128", "毛毡（欧美版）")
        .put("129", "美式复古")
        .put("130", "蒸汽朋克")
        .put("131", "赛博朋克")
        .put("132", "素描")
        .put("133", "莫奈花园")
        .put("134", "厚涂手绘").build();


    public static void main(String[] args) {
//        String videoUrl = "https://ims-media.oss-cn-beijing.aliyuncs.com/output/20241015/3c0e88987f608223a7c8eb8baaf0ea75.mp4";
//        new TengxunYunVCLMService().videoStyle(videoUrl);
        String imageUrl = "https://img95.699pic.com/photo/32100/3121.jpg_wh860.jpg";
//        new TengxunYunVCLMService().queryVideoStyleJob();
//        new TencentCloudLMServiceApiUtil().imageDance(imageUrl, "ke3");
//        new VCLMServiceApiUtil().queryImageDanceJob("1238339107692945408");
//        new TencentCloudLMServiceApiUtil().imageStyle("https://static.cdyrjygs.com/test/20241016203406.png");

        TencentCloudLMServiceApiUtil tencentCloudLMServiceApiUtil = new TencentCloudLMServiceApiUtil();


//        String[] imageArray = {"https://ims-media.oss-cn-beijing.aliyuncs.com/test/20241017210425.png",
////            "https://ims-media.oss-cn-beijing.aliyuncs.com/test/20241017210512.png",
////            "https://ims-media.oss-cn-beijing.aliyuncs.com/test/20241017210508.png",
//            "https://ims-media.oss-cn-beijing.aliyuncs.com/test/20241017210502.png",
//            "https://ims-media.oss-cn-beijing.aliyuncs.com/test/20241017210457.png",
//            "https://ims-media.oss-cn-beijing.aliyuncs.com/test/20241017210429.png"};
//        Arrays.stream(imageArray).forEach(img -> {
//            ImageToImageResponse imageToImageResponse = tencentCloudLMServiceApiUtil.imageStyle(img, "201");
//            String resultImage = imageToImageResponse.getResultImage();
//            System.out.println("resultImage = " + resultImage);
//
//        });
        IMAGE_STYLE_MAP.keySet().stream().forEach(entry -> {
            ImageToImageResponse imageToImageResponse = tencentCloudLMServiceApiUtil.imageStyle("", "https://ims-media.oss-cn-beijing.aliyuncs.com/gazi/20241018143901.png", entry);
            String resultImage = imageToImageResponse.getResultImage();
            System.out.println("风格:" + IMAGE_STYLE_MAP.get(entry) + " ,图片地址:" + resultImage);
        });

    }




}
