package com.eleven.cms.zhmb.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: zhmb_face_fusion_template
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Data
@TableName("zhmb_face_fusion_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "zhmb_face_fusion_template对象", description = "zhmb_face_fusion_template")
public class ZhmbFaceFusionTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 活动id
     */
    @Excel(name = "活动id", width = 15)
    @ApiModelProperty(value = "活动id")
    private String projectId;
    /**
     * 素材id
     */
    @Excel(name = "素材id", width = 15)
    @ApiModelProperty(value = "素材id")
    private String modelId;
    /**
     * 腾讯云任务id
     */
    @Excel(name = "腾讯云任务id", width = 15)
    @ApiModelProperty(value = "腾讯云任务id")
    private String jobId;
    /**
     * 栏目类型
     */
    @Excel(name = "栏目类型", width = 15)
    @ApiModelProperty(value = "栏目类型")
    @Dict(dicCode = "zhmb_column_code")
    private String columnType;
    /**
     * 封面图片
     */
    @Excel(name = "封面图片", width = 15)
    @ApiModelProperty(value = "封面图片")
    private String coverUrl;
    /**
     * 视频地址
     */
    @Excel(name = "视频地址", width = 15)
    @ApiModelProperty(value = "视频地址")
    private String videoUrl;
    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
