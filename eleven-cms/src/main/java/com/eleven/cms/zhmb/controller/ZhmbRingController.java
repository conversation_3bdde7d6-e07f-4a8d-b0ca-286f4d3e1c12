package com.eleven.cms.zhmb.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.zhmb.entity.ZhmbRing;
import com.eleven.cms.zhmb.service.IZhmbRingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: zhmb_ring
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Api(tags = "zhmb_ring")
@RestController
@RequestMapping("/cms.zhmb/zhmbRing")
@Slf4j
public class ZhmbRingController extends JeecgController<ZhmbRing, IZhmbRingService> {
    @Autowired
    private IZhmbRingService zhmbRingService;

    /**
     * 分页列表查询
     *
     * @param zhmbRing
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "zhmb_ring-分页列表查询")
    @ApiOperation(value = "zhmb_ring-分页列表查询", notes = "zhmb_ring-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ZhmbRing zhmbRing,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ZhmbRing> queryWrapper = QueryGenerator.initQueryWrapper(zhmbRing, req.getParameterMap());
        Page<ZhmbRing> page = new Page<ZhmbRing>(pageNo, pageSize);
        IPage<ZhmbRing> pageList = zhmbRingService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param zhmbRing
     * @return
     */
    @AutoLog(value = "zhmb_ring-添加")
    @ApiOperation(value = "zhmb_ring-添加", notes = "zhmb_ring-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ZhmbRing zhmbRing) {
        zhmbRingService.save(zhmbRing);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zhmbRing
     * @return
     */
    @AutoLog(value = "zhmb_ring-编辑")
    @ApiOperation(value = "zhmb_ring-编辑", notes = "zhmb_ring-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ZhmbRing zhmbRing) {
        zhmbRingService.updateById(zhmbRing);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "zhmb_ring-通过id删除")
    @ApiOperation(value = "zhmb_ring-通过id删除", notes = "zhmb_ring-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        zhmbRingService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "zhmb_ring-批量删除")
    @ApiOperation(value = "zhmb_ring-批量删除", notes = "zhmb_ring-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.zhmbRingService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "zhmb_ring-通过id查询")
    @ApiOperation(value = "zhmb_ring-通过id查询", notes = "zhmb_ring-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ZhmbRing zhmbRing = zhmbRingService.getById(id);
        if (zhmbRing == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(zhmbRing);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param zhmbRing
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZhmbRing zhmbRing) {
        return super.exportXls(request, zhmbRing, ZhmbRing.class, "zhmb_ring");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZhmbRing.class);
    }

}
