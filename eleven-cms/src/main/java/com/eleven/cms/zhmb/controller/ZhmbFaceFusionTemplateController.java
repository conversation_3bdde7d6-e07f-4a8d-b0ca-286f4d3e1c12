package com.eleven.cms.zhmb.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.zhmb.entity.ZhmbFaceFusionTemplate;
import com.eleven.cms.zhmb.service.IZhmbFaceFusionTemplateService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: zhmb_face_fusion_template
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Api(tags = "zhmb_face_fusion_template")
@RestController
@RequestMapping("/cms.zhmb/zhmbFaceFusionTemplate")
@Slf4j
public class ZhmbFaceFusionTemplateController extends JeecgController<ZhmbFaceFusionTemplate, IZhmbFaceFusionTemplateService> {
    @Autowired
    private IZhmbFaceFusionTemplateService zhmbFaceFusionTemplateService;

    /**
     * 分页列表查询
     *
     * @param zhmbFaceFusionTemplate
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "zhmb_face_fusion_template-分页列表查询")
    @ApiOperation(value = "zhmb_face_fusion_template-分页列表查询", notes = "zhmb_face_fusion_template-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ZhmbFaceFusionTemplate zhmbFaceFusionTemplate,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ZhmbFaceFusionTemplate> queryWrapper = QueryGenerator.initQueryWrapper(zhmbFaceFusionTemplate, req.getParameterMap());
        Page<ZhmbFaceFusionTemplate> page = new Page<ZhmbFaceFusionTemplate>(pageNo, pageSize);
        IPage<ZhmbFaceFusionTemplate> pageList = zhmbFaceFusionTemplateService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param zhmbFaceFusionTemplate
     * @return
     */
    @AutoLog(value = "zhmb_face_fusion_template-添加")
    @ApiOperation(value = "zhmb_face_fusion_template-添加", notes = "zhmb_face_fusion_template-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ZhmbFaceFusionTemplate zhmbFaceFusionTemplate) {
        zhmbFaceFusionTemplateService.save(zhmbFaceFusionTemplate);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zhmbFaceFusionTemplate
     * @return
     */
    @AutoLog(value = "zhmb_face_fusion_template-编辑")
    @ApiOperation(value = "zhmb_face_fusion_template-编辑", notes = "zhmb_face_fusion_template-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ZhmbFaceFusionTemplate zhmbFaceFusionTemplate) {
        zhmbFaceFusionTemplateService.updateById(zhmbFaceFusionTemplate);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "zhmb_face_fusion_template-通过id删除")
    @ApiOperation(value = "zhmb_face_fusion_template-通过id删除", notes = "zhmb_face_fusion_template-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        zhmbFaceFusionTemplateService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "zhmb_face_fusion_template-批量删除")
    @ApiOperation(value = "zhmb_face_fusion_template-批量删除", notes = "zhmb_face_fusion_template-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.zhmbFaceFusionTemplateService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "zhmb_face_fusion_template-通过id查询")
    @ApiOperation(value = "zhmb_face_fusion_template-通过id查询", notes = "zhmb_face_fusion_template-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ZhmbFaceFusionTemplate zhmbFaceFusionTemplate = zhmbFaceFusionTemplateService.getById(id);
        if (zhmbFaceFusionTemplate == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(zhmbFaceFusionTemplate);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param zhmbFaceFusionTemplate
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZhmbFaceFusionTemplate zhmbFaceFusionTemplate) {
        return super.exportXls(request, zhmbFaceFusionTemplate, ZhmbFaceFusionTemplate.class, "zhmb_face_fusion_template");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZhmbFaceFusionTemplate.class);
    }

}
