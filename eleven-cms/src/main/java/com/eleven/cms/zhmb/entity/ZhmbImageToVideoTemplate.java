package com.eleven.cms.zhmb.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: zhmb_image_to_video_template
 * @Author: jeecg-boot
 * @Date: 2024-10-18
 * @Version: V1.0
 */
@Data
@TableName("zhmb_image_to_video_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "zhmb_image_to_video_template对象", description = "zhmb_image_to_video_template")
public class ZhmbImageToVideoTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 图片风格化
     */
    @Excel(name = "图片风格化", width = 15)
    @ApiModelProperty(value = "图片风格化")
    @Dict(dicCode = "zhmb_image_style_code")
    private String imageStyle;
    /**
     * 封面图
     */
    @Excel(name = "封面图", width = 15)
    @ApiModelProperty(value = "封面图")
    private String coverUrl;
    /**
     * 视频地址
     */
    @Excel(name = "视频地址", width = 15)
    @ApiModelProperty(value = "视频地址")
    private String videoUrl;
    /**
     * 背景音乐
     */
    @Excel(name = "背景音乐", width = 15)
    @ApiModelProperty(value = "背景音乐")
    private String bgmUrl;
    /**
     * 特效
     */
    @Excel(name = "特效", width = 15)
    @ApiModelProperty(value = "特效")
    private String vfx;
    /**
     * 场景
     */
    @Excel(name = "场景", width = 15)
    @ApiModelProperty(value = "场景")
    private String transition;
    /**
     * 场景一时长
     */
    @Excel(name = "场景一时长", width = 15)
    @ApiModelProperty(value = "场景一时长")
    private String firstDuration;
    /**
     * 场景二时长
     */
    @Excel(name = "场景二时长", width = 15)
    @ApiModelProperty(value = "场景二时长")
    private String sedondDuration;
    /**
     * 转场时长
     */
    @Excel(name = "转场时长", width = 15)
    @ApiModelProperty(value = "转场时长")
    private String transDuration;
    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
