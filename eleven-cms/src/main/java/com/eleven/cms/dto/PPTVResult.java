package com.eleven.cms.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PPTVResult implements Serializable {
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("errorCode")
    private String errorCode;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        @JsonProperty("innerOrderId")
        private String innerOrderId;
    }
}
