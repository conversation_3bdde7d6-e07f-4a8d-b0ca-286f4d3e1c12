package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/3 14:46
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FaCaiShuNotify implements Serializable {

    /**
     * orderTime : 2024-04-02 14:09:41
     * pointNum : home_leave
     * phone : MTM4NTM5NTA4MTg=
     * sessionId : 9w97b5046fb5654358ac1e681de2
     * province :
     * isp :
     */

    @JsonProperty("orderTime")
    private String orderTime;
    @JsonProperty("pointNum")
    private String pointNum;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("sessionId")
    private String sessionId;
    @JsonProperty("province")
    private String province;
    @JsonProperty("isp")
    private String isp;
}
