package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WoReadNotifyResult implements Serializable {

    /*必传，订单ID，对应预订购返参：out_trade_no*/
    @JsonProperty("orderid")
    private String orderId;
    /*必传，用户ID*/
    @JsonProperty("userid")
    private String userId;
    /*必传，手机号码*/
    @JsonProperty("useraccount")
    private String userAccount;
    /*success*/
    @JsonProperty("message")
    private String message;

}
