package com.eleven.cms.dto;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * Author: <EMAIL>
 * Date: 2024/3/15 15:01
 * Desc: 从编辑后台导出的订阅库excel
 */
@Data
public class MusicDyExcelDto {
    /**版权id*/
    @Excel(name = "版权编号")
    private String copyrightId;
    /**歌曲名*/
    @Excel(name = "版权名称")
    private String musicName;
    /**歌手名*/
    @Excel(name = "歌手名称")
    private String singerName;
    /**视频彩铃产品id*/
    @Excel(name = "视频彩铃ID")
    private String vrbtProductId;
    /**cp id*/
    @Excel(name = "CPID")
    private String cpId;
    /**有效期*/
    @Excel(name = "有效期", format = "yyyy-MM-dd")
    private Date expiryDate;
    /**创建时间 歌曲在咪咕音乐后台的创建时间*/
    @Excel(name = "创建时间", format = "yyyy-MM-dd")
    private Date createTime;
}
