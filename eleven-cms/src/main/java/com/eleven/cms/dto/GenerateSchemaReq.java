package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@Data
public class GenerateSchemaReq {

    @NotEmpty(message = "来源不能为空")
    private String resource;

    /**
     * 访问路径
     */
    private String path;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 不设置过期时间
     */
    private Boolean noExpire = false;

    /**
     * 查询参数
     */
    private String query;
}
