package com.eleven.cms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @datetime 2024/10/8 15:38
 */
@Data
public class HaiYiFilterTemplateListDTO {

    @NotNull(message = "分页页码不能为空!")
    @ApiModelProperty("分页页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空！")
    @ApiModelProperty("分页每页页数，最大为50")
    private Integer pageSize;

    @NotBlank(message = "分类id不能为空!")
    @ApiModelProperty("分类id")
    private String tagId;
}
