package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class WxrefundNotifyParam implements Serializable {

    private static final long serialVersionUID = 3194877572505674535L;
    @JsonProperty("return_code")
    private String returnCode;


    @JsonProperty("appid")
    private String appId;

    @JsonProperty("mch_id")
    private String mchId;


    @JsonProperty("nonce_str")
    private String nonceStr;


    @JsonProperty("req_info")
    private String reqInfo;

    @JsonProperty("return_msg")
    private String returnMsg;

}
