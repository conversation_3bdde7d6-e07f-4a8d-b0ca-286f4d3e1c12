package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class AliSignUpNotifyParam implements Serializable {

	private static final long serialVersionUID = 3194877572505674535L;
    /**
     http://api.test.alipay.net/atinterface/receive_notify.htm?
     charset=utf-8
     &notify_time=2020-04-24 11:28:20
     &alipay_user_id=2088*****9864
     &sign=ooWBuCDIQSumOpaGBcQz+aoAqyGh3W6EqA/gmyPYwLJ2REFijY***9XPTApI9YglZyMw+ZMhd3kb0mh4RAXMrb6me
     &external_agreement_no=138528528786000
     &version=1.0
     &sign_time=2020-04-24 11:28:20
     &notify_id=2020042400222112820072971427431169
     &notify_type=dut_user_sign
     &agreement_no=20205224633661163886
     &invalid_time=2115-02-01 00:00:00
     &auth_app_id=2014*****222
     &personal_product_code=CYCLE_PAY_AUTH_P
     &valid_time=2020-04-24 11:28:20
     &login_token=1948bbcaac503bb499a1c62dbb584d0f_86
     &app_id=2014*****22
     &sign_type=RSA2
     &sign_scene=INDUSTRY|CARRENTAL
     &status=NORMAL
     &alipay_logon_id=159******10
     */

    @JsonProperty("charset")
    private String charset;
    @JsonProperty("notify_time")
    private String notifyTime;
    @JsonProperty("alipay_user_id")
    private String alipayUserId;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("external_agreement_no")
    private String externalAgreementNo;
    @JsonProperty("version")
    private String version;
    @JsonProperty("sign_time")
    private String signTime;
    @JsonProperty("notify_id")
    private String notifyId;
    @JsonProperty("notifyType")
    private String notifyType;
    @JsonProperty("agreement_no")
    private String agreementNo;
    @JsonProperty("invalid_time")
    private String invalidTime;
    @JsonProperty("auth_app_id")
    private String authAppId;
    @JsonProperty("personal_product_code")
    private String personalProductCode;
    @JsonProperty("valid_time")
    private String validTime;
    @JsonProperty("login_token")
    private String loginToken;
    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("sign_type")
    private String signType;
    @JsonProperty("sign_scene")
    private String signScene;
    @JsonProperty("status")
    private String status;
    @JsonProperty("alipay_logon_id")
    private String alipayLogonId;



}

