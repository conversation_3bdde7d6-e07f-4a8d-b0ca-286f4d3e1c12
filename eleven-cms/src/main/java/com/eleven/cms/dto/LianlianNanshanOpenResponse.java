package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/29 9:51
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LianlianNanshanOpenResponse implements Serializable {

    /**
     * errorCode : 200
     * status : OK
     * errorMessage : 处理成功
     */

    @JsonProperty("errorCode")
    private String errorCode;
    @JsonProperty("status")
    private String status;
    @JsonProperty("errorMessage")
    private String errorMessage;
    @JsonProperty("data")
    private Object data;
}
