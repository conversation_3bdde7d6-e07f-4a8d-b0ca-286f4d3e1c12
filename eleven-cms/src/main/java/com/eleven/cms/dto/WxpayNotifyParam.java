package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class WxpayNotifyParam implements Serializable {

	private static final long serialVersionUID = 3194877572505674535L;

    /**
     * <xml>
     * <appid><![CDATA[wx8ad568ceec585da2]]></appid>
     * <mch_id><![CDATA[1565728481]]></mch_id>
     * <out_trade_no><![CDATA[w1_13438828200_1594794218718]]></out_trade_no>
     * <total_fee>1</total_fee>
     * <cash_fee><![CDATA[1]]></cash_fee>
     * <return_code><![CDATA[SUCCESS]]></return_code>
     * <result_code><![CDATA[SUCCESS]]></result_code>
     * <time_end><![CDATA[**************]]></time_end>
     * <transaction_id><![CDATA[4200000576202007156246952536]]></transaction_id>
     * <nonce_str><![CDATA[zQMrYEy4HbJ7CWH4Ytf999QUr0xt4gNr]]></nonce_str>
     * <openid><![CDATA[osVrzwnski5A-jrKNdJkt_EXzM3I]]></openid>
     * <sign><![CDATA[2D9ACC7D76291CA1AC44CCF9253772F478FAA020FC902AA7FA348A6A81DF7830]]></sign>
     * <trade_type><![CDATA[MWEB]]></trade_type>
     * <fee_type><![CDATA[CNY]]></fee_type>
     * <bank_type><![CDATA[OTHERS]]></bank_type>
     * <is_subscribe><![CDATA[N]]></is_subscribe>
     * </xml>
     * 返回状态码	return_code	是	String(16)	SUCCESS	SUCCESS/FAIL 此字段是通信标识，非交易标识，交易是否成功需要查看result_code来判断
     * 返回信息	return_msg	否	String(128)	签名失败	返回信息，如非空，为错误原因 签名失败 参数格式校验错误
     */

    @JsonProperty("appid")
    private String appId;
    @JsonProperty("mch_id")
    private String mchId;
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    @JsonProperty("total_fee")
    private String totalFee;
    @JsonProperty("cash_fee")
    private String cashFee;
    @JsonProperty("return_code")
    private String returnCode;
    @JsonProperty("result_code")
    private String resultCode;
    @JsonProperty("return_msg")
    private String returnMsg;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyyMMddHHmmss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonProperty("time_end")
    private LocalDateTime timeEnd;

    @JsonProperty("transaction_id")
    private String transactionId;

    @JsonProperty("nonce_str")
    private String nonceStr;
    @JsonProperty("openid")
    private String openId;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("trade_type")
    private String tradeType;
    @JsonProperty("fee_type")
    private String feeType;
    @JsonProperty("bank_type")
    private String bankType;
    //@JsonProperty("is_subscribe")
    //private String isSubscribe;

}

