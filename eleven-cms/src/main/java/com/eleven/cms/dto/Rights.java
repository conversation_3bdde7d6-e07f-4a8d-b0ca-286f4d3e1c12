package com.eleven.cms.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Rights {


    /**
     * 业务id
     */
    @TableField(exist = false)
    private String serviceId;
    /**
     * 权益Id
     */
    @TableField(exist = false)
    private String rightsId;
    /**
     * 权益包名
     */
    @TableField(exist = false)
    private String packName;
    /**
     * 登录账号
     */
    @TableField(exist = false)
    private String msisdn;
    /**
     * 产品id
     */
    @TableField(exist = false)
    private String couponId;
    /**
     * 权益名称
     */
    @TableField(exist = false)
    private String rightsName;

    /**
     * 产品价格（单位：分）
     */
    @TableField(exist = false)
    private Integer productPrice;

    /**充值方式:0=直充,1=券码*/
    @TableField(exist = false)
    private Integer rechargeState;
    /**
     * 权益月份
     */
    @TableField(exist = false)
    private String rightsMonth;
    /**
     * 充值预约分钟
     */
    @TableField(exist = false)
    private Long rechargeDelayMinute;



    /**支付时间*/
    @TableField(exist = false)
    private Date payTime;

    @TableField(exist = false)
    private Integer isAccount;

    /**归属公司*/
    @TableField(exist = false)
    private String companyOwner;


    /**领取来源*/
    @TableField(exist = false)
    private String rechargeSource;

    /**渠道号*/
    @TableField(exist = false)
    private String channel;

    /**子渠道号*/
    @TableField(exist = false)
    private String subChannel;



    /**兑换码*/
    @TableField(exist = false)
    private String couponCode;

    public Rights(String serviceId, String rightsId, String packName, String msisdn) {
        this.serviceId = serviceId;
        this.rightsId = rightsId;
        this.packName = packName;
        this.msisdn = msisdn;
    }

    public Rights(String couponId, String rightsName, Integer productPrice, String rightsMonth,String companyOwner) {
        this.couponId = couponId;
        this.rightsName = rightsName;
        this.productPrice = productPrice;
        this.rightsMonth = rightsMonth;
        this.companyOwner = companyOwner;
    }
    public Rights(String couponId, String rightsName, Integer productPrice, String rightsMonth,Integer rechargeState,String companyOwner) {
        this.couponId = couponId;
        this.rightsName = rightsName;
        this.productPrice = productPrice;
        this.rightsMonth = rightsMonth;
        this.rechargeState = rechargeState;
        this.companyOwner = companyOwner;
    }
}
