package com.eleven.cms.dto;

import com.eleven.cms.vo.LianLianFenXiaoProduct;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/15 14:59
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianlianProductUpdateNotifyLog implements Serializable {

    /**
     * productId : 954855
     * onlyName : 粉小越东南亚料理2店通用套餐
     * productSimpleDesc : 【粉小越东南亚料理丨绿地伊藤店、合生汇店】地道东南亚风味，舌尖优享，99元购【粉小越双人精致餐】，168元购【3-4人欢聚套餐】冬阴功汤、特色海鲜菠萝炒饭、马来咖喱牛腩面包、泰式凤爪、越南传统芒果鲜虾米纸卷、泰式青柠蒸鲈鱼、泰式街头炸虾片等
     * shareText : 【粉小越东南亚料理丨绿地伊藤店、合生汇店】地道东南亚风味，舌尖优享，99元购【粉小越双人精致餐】，168元购【3-4人欢聚套餐】冬阴功汤、特色海鲜菠萝炒饭、马来咖喱牛腩面包、泰式凤爪、越南传统芒果鲜虾米纸卷、泰式青柠蒸鲈鱼、泰式街头炸虾片等
     * title : 粉小越东南亚料理丨两店适用
     * channelMallImg :
     * saleAmount : 226
     * stockAmount : 1198
     * itemStock : 0
     * salePrice : 9900
     * originPrice : 26400
     * isSoldOut : 0
     * faceImg : https://cdn.lianlianlvyou.com/lianlian/common/5329104c9b8c4995ada8dab21a8148ac.jpg
     * shareImg : https://cdn.lianlianlvyou.com/lianlian/common/5329104c9b8c4995ada8dab21a8148ac.jpg
     * attention :
     * bookingType : 1
     * bookingShowAddress : 0
     * orderShowIdCard : 0
     * orderShowDate : 0
     * beginTime : 2023-04-20 00:00:00
     * endTime : 2023-05-19 15:59:59
     * validBeginDate : 2023-04-19 16:00:00
     * validEndDate : 2023-06-30 15:59:59
     * releaseTime : 2023-04-20 01:47:18
     * singleMin : 1
     * singleMax : 10
     * ecommerce : 0
     * categoryPath : 餐饮类-异域菜-其他东南亚菜
     * productCategoryId : 141
     * categoryName : 其他东南亚菜
     * qualificationsList : ["http://ll-oss-contract.oss-cn-beijing.aliyuncs.com/ll-center/merchant/9215225370.jpg?Expires=1682047796&OSSAccessKeyId=LTAI5tRSgidRx1faBSfwZniV&Signature=D9tLjN%2BntQZTM6xy4fH%2BrH4wOm8%3D"]
     * itemList : [{"itemId":1505581,"subTitle":"工作日特惠双人套餐","salePrice":9900,"originPrice":26400,"channelPrice":9255,"stock":10,"singleMax":10,"codeAmount":1}]
     * shopList : [{"id":521535,"name":"粉小越东南亚料理","latitude":"30.605176","longitude":"104.155131","cityCode":"510100","address":"喜树街35号1层伊藤广场6F-TY010","phoneNumber":"02885550521"}]
     * noticeVOList : [{"titles":"预约方式","option":[{"optionName":"至少提前1天短信网址预约，高峰期需等位"}]}]
     * productItemsContentMap : {"1505582":[{"id":10902598,"productId":954855,"productItemId":1505582,"contentDesc":"冬阴功汤（小）","numberAndUnit":"1","originPrice":55400,"contentType":2,"status":0,"createDate":"2023-04-19 11:17:28","updateTime":"2023-04-19 11:17:28"}]}
     * imgList : [{"url":"https://cdn2.lianlianlvyou.com/lianlian/common/0cd2169017df499ebd8ddf3ff65cea30.jpg","sort":0}]
     */

    @JsonProperty("productId")
    private int productId;
    @JsonProperty("onlyName")
    private String onlyName;
    @JsonProperty("productSimpleDesc")
    private String productSimpleDesc;
    @JsonProperty("shareText")
    private String shareText;
    @JsonProperty("title")
    private String title;
    @JsonProperty("channelMallImg")
    private String channelMallImg;
    @JsonProperty("saleAmount")
    private int saleAmount;
    @JsonProperty("stockAmount")
    private int stockAmount;
    @JsonProperty("itemStock")
    private int itemStock;
    @JsonProperty("salePrice")
    private int salePrice;
    @JsonProperty("originPrice")
    private int originPrice;
    @JsonProperty("isSoldOut")
    private int isSoldOut;
    @JsonProperty("faceImg")
    private String faceImg;
    @JsonProperty("shareImg")
    private String shareImg;
    @JsonProperty("attention")
    private String attention;
    @JsonProperty("bookingType")
    private int bookingType;
    @JsonProperty("bookingShowAddress")
    private int bookingShowAddress;
    @JsonProperty("orderShowIdCard")
    private int orderShowIdCard;
    @JsonProperty("orderShowDate")
    private int orderShowDate;
    @JsonProperty("beginTime")
    private String beginTime;
    @JsonProperty("endTime")
    private String endTime;
    @JsonProperty("validBeginDate")
    private String validBeginDate;
    @JsonProperty("validEndDate")
    private String validEndDate;
    @JsonProperty("releaseTime")
    private String releaseTime;
    @JsonProperty("singleMin")
    private int singleMin;
    @JsonProperty("singleMax")
    private int singleMax;
    @JsonProperty("ecommerce")
    private int ecommerce;
    @JsonProperty("categoryPath")
    private String categoryPath;
    @JsonProperty("productCategoryId")
    private int productCategoryId;
    @JsonProperty("categoryName")
    private String categoryName;
    @JsonProperty("productItemsContentMap")
    private Map<String,List<ProductItemsContentMap>> productItemsContentMap;
    @JsonProperty("qualificationsList")
    private List<String> qualificationsList;
    @JsonProperty("itemList")
    private List<ItemList> itemList;
    @JsonProperty("shopList")
    private List<ShopList> shopList;
    @JsonProperty("noticeVOList")
    private List<NoticeVOList> noticeVOList;
    @JsonProperty("imgList")
    private List<ImgList> imgList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ProductItemsContentMap implements Serializable {
        /**
         * id : 10902598
         * productId : 954855
         * productItemId : 1505582
         * contentDesc : 冬阴功汤（小）
         * numberAndUnit : 1
         * originPrice : 55400
         * contentType : 2
         * status : 0
         * createDate : 2023-04-19 11:17:28
         * updateTime : 2023-04-19 11:17:28
         */

        @JsonProperty("id")
        private int id;
        @JsonProperty("productId")
        private int productId;
        @JsonProperty("productItemId")
        private int productItemId;
        @JsonProperty("contentDesc")
        private String contentDesc;
        @JsonProperty("numberAndUnit")
        private String numberAndUnit;
        @JsonProperty("originPrice")
        private int originPrice;
        @JsonProperty("contentType")
        private int contentType;
        @JsonProperty("status")
        private int status;
        @JsonProperty("createDate")
        private String createDate;
        @JsonProperty("updateTime")
        private String updateTime;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ItemList implements Serializable {
        /**
         * itemId : 1505581
         * subTitle : 工作日特惠双人套餐
         * salePrice : 9900
         * originPrice : 26400
         * channelPrice : 9255
         * stock : 10
         * singleMax : 10
         * codeAmount : 1
         */

        @JsonProperty("itemId")
        private int itemId;
        @JsonProperty("subTitle")
        private String subTitle;
        @JsonProperty("salePrice")
        private int salePrice;
        @JsonProperty("originPrice")
        private int originPrice;
        @JsonProperty("channelPrice")
        private int channelPrice;
        @JsonProperty("stock")
        private int stock;
        @JsonProperty("singleMax")
        private int singleMax;
        @JsonProperty("codeAmount")
        private int codeAmount;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ShopList implements Serializable {
        /**
         * id : 521535
         * name : 粉小越东南亚料理
         * latitude : 30.605176
         * longitude : 104.155131
         * cityCode : 510100
         * address : 喜树街35号1层伊藤广场6F-TY010
         * phoneNumber : 02885550521
         */

        @JsonProperty("id")
        private int id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("latitude")
        private String latitude;
        @JsonProperty("longitude")
        private String longitude;
        @JsonProperty("cityCode")
        private String cityCode;
        @JsonProperty("address")
        private String address;
        @JsonProperty("phoneNumber")
        private String phoneNumber;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class NoticeVOList implements Serializable {
        /**
         * titles : 预约方式
         * option : [{"optionName":"至少提前1天短信网址预约，高峰期需等位"}]
         */

        @JsonProperty("titles")
        private String titles;
        @JsonProperty("option")
        private List<Option> option;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class Option implements Serializable {
            /**
             * optionName : 至少提前1天短信网址预约，高峰期需等位
             */

            @JsonProperty("optionName")
            private String optionName;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ImgList implements Serializable {
        /**
         * url : https://cdn2.lianlianlvyou.com/lianlian/common/0cd2169017df499ebd8ddf3ff65cea30.jpg
         * sort : 0
         */

        @JsonProperty("url")
        private String url;
        @JsonProperty("sort")
        private int sort;
    }
}
