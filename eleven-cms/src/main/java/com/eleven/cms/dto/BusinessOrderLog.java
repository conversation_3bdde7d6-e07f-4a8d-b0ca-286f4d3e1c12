package com.eleven.cms.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessOrderLog {
    /**
     * 手机号
     */
    @TableField(exist = false)
    private String mobile;

    @TableField(exist = false)
    private String subChannel;

    @TableField(exist = false)
    private String createTimeFrom;
    @TableField(exist = false)
    private String createTimeTo;
    /**
     * 渠道id
     */
    @TableField(exist = false)
    private String channelCode;

    @TableField(exist = false)
    private String channel;

    @TableField(exist = false)
    private String isp;
    @TableField(exist = false)
    private String serviceId;
}
