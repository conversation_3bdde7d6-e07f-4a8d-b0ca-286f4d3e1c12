package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class WangyiyunMMNotifyParam implements Serializable {

	private static final long serialVersionUID = 3194877572505674535L;


	/*商户编号*/
    @JsonProperty("merc_id")
    private String mercId;
    /*应用编号*/
    @JsonProperty("app_id")
    private String appId;
    /*支付中心订单号*/
    @JsonProperty("orderid")
    private String orderId;
    /*商户应用自定义订单号*/
    @JsonProperty("app_orderid")
    private String appOrderId;
    /*商户用户名*/
    @JsonProperty("userid")
    private String userId;
    /*手机号*/
    @JsonProperty("phone")
    private String phone;
    /*支付时间 YYYYMMDDHHMMSS*/
    @JsonProperty("pay_time")
    private String payTime;
    /*订单金额*/
    @JsonProperty("rec_amount")
    private String recAmount;
    /*实际支付金额*/
    @JsonProperty("pay_amount")
    private String payAmount;
    /*支付状态,0 订单创建,1 订购成功,2 订购失败,3 包月退订,4 续订失败,5 退款成功,6 退款失败,7 退款处理中*/
    @JsonProperty("status")
    private String status;
    /*是否包月计费*/
    @JsonProperty("is_monthly")
    private String isMonthly;
    /*是否续订*/
    @JsonProperty("is_renew")
    private String isRenew;



}

