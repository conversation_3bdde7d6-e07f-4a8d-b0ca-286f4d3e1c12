package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/31 21:07
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JunboNotifyResult implements Serializable {

    /**
     * code : 0
     * msg : 成功
     * data : 5a144ca5055b05272a5cd54e9d3c3c3c
     * info : null
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private String data;
    @JsonProperty("info")
    private Object info;
}
