package com.eleven.cms.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PPTVRequest {
    @JsonProperty("phone")
    @JSONField(name="phone")
    public String phone;

//    @JsonProperty("snId")
//    @J<PERSON>NField(name="snId")
//    private String snId;
//
//    @JsonProperty("openId")
//    @JSONField(name="openId")
//    private String openId;
//
//    @JsonProperty("canal")
//    @J<PERSON>NField(name="canal")
//    private String canal;

    @JsonProperty("channel")
    @JSONField(name="channel")
    public String channel;

    @JsonProperty("outOrderId")
    @JSONField(name="outOrderId")
    public String outOrderId;

    @JsonProperty("productId")
    @JSONField(name="productId")
    public String productId;


//    @JsonProperty("goodsNo")
//    @JSONField(name="goodsNo")
//    private String goodsNo;


    @JsonProperty("orderTime")
    @JSONField(name="orderTime")
    public String orderTime;

    @JsonProperty("sign")
    @JSONField(name="sign")
    public String sign;
}
