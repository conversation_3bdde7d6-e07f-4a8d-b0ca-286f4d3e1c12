package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Slf4j
public class MiGuHuYuResult {
    //订单号
    @JsonProperty("orderId")
    private String orderId;

    //快游用户 id
    @JsonProperty("userId")
    private String userId;


    //发起时间
    @JsonProperty("createTime")
    private String createTime;


    //用户手机号
    @JsonProperty("phoneNum")
    private String phoneNum;


    //道具标识
    @JsonProperty("productCode")
    private String productCode;


    //扩展字段（咪咕互娱透传用）
    @JsonProperty("extrInfo")
    private String extrInfo;

    public static String decodeUserId2Mobile(String userId) {
        try {
            return Long.toString(Long.valueOf(userId),13);
        } catch (Exception e) {
            log.info("咪咕互娱userId解码手机号失败",e);
            return null;
        }
    }
}
