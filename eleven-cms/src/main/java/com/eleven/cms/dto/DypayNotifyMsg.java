package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2022/5/25 17:15
 * Desc:Todo
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@Data
public class DypayNotifyMsg {
     /*
     * {"appid":"tt0323759e9c1da7ed01","cp_orderno":"4028823f80f4ec9d0180f9d969900024","cp_extra":"","way":"1","channel_no":"4351101096202205258333691623","channel_gateway_no":"","payment_order_no":"PCP2022052514133439008384033031","out_channel_order_no":"4351101096202205258333691623","total_amount":1,"status":"SUCCESS","seller_uid":"70989217136054458960","extra":"","item_id":"","paid_at":1653459225,"message":"","order_id":"7101553088682543375"}
     * */

    @JsonProperty("appid")
    private String appid;
    @JsonProperty("cp_orderno")
    private String cpOrderno;
    @JsonProperty("cp_extra")
    private String cpExtra;
    @JsonProperty("way")
    private String way;
    @JsonProperty("channel_no")
    private String channelNo;
    @JsonProperty("channel_gateway_no")
    private String channelGatewayNo;
    @JsonProperty("payment_order_no")
    private String paymentOrderNo;
    @JsonProperty("out_channel_order_no")
    private String outChannelOrderNo;
    @JsonProperty("total_amount")
    private Integer totalAmount;
    @JsonProperty("status")
    private String status;
    @JsonProperty("seller_uid")
    private String sellerUid;
    @JsonProperty("extra")
    private String extra;
    @JsonProperty("item_id")
    private String itemId;
    @JsonProperty("paid_at")
    private Integer paidAt;
    @JsonProperty("message")
    private String message;
    @JsonProperty("order_id")
    private String orderId;
}
