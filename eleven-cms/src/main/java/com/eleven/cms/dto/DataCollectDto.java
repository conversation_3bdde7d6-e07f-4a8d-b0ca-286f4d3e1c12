package com.eleven.cms.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业彩铃订购数据汇总
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/19 14:56
 **/
@Data
public class DataCollectDto implements Serializable {
    /**省份*/
    @TableField(exist = false)
    private String province;
    /**type*/
    @Excel(name = "订购时间", width = 15)
    @TableField(exist = false)
    private String type;
    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @TableField(exist = false)
    private String channel;
    /**订购数量*/
    @Excel(name = "订购数量", width = 15)
    @TableField(exist = false)
    private String sub;
    /**退订数量*/
    @Excel(name = "退订数量", width = 15)
    @TableField(exist = false)
    private String unSub;
    /**退订率*/
    @Excel(name = "退订率", width = 15)
    @TableField(exist = false)
    private String unSubRate;
    /**60分钟退订数量*/
    @Excel(name = "60分钟退订数量", width = 15)
    @TableField(exist = false)
    private String verifyStatus;
    /**60分钟退订数量*/
    @TableField(exist = false)
    private Integer verifyStatusCount;
    /**60分钟退订率*/
    @Excel(name = "60分钟退订率", width = 15)
    @TableField(exist = false)
    private String verifyStatusRate;
    /**60分钟退订率*/
    @TableField(exist = false)
    private String verifyStatusRateCount;

    //订购时间
    @TableField(exist = false)
    private Date updateTime;

    /**订购数量*/
    @TableField(exist = false)
    private Integer subCount;
    /**退订数量*/
    @TableField(exist = false)
    private Integer unSubCount;
    /**退订率*/
    @TableField(exist = false)
    private String unSubRateCount;

    @TableField(exist = false)
    private String updateTimeBegin;

    @TableField(exist = false)
    private String updateTimeEnd;
    /**执行日期*/
    @TableField(exist = false)
    private String executeDate;

}
