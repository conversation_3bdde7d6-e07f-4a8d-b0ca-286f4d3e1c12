package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 破解计费返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KugouOrderResult {

    public static final KugouOrderResult FAIL_RESULT = new KugouOrderResult("0", "通讯失败",null);

    @JsonProperty("status")
    private String status;
    @JsonProperty("error_code")
    private String errorCode;
    @JsonProperty("data")
    private KugouOrderDataResult data;

    public static KugouOrderResult fail() {
        return FAIL_RESULT;
    }

    public boolean isOK(){
        return "1".equals(status);
    }
}
