package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.StringJoiner;

/**
 * Author: <EMAIL>
 * Date: 2021/1/14 13:24
 * Desc:Todo
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class JunboResult {
    /**
     * code : 1025
     * msg : 充值号码充值次数超过当日限制次数
     * orderId : null
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("orderSEQ")
    private String orderSEQ;
    @JsonProperty("info")
    private String info;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderSEQ() {
        return orderSEQ;
    }

    public void setOrderSEQ(String orderSEQ) {
        this.orderSEQ = orderSEQ;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", JunboResult.class.getSimpleName() + "[", "]").add("code='" + code + "'")
                                                                                   .add("msg='" + msg + "'")
                                                                                   .add("orderId=" + orderId)
                                                                                   .add("orderSEQ=" + orderSEQ)
                                                                                   .add("info=" + info)
                                                                                   .toString();
    }
}
