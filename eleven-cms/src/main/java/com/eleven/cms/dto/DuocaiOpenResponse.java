package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/29 9:51
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DuocaiOpenResponse implements Serializable {
    /**
     * message : 处理成功
     * code : 0
     * result : {"channelOrderId":"54817571265917572","couponId":"571957120957123","writeoffType":1,"voucher":"18744332212","couponStatus":0}
     */

    @JsonProperty("message")
    private String message;
    @JsonProperty("code")
    private int code;
    @JsonProperty("result")
    private Result result;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Result implements Serializable {
        /**
         * channelOrderId : 54817571265917572
         * couponId : 571957120957123
         * writeoffType : 1
         * voucher : 18744332212
         * couponStatus : 0
         */
        //供应商的订单号
        @JsonProperty("channelOrderId")
        private String channelOrderId;
        //券码的唯一 id
        @JsonProperty("couponId")
        private String couponId;
        //核销方式:1，二维码2，条形码3，二维码和条形码 4，卡券URL 地址5，密码6，卡号和密码（无需接入系统核销可默认选择二维码）
        @JsonProperty("writeoffType")
        private int writeoffType;
        //核销凭证，根据核销方式填充数据：1、2、3 类型时候会根据内容生产条形码或二维码；
        // 4 类型时是卡券URL 地址；
        // 5类型是密码；
        // 6 是卡号和密码，内容如下的json 字符串：{"cardNum":"12r4f5fy6g4lwf5","password":"dfg3e3423"} （ 无核销凭证可传入券ID与couponId 相同）
        @JsonProperty("voucher")
        private String voucher;
        //订单状态：0-待发放1-待使用2-已使用3-已取消 4-已过期
        @JsonProperty("couponStatus")
        private int couponStatus;
    }
}
