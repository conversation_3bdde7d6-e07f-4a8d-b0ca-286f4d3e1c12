package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/15 14:28
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianlianRefundNotifyLog implements Serializable {

    /**
     * thirdOrderId : 1671588991573027
     * channelOrderId : C221221000057000004
     * orderId : 221221000057
     * chargeAmount : 0
     * channelId : 4
     * applyAmount : 15
     * refundAmount : 15
     */

    @JsonProperty("thirdOrderId")
    private String thirdOrderId;
    @JsonProperty("channelOrderId")
    private String channelOrderId;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("chargeAmount")
    private int chargeAmount;
    @JsonProperty("channelId")
    private int channelId;
    @JsonProperty("applyAmount")
    private int applyAmount;
    @JsonProperty("refundAmount")
    private int refundAmount;
}
