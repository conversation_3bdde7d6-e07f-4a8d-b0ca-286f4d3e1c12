package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class WxInfoNotifyParam implements Serializable {

      private static final long serialVersionUID = 3194877572505674535L;
      @JsonProperty("out_refund_no")
      private String outRefundNo;

      @JsonProperty("out_trade_no")
      private String outTradeNo;

      @JsonProperty("refund_account")
      private String refundAccount;

      @JsonProperty("refund_fee")
      private String refundFee;

      @JsonProperty("refund_id")
      private String refundId;

      @JsonProperty("refund_recv_accout")
      private String refundRecvAccout;

      @JsonProperty("refund_request_source")
      private String refundRequestSource;

      @JsonProperty("refund_status")
      private String refundStatus;

      @JsonProperty("settlement_refund_fee")
      private String settlementRefundFee;

      @JsonProperty("settlement_total_fee")
      private String settlementTotalFee;

      @JsonProperty("success_time")
      private String successTime;

      @JsonProperty("total_fee")
      private String totalFee;

      @JsonProperty("transaction_id")
      private String transactionId;

      @JsonProperty("cash_refund_fee")
      private String cashRefundFee;

}
