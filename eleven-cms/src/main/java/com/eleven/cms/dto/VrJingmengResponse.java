package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/18 14:51
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VrJingmengResponse implements Serializable {

    /**
     * transactionId : MIGU202003300830570000119506
     * mobile : 13985685212
     * response : OK
     */

    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("mobile")
    private String mobile;
    @JsonProperty("response")
    private String response;
}
