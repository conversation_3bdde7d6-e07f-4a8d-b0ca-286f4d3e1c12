package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.StringJoiner;

/**
 * Author: <EMAIL>
 * Date: 2021/1/14 13:21
 * Desc:Todo
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class JunboResp {


    /**
     * T : 1610526965943
     * S : 100
     * F : 1349273995275464705
     * C : {"code":"1025","msg":"充值号码充值次数超过当日限制次数","orderId":null}
     */

    @JsonProperty("T")
    private long T;
    @JsonProperty("S")
    private String S;
    @JsonProperty("F")
    private String F;
    @JsonProperty("C")
    private JunboResult junboResult;

    public long getT() {
        return T;
    }

    public void setT(long T) {
        this.T = T;
    }

    public String getS() {
        return S;
    }

    public void setS(String S) {
        this.S = S;
    }

    public String getF() {
        return F;
    }

    public void setF(String F) {
        this.F = F;
    }

    public JunboResult getJunboResult() {
        return junboResult;
    }

    public void setJunboResult(JunboResult junboResult) {
        this.junboResult = junboResult;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", JunboResp.class.getSimpleName() + "[", "]").add("T=" + T)
                                                                                 .add("S='" + S + "'")
                                                                                 .add("F='" + F + "'")
                                                                                 .add("junboResult=" + junboResult)
                                                                                 .toString();
    }

}
