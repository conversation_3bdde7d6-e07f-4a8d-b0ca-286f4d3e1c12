package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/11 15:31
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WxJsAPIpayNotifyParam implements Serializable {

    /**
     * id : EV-2018022511223320873
     * create_time : 2015-05-20T13:29:35+08:00
     * resource_type : encrypt-resource
     * event_type : TRANSACTION.SUCCESS
     * summary : 支付成功
     * resource : {"original_type":"transaction","algorithm":"AEAD_AES_256_GCM","ciphertext":"","associated_data":"","nonce":""}
     */

    @JsonProperty("id")
    private String id;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("resource_type")
    private String resourceType;
    @JsonProperty("event_type")
    private String eventType;
    @JsonProperty("summary")
    private String summary;
    @JsonProperty("resource")
    private Resource resource;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Resource implements Serializable {

        private static final long serialVersionUID = 3194877572505674535L;
        /**
         * original_type : transaction
         * algorithm : AEAD_AES_256_GCM
         * ciphertext :
         * associated_data :
         * nonce :
         */

        @JsonProperty("original_type")
        private String originalType;
        @JsonProperty("algorithm")
        private String algorithm;
        @JsonProperty("ciphertext")
        private String ciphertext;
        @JsonProperty("associated_data")
        private String associatedData;
        @JsonProperty("nonce")
        private String nonce;
    }
}
