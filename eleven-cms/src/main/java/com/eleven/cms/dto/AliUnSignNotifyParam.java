package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class AliUnSignNotifyParam implements Serializable {

	private static final long serialVersionUID = 3194877572505674535L;
    /**
     http://api.test.alipay.net/atinterface/receive_notify.htm?
     charset=UTF-8
     &notify_time=2022-08-08 10%3A44%3A45
     &unsign_time=2022-08-08 10%3A44%3A45
     &alipay_user_id=2088******772180
     &sign=$$$
     &external_agreement_no=dk20220808mm220281113
     &version=1.0
     &notify_id=2022080800222104445014011428773938
     &notify_type=dut_user_unsign
     &agreement_no=2022******3902719118
     &auth_app_id=20141******13222
     &personal_product_code=CYCLE_PAY_AUTH_P
     &app_id=20141******13222
     &sign_type=RSA2
     &alipay_logon_id=156******05
     &status=UNSIGN
     &sign_scene=INDUSTRY%7CCARRENTAL
     */

    @JsonProperty("charset")
    private String charset;
    @JsonProperty("notify_time")
    private String notifyTime;
    @JsonProperty("unsign_time")
    private String unsignTime;
    @JsonProperty("alipay_user_id")
    private String alipayUserId;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("external_agreement_no")
    private String externalAgreementNo;
    @JsonProperty("version")
    private String version;
    @JsonProperty("notify_id")
    private String notifyId;
    @JsonProperty("notifyType")
    private String notifyType;
    @JsonProperty("agreement_no")
    private String agreementNo;
    @JsonProperty("auth_app_id")
    private String authAppId;
    @JsonProperty("personal_product_code")
    private String personalProductCode;
    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("sign_type")
    private String signType;
    @JsonProperty("sign_scene")
    private String signScene;
    @JsonProperty("status")
    private String status;
    @JsonProperty("alipay_logon_id")
    private String alipayLogonId;



}

