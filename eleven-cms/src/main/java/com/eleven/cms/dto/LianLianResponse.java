package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/15 10:08
 **/

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
public class LianLianResponse implements Serializable {

    /**
     * data : xxx
     * code : 200
     * msg : xxxx
     */

    @JsonProperty("data")
    private String data;
    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
}
