package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/15 12:01
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianlianUseNotifyLog implements Serializable {

    /**
     * channelId : 10077
     * channelOrderId : C230329049774010077
     * thirdOrderId : 695135435701473280
     * orderId : 230329049774
     * corderId : 230329049774
     * completeDate : 2023-04-11 18:20:41
     */

    @JsonProperty("channelId")
    private int channelId;
    @JsonProperty("channelOrderId")
    private String channelOrderId;
    @JsonProperty("thirdOrderId")
    private String thirdOrderId;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("corderId")
    private String corderId;
    @JsonProperty("completeDate")
    private String completeDate;
}
