package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WechatComplainNotify implements Serializable {

    /**
     * id : EV-2018022511223320873
     * create_time : 2015-05-20T13:29:35.120+08:00
     * resource_type : encrypt-resource
     * event_type : COMPLAINT.CREATE
     * resource : {"algorithm":"AEAD_AES_256_GCM","ciphertext":"...","nonce":"...","associated_data":""}
     */

    @JsonProperty("id")
    private String id;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("resource_type")
    private String resourceType;
    @JsonProperty("event_type")
    private String eventType;

    @JsonProperty("summary")
    private String summary;

    @JsonProperty("resource")
    private Resource resource;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Resource implements Serializable {
        /**
         * algorithm : AEAD_AES_256_GCM
         * ciphertext : ...
         * nonce : ...
         * associated_data :
         */

        @JsonProperty("algorithm")
        private String algorithm;
        @JsonProperty("ciphertext")
        private String ciphertext;
        @JsonProperty("nonce")
        private String nonce;
        @JsonProperty("associated_data")
        private String associatedData;
    }
}
