package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CouponCodeTuNiuImport {

    @Excel(name = "卡密")
    private String couponCode;

    @Excel(name = "创建时间")
    private String createTime;
    /**创建人*/
    @Excel(name = "创建人")
    private String createBy;

    /**过期时间*/
    @Excel(name = "过期时间")
    private String invalidTime;
}
