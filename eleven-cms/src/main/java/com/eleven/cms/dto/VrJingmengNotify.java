package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 咪咕快游VR竞盟通知
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/18 11:48
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class VrJingmengNotify implements Serializable {

    /**
     * transactionId : MIGU202003300830570000119506
     * mobile : 13985685212
     * product : 760000153417
     * actionTime : 20200330093100
     * price : 2500
     * status : 100
     * feeType : 1
     * region : 2
     * channel : ABCD001
     */

    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("mobile")
    private String mobile;
    @JsonProperty("product")
    private String product;
    @JsonProperty("actionTime")
    private String actionTime;
    @JsonProperty("price")
    private Integer price;
    @JsonProperty("status")
    private String status;
    @JsonProperty("feeType")
    private String feeType;
    @JsonProperty("region")
    private String region;
    @JsonProperty("channel")
    private String channel;
}
