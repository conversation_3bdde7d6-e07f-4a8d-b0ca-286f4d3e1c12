package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/13 15:32
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class JunboCunLiangNotify {
    public static final String UN_SUB = "14";
    public static final String CODE_OK = "7";
    public static final String CODE_FAIL = "8";

    //权限id：骏伯运营负责人提供
    @JsonProperty("pid")
    private Long pid;
    //骏伯订单号
    @JsonProperty("sysOrderId")
    private String sysOrderId;
    //产品编码：骏伯运营负责人提供
    @JsonProperty("productCode")
    private String productCode;
    //渠道编码
    @JsonProperty("mediaCode")
    private String mediaCode;
    //订单状态编码 7下单成功   8下单失败  14退订订单
    @JsonProperty("orderStatus")
    private String orderStatus;
    //订单状态说明
    @JsonProperty("orderStatusMsg")
    private String  orderStatusMsg;
    //运营商查询/回调原始报文（下游需要，且骏伯内部需对接各运营商对应报文才有值，如不需要可忽略）
    @JsonProperty("responseMsg")
    private String responseMsg;
    //订单备注
    @JsonProperty("remark")
    private String remark;
    //运营商订单号
    @JsonProperty("orderCode")
    private String orderCode;
    //用户手机（已加密-解密方式参考下文7.解密说明-如无需求使用该字段，则不对接解密流程）
    @JsonProperty("contactNumber")
    private String contactNumber;

    public boolean isOK(){
        return CODE_OK.equals(orderStatus);
    }
    public boolean isFail(){
        return CODE_FAIL.equals(orderStatus);
    }

    public boolean isUnSub(){
        return UN_SUB.equals(orderStatus);
    }
}
