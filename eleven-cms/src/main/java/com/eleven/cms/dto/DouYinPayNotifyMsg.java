package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/26 10:20
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DouYinPayNotifyMsg implements Serializable {
    /**
     * app_id : tt07e371xxxxxxx
     * out_order_no : motb52726742593307630520652
     * order_id : ext_order_123
     * status : SUCCESS
     * total_amount : 1
     * discount_amount : 0
     * pay_channel : 1
     * channel_pay_id : 2iu2082897r9hflquf
     * merchant_uid : 1231123
     * message :
     * event_time : 1692775192000
     * user_bill_pay_id : DPTS12031230128124421
     */

    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("out_order_no")
    private String outOrderNo;
    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("status")
    private String status;
    @JsonProperty("total_amount")
    private int totalAmount;
    @JsonProperty("discount_amount")
    private int discountAmount;
    @JsonProperty("pay_channel")
    private int payChannel;
    @JsonProperty("channel_pay_id")
    private String channelPayId;
    @JsonProperty("merchant_uid")
    private String merchantUid;
    @JsonProperty("message")
    private String message;
    @JsonProperty("event_time")
    private long eventTime;
    @JsonProperty("user_bill_pay_id")
    private String userBillPayId;
}
