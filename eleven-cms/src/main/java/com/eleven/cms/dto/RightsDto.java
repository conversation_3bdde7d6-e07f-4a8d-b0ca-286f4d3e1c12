package com.eleven.cms.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RightsDto {
    @TableField(exist = false)
    private String optionKey;

    @TableField(exist = false)
    private String optionValue;

    @TableField(exist = false)
    private String serviceId;


    public RightsDto(String optionKey, String optionValue) {
        this.optionKey = optionKey;
        this.optionValue = optionValue;
    }


    //重写equals方法
    @Override
    public boolean equals(Object obj) {
        RightsDto user = (RightsDto) obj;
        return optionKey.equals(user.getOptionKey()) && (optionValue==user.getOptionValue());
    }

    //重写hashCode方法
    @Override
    public int hashCode() {
        String str = optionKey + optionValue;
        return str.hashCode();
    }
}
