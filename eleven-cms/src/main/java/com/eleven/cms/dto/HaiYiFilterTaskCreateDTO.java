package com.eleven.cms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @datetime 2024/10/8 15:47
 */
@Data
public class HaiYiFilterTaskCreateDTO {

    @NotBlank(message = "滤镜模版Id不能为空！")
    @ApiModelProperty("滤镜模版Id")
    private String hyTemplateId;

    @NotBlank(message = "图片链接不能为空！")
    @ApiModelProperty("图片链接")
    private String imageUrl;

    private Integer width;

    private Integer height;

    private String prompt;
}
