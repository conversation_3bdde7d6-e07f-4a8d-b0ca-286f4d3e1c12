package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class QyclWxpayNotifyParam implements Serializable {

	private static final long serialVersionUID = 3194877572505674535L;

    /**
     * <xml><appid><![CDATA[wx25501bc32010e60b]]></appid>
     * <bank_type><![CDATA[OTHERS]]></bank_type>
     * <cash_fee><![CDATA[1]]></cash_fee>
     * <fee_type><![CDATA[CNY]]></fee_type>
     * <is_subscribe><![CDATA[N]]></is_subscribe>
     * <mch_id><![CDATA[**********]]></mch_id>
     * <nonce_str><![CDATA[JVQD5n0v689swwdc5zHyhOHv4vOIKuyD]]></nonce_str>
     * <openid><![CDATA[ovLtl5MBMvLmAsv57qKxs0U74VQE]]></openid>
     * <out_trade_no><![CDATA[1590976951739756545]]></out_trade_no>
     * <result_code><![CDATA[SUCCESS]]></result_code>
     * <return_code><![CDATA[SUCCESS]]></return_code>
     * <sign><![CDATA[5F34A56CEF8D431334D8A89D7FF81597834233AD439F1C2AFF72F7F6136D45F0]]></sign>
     * <time_end><![CDATA[**************]]></time_end>
     * <total_fee>1</total_fee>
     * <trade_type><![CDATA[JSAPI]]></trade_type>
     * <transaction_id><![CDATA[4200001626202211115209777485]]></transaction_id>
     * </xml>
     * 返回状态码	return_code	是	String(16)	SUCCESS	SUCCESS/FAIL 此字段是通信标识，非交易标识，交易是否成功需要查看result_code来判断
     * 返回信息	return_msg	否	String(128)	签名失败	返回信息，如非空，为错误原因 签名失败 参数格式校验错误
     */
    @JsonProperty("transaction_id")
    private String transactionId;

    @JsonProperty("nonce_str")
    private String nonceStr;

    @JsonProperty("bank_type")
    private String bankType;

    @JsonProperty("openid")
    private String openId;

    @JsonProperty("sign")
    private String sign;

    @JsonProperty("fee_type")
    private String feeType;

    @JsonProperty("mch_id")
    private String mchId;

    @JsonProperty("cash_fee")
    private String cashFee;

    @JsonProperty("out_trade_no")
    private String outTradeNo;

    @JsonProperty("appid")
    private String appId;

    @JsonProperty("total_fee")
    private String totalFee;

    @JsonProperty("trade_type")
    private String tradeType;

    @JsonProperty("result_code")
    private String resultCode;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyyMMddHHmmss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonProperty("time_end")
    private LocalDateTime timeEnd;

    @JsonProperty("is_subscribe")
    private String isSubscribe;

    @JsonProperty("return_code")
    private String returnCode;

}

