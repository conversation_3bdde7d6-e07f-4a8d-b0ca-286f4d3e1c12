package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class WechatpayNotifyParam implements Serializable {

	private static final long serialVersionUID = 3194877572505674535L;
    /**
     * id : a4ccb132-**************-0c601a56c34f
     * create_time : 2025-01-24T10:02:14+08:00
     * resource_type : encrypt-resource
     * event_type : TRANSACTION.SUCCESS
     * summary : 支付成功
     * resource : {"original_type":"transaction","algorithm":"AEAD_AES_256_GCM","ciphertext":"MqEcQLOshAspXqtJSv+nFSKplVg4HUoJcFeRVi6ri+ecxdiqVZEZeqB/w2qLT0LP9S4fly+LK2Ajrjt7X1/91fbMvx0pBXojTVOR1BYdf7KxHh0ePFCAJXg6ygjV2uS+0FwTzR6y7FUZJQUkkcjM7XIUc2oOtN1TVyDwNuMeWcmE9wNy9nvJSoxb49dozk7EGfbafab/MKctqR8dftiOuM5if8hiaXEZ7R6eqoDiLZvHf+lVtKd4RzGGJeYf/Ea6N9clj94AU6AUVS86UiiF91tugoaQvlgA6E9Gv+mYJ8R+J/niAdKb7VjQkFYcB3tCRECVQhb/cadzOXhEaWx996LsLHAClnbRTuGUxilviRsGXprA2ZgmMXc6DNYAUwOn4g0LPaEDT36vl1B6vw6cmLv8uBImGMXqE1/57VjnDTehkgpvSKvmwppsVIS75V8dfWjUyswVs6g+ZUNQ/tN3dirlAq5cTk5iswgkBq0ELC40Zqq6orNoJWnGXcnIIeAC9XP8E5xztDXVsyxd1WgUPrIaQUgUXRscqXQa63JmX4WiYo2DkZcEb6mEKHbPe2MoVJjuHeURhqOKOqWEz9X3EgU=","associated_data":"transaction","nonce":"nKV0giifhndO"}
     */

    @JsonProperty("id")
    private String id;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("resource_type")
    private String resourceType;
    @JsonProperty("event_type")
    private String eventType;
    @JsonProperty("summary")
    private String summary;
    @JsonProperty("resource")
    private Resource resource;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Resource implements Serializable {
        /**
         * original_type : transaction
         * algorithm : AEAD_AES_256_GCM
         * ciphertext : MqEcQLOshAspXqtJSv+nFSKplVg4HUoJcFeRVi6ri+ecxdiqVZEZeqB/w2qLT0LP9S4fly+LK2Ajrjt7X1/91fbMvx0pBXojTVOR1BYdf7KxHh0ePFCAJXg6ygjV2uS+0FwTzR6y7FUZJQUkkcjM7XIUc2oOtN1TVyDwNuMeWcmE9wNy9nvJSoxb49dozk7EGfbafab/MKctqR8dftiOuM5if8hiaXEZ7R6eqoDiLZvHf+lVtKd4RzGGJeYf/Ea6N9clj94AU6AUVS86UiiF91tugoaQvlgA6E9Gv+mYJ8R+J/niAdKb7VjQkFYcB3tCRECVQhb/cadzOXhEaWx996LsLHAClnbRTuGUxilviRsGXprA2ZgmMXc6DNYAUwOn4g0LPaEDT36vl1B6vw6cmLv8uBImGMXqE1/57VjnDTehkgpvSKvmwppsVIS75V8dfWjUyswVs6g+ZUNQ/tN3dirlAq5cTk5iswgkBq0ELC40Zqq6orNoJWnGXcnIIeAC9XP8E5xztDXVsyxd1WgUPrIaQUgUXRscqXQa63JmX4WiYo2DkZcEb6mEKHbPe2MoVJjuHeURhqOKOqWEz9X3EgU=
         * associated_data : transaction
         * nonce : nKV0giifhndO
         */

        @JsonProperty("original_type")
        private String originalType;
        @JsonProperty("algorithm")
        private String algorithm;
        @JsonProperty("ciphertext")
        private String ciphertext;
        @JsonProperty("associated_data")
        private String associatedData;
        @JsonProperty("nonce")
        private String nonce;
    }


}

