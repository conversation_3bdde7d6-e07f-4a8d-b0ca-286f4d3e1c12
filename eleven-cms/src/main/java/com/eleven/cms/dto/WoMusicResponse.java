package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WoMusicResponse implements Serializable {

    public static final String RETURN_CODE_SUCCESS = "000000";
    /**
     * returnCode : 000000
     * description :
     * subedProducts : [{"productId":"4900753400","productName":"心动彩铃10元包月","status":"1","subTime":"20220302100708"}]
     */
    //000000	成功
    //200001	输入的必选参数为空
    //200002	参数格式错误
    //100001	系统忙
    //100005	数据库执行失败
    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("description")
    private String description;
    @JsonProperty("subedProducts")
    private List<SubedProducts> subedProducts;

    public boolean isOK(){
        return StringUtils.isNotBlank(this.getReturnCode()) && RETURN_CODE_SUCCESS.equals(this.getReturnCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class SubedProducts implements Serializable {
        /**
         * productId : 4900753400
         * productName : 心动彩铃10元包月
         * status : 1
         * subTime : 20220302100708
         */

        @JsonProperty("productId")
        private String productId;
        @JsonProperty("productName")
        private String productName;
        //订购状态
        //1: 正式订购（正常）
        //2: 暂停（隐藏）
        //4: 订购关系停止（等待删除）
        @JsonProperty("status")
        private String status;

        @JsonProperty("subTime")
        private String subTime;

        @JsonProperty("unSubTime")
        private String unSubTime;
    }
}
