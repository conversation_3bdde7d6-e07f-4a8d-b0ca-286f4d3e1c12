package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/12 15:14
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WechatPayDecodeNotifyParam implements Serializable {
    private static final long serialVersionUID = 3194877572505674535L;
    /**
     * mchid : **********
     * appid : wx8ad568ceec585da2
     * out_trade_no : 159154016561697076842712
     * transaction_id : 4200002012202310129346712305
     * trade_type : JSAPI
     * trade_state : SUCCESS
     * trade_state_desc : 支付成功
     * bank_type : CEB_CREDIT
     * attach :
     * success_time : 2023-10-12T10:14:24+08:00
     * payer : {"openid":"osVrzwochhhbRiBnLk-FIAiGqGkU"}
     * amount : {"total":1700,"payer_total":1700,"currency":"CNY","payer_currency":"CNY"}
     */

    @JsonProperty("mchid")
    private String mchid;
    @JsonProperty("appid")
    private String appid;
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    @JsonProperty("transaction_id")
    private String transactionId;
    @JsonProperty("trade_type")
    private String tradeType;
    @JsonProperty("trade_state")
    private String tradeState;
    @JsonProperty("trade_state_desc")
    private String tradeStateDesc;
    @JsonProperty("bank_type")
    private String bankType;
    @JsonProperty("attach")
    private String attach;
    @JsonProperty("success_time")
    private String successTime;
    @JsonProperty("payer")
    private Payer payer;
    @JsonProperty("amount")
    private Amount amount;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Payer implements Serializable {
        /**
         * openid : osVrzwochhhbRiBnLk-FIAiGqGkU
         */

        @JsonProperty("openid")
        private String openid;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Amount implements Serializable {
        /**
         * total : 1700
         * payer_total : 1700
         * currency : CNY
         * payer_currency : CNY
         */

        @JsonProperty("total")
        private Integer total;
        @JsonProperty("payer_total")
        private Integer payerTotal;
        @JsonProperty("currency")
        private String currency;
        @JsonProperty("payer_currency")
        private String payerCurrency;
    }
}
