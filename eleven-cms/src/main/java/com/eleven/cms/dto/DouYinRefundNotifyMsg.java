package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/26 16:42
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DouYinRefundNotifyMsg implements Serializable {

    /**
     * app_id : ttcfdbb96650e33350
     * status : SUCCESS
     * order_id : motb7057980663048
     * cp_extra :
     * message :
     * event_time : 1643185934447
     * refund_id : motb7014925531429
     * out_refund_no : 530402398023
     * refund_total_amount : 1
     * is_all_settled : false
     * refund_item_detail : {"item_order_quantity":1,"item_order_detail":[{"refund_amount":1,"item_order_id":"motb75515980663048"}]}
     */

    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("status")
    private String status;
    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("cp_extra")
    private String cpExtra;
    @JsonProperty("message")
    private String message;
    @JsonProperty("event_time")
    private long eventTime;
    @JsonProperty("refund_id")
    private String refundId;
    @JsonProperty("out_refund_no")
    private String outRefundNo;
    @JsonProperty("refund_total_amount")
    private int refundTotalAmount;
    @JsonProperty("is_all_settled")
    private boolean isAllSettled;
    @JsonProperty("refund_item_detail")
    private RefundItemDetail refundItemDetail;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class RefundItemDetail implements Serializable {
        /**
         * item_order_quantity : 1
         * item_order_detail : [{"refund_amount":1,"item_order_id":"motb75515980663048"}]
         */

        @JsonProperty("item_order_quantity")
        private int itemOrderQuantity;
        @JsonProperty("item_order_detail")
        private List<ItemOrderDetail> itemOrderDetail;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ItemOrderDetail implements Serializable {
            /**
             * refund_amount : 1
             * item_order_id : motb75515980663048
             */

            @JsonProperty("refund_amount")
            private int refundAmount;
            @JsonProperty("item_order_id")
            private String itemOrderId;
        }
    }
}
