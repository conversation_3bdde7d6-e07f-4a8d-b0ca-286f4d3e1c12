package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/26 16:28
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DouYinRefund implements Serializable {
    public static final Integer CODE_OK = 0;
    /**
     * err_msg :
     * err_no : 0
     * log_id : 202312291437241DF526835CCA296AC529
     * data : {"refund_id":"motb73178975800488286725902"}
     */

    @JsonProperty("err_msg")
    private String errMsg;
    @JsonProperty("err_no")
    private Integer errNo;
    @JsonProperty("log_id")
    private String logId;
    @JsonProperty("data")
    private Data data;
    /**
     * 判断是否成功
     */
    public boolean isOK() {
        return CODE_OK.equals(this.getErrNo()) ;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * refund_id : motb73178975800488286725902
         */

        @JsonProperty("refund_id")
        private String refundId;
    }
}
