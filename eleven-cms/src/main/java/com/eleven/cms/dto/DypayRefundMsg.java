package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@Data
public class DypayRefundMsg implements Serializable {

    /**
     * appid : ttb8bece032785e300
     * cp_refundno : RD818440313350422528011772773
     * cp_extra :
     * status : SUCCESS
     * refund_amount : 13800
     * is_all_settled : false
     * refunded_at : 1645523993
     * message : 商户余额不足
     * order_id : 7064214528778700000
     * refund_no : 6926510404499680000
     */

    @JsonProperty("appid")
    private String appid;
    @JsonProperty("cp_refundno")
    private String cpRefundno;
    @JsonProperty("cp_extra")
    private String cpExtra;
    @JsonProperty("status")
    private String status;
    @JsonProperty("refund_amount")
    private int refundAmount;
    @JsonProperty("is_all_settled")
    private boolean isAllSettled;
    @JsonProperty("refunded_at")
    private int refundedAt;
    @JsonProperty("message")
    private String message;
    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("refund_no")
    private String refundNo;
}
