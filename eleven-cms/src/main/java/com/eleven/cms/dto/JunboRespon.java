package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2021/1/14 13:21
 * Desc:Todo
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JunboRespon implements Serializable {
    public static final String CODE_OK = "0";
    public static final JunboRespon FAIL_RESPON = new JunboRespon("9999", "通讯失败");
    /**
     * code : 0
     * msg : 成功
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    /**
     * 判断是否成功
     */
    public boolean isOK() {
        return CODE_OK.equals(this.getCode());
    }
}
