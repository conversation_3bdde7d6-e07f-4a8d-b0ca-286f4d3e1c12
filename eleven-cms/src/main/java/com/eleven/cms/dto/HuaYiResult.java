package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 华逸多会员直充结果回调
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/7/11 15:26
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HuaYiResult implements Serializable {


    /**
     * cardType : MTDC_30
     * channelCode : YOURAN_001
     * money : 18.55
     * msg : 兑换成功
     * phone : 13438828200
     * status : 000000
     * sysOrderId : 202307111754096313254
     */
    @JsonProperty("cardType")
    private String cardType;
    @JsonProperty("channelCode")
    private String channelCode;
    @JsonProperty("money")
    private double money;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("status")
    private String status;
    @JsonProperty("sysOrderId")
    private String sysOrderId;
}
