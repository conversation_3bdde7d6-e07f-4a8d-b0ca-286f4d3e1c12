package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/24 10:23
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WechatpayNotify implements Serializable {

    /**
     * mchid : **********
     * appid : wx779f98ee4d27eca4
     * out_trade_no : 6cf1055938aa5ce40083e50dbd6af201
     * transaction_id : 4200002544202501242233352426
     * trade_type : APP
     * trade_state : SUCCESS
     * trade_state_desc : 支付成功
     * bank_type : CMB_CREDIT
     * attach :
     * success_time : 2025-01-24T10:02:14+08:00
     * payer : {"openid":"oPzzO63-Zo95Pxe7c6NTGuAQyhMk"}
     * amount : {"total":1,"payer_total":1,"currency":"CNY","payer_currency":"CNY"}
     */

    @JsonProperty("mchid")
    private String mchid;
    @JsonProperty("appid")
    private String appid;
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    @JsonProperty("transaction_id")
    private String transactionId;
    @JsonProperty("trade_type")
    private String tradeType;
    @JsonProperty("trade_state")
    private String tradeState;
    @JsonProperty("trade_state_desc")
    private String tradeStateDesc;
    @JsonProperty("bank_type")
    private String bankType;
    @JsonProperty("attach")
    private String attach;
    @JsonProperty("success_time")
    private String successTime;
    @JsonProperty("payer")
    private Payer payer;
    @JsonProperty("amount")
    private Amount amount;
    @JsonProperty("refund_status")
    private String  refundStatus;
    @JsonProperty("out_refund_no")
    private String  outRefundNo;
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Payer implements Serializable {
        /**
         * openid : oPzzO63-Zo95Pxe7c6NTGuAQyhMk
         */

        @JsonProperty("openid")
        private String openid;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Amount implements Serializable {
        /**
         * total : 1
         * payer_total : 1
         * currency : CNY
         * payer_currency : CNY
         */

        @JsonProperty("total")
        private int total;
        @JsonProperty("payer_total")
        private int payerTotal;
        @JsonProperty("currency")
        private String currency;
        @JsonProperty("payer_currency")
        private String payerCurrency;
    }
}
