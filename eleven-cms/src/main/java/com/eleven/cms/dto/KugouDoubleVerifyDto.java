package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KugouDoubleVerifyDto implements Serializable {

    public static final Integer EXIST_TRUE = 1;
    public static final Integer EXIST_FALSE = 0;

    private ExistData data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ExistData implements Serializable {
        private Integer exist;
    }

    public KugouDoubleVerifyDto existTrue(){
        ExistData existData = new ExistData();
        existData.setExist(EXIST_TRUE);
        this.setData(existData);
        return this;
    }
    public KugouDoubleVerifyDto existFalse(){
        ExistData existData = new ExistData();
        existData.setExist(EXIST_FALSE);
        this.setData(existData);
        return this;
    }
}
