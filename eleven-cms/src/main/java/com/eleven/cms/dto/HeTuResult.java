package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 河图会员直充结果回调
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HeTuResult {
    //券码
    @JsonProperty("couponCode")
    private String couponCode;
    //权益类型
    @JsonProperty("type")
    private String type;
    //使用时间
    @JsonProperty("useTime")
    private String useTime;
    //状态(1=充值成功,2=充值失败)
    @JsonProperty("status")
    private String status;
    //描述
    @JsonProperty("callbackMsg")
    private String callbackMsg;

    //区服
    @JsonProperty("sendServer")
    private String sendServer;

    //角色
    @JsonProperty("sendRole")
    private String sendRole;
}
