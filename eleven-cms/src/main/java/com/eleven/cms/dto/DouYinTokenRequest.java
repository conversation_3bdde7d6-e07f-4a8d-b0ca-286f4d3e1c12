package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.http.HttpStatus;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/29 15:00
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DouYinTokenRequest implements Serializable {
    public static final Integer CODE_OK = 0;
    /**
     * err_no : 0
     * err_tips : success
     * data : {"session_key":"rc2Q26xGDYThbfYUqj0Lww==","openid":"_000kHEHtoTdtViCEPZeY1C5dcfIJmy0eLZU","anonymous_openid":"IaXb19sBdNiDwR9v","unionid":"ffa1e598-d303-48e5-b724-5ded375609bf","dopenid":""}
     */

    @JsonProperty("err_no")
    private Integer errNo;
    @JsonProperty("err_tips")
    private String errTips;
    @JsonProperty("data")
    private Data data;
    /**
     * 判断是否成功
     */
    public boolean isOK() {
        return CODE_OK.equals(this.getErrNo()) && this.getData()!=null;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * session_key : rc2Q26xGDYThbfYUqj0Lww==
         * openid : _000kHEHtoTdtViCEPZeY1C5dcfIJmy0eLZU
         * anonymous_openid : IaXb19sBdNiDwR9v
         * unionid : ffa1e598-d303-48e5-b724-5ded375609bf
         * dopenid :
         */

        @JsonProperty("session_key")
        private String sessionKey;
        @JsonProperty("openid")
        private String openid;
        @JsonProperty("anonymous_openid")
        private String anonymousOpenid;
        @JsonProperty("unionid")
        private String unionid;
        @JsonProperty("dopenid")
        private String dopenid;
    }
}
