package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/7 11:10
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DuocaiProductResponse implements Serializable {

    /**
     * message : 获取验证码成功
     * code : 0
     * result : {"goodsName":"10 元代金券","goodsId":"23409123345345"}
     */

    @JsonProperty("message")
    private String message;
    @JsonProperty("code")
    private int code;
    @JsonProperty("result")
    private Result result;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Result implements Serializable {
        /**
         * goodsName : 10 元代金券
         * goodsId : 23409123345345
         */
        //商品名称
        @JsonProperty("goodsName")
        private String goodsName;
        //商品ID
        @JsonProperty("goodsId")
        private String goodsId;
        //是 1 商品状态：0-下架1-上架
        @JsonProperty("goodsStatus")
        private Integer goodsStatus;
        //商品主图
        @JsonProperty("goodsImage")
        private String goodsImage;
        //面额
        @JsonProperty("price")
        private BigDecimal price;
        //库存
        @JsonProperty("stockAmount")
        private Integer stockAmount;
        //结算价
        @JsonProperty("settlementPrice")
        private BigDecimal settlementPrice;
        //核销方式：卡券类型特有核销方式-券码特有：1，二维码2，条形码3，二维码和条形码4，卡券URL地址5，密码6，卡号和密码
        @JsonProperty("ticketFormatType")
        private Integer ticketFormatType;
        //充值类型: 充值类型特有充值类型: 1. 厂家直冲2.卖家直发3.官方直充4.扫码直充5.卖家代充6.卡密
        @JsonProperty("delivery")
        private Integer delivery;
        //品牌
        @JsonProperty("brand")
        private String brand;
        //权益描述
        @JsonProperty("description")
        private String description;
        //使用规则
        @JsonProperty("useRule")
        private String useRule;
    }
}
