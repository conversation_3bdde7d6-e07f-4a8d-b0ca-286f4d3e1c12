package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 电信云游戏通知
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/7 16:48
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DianxinCloudGameNotify implements Serializable {

    /**
     * mchId : 70231
     * phone : 13xxxxxxxxx
     * sequenceId : 202312201805462393548298
     * transData : Cjq31X2ytRgE9Fjz
     * subtime : 2023-12-20 18:06:37
     * resultCode : 1
     * signature : a64d436ca5dd3bf25a8f97aca55b7f9f
     */

    @JsonProperty("mchId")
    private int mchId;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("sequenceId")
    private String sequenceId;
    @JsonProperty("transData")
    private String transData;
    @JsonProperty("subtime")
    private String subtime;
    @JsonProperty("resultCode")
    private Integer resultCode;
    @JsonProperty("errMsg")
    private String errMsg;


    @JsonProperty("signature")
    private String signature;
}
