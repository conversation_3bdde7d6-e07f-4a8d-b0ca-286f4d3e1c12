package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/4 16:06
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XunYouQueryResp implements Serializable {

    /**
     * orderId : 9618411012fb4fb0a713e0ad3dff652b
     * productName : 合作手机号订单-firstcharge
     * description : 麦禾合作,1
     * num : 1
     * totalFee : 10.8
     * freeDays : 0
     * status : 3
     * createTime : 2024-12-04 15:47:50
     * tradeNo : 20241204145825
     * paidTime : 20241204154750
     * seconds : 0
     * accelDays : 30
     */

    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("productName")
    private String productName;
    @JsonProperty("description")
    private String description;
    @JsonProperty("num")
    private int num;
    @JsonProperty("totalFee")
    private double totalFee;
    @JsonProperty("freeDays")
    private int freeDays;
    @JsonProperty("status")
    private int status;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("tradeNo")
    private String tradeNo;
    @JsonProperty("paidTime")
    private String paidTime;
    @JsonProperty("seconds")
    private int seconds;
    @JsonProperty("accelDays")
    private int accelDays;
    public boolean isOk() {
        return StringUtils.isNotBlank(this.getOrderId());
    }
    public static final XunYouQueryResp FAIL_RESULT = new XunYouQueryResp();
}
