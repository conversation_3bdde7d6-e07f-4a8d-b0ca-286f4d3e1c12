package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MiGuKuaiYouVRJingMengGetUserResult implements Serializable {
    public static final String CODE_OK = "200";
    /**
     * data : [{"product":"006145987017","province":"北京","mobile":"18500138792","isLegalUser":"true","status":100},{"product":"006145987027","province":"北京","mobile":"18500138792","isLegalUser":"true","status":100}]
     * result : {"code":"200","msg":"SUCCESS"}
     */

    @JsonProperty("result")
    private Result result;
    @JsonProperty("data")
    private List<Data> data;
    public boolean isOK() {
        return data!=null && !data.isEmpty();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Result implements Serializable {
        /**
         * code : 200
         * msg : SUCCESS
         */

        @JsonProperty("code")
        private String code;
        @JsonProperty("msg")
        private String msg;
        public boolean isOK() {
            return CODE_OK.equals(code);
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * product : 006145987017
         * province : 北京
         * mobile : 18500138792
         * isLegalUser : true
         * status : 100
         */

        @JsonProperty("product")
        private String product;
        @JsonProperty("province")
        private String province;
        @JsonProperty("mobile")
        private String mobile;
        @JsonProperty("isLegalUser")
        private String isLegalUser;
        @JsonProperty("status")
        private Integer status;
    }
}
