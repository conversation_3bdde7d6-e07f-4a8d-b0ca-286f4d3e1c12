package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/6 15:48
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DuoCaiOpenNotify implements Serializable {

    /**
     * trxId : *****************
     * goodsId : ***************
     * count : 1
     * accountNo : ***********
     * orderId : *****************
     * settlementPrice : 0
     * callbackUrl : https://api.gzgdxmt.com/supplier/imsApi/order/callback/4
     */

    @JsonProperty("trxId")
    private String trxId;
    @JsonProperty("goodsId")
    private String goodsId;
    @JsonProperty("count")
    private int count;
    @JsonProperty("accountNo")
    private String accountNo;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("settlementPrice")
    private String settlementPrice;
    @JsonProperty("callbackUrl")
    private String callbackUrl;
    @JsonProperty("createTime")
    private String createTime;
}
