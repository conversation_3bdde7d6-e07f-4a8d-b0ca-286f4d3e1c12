package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/4 15:52
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XunYouRechargeResp implements Serializable {
    /**
     * phoneNum : 15915401656
     * partnerOrderId : 20241204145825
     * orderId : 9618411012fb4fb0a713e0ad3dff652b
     */

    @JsonProperty("phoneNum")
    private String phoneNum;
    @JsonProperty("partnerOrderId")
    private String partnerOrderId;
    @JsonProperty("orderId")
    private String orderId;
    public boolean isOk() {
        return StringUtils.isNotBlank(this.getOrderId());
    }
    public static final XunYouRechargeResp FAIL_RESULT = new XunYouRechargeResp();
}
