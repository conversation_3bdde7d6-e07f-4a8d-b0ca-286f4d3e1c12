package com.eleven.cms.dto;

import lombok.Data;
import org.jeecg.common.constant.CommonConstant;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/13 14:40
 **/
@Data
public class MiGuHuYuNotifyResp {
    private int isDirect;
    private int returnCode;
    private String msg;


    public static MiGuHuYuNotifyResp ok(String msg) {
        MiGuHuYuNotifyResp notify=new MiGuHuYuNotifyResp();
        notify.setIsDirect(CommonConstant.STATUS_NORMAL);
        notify.setReturnCode(CommonConstant.LOG_TYPE_1);
        notify.setMsg(msg);
        return notify;
    }


    public static MiGuHuYuNotifyResp error(String msg) {
        MiGuHuYuNotifyResp notify=new MiGuHuYuNotifyResp();
        notify.setIsDirect(CommonConstant.STATUS_NORMAL);
        notify.setReturnCode(CommonConstant.STATUS_NORMAL);
        notify.setMsg(msg);
        return notify;
    }
}
