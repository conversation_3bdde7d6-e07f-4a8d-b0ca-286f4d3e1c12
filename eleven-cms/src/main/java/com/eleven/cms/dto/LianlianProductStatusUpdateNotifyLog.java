package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/15 14:48
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianlianProductStatusUpdateNotifyLog implements Serializable {

    /**
     * productId : 3288717
     * type : 1
     * items : [4596135]
     */

    @JsonProperty("productId")
    private int productId;
    @JsonProperty("type")
    private int type;
    @JsonProperty("items")
    private List<Integer> items;
}
