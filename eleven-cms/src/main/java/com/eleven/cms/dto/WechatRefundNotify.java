package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/24 10:49
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WechatRefundNotify implements Serializable {

    /**
     * amount : {"currency":"CNY","discount_refund":0,"from":[],"payer_refund":1,"payer_total":1,"refund":1,"refund_fee":0,"settlement_refund":1,"settlement_total":1,"total":1}
     * channel : ORIGINAL
     * create_time : 2025-01-24T10:48:12+08:00
     * funds_account : AVAILABLE
     * out_refund_no : 0d37fa95dc323a132c6dc0d87f2c2d28
     * out_trade_no : 6cf1055938aa5ce40083e50dbd6af201
     * promotion_detail : []
     * refund_id : 50302602022025012490342294171
     * status : PROCESSING
     * transaction_id : 4200002544202501242233352426
     * user_received_account : 招商银行信用卡3829
     */

    @JsonProperty("amount")
    private Amount amount;
    @JsonProperty("channel")
    private String channel;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("funds_account")
    private String fundsAccount;
    @JsonProperty("out_refund_no")
    private String outRefundNo;
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    @JsonProperty("refund_id")
    private String refundId;
    @JsonProperty("status")
    private String status;
    @JsonProperty("transaction_id")
    private String transactionId;
    @JsonProperty("user_received_account")
    private String userReceivedAccount;
    @JsonProperty("promotion_detail")
    private List<?> promotionDetail;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Amount implements Serializable {
        /**
         * currency : CNY
         * discount_refund : 0
         * from : []
         * payer_refund : 1
         * payer_total : 1
         * refund : 1
         * refund_fee : 0
         * settlement_refund : 1
         * settlement_total : 1
         * total : 1
         */

        @JsonProperty("currency")
        private String currency;
        @JsonProperty("discount_refund")
        private int discountRefund;
        @JsonProperty("payer_refund")
        private int payerRefund;
        @JsonProperty("payer_total")
        private int payerTotal;
        @JsonProperty("refund")
        private int refund;
        @JsonProperty("refund_fee")
        private int refundFee;
        @JsonProperty("settlement_refund")
        private int settlementRefund;
        @JsonProperty("settlement_total")
        private int settlementTotal;
        @JsonProperty("total")
        private int total;
        @JsonProperty("from")
        private List<?> from;
    }
}
