package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 南山下发途牛权益通知
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/28 15:04
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NanshanOpenNotify implements Serializable {

    /**
     * timestamp : 20240401104105271
     * version : V1.0.0
     * dataLinkId : 1774628028782669832
     * phone : 13261339881
     * orderNo : 1774628028782669832
     * goodsNo : tuniu
     * notifyUrl : http://core-test-receive-center.tuopukeji.cn/api/crossBridge/notify
     * num : 1
     */

    @JsonProperty("timestamp")
    private String timestamp;
    @JsonProperty("version")
    private String version;
    @JsonProperty("dataLinkId")
    private String dataLinkId;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("orderNo")
    private String orderNo;
    @JsonProperty("goodsNo")
    private String goodsNo;
    @JsonProperty("notifyUrl")
    private String notifyUrl;
    @JsonProperty("num")
    private String num;
}
