package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WechatComplainResponse implements Serializable {
    /**
     * data : [{"complaint_detail":"办彩铃交了钱，没有彩铃","complaint_full_refunded":false,"complaint_id":"200000020230407000073884047","complaint_media_list":[],"complaint_order_info":[{"amount":9900,"out_trade_no":"1643188267615031298","transaction_id":"4200001798202304044083268377"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-07T00:21:31+08:00","incoming_user_response":false,"payer_phone":"f3EBE+qoTq9akLoUQYaVuf7NoLz3i0pLdnEf2JZt8ZwvT7YVxAXUYonKVVDIxr3jB+iLsjj5MK2wgGsnwVWp2pbJ2a/zFBOpyk9lRi+eole61OjbkzBaM3INVQrD44muhZSv+h5oU4KQ+20BpzLxCkgQ591u/VmNhRPXxf2jPDVG8HdXWMWIFUzFXvA+xTHsMP4Ywa7GdbjylWPkC/19rxGaSbqOw7/jjeWl99f7ENy+LAvol1qOeV8CpiFZaH9TgsEvEA/0y89mstwkEuKzozPcFmEQYmI1LZ/wotVLtBp452E8KcmNmWRcDpg/8S/R/waZk/gmsDNWdnAOGUx5JA==","problem_description":"不满意商家的商品或服务","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"为了一个视频彩铃我都交了99元，还用不了，什么企业家这是，完全是诈骗，退费退费","complaint_full_refunded":true,"complaint_id":"200000020230406130073753756","complaint_media_list":[{"media_type":"USER_COMPLAINT_IMAGE","media_url":["https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyMzA0MDYxMzAwNzM3NTM3NTYYACCi%2B96hBigBMAE4AQ%3D%3D"]}],"complaint_order_info":[{"amount":9900,"out_trade_no":"1642698978993229826","transaction_id":"4200001769202304033607941890"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-06T13:48:57+08:00","incoming_user_response":false,"problem_description":"交易被骗","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"我够买的企业广告至今为开通","complaint_full_refunded":false,"complaint_id":"200000020230405090073487130","complaint_media_list":[],"complaint_order_info":[{"amount":9900,"out_trade_no":"1641452067862609922","transaction_id":"4200001800202303305840564783"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-05T09:54:02+08:00","incoming_user_response":false,"payer_phone":"OAFVPTkAPmBIh8RdtIIijj7uHNh8ZMPjlUkQROjcJNItug6A70cPhmZuLz1K4+RendOJzVKk3fkitaNvLbLv3B0GWLSWa0juFWf1d+uqH1rpWKRCzY9UKxK7iUnBXHu34PMTlXRXvMd6A7bI8RAZjjCnBJLDwXR5hOBu4zChLi+kSNtqfoqWY1CepZ6uCqC+Ulz4DiYlQdHr0qJJfhwSGBBgK3kYtPbjnDBvpNHsroxgaDb4HiLv9TJ2XwabBVgwnz9SFxjXUBgmRJOD7Y6npPRobSoDXdSjsZ7FHuA/dNeefu7OncqR+B8hoc1jN8c4NfAx9qckOYCSnXMdx/N/6Q==","problem_description":"不满意商家的商品或服务","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"用户不存在音乐在手上","complaint_full_refunded":false,"complaint_id":"200000020230404220073423785","complaint_media_list":[],"complaint_order_info":[{"amount":9900,"out_trade_no":"1643210498206396417","transaction_id":"4200001786202304040680855273"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-04T22:35:08+08:00","incoming_user_response":false,"problem_description":"交易被骗","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"付钱给你了，铃声没有改变","complaint_full_refunded":false,"complaint_id":"200000020230404210073412397","complaint_media_list":[],"complaint_order_info":[{"amount":990,"out_trade_no":"1642942465965662209","transaction_id":"4200001802202304045121241805"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-04T21:25:30+08:00","incoming_user_response":false,"payer_phone":"nt1mJSxed8wqyFfk4WE53sefPPrmIQXWGeMgeI2GXjyTIa0fbtNBQVYWTa3fXQv6EhEOtymsYAoDl8IhLNPtE/0yLyXPQALTZwFHPgPdcyoncwHd8qFQgmiqdezrYqQiK8Ioa0LgBId2nTF05UVJuRRSEtxGszZszLAE6d8nRrNo+axwRaWj2vnBlWk0wEkyC8ejw7HGPxwT000kQXMvYeebUPrzIpObeodfV9WDWQj7hZ8chXKErA+zJMWbb1gLH2EbmiGoaoNXxPofRRFzGvWlPFA7gt4xtCdXftWWVtSeuRZD17LTNxrupyoGN3UsNmzIKEgO37Z1MU1ybd8IFA==","problem_description":"不满意商家的商品或服务","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"原来说99元，为什么又要每月收钱","complaint_full_refunded":true,"complaint_id":"200000020230404200073400620","complaint_media_list":[{"media_type":"USER_COMPLAINT_IMAGE","media_url":["https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyMzA0MDQyMDAwNzM0MDA2MjAYACCi%2B96hBigBMAE4AQ%3D%3D"]}],"complaint_order_info":[{"amount":9900,"out_trade_no":"1643206011408412673","transaction_id":"4200001780202304043739121592"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-04T20:48:35+08:00","incoming_user_response":false,"payer_phone":"p64D4CcqE+I7Li3MQ5xX/ry8kbmwsQdBeAhS5IgfTl7aUTA9+kjTfT22nvKpF1bQi57lp2N43yCNg928gptEr0S/VkDlBiwCNvxRVvwOkZ7Tk/ft7b9qWHkVXQUGBcaUQmgbAkOCc24/bP64IY27YgT+2P9XDCyTuOurcFslXfWUI1v0EblRpVhlIzEZgGA6U5NvcDkqKz5Bo6eJnzmONAp5sNw1OUn3FOqEZhLJIwQAbM5xJyesaekxWYLlrxkM9BQRDBZYxcOfPN8FET+6rD7A8LgKsf5ubDm0R+OmGUMn4vSJzEfYBq4yWLa1oKRIHxsvjAYfisraJcMqJRCxVg==","problem_description":"不满意商家的商品或服务","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"费用太高想退订收费太高其他平台8、8元这个平台收费99元","complaint_full_refunded":true,"complaint_id":"200000020230404190073378807","complaint_media_list":[],"complaint_order_info":[{"amount":9900,"out_trade_no":"1643207543336005634","transaction_id":"4200001769202304047135553199"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-04T19:20:25+08:00","incoming_user_response":false,"payer_phone":"S6nRav7RbY8276lYe+YKvaOmKFiXxBKSq6zFS4x0Xe9vQVGEfD7uSdzpQgzIwpeboP0hYNjIpHSi/iLevIs9WXmvSoJcuHBce8/OcE2iMh+iDZZNH7clVEnVDnh5pEKkzxLEROV2xRrqmjGUXUEMJ65ou9HbbnYkIQTUUcdmUdZBUfryywkiuaAj9esHOhZtZJ2rTqm/Gpihc/qNFab9qFMMjgYhG+zkh26xcbA9JMjCSNc3CsDrWcvGV/hvkFPwc8vIABBSJ3ZhNxrRDZFqliYrDKIAFngBKcceCGB1BYOLP33soHl2z3cETwvV7ZGQn+FLEsRNWsjfs43YMWcWhg==","problem_description":"不满意商家的商品或服务","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"钱付多了要求退款请求商家查询","complaint_full_refunded":false,"complaint_id":"200000020230404180073381009","complaint_media_list":[],"complaint_order_info":[{"amount":9900,"out_trade_no":"1643195716271140866","transaction_id":"4200001782202304042191490050"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-04T18:46:24+08:00","incoming_user_response":false,"payer_phone":"i3UeZ0AZT9ql5NiPhpiN4w+KVt/xm/KpBHsgJeeE6eBusiNnSHR5/d6BByK+6u5x1hugRf9fhBP+MH4uYncFImF22ZS0RDq2oB5dwYtlcf2ymJsuS+zNPX+DacD3f0nLPpGmHCTdC+4EWQeAzjcaM60vcozASUAQimNOJxpuPrEUYpPWeRRVk3t7I8mydTE+bi55q4FDOzpkpOvAy/sTuxOJ5cQXxU9PjEQz6PbhYQiyyMt+27MrMURYIQuMYuffjacluveL4qvE+JdQTYRjO7kMT4VFc+v0VH0PP7ILNm3zUKCy37JyP017w93gGbkUARlO6Hn+cKjOJX3+FhEnBA==","problem_description":"其他问题","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"快平台进行宣传诈骗，99块钱终身使用，实际情况为正常每月交费使用。纯诈骗","complaint_full_refunded":true,"complaint_id":"200000020230403190073178017","complaint_media_list":[{"media_type":"USER_COMPLAINT_IMAGE","media_url":["https://api.mch.weixin.qq.com/v3/merchant-service/images/ChsyMDAwMDAwMjAyMzA0MDMxOTAwNzMxNzgwMTcYACCi%2B96hBigBMAE4AQ%3D%3D"]}],"complaint_order_info":[{"amount":9900,"out_trade_no":"1642848867802783745","transaction_id":"4200001786202304034550851062"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-03T19:54:20+08:00","incoming_user_response":false,"payer_phone":"njwv+61nhAYxDsiIOqX4IsuPyZYNhP+lq9vGU9ICmIpJO6qGo+O7g0RKW8KE4ZsYQtFLxqsFBXMQ7zGvDIGcxJMjfMaN+8vEuyq8KXHPhJ7JLb6l1zlP6fj4ufh5fWYrLTiOWm1pz9ZTAqvjF0wH5vrrY8kq+T9Ab0PDfNKLP4cv+IpgB22k/8qU8TDr4lMRataD008oXlcozYddguQAFf//Znhuiy/n56lqY+vEvXm+IY0qXdZfKUsLnKCsy19a/mXE4oRnaGOh9mYaoMuzHZ8Yc/217sIU9Dwh6YpydqUc/vxztHNp/rDJUSH7vvFlDFC7pTXLZxx8lXpNezud4Q==","problem_description":"其他问题","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]},{"complaint_detail":"不真实，我不想要了，要退款","complaint_full_refunded":true,"complaint_id":"200000020230403180073146747","complaint_media_list":[],"complaint_order_info":[{"amount":990,"out_trade_no":"1642800314644533249","transaction_id":"4200001781202304038378874702"}],"complaint_state":"PROCESSED","complaint_time":"2023-04-03T18:00:39+08:00","incoming_user_response":false,"payer_phone":"RwZxFFIjypS+zQ6ru2HjA9x50nmHUFMSIUvZX918le4DLBJYqqtiVs2a4se6BtCVE4IUgju3HCuTWQS8E6e+7FiyNMIs4eVYN1o9XKIVG26iPjGWkpwaHrvk28Lyx80cWrXSPw32TQfE1Iu0TrwIpeyqz+k3TskaUknMqKAvZRDshQahkr1uzpQy57pyrlvkA2u5orAypx536+gFK3odcROxbuihII5TMCzQsABUq6wNwlozZsu2usTSeYpKTS8oweHXoLtLIHutVwc061gj9TaOoVRHgdmKB4B2HOXnipNilwa4wvsBQf7/1xikJZQjLEWWJmtOpNPabZ/AB5+QrQ==","problem_description":"不满意商家的商品或服务","problem_type":"OTHERS","service_order_info":[],"user_complaint_times":1,"user_tag_list":[]}]
     * limit : 10
     * offset : 0
     * total_count : 26
     */

    @JsonProperty("limit")
    private Integer limit;
    @JsonProperty("offset")
    private Integer offset;
    @JsonProperty("total_count")
    private Integer totalCount;
    @JsonProperty("data")
    private List<Data> data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * complaint_detail : 办彩铃交了钱，没有彩铃
         * complaint_full_refunded : false
         * complaint_id : 200000020230407000073884047
         * complaint_media_list : []
         * complaint_order_info : [{"amount":9900,"out_trade_no":"1643188267615031298","transaction_id":"4200001798202304044083268377"}]
         * complaint_state : PROCESSED
         * complaint_time : 2023-04-07T00:21:31+08:00
         * incoming_user_response : false
         * payer_phone : f3EBE+qoTq9akLoUQYaVuf7NoLz3i0pLdnEf2JZt8ZwvT7YVxAXUYonKVVDIxr3jB+iLsjj5MK2wgGsnwVWp2pbJ2a/zFBOpyk9lRi+eole61OjbkzBaM3INVQrD44muhZSv+h5oU4KQ+20BpzLxCkgQ591u/VmNhRPXxf2jPDVG8HdXWMWIFUzFXvA+xTHsMP4Ywa7GdbjylWPkC/19rxGaSbqOw7/jjeWl99f7ENy+LAvol1qOeV8CpiFZaH9TgsEvEA/0y89mstwkEuKzozPcFmEQYmI1LZ/wotVLtBp452E8KcmNmWRcDpg/8S/R/waZk/gmsDNWdnAOGUx5JA==
         * problem_description : 不满意商家的商品或服务
         * problem_type : OTHERS
         * service_order_info : []
         * user_complaint_times : 1
         * user_tag_list : []
         */

        @JsonProperty("complaint_detail")
        private String complaintDetail;
        @JsonProperty("complaint_full_refunded")
        private Boolean complaintFullRefunded;
        @JsonProperty("complaint_id")
        private String complaintId;
        @JsonProperty("complaint_state")
        private String complaintState;
        @JsonProperty("complaint_time")
        private String complaintTime;
        @JsonProperty("incoming_user_response")
        private Boolean incomingUserResponse;
        @JsonProperty("payer_phone")
        private String payerPhone;
        @JsonProperty("problem_description")
        private String problemDescription;
        @JsonProperty("problem_type")
        private String problemType;
        @JsonProperty("apply_refund_amount")
        private Integer applyRefundAmount;
        @JsonProperty("user_complaint_times")
        private Integer userComplaintTimes;
        @JsonProperty("complaint_media_list")
        private List<?> complaintMediaList;
        @JsonProperty("complaint_order_info")
        private List<ComplaintOrderInfo> complaintOrderInfo;
        @JsonProperty("service_order_info")
        private List<?> serviceOrderInfo;
        @JsonProperty("user_tag_list")
        private List<?> userTagList;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @lombok.Data
        public static class ComplaintOrderInfo implements Serializable {
            /**
             * amount : 9900
             * out_trade_no : 1643188267615031298
             * transaction_id : 4200001798202304044083268377
             */

            @JsonProperty("amount")
            private Integer amount;
            @JsonProperty("out_trade_no")
            private String outTradeNo;
            @JsonProperty("transaction_id")
            private String transactionId;
        }
    }
}
