package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 酷狗回收酷狗音乐的豪华vip或音乐包响应封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KugouRecoverOrderResult {

    public static final KugouRecoverOrderResult FAIL_RESULT = new KugouRecoverOrderResult("0", "通讯失败",null);

    @JsonProperty("status")
    private String status;
    @JsonProperty("error_code")
    private String errorCode;
    @JsonProperty("data")
    private String data;

    public static KugouRecoverOrderResult fail() {
        return FAIL_RESULT;
    }

    public boolean isOK(){
        return "1".equals(status);
    }
}
