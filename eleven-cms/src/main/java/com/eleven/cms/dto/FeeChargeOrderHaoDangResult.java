package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/18 16:25
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeeChargeOrderHaoDangResult implements Serializable {

    /**
     * respCode : 0000
     */

    @JsonProperty("respCode")
    private String respCode;
    public static final FeeChargeOrderHaoDangResult FAIL_RESULT = new FeeChargeOrderHaoDangResult("500");

    public static final FeeChargeOrderHaoDangResult OK_RESULT = new FeeChargeOrderHaoDangResult("0000");
}
