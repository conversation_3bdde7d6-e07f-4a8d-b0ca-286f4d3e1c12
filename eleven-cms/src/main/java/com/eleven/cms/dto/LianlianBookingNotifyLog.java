package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/15 14:36
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianlianBookingNotifyLog implements Serializable {

    /**
     * channelId : 1
     * channelOrderId : C221221000057000004
     * thirdOrderId : 1671588991573027
     * orderId : 221221000057
     * status : 1
     * bookingDate : 2022-12-22 10:52:03
     */

    @JsonProperty("channelId")
    private int channelId;
    @JsonProperty("channelOrderId")
    private String channelOrderId;
    @JsonProperty("thirdOrderId")
    private String thirdOrderId;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("status")
    private int status;
    @JsonProperty("bookingDate")
    private String bookingDate;
}

