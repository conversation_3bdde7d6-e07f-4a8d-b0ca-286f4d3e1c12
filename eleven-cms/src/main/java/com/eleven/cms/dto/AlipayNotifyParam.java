package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class AlipayNotifyParam implements Serializable {

	private static final long serialVersionUID = 3194877572505674535L;
    /**
     * gmt_create : 2020-04-09 13:57:28
     * charset : UTF-8
     * seller_email : <EMAIL>
     * subject : 互助平台充值
     * sign : BHi1fM4pcsn9KdAhUOCVOFFMnzHv9VPUpIglHqRlo8/PM/mBlcjllGnv2y3JPJSpTsZofXR/Rt24TOXNiMDl0AyUTDN7x2r51ceCNbnD0gPR4FSYgZ8aLtLQDQKvJt8+S2nVnMpnO0jTLFehndVk7x++q7Ri7IyZ2sbn2pEiJ2DyUxdZROJ8z613yaWleOeCcd7dveLcfLO5g3wRz4DvV4iffGFeXliXZ/2hupDIQuotahysrhOTBhGxmctX7YbNT2VUdwRwMzEXak3Ecalg3B1NOsdAn1RQaFa2tERJQobJGBwGZg8swJ96Y6MP7euaMpq3u75nOA2goZ6lpRlb8w==
     * buyer_id : ****************
     * invoice_amount : 0.01
     * notify_id : 056d0346b25253a9067163fe02aed8chs1
     * fund_bill_list : [{"amount":"0.01","fundChannel":"ALIPAYACCOUNT"}]
     * notify_type : trade_status_sync
     * trade_status : TRADE_SUCCESS
     * receipt_amount : 0.01
     * buyer_pay_amount : 0.01
     * app_id : ****************
     * sign_type : RSA2
     * seller_id : ****************
     * gmt_payment : 2020-04-09 13:57:29
     * notify_time : 2020-04-09 13:57:30
     * version : 1.0
     * out_trade_no : 0948229f9e084d1793dfcbb70ea561a2
     * total_amount : 0.01
     * trade_no : 2020040922001402230504519221
     * auth_app_id : ****************
     * buyer_logon_id : gkh***@sandbox.com
     * point_amount : 0.00
     */

    @JsonProperty("app_id")
    private String appId;
    //@JsonProperty("auth_app_id")
    //private String authAppId;
    
    @JsonProperty("trade_no")
    private String tradeNo; // 支付宝交易凭证号
    @JsonProperty("out_trade_no")
    private String outTradeNo; // 原支付请求的商户订单号

    @JsonProperty("subject")
    private String subject;  // 商品的标题/交易标题/订单标题/订单关键字等
    //@JsonProperty("body")
    //private String body; // 该订单的备注、描述、明细等。对应请求时的body参数，原样通知回来
    @JsonProperty("fund_bill_list")
    private String fundBillList;  // 支付成功的各个渠道金额信息,array

    @JsonProperty("trade_status")
    private String tradeStatus;  // 交易目前所处的状态，见交易状态说明
    
    //@JsonProperty("sign")
    //private String sign;
    //@JsonProperty("sign_type")
    //private String signType;

    @JsonProperty("total_amount")
    private BigDecimal totalAmount;  // 本次交易支付的订单金额
    //@JsonProperty("point_amount")
    //private BigDecimal pointAmount;
    @JsonProperty("receipt_amount")
    private BigDecimal receiptAmount; // 商家在交易中实际收到的款项
    @JsonProperty("buyer_pay_amount")
    private BigDecimal buyerPayAmount;  // 用户在交易中支付的金额
    //@JsonProperty("invoice_amount")
    //private BigDecimal invoiceAmount;


    @JsonProperty("buyer_id")
    private String buyerId;  // 买家支付宝账号对应的支付宝唯一用户号。以2088开头的纯16位数字
    @JsonProperty("buyer_logon_id")
    private String buyerLogonId; // 买家支付宝账号
    
    @JsonProperty("seller_id")
    private String sellerId;  // 卖家支付宝用户号
    @JsonProperty("seller_email")
    private String sellerEmail;  // 卖家支付宝账号

    //@JsonProperty("charset")
    //private String charset;
    //@JsonProperty("version")
    //private String version;
    @JsonProperty("notify_id")
    private String notifyId;
    @JsonProperty("notify_type")
    private String notifyType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonProperty("notify_time")
    private LocalDateTime notifyTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonProperty("gmt_payment")
    private LocalDateTime gmtPayment;  // 该笔交易的买家付款时间。格式为yyyy-MM-dd HH:mm:ss
    //@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    //@JsonSerialize(using = LocalDateTimeSerializer.class)
    //@JsonDeserialize(using = LocalDateTimeDeserializer.class)
    //@JsonProperty("gmt_close")
    //private LocalDateTime gmtClose; // 该笔交易结束时间。格式为yyyy-MM-dd HH:mm:ss
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonProperty("gmt_create")
    private LocalDateTime gmtCreate;  // 该笔交易创建的时间。格式为yyyy-MM-dd HH:mm:ss

    //@JsonProperty("out_biz_no")
    //private String outBizNo; // 商户业务ID，主要是退款通知中返回退款申请的流水号
    //@JsonProperty("refund_fee")
    //private BigDecimal refundFee; // 退款通知中，返回总退款金额，单位为元，支持两位小数
    
    @JsonProperty("passback_params")
    private String passbackParams; // 公共回传参数，如果请求时传递了该参数，则返回给商户时会在异步通知时将该参数原样返回。

    public static void main(String[] args) throws JsonProcessingException {
        //String json = "{\"gmt_create\":\"2020-04-09 13:57:28\",\"charset\":\"UTF-8\",\"seller_email\":\"<EMAIL>\",\"subject\":\"互助平台充值\",\"sign\":\"BHi1fM4pcsn9KdAhUOCVOFFMnzHv9VPUpIglHqRlo8/PM/mBlcjllGnv2y3JPJSpTsZofXR/Rt24TOXNiMDl0AyUTDN7x2r51ceCNbnD0gPR4FSYgZ8aLtLQDQKvJt8+S2nVnMpnO0jTLFehndVk7x++q7Ri7IyZ2sbn2pEiJ2DyUxdZROJ8z613yaWleOeCcd7dveLcfLO5g3wRz4DvV4iffGFeXliXZ/2hupDIQuotahysrhOTBhGxmctX7YbNT2VUdwRwMzEXak3Ecalg3B1NOsdAn1RQaFa2tERJQobJGBwGZg8swJ96Y6MP7euaMpq3u75nOA2goZ6lpRlb8w==\",\"buyer_id\":\"****************\",\"invoice_amount\":\"0.01\",\"notify_id\":\"056d0346b25253a9067163fe02aed8chs1\",\"fund_bill_list\":\"[{\\\"amount\\\":\\\"0.01\\\",\\\"fundChannel\\\":\\\"ALIPAYACCOUNT\\\"}]\",\"notify_type\":\"trade_status_sync\",\"trade_status\":\"TRADE_SUCCESS\",\"receipt_amount\":\"0.01\",\"buyer_pay_amount\":\"0.01\",\"app_id\":\"****************\",\"sign_type\":\"RSA2\",\"seller_id\":\"****************\",\"gmt_payment\":\"2020-04-09 13:57:29\",\"notify_time\":\"2020-04-09 13:57:30\",\"version\":\"1.0\",\"out_trade_no\":\"0948229f9e084d1793dfcbb70ea561a2\",\"total_amount\":\"0.01\",\"trade_no\":\"2020040922001402230504519221\",\"auth_app_id\":\"****************\",\"buyer_logon_id\":\"gkh***@sandbox.com\",\"point_amount\":\"0.00\"}";
        String json = "";
        ObjectMapper mapper = new ObjectMapper();

       AlipayNotifyParam
               notifyParam = mapper.readerFor(AlipayNotifyParam.class).readValue(json);

        System.out.println(notifyParam);
    }

}

