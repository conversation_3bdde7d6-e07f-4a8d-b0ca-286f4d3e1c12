package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jeecg.common.constant.CommonConstant;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 17:32
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XimileRechargeNotifyResp implements Serializable {

    /**
     * retCode : 200
     * retMsg : 回调成功
     */

    @JsonProperty("retCode")
    private String retCode;
    @JsonProperty("retMsg")
    private String retMsg;



    public static XimileRechargeNotifyResp ok(String msg) {
        XimileRechargeNotifyResp notify=new XimileRechargeNotifyResp();
        notify.setRetCode(String.valueOf(CommonConstant.SC_OK_200));
        notify.setRetMsg(msg);
        return notify;
    }


    public static XimileRechargeNotifyResp error(String msg) {
        XimileRechargeNotifyResp notify=new XimileRechargeNotifyResp();
        notify.setRetCode(String.valueOf(CommonConstant.SC_INTERNAL_SERVER_ERROR_500));
        notify.setRetMsg(msg);
        return notify;
    }
}
