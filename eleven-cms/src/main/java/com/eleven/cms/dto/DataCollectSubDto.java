package com.eleven.cms.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 企业彩铃订购数据汇总
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/19 14:56
 **/
@Data
public class DataCollectSubDto  implements Serializable {
    //省份
    @Excel(name = "省份", width = 15)
    @TableField(exist = false)
    private String province;
    //订购数量
    @Excel(name = "订购数量", width = 15)
    @TableField(exist = false)
    private String subCount;
    //渠道号
    @Excel(name = "渠道号", width = 15)
    @TableField(exist = false)
    private String channel;
    //开始时间
    @TableField(exist = false)
    private String createTimeBegin;
    //结束时间
    @TableField(exist = false)
    private String createTimeEnd;
    //手机号
    @TableField(exist = false)
    private String mobile;

}
