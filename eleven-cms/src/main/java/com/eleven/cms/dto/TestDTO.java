package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @datetime 2024/11/12 16:23
 */
@Data
public class TestDTO {

    private String id;

    @Excel(name = "渠道号", width = 15)
    private String channel;

    @Excel(name = "咪咕渠道号", width = 15)
    private String miguChannel;

    @Excel(name = "手机号", width = 15)
    private String mobile;

    @Excel(name = "创建时间", width = 15)
    private String createTime;

    @Excel(name = "订购状态 0:失败 1:成功", width = 15)
    private Integer status;

    @Excel(name = "退订状态 0:未退订 1:已退订", width = 15)
    private Integer tdStatus;
}
