package com.eleven.cms.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
@Data
public class BusinessChargeDto implements Serializable {
    private static final long serialVersionUID = 1L;
    //省份
    @Excel(name = "省份", width = 15)
    @TableField(exist = false)
    private String province;
    //城市
    @Excel(name = "城市", width = 15)
    @TableField(exist = false)
    private String city;

    /**
     * 业务ID
     */
    @TableField(exist = false)
    private String serviceId;

    @Excel(name = "业务名字", width = 15)
    @TableField(exist = false)
    private String serviceName;

    @Excel(name = "数量", width = 15)
    @TableField(exist = false)
    private String sumCount;

    @Excel(name = "充值描述", width = 15)
    @TableField(exist = false)
    private String remark;

    /**
     * 权益ID
     */
    @TableField(exist = false)
    private String couponId;

    @Excel(name = "权益名称", width = 15)
    @TableField(exist = false)
    private String couponName;

    @Excel(name = "权益单价", width = 15)
    @TableField(exist = false)
    private Integer couponPrice;

    @Excel(name = "金额", width = 15)
    @TableField(exist = false)
    private Long sumMoney;

    //领取开始时间
    @TableField(exist = false)
    private String createTime_begin;
    //领取结束时间
    @TableField(exist = false)
    private String createTime_end;
    //领取开始时间
    @TableField(exist = false)
    private Date createTimeBegin;
    //领取结束时间
    @TableField(exist = false)
    private Date createTimeEnd;
    //充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充
    @TableField(exist = false)
    private String status;


}
