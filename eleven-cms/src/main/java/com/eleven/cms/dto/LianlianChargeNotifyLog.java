package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/15 10:26
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianlianChargeNotifyLog implements Serializable {

    /**
     * channelId : 111
     * channelOrderId : C230421021006010140
     * thirdOrderId : LLO1649240142329217024
     * batchCode : {"bookingUrl":"http://qd.llzby.vip/s/UUwJUNkW","qrCodeImgUrl":"https://cdn.lianlianlvyou.com/n/item/qrcode/1348629/0d554c8dbd339e5e19b0e8145c0d18ad.png","detailUrl":"http://qd.llzby.vip/s/XdIfp8A2"}
     * codeList : [{"orderId":"230421021006","code":"134862983466850","qrCodeImgUrl":"https://cdn.lianlianlvyou.com/n/item/qrcode/1348629/76053c190a2d7f4a4b3fa5d1372f7fd8.png","qrcodeUrl":"xxxxx"}]
     * qrCodeTime : 2023-04-21 10:34:42
     * sendCodeStatus : 1
     */

    @JsonProperty("channelId")
    private int channelId;
    @JsonProperty("channelOrderId")
    private String channelOrderId;
    @JsonProperty("thirdOrderId")
    private String thirdOrderId;
    @JsonProperty("batchCode")
    private BatchCode batchCode;
    @JsonProperty("qrCodeTime")
    private String qrCodeTime;
    @JsonProperty("sendCodeStatus")
    private Integer sendCodeStatus;
    @JsonProperty("codeList")
    private List<CodeList> codeList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class BatchCode implements Serializable {
        /**
         * bookingUrl : http://qd.llzby.vip/s/UUwJUNkW
         * qrCodeImgUrl : https://cdn.lianlianlvyou.com/n/item/qrcode/1348629/0d554c8dbd339e5e19b0e8145c0d18ad.png
         * detailUrl : http://qd.llzby.vip/s/XdIfp8A2
         */

        @JsonProperty("bookingUrl")
        private String bookingUrl;
        @JsonProperty("qrCodeImgUrl")
        private String qrCodeImgUrl;
        @JsonProperty("detailUrl")
        private String detailUrl;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class CodeList implements Serializable {
        /**
         * orderId : 230421021006
         * code : 134862983466850
         * qrCodeImgUrl : https://cdn.lianlianlvyou.com/n/item/qrcode/1348629/76053c190a2d7f4a4b3fa5d1372f7fd8.png
         * qrcodeUrl : xxxxx
         */

        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("code")
        private String code;
        @JsonProperty("qrCodeImgUrl")
        private String qrCodeImgUrl;
        @JsonProperty("qrcodeUrl")
        private String qrcodeUrl;
    }
}
