package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WechatComplainDetail implements Serializable {

    /**
     * complaint_id : 200201820200101080076610000
     * complaint_time : 2015-05-20T13:29:35.120+08:00
     * complaint_detail : 反馈一个重复扣费的问题
     * complainted_mchid : 1900012181
     * complaint_state : PENDING
     * payer_phone : sGdNeTHMQGlxCWiUyHu6XNO9GCYln2Luv4HhwJzZBfcL12sB
     * payer_openid : oUpF8uMuAJO_M2pxb1Q9zNjWeS6o
     * complaint_media_list : [{"media_type":"USER_COMPLAINT_IMAGE","media_url":["https://api.mch.weixin.qq.com/v3/merchant-service/images/xxxxx"]}]
     * complaint_order_info : [{"transaction_id":"4200000404201909069117582536","out_trade_no":"20190906154617947762231","amount":3}]
     * service_order_info : [{"order_id":"15646546545165651651","out_order_no":"1234323JKHDFE1243252","state":"DOING"}]
     * complaint_full_refunded : true
     * incoming_user_response : true
     * problem_description : 不满意商家服务
     * user_complaint_times : 1
     * additional_info : {"share_power_info":{"return_time":"2023-02-10T14:44:00+08:00"},"type":"SHARE_POWER_TYPE"}
     */

    @JsonProperty("complaint_id")
    private String complaintId;
    @JsonProperty("complaint_time")
    private String complaintTime;
    @JsonProperty("complaint_detail")
    private String complaintDetail;
    @JsonProperty("complainted_mchid")
    private Integer complaintedMchid;
    @JsonProperty("complaint_state")
    private String complaintState;
    @JsonProperty("payer_phone")
    private String payerPhone;
    @JsonProperty("payer_openid")
    private String payerOpenid;

    @JsonProperty("complaint_full_refunded")
    private Boolean complaintFullRefunded;
    @JsonProperty("incoming_user_response")
    private Boolean incomingUserResponse;
    @JsonProperty("problem_description")
    private String problemDescription;
    @JsonProperty("user_complaint_times")
    private Integer userComplaintTimes;

    @JsonProperty("problem_type")
    private String problemType;

    @JsonProperty("apply_refund_amount")
    private Integer applyRefundAmount;

    @JsonProperty("additional_info")
    private AdditionalInfo additionalInfo;
    @JsonProperty("complaint_media_list")
    private List<ComplaintMediaList> complaintMediaList;
    @JsonProperty("complaint_order_info")
    private List<ComplaintOrderInfo> complaintOrderInfo;
    @JsonProperty("service_order_info")
    private List<ServiceOrderInfo> serviceOrderInfo;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class AdditionalInfo implements Serializable {
        /**
         * share_power_info : {"return_time":"2023-02-10T14:44:00+08:00"}
         * type : SHARE_POWER_TYPE
         */

        @JsonProperty("share_power_info")
        private SharePowerInfo sharePowerInfo;
        @JsonProperty("type")
        private String type;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class SharePowerInfo implements Serializable {
            /**
             * return_time : 2023-02-10T14:44:00+08:00
             */

            @JsonProperty("return_time")
            private String returnTime;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ComplaintMediaList implements Serializable {
        /**
         * media_type : USER_COMPLAINT_IMAGE
         * media_url : ["https://api.mch.weixin.qq.com/v3/merchant-service/images/xxxxx"]
         */

        @JsonProperty("media_type")
        private String mediaType;
        @JsonProperty("media_url")
        private List<String> mediaUrl;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ComplaintOrderInfo implements Serializable {
        /**
         * transaction_id : 4200000404201909069117582536
         * out_trade_no : 20190906154617947762231
         * amount : 3
         */

        @JsonProperty("transaction_id")
        private String transactionId;
        @JsonProperty("out_trade_no")
        private String outTradeNo;
        @JsonProperty("amount")
        private Integer amount;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ServiceOrderInfo implements Serializable {
        /**
         * order_id : 15646546545165651651
         * out_order_no : 1234323JKHDFE1243252
         * state : DOING
         */

        @JsonProperty("order_id")
        private String orderId;
        @JsonProperty("out_order_no")
        private String outOrderNo;
        @JsonProperty("state")
        private String state;
    }
}
