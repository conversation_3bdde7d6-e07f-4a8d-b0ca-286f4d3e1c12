package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/11 16:11
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WxJsAPIpayDecodeNotifyParam implements Serializable {

    /**
     * sp_appid : wx8888888888888888
     * sp_mchid : **********
     * sub_appid : wxd678efh567hg6999
     * sub_mchid : **********
     * out_trade_no : 1217752501201407033233368018
     * trade_state_desc : 支付成功
     * trade_type : MICROPAY
     * attach : 自定义数据
     * transaction_id : 1217752501201407033233368018
     * trade_state : SUCCESS
     * bank_type : CMC
     * success_time : 2018-06-08T10:34:56+08:00
     * amount : {"payer_total":100,"total":100,"currency":"CNY","payer_currency":"CNY"}
     * promotion_detail : [{"amount":100,"wechatpay_contribute":0,"coupon_id":"109519","scope":"GLOBAL","merchant_contribute":0,"name":"单品惠-6","other_contribute":0,"currency":"CNY","stock_id":"931386","goods_detail":[{"goods_remark":"商品备注信息","quantity":1,"discount_amount":1,"goods_id":"M1006","unit_price":100},{"goods_remark":"商品备注信息","quantity":1,"discount_amount":1,"goods_id":"M1006","unit_price":100}]},{"amount":100,"wechatpay_contribute":0,"coupon_id":"109519","scope":"GLOBAL","merchant_contribute":0,"name":"单品惠-6","other_contribute":0,"currency":"CNY","stock_id":"931386","goods_detail":[{"goods_remark":"商品备注信息","quantity":1,"discount_amount":1,"goods_id":"M1006","unit_price":100},{"goods_remark":"商品备注信息","quantity":1,"discount_amount":1,"goods_id":"M1006","unit_price":100}]}]
     * payer : {"openid":"oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"}
     * scene_info : {"device_id":"013467007045764"}
     */

    @JsonProperty("sp_appid")
    private String spAppid;
    @JsonProperty("sp_mchid")
    private String spMchid;
    @JsonProperty("sub_appid")
    private String subAppid;
    @JsonProperty("sub_mchid")
    private String subMchid;
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    @JsonProperty("trade_state_desc")
    private String tradeStateDesc;
    @JsonProperty("trade_type")
    private String tradeType;
    @JsonProperty("attach")
    private String attach;
    @JsonProperty("transaction_id")
    private String transactionId;
    @JsonProperty("trade_state")
    private String tradeState;
    @JsonProperty("bank_type")
    private String bankType;
    @JsonProperty("success_time")
    private String successTime;
    @JsonProperty("amount")
    private Amount amount;
    @JsonProperty("payer")
    private Payer payer;
    @JsonProperty("scene_info")
    private SceneInfo sceneInfo;
    @JsonProperty("promotion_detail")
    private List<PromotionDetail> promotionDetail;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Amount implements Serializable {
        /**
         * payer_total : 100
         * total : 100
         * currency : CNY
         * payer_currency : CNY
         */

        @JsonProperty("payer_total")
        private int payerTotal;
        @JsonProperty("total")
        private int total;
        @JsonProperty("currency")
        private String currency;
        @JsonProperty("payer_currency")
        private String payerCurrency;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Payer implements Serializable {
        /**
         * openid : oUpF8uMuAJO_M2pxb1Q9zNjWeS6o
         */

        @JsonProperty("openid")
        private String openid;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class SceneInfo implements Serializable {
        /**
         * device_id : 013467007045764
         */

        @JsonProperty("device_id")
        private String deviceId;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class PromotionDetail implements Serializable {
        /**
         * amount : 100
         * wechatpay_contribute : 0
         * coupon_id : 109519
         * scope : GLOBAL
         * merchant_contribute : 0
         * name : 单品惠-6
         * other_contribute : 0
         * currency : CNY
         * stock_id : 931386
         * goods_detail : [{"goods_remark":"商品备注信息","quantity":1,"discount_amount":1,"goods_id":"M1006","unit_price":100},{"goods_remark":"商品备注信息","quantity":1,"discount_amount":1,"goods_id":"M1006","unit_price":100}]
         */

        @JsonProperty("amount")
        private int amount;
        @JsonProperty("wechatpay_contribute")
        private int wechatpayContribute;
        @JsonProperty("coupon_id")
        private String couponId;
        @JsonProperty("scope")
        private String scope;
        @JsonProperty("merchant_contribute")
        private int merchantContribute;
        @JsonProperty("name")
        private String name;
        @JsonProperty("other_contribute")
        private int otherContribute;
        @JsonProperty("currency")
        private String currency;
        @JsonProperty("stock_id")
        private String stockId;
        @JsonProperty("goods_detail")
        private List<GoodsDetail> goodsDetail;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class GoodsDetail implements Serializable {
            /**
             * goods_remark : 商品备注信息
             * quantity : 1
             * discount_amount : 1
             * goods_id : M1006
             * unit_price : 100
             */

            @JsonProperty("goods_remark")
            private String goodsRemark;
            @JsonProperty("quantity")
            private int quantity;
            @JsonProperty("discount_amount")
            private int discountAmount;
            @JsonProperty("goods_id")
            private String goodsId;
            @JsonProperty("unit_price")
            private int unitPrice;
        }
    }
}
