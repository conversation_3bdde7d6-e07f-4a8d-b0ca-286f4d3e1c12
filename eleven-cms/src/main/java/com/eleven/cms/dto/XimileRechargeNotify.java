package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 西米乐权益充值通知
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 17:30
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XimileRechargeNotify implements Serializable {
    public static final String RESP_CODE_OK = "200";
    /**
     * accountId : ***********
     * createTime : 2024-06-19 16:36:50
     * orderId : *************
     * payAmount : 0.0000
     * retCode : 100
     * retMsg : 充值失败
     * sign : f1a2210a07fae1d48690b8549289f4d9
     * sysOrderId : 20240619163649932183615
     */

    @JsonProperty("accountId")
    private String accountId;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("payAmount")
    private String payAmount;
    @JsonProperty("retCode")
    private String retCode;
    @JsonProperty("retMsg")
    private String retMsg;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("sysOrderId")
    private String sysOrderId;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getRetCode());
    }
}
