package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 破解计费返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KugouQueryDataResult {

    /*直充订单号*/
    @JsonProperty("ordernumber")
    private String orderNumber;
    /*直充类型（豪华vip或音乐包）*/
    @JsonProperty("product_type")
    private String productType;
    /*充值天数*/
    @JsonProperty("days")
    private String days;
    /*订单更新日期*/
    @JsonProperty("update_time")
    private String updateTime;
    /*订单状态，0未到账，1到账，-1失败*/
    @JsonProperty("status")
    private String status;
}
