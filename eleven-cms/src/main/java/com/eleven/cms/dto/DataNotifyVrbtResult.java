package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DataNotifyVrbtResult implements Serializable {

    /**
     * orderId : 62500046123
     * departmentId : 3099954434299
     * departmentName : 测试一下吧18482155682
     * billNum : 13438828200
     * createTime : 2023-04-19 16:04:09
     * provinceId : 280
     * provinceName : 四川
     * locationId : 2800
     * locationName : 成都
     * userStatus : 13
     */

    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("departmentId")
    private String departmentId;
    @JsonProperty("departmentName")
    private String departmentName;
    @JsonProperty("billNum")
    private String billNum;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("provinceId")
    private String provinceId;
    @JsonProperty("provinceName")
    private String provinceName;
    @JsonProperty("locationId")
    private String locationId;
    @JsonProperty("locationName")
    private String locationName;
    @JsonProperty("userStatus")
    private String userStatus;
}
