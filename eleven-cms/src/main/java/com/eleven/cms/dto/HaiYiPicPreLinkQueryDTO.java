package com.eleven.cms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @datetime 2024/10/8 15:52
 */
@Data
public class HaiYiPicPreLinkQueryDTO {

    @NotBlank(message = "文件类型不能为空！")
    @ApiModelProperty("文件类型，限制为：image/png  image/jpeg  image/jpg")
    private String contentType;

    @NotBlank(message = "文件名不能为空！")
    @ApiModelProperty("文件名")
    private String fileName;

    @NotNull(message = "文件大小不能为空！")
    @ApiModelProperty("文件大小，单位字节")
    private Long fileSize;
}
