package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/26 15:51
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DouYinToken implements Serializable {
    public static final Integer CODE_OK = 0;
    /**
     * data : {"access_token":"clt.d69dec57e3643dafe27879d4c397bb4bAoYUs9V1b7RcM71vrxE******","captcha":"","desc_url":"","description":"","error_code":0,"expires_in":7200,"log_id":"20230525192621789E357B281******"}
     * message : success
     */

    @JsonProperty("data")
    private Data data;
    @JsonProperty("message")
    private String message;
    /**
     * 判断是否成功
     */
    public boolean isOK() {
        return this.getData()!=null  && CODE_OK.equals(this.getData().getErrorCode()) ;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * access_token : clt.d69dec57e3643dafe27879d4c397bb4bAoYUs9V1b7RcM71vrxE******
         * captcha :
         * desc_url :
         * description :
         * error_code : 0
         * expires_in : 7200
         * log_id : 20230525192621789E357B281******
         */

        @JsonProperty("access_token")
        private String accessToken;
        @JsonProperty("captcha")
        private String captcha;
        @JsonProperty("desc_url")
        private String descUrl;
        @JsonProperty("description")
        private String description;
        @JsonProperty("error_code")
        private Integer errorCode;
        @JsonProperty("expires_in")
        private Integer expiresIn;
        @JsonProperty("log_id")
        private String logId;



    }
}
