package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 破解计费返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KugouOrderDataResult {

    /*直充的酷狗id加密后的值*/
    @JsonProperty("dr_openid")
    private String drOpenid;
    /*酷狗直充订单号*/
    @JsonProperty("kg_ordernumber")
    private String kgOrderNumber;
    /*订单号*/
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    /*酷狗账号昵称*/
    @JsonProperty("nick")
    private String nick;
    /*酷狗账号头像*/
    @JsonProperty("icon")
    private String icon;
}
