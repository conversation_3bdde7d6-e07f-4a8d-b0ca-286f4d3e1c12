package com.eleven.cms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/15 10:10
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianlianNotifyLog implements Serializable {
    public static final Integer  CODE_OK =200;
    /**
     * code : 200
     * data : {"encryptedData":"1Xlwat3T6pu9nJtTR19Kk2zsWmgPutBAS9nX+YmzGaVZ7klWUUTLiln2ReRSJVWemQIDPz1zo68siB0+g7lJbJ+/P/HZeP2qk108i1vLOlOubHl7B4IQBfULcb2yXRsZjnJVmP4JxTEBb4VNw8pBmbzVEPYFe9MRlbPItlwHyJQ2zce0JcMnqbXxK/yc9ARfajwmtWRCjI6N73hLrjf2jYy5/mvixiZOBiFNZbLD2ZO8d+l7lNgZH9cWTEqry0lyD91pPQzhfPICG1IAt1Oc182NjNVLt4QQ83hxJZOFyhT0OXeZMMh/ePe588g4Y81XapXVbrf4FYYtFrWofcgfp2qkp1tmJTBZ2H1b2/8w9M/9+SupVUnkOXLoqJA3K8OE3IimQuH0VIcWoROs6iYRyeJg7YClxjLAEpXy/czopsosrhUWls25o+AiCpEM7dsovO6iq4U3jFQ9WnWNOyAts9GR9ROOK4B/7ggF98/wQ9o1/cyGGNYFPrQDcflIeyiSUlEG9KASr0Ib927uJiyMIgkHIXqjgJB1mT+h5CDFUatl1J6tvdke5KhVS9f/2hBcpmWZE6iOTAhCGGxsL/iHIGO1PMXpXNpbi7dgG1tL1oHaII3Rp0M0bGE4XO8cNTT2Ps/DDsKOzsjtH2GSGLfU+0XBfbSkyKHcY/ohu0fFeVERP2cdf/lyrKwCJd31QzAyzmMn/e6pphRdUGG948nJ6d9ThYUewni/F2z5TTD9xK9Eq07CvS/9yH3ztwN4wPl9yUi4dFq5YW6Esmc1uiQO3A6Q5XoBiVMWhY7hAJnvZ6m16NX0y1D81EyCCAg42KSGtxThmxbqDMZd7GywBB3IQFJtVqfdxRFFRtHKDRgnc3vaJ9xFxdDZK20n6lR0CXwTNz/ZVsZbOwBCaMMJJvQY6j+a+OxjH5xDcO+uehK73B5FO0fz3K6o9OVK/7o35zuHZDT+F132fWsNddeuQ17DB/CZnYV+wmDcTvsOudUJFYN3NKuoSwMmaZlhYT6Ruew8vk4uKgJ4WLPg+4WMX4RyGtArqVAupj3rGYVOTFbUk7f8WoZP9uyRHf9omCcCmV1ppYl7vxJoPjLGYxmkZZKO+kAk3z3PLPKiVxTWtaIcuyF2dtpU9ki944+I6WrgGbkUzsvT+zL+MlWR88CxRXJfa9ht15Qs9DPLzuDwvsC9xVqPQwUqFtpEgY8h+ZyJuLpEYx72x7xTb/fTqogjdq56StyIpkLh9FSHFqETrOomEckjJlFWDiAVu7xuO0jcPK6Gd5t7pljk+WhlBXaCb0bz9aLQCNZIu5P+4Rs0cpd0Qk0N15hUbinKzjY9oKFagHDX","sign":"9815810a766bfbe1df7f03a89c105e7d","channelId":4,"timestamp":1671675223660}
     * success : true
     * message : 请求响应成功!
     */

    @JsonProperty("code")
    private int code;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("success")
    private boolean success;
    @JsonProperty("message")
    private String message;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode()) && success==true && this.getData()!=null;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * encryptedData : 1Xlwat3T6pu9nJtTR19Kk2zsWmgPutBAS9nX+YmzGaVZ7klWUUTLiln2ReRSJVWemQIDPz1zo68siB0+g7lJbJ+/P/HZeP2qk108i1vLOlOubHl7B4IQBfULcb2yXRsZjnJVmP4JxTEBb4VNw8pBmbzVEPYFe9MRlbPItlwHyJQ2zce0JcMnqbXxK/yc9ARfajwmtWRCjI6N73hLrjf2jYy5/mvixiZOBiFNZbLD2ZO8d+l7lNgZH9cWTEqry0lyD91pPQzhfPICG1IAt1Oc182NjNVLt4QQ83hxJZOFyhT0OXeZMMh/ePe588g4Y81XapXVbrf4FYYtFrWofcgfp2qkp1tmJTBZ2H1b2/8w9M/9+SupVUnkOXLoqJA3K8OE3IimQuH0VIcWoROs6iYRyeJg7YClxjLAEpXy/czopsosrhUWls25o+AiCpEM7dsovO6iq4U3jFQ9WnWNOyAts9GR9ROOK4B/7ggF98/wQ9o1/cyGGNYFPrQDcflIeyiSUlEG9KASr0Ib927uJiyMIgkHIXqjgJB1mT+h5CDFUatl1J6tvdke5KhVS9f/2hBcpmWZE6iOTAhCGGxsL/iHIGO1PMXpXNpbi7dgG1tL1oHaII3Rp0M0bGE4XO8cNTT2Ps/DDsKOzsjtH2GSGLfU+0XBfbSkyKHcY/ohu0fFeVERP2cdf/lyrKwCJd31QzAyzmMn/e6pphRdUGG948nJ6d9ThYUewni/F2z5TTD9xK9Eq07CvS/9yH3ztwN4wPl9yUi4dFq5YW6Esmc1uiQO3A6Q5XoBiVMWhY7hAJnvZ6m16NX0y1D81EyCCAg42KSGtxThmxbqDMZd7GywBB3IQFJtVqfdxRFFRtHKDRgnc3vaJ9xFxdDZK20n6lR0CXwTNz/ZVsZbOwBCaMMJJvQY6j+a+OxjH5xDcO+uehK73B5FO0fz3K6o9OVK/7o35zuHZDT+F132fWsNddeuQ17DB/CZnYV+wmDcTvsOudUJFYN3NKuoSwMmaZlhYT6Ruew8vk4uKgJ4WLPg+4WMX4RyGtArqVAupj3rGYVOTFbUk7f8WoZP9uyRHf9omCcCmV1ppYl7vxJoPjLGYxmkZZKO+kAk3z3PLPKiVxTWtaIcuyF2dtpU9ki944+I6WrgGbkUzsvT+zL+MlWR88CxRXJfa9ht15Qs9DPLzuDwvsC9xVqPQwUqFtpEgY8h+ZyJuLpEYx72x7xTb/fTqogjdq56StyIpkLh9FSHFqETrOomEckjJlFWDiAVu7xuO0jcPK6Gd5t7pljk+WhlBXaCb0bz9aLQCNZIu5P+4Rs0cpd0Qk0N15hUbinKzjY9oKFagHDX
         * sign : 9815810a766bfbe1df7f03a89c105e7d
         * channelId : 4
         * timestamp : 1671675223660
         */

        @JsonProperty("encryptedData")
        private String encryptedData;
        @JsonProperty("sign")
        private String sign;
        @JsonProperty("channelId")
        private int channelId;
        @JsonProperty("timestamp")
        private long timestamp;
    }
}
