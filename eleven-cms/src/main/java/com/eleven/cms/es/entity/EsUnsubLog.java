package com.eleven.cms.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * 退订日志记录文档
 * @author: cai lei
 * @create: 2021-07-27 15:36
 */
@Data
@Document(indexName = "unsub_log", type = "_doc", createIndex = false, shards = 1, replicas = 0)
public class EsUnsubLog {

    @Id
    private String id;

    /**
     * 手机号
     */
    @Field(type = FieldType.Keyword)
    private String mobile;

    /**
     * 退订业务
     */
    @Field(type = FieldType.Keyword)
    private String bizType;

    /**
     * 渠道号
     */
    @Field(type = FieldType.Keyword)
    private String channel;


    /**
     * 退订结果
     */
    @Field(type = FieldType.Keyword)
    private String response;

    /**
     * 备注
     */
    @Field(type = FieldType.Keyword)
    private String remake;


    /**
     * 退订类型
     */
    @Field(type = FieldType.Keyword)
    private String action;

    /**
     * 保留字段1
     */
    @Field(type = FieldType.Keyword)
    private String extend1;

    /**
     * 保
     * 留字段2
     */
    @Field(type = FieldType.Keyword)
    private String extend2;

    /**
     * 保留字段3
     */
    @Field(type = FieldType.Keyword)
    private String extend3;

    /**
     * 创建日期
     */
    @Field(type = FieldType.Long)
    private Date createTime;

}
