package com.eleven.cms.es.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;


/**
 * 渠道订购 Entity
 *
 * <AUTHOR>
 * @date 2020-07-16 10:37:54
 */
@Document(indexName = "order_fail_log", type = "_doc", createIndex = false, shards = 1, replicas = 0)
@Data
public class EsOrderFailLog {
    /**
     * 主键
     */
    @Id
    private String id;
    /**
     * 渠道号
     */
    @Field(type = FieldType.Keyword)
    private String channel;
    /**
     * 子渠道号
     */
    @Field(type = FieldType.Keyword)
    private String subChannel;
    /**
     * 手机号
     */
    @Field(type = FieldType.Keyword)
    private String mobile;
    /**
     * 运营商
     */
    @Field(type = FieldType.Keyword)
    private String isp;
    /**
     * 省份
     */
    @Field(type = FieldType.Keyword)
    private String province;
    /**
     * 城市
     */
    @Field(type = FieldType.Keyword)
    private String city;
    /**
     * 订购结果
     */
    @Field(type = FieldType.Keyword)
    private String result;
    /**
     * 从哪个页面发起的订阅
     */
    @Field(type = FieldType.Keyword)
    private String referer;
    /**
     * 格式化的创建时间
     */
    @Field(type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
}
