package com.eleven.cms.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * @author: cai lei
 * @create: 2021-07-20 14:12
 */
@Document(indexName = "operation_sms",type = "_doc",createIndex = false, shards = 1, replicas = 0)
@Data
public class EsOperationSms {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 渠道号
     */
    @Field(type = FieldType.Keyword)
    private String channel;

    /**
     * 子渠道号
     */
    @Field(type = FieldType.Keyword)
    private String subChannel;

    /**
     * 手机号
     */
    @Field(type = FieldType.Keyword)
    private String mobile;


    /**
     * 运营商 1移动用户,3联通用户,4电信用户
     */
    @Field(type = FieldType.Keyword)
    private String isp;



    /**
     * 浏览器ua
     */
    @Field(type = FieldType.Keyword)
    private String userAgent;

    /**
     * ip
     */
    @Field(type = FieldType.Keyword)
    private String ip;


    /**
     * 验证码
     */
    @Field(type = FieldType.Keyword)
    private String code;


    /**
     * 操作时间
     */
    @Field(type = FieldType.Long)
    private Date operationTime;

    /**
     * 操作类型 1下发短信 2提交短信
     */
    @Field(type = FieldType.Keyword)
    private String operationType;




}
