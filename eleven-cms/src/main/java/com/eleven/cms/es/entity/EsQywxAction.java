package com.eleven.cms.es.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * 企业微信 Entity
 *
 * <AUTHOR>
 * @date 2020-07-16 10:37:54
 */
@Document(indexName = "qywx_action", createIndex = false, shards = 1, replicas = 0)
@Data
public class EsQywxAction {

    /**
     * 主键
     */
    @Id
    private String id;

    @Field(type = FieldType.Keyword)
    private String userId;

    @Field(type = FieldType.Keyword)
    private String action;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private Date createTime;

    /**
     * 格式化的创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime fmtCreateTime;


}
