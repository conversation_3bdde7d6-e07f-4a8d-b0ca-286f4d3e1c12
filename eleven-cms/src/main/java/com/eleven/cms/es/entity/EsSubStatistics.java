package com.eleven.cms.es.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @Description: 渠道订阅数据统计
 * @Author: jeecg-boot
 * @Date:   2020-09-23
 * @Version: V1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = "sub_statistics", type = "_doc", createIndex = false, shards = 1, replicas = 0)
public class EsSubStatistics {

    /**
     * 主键
     */
    @Id
    private String id;
    /**渠道号*/
    @Excel(name = "渠道号", width = 20)
    @ApiModelProperty(value = "渠道号")
    @Field(type = FieldType.Keyword)
    private String channel;
    /**业务类型,vrbt:视频彩铃,rt:振铃*/
    @Excel(name = "业务类型", width = 20)
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "业务类型")
    private String bizType;
    /**子渠道号*/
    @Excel(name = "子渠道号", width = 15)
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;
    /**省份*/
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    @Field(type = FieldType.Keyword)
    private String province;
    /**校验包月状态(延迟60分钟)*/
    @Excel(name = "总量", width = 15)
    @ApiModelProperty(value = "总量")
    @Field(type = FieldType.Long)
    private Long total;
    /**校验包月状态(延迟60分钟)*/
    @Excel(name = "存量", width = 15)
    @ApiModelProperty(value = "存量")
    @Field(type = FieldType.Long)
    private Long succ;
    /**订购月份,格式如:2024-01*/
    @Excel(name = "订购月份", width = 20)
    @ApiModelProperty(value = "订购月份")
    @Field(type = FieldType.Keyword)
    private String subMonth;
    /**业统计月份,格式如:2024-01*/
    @Excel(name = "统计月份", width = 20)
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "统计月份")
    private String statsMonth;

}
