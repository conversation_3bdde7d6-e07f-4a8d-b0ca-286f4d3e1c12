package com.eleven.cms.es.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;

/**
 * 尾量
 *
 * <AUTHOR>
 * @datetime 2024/12/13 10:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = "#{@indexNameResolver.resolveIndexName('insert_code')}")
public class EsInsertCode {

    @Id
    @ApiModelProperty(value = "id")
    private String id;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "用户手机号")
    private String mobile;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "前端生成的用户唯一标识")
    private String userId;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "0:通用 1:自定义")
    private String type;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "0:H5 1:小程序 2:APP")
    private String originType;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "具体的来源名称")
    private String originName;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "渠道号")
    private String channel;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "页面归属")
    private String owner;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "页面完整链接")
    private String source;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "是否自动获取验证码 0:否 1:是")
    private String autoGetSms;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "成功弹窗类型是否启用 0:否 1:是")
    private String okPop;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "成功时的弹窗类型")
    private String okPopType;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "失败弹窗类型是否启用 0:否 1:是")
    private String failPop;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "失败时的弹窗类型")
    private String failPopType;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "开通结果")
    private String subResult;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "事件码")
    private String eventCode;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "事件子码")
    private String eventChildCode;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "用户省份编码")
    private String provinceCode;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "用户所在省份")
    private String provinceName;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "用户市区编码")
    private String cityCode;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "用户所在市区")
    private String cityName;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "投放广告平台")
    private String plat;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "制作页面的系统名称")
    private String sysName;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "回退跳转类型")
    private String backType;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "兜底")
    private String endType;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "跳转的尾量类型名称")
    private String tailName;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "跳转的尾量类型编码 0:自定义弹窗 1:流量果-鸿盛 2:尾量（兜底） 4:发财树 5:I流量 8:流量果-鲲鹏(向-董) 99:未配置")
    private String tailCode;

    @ApiModelProperty(value = "触发条件 wl:代表尾量 click:点击 pv:页面访问PV")
    @Field(type = FieldType.Keyword)
    private String trigger;

    @ApiModelProperty(value = "触发行为 0:尾量跳转失败 1:尾量跳转成功")
    @Field(type = FieldType.Keyword)
    private String triggerAction;

    @ApiModelProperty(value = "链接参数")
    @Field(type = FieldType.Keyword)
    private String linkParam;

    @ApiModelProperty(value = "上级-来源页")
    @Field(type = FieldType.Keyword)
    private String prevPage;

    @ApiModelProperty(value = "下级-转向页")
    @Field(type = FieldType.Keyword)
    private String nextPage;

    @ApiModelProperty(value = "订阅状态 0:未开通 1:开通")
    @Field(type = FieldType.Keyword)
    private String subStatus;

    @ApiModelProperty(value = "登录状态 0:未登录 1:已登录")
    @Field(type = FieldType.Keyword)
    private String isLogin;

    @ApiModelProperty(value = "IP")
    @Field(type = FieldType.Keyword)
    private String ip;

    @ApiModelProperty(value = "运营商")
    @Field(type = FieldType.Keyword)
    private String isp;

    @ApiModelProperty(value = "业务办理状态 0:未获取到页面配置 1:获取验证码失败 2:提交验证码失败 3:提交验证码成功 4:点击页面返回（取消提交）")
    @Field(type = FieldType.Keyword)
    private String bizHandleStatus;

    @ApiModelProperty(value = "app版本")
    @Field(type = FieldType.Keyword)
    private String appVersion;

    @ApiModelProperty(value = "用户设备类型")
    @Field(type = FieldType.Keyword)
    private String userDeviceType;

    @ApiModelProperty(value = "创建时间")
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建时间时间戳")
    @Field(type = FieldType.Long)
    private Long createTimestamp;
}