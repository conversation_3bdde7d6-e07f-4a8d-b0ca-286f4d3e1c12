package com.eleven.cms.es.service;


import com.eleven.cms.entity.Qywx;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-07-15 11:57
 */
public interface IEsDataService {

    void saveOrUpdateSubscribeAsync(Subscribe subscribe);

    void saveOrUpdateSubscribe(Subscribe subscribe);

    void saveEsSendSms(Subscribe subscribe);

    void saveEsSubmitSms(Subscribe subscribe);

    void syncEsData();

    void batchSaveOrUpdateSubscribe(List<Subscribe> list);

    List<Map<String, Object>> queryAllBizByMobile(String mobile);

    List<EsSubscribe> queryRecentAllBizByMobile(String mobile, long recentDays);

    void saveUnsubLogAsync(EsUnsubLog esUnsubLog);

    Integer getFeedbackStatusById(String id);

    void updateSubscribeStatusResultById(String subscriberId, Integer status, String result);

    void updateSubscribeVerfyStatusById(String subscriberId, Integer verfyStatus);

    void removeSubscribeById(String id);

    void transTestEsSub(Subscribe subscribe);

    void transTestEsOrder(Subscribe subscribe);

    void modifyAndfindTestEsOrder(String id);

    EsSubscribe searchById(String id);

    List<EsSubscribe> findEsSubscribeByMobileAndChannels(String mobile, List<String> channelList);

    List<EsSubscribe> findTodayEsSubscribeByMobileAndChannels(String mobile, List<String> channelList);

    EsSubscribe findRecentEsSubscribeByMobileAndChannel(String mobile, String channel);

    void saveUnsubLog(EsSubscribe esSubscribe, String respMsg, String action);

    List<EsSubStatistics> subscribeStatisticsLast6MonthByChannel(LocalDate startDay, LocalDate endDay, String channelCode);

    List<EsSubStatistics> subscribeStatisticsLast6MonthAllChannel(LocalDate startDay, LocalDate endDay);

    List<EsSubscribe> findTodayEsSubscribeByOrderIdAndChannels(String orderId, List<String> channelList);

    Map<Long, Long> subscribeStatisticsQyclVerifyStatusToday(String channelCode);

    Map<String, Boolean> subscribeMonitoAllChannel(long recentHours, int topN);

    List<EsSubscribe> findLast3MonthEsSubscribeByMobile(String mobile, String channelCode);

    void saveEsBusinessLog(EsBusinessLog esBusinessLog);

    void updateSubscribeVerfyStatusDailyById(String subscriberId, Integer verifyStatusDaily);

    void updateSubVerfyStatusById(String subscriberId, Integer verfyStatus);

    EsSubscribe findEsSubscribeByMobileAndChannel(String mobile, String channel);


    EsSubscribe findSameMonthEsSubscribeByMobileAndChannel(String mobile,String channel);

    void saveEsOrderFailLog(EsOrderFailLog esOrderFailLog);
    void saveEsQywx(EsQywx esQywx);

    void saveEsQywxAction(EsQywxAction esQywxAction);

    void updatesOrderFailLogIndex(String index, int size) ;

    EsSubscribe findEsSubscribeByMobileAndBizType(String mobile, String bizType);

    void saveOrUpdateQywxAsync(Qywx qywx);

    void updateQywxByAssignFieldById(String id, String field, String value);

    void updateSubscribeEs(Subscribe subscribe);
}
