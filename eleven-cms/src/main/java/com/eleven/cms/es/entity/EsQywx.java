package com.eleven.cms.es.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 渠道订购 Entity
 *
 * <AUTHOR>
 * @date 2020-07-16 10:37:54
 */
@Document(indexName = "qywx", createIndex = false, shards = 1, replicas = 0)
@Data
public class EsQywx {

    /**
     * 主键
     */
    @Id
    private String id;

    @Field(type = FieldType.Keyword)
    private String userId;

    @Field(type = FieldType.Keyword)
    private String subChannel;

    @Field(type = FieldType.Integer)
    private Integer reportStatus;

    /**
     * 渠道号
     */
    @Field(type = FieldType.Keyword)
    private String customerChannel;

    /**
     * 落地页
     */
    @Field(type = FieldType.Keyword)
    private String pageUrl;

    /**
     * 获客链接id
     */
    @Field(type = FieldType.Keyword)
    private String hkLinkId;

    /**
     * 状态
     */
    @Field(type = FieldType.Integer)
    private Integer status;

    /**
     * 添加好友时间
     */
    @Field(type = FieldType.Long)
    private Date customerAddTime;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private Date createTime;

    /**
     * 格式化的创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fmtCreateTime;




}
