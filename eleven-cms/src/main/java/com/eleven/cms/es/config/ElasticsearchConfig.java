package com.eleven.cms.es.config;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.RestClients;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * @author: cai lei
 * @create: 2021-07-08 10:45
 */
@Configuration
public class ElasticsearchConfig {

    @Value("${spring.elasticsearch.rest.uris}")
    private String[] esUris;
    @Value("${spring.elasticsearch.rest.username}")
    private String username;
    @Value("${spring.elasticsearch.rest.password}")
    private String password;
    @Value("${spring.elasticsearch.rest.connection-timeout}")
    private Long connectionTimeout;
    @Value("${spring.elasticsearch.rest.read-timeout}")
    private Long readTimeout;
    @Autowired
    private Environment environment;

    //@Bean
    //public RestHighLevelClient client() {
    //    String userName = environment.getProperty("spring.elasticsearch.rest.username");
    //    String password = environment.getProperty("spring.elasticsearch.rest.password");
    //    HttpHost[] httpHosts = new HttpHost[esUris.length];
    //    //将地址转换为http主机数组，未配置端口则采用默认9200端口，配置了端口则用配置的端口
    //    for (int i = 0; i < httpHosts.length; i++) {
    //        if (!StringUtils.isEmpty(esUris[i])) {
    //            if (esUris[i].contains(":")) {
    //                String[] uris = esUris[i].split(":");
    //                httpHosts[i] = new HttpHost(uris[0], Integer.parseInt(uris[1]), "http");
    //            } else {
    //                httpHosts[i] = new HttpHost(esUris[i], 9200, "http");
    //            }
    //        }
    //    }
    //    //判断，如果未配置用户名，则进行无用户名密码连接，配置了用户名，则进行用户名密码连接
    //    if (StringUtils.isEmpty(userName)) {
    //        RestHighLevelClient client = new RestHighLevelClient(RestClient.builder(httpHosts));
    //        return client;
    //    } else {
    //
    //        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
    //        credentialsProvider.setCredentials(AuthScope.ANY,
    //                //es账号密码
    //                new UsernamePasswordCredentials(userName, password));
    //        RestHighLevelClient client = new RestHighLevelClient(
    //                RestClient.builder(httpHosts)
    //                        .setHttpClientConfigCallback((httpClientBuilder) -> {
    //                            //                                httpClientBuilder.setMaxConnTotal(maxConnection);
    //                            httpClientBuilder.disableAuthCaching();
    //                            httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
    //                            httpClientBuilder.setConnectionReuseStrategy((response, context) -> true); // keepAlive use true
    //                            httpClientBuilder.setKeepAliveStrategy((response, context) -> TimeUnit.MINUTES.toMillis(3)); // keepAlive timeout sec
    //                            return httpClientBuilder;
    //                        })
    //                        .setRequestConfigCallback(builder -> {
    //                            builder.setConnectTimeout(60000);
    //                            builder.setSocketTimeout(60000);
    //                            builder.setConnectionRequestTimeout(60000);
    //                            return builder;
    //                        })
    //        );
    //        return client;
    //    }
    //}

    @Bean(destroyMethod = "close")
    public RestHighLevelClient client() {

        //HttpHeaders httpHeaders = new HttpHeaders().;
        //httpHeaders.add("some-header", "on every request");

        ClientConfiguration clientConfiguration = ClientConfiguration.builder()
                .connectedTo(esUris)               // 使用构建器提供集群地址、设置默认 HttpHeaders 或启用 SSL。
                //.usingSsl()                                                    // 可选择启用 SSL
                //.withProxy("localhost:8888")                                   // （可选）设置代理
                //.withPathPrefix("ela")                                         // 可选地设置路径前缀，主要用于在某些反向代理后面的不同集群时
                .withConnectTimeout(Duration.ofMillis(connectionTimeout))                     // 设置连接超时，默认是10秒
                .withSocketTimeout(Duration.ofMillis(readTimeout))                      // 设置socket超时，默认是5秒
                //.withDefaultHeaders(defaultHeaders)                            // 可选地设置header
                .withBasicAuth(username, password)                             // 添加基本​​身份验证
                //.withHeaders(() -> {                                           // 可以指定一个 Supplier<Header> 函数，每次在请求发送到 Elasticsearch 之前都会调用该函数 - 例如，当前时间被写入header中
                //    HttpHeaders headers = new HttpHeaders();
                //    headers.add("currentTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                //    return headers;
                //})
                //.withWebClientConfigurer(                                        // 用于响应式设置配置 WebClient 的功能
                //        ReactiveRestClients.WebClientConfigurationCallback.from(webClient -> {
                //            // ...
                //            return webClient;
                //        }))
                //.withClientConfigurer(                                       // 对于非反应式设置，配置 REST 客户端的功能
                //        RestClients.RestClientConfigurationCallback.from(clientBuilder -> {
                //            // ...
                //            return clientBuilder;
                //        }))
                //. // ... other options
                .build();

        return RestClients.create(clientConfiguration).rest();
    }
}
