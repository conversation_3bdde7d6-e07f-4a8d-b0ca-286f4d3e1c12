package com.eleven.cms.es.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 渠道订购 Entity
 *
 * <AUTHOR>
 * @date 2020-07-16 10:37:54
 */
@Document(indexName = "subscribe", type = "_doc", createIndex = false, shards = 1, replicas = 0)
@Data
public class EsSubscribe {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 手机号
     */
    @Field(type = FieldType.Keyword)
    private String mobile;

    /**
     * 业务类型
     */
    @Field(type = FieldType.Keyword)
    private String bizType;

    /**
     * 渠道号
     */
    @Field(type = FieldType.Keyword)
    private String channel;

    /**
     * 子渠道号
     */
    @Field(type = FieldType.Keyword)
    private String subChannel;

    /**
     * 咪咕serviceId
     */
    @Field(type = FieldType.Keyword)
    private String serviceId;

    /**
     * 状态(-1=初始,0=失败,1=成功)
     */
    @Field(type = FieldType.Integer)
    private Integer status;

    /**
     * 订购结果
     */
    @Field(type = FieldType.Keyword)
    private String result;

    /**
     * 推送至即时开通服务器返回编码
     */
    @Field(type = FieldType.Integer)
    private Integer pushRespCode;

    /**
     * 推送至即时开通服务器返回消息
     */
    @Field(type = FieldType.Keyword)
    private String pushRespMessage;

    /**
     * 信息流彩图上报状态:-1未上报,0上报失败,1上报成功
     */
    @Field(type = FieldType.Integer)
    private Integer feedbackStatusCaitu;

    /**
     * 价格:单位分
     */
    @Field(type = FieldType.Integer)
    private Integer price;

    /**
     * 省份
     */
    @Field(type = FieldType.Keyword)
    private String province;

    /**
     * 省份编码
     */
    @Field(type = FieldType.Keyword)
    private String provinceCode;

    /**
     * 城市
     */
    @Field(type = FieldType.Keyword)
    private String city;

    /**
     * 城市编码
     */
    @Field(type = FieldType.Keyword)
    private String cityCode;

    /**
     * 浏览器ua
     */
    @Field(type = FieldType.Keyword)
    private String userAgent;

    /**
     * 从哪个页面发起的订阅
     */
    @Field(type = FieldType.Keyword)
    private String referer;

    /**
     * 设备信息
     */
    @Field(type = FieldType.Keyword)
    private String deviceInfo;

    /**
     * ip
     */
    @Field(type = FieldType.Keyword)
    private String ip;

    /**
     * 来源
     */
    @Field(type = FieldType.Keyword)
    private String source;

    /**
     * 备注
     */
    @Field(type = FieldType.Keyword)
    private String remark;

    /**
     * 业务发生时间
     */
    //@Field(type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @Field(type = FieldType.Long)
    private Date bizTime;

    /**
     * 业务开通时间
     */
    //@Field(type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @Field(type = FieldType.Long)
    private Date openTime;

    /**
     * 创建时间
     */
    //@Field(type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @Field(type = FieldType.Long)
    private Date createTime;

    /**
     * 格式化的创建时间
     */
    @Field(type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date fmtCreateTime;

    /**
     * 广告平台名称
     */
    @Field(type = FieldType.Keyword)
    private String adPlatform;

    /**
     * 优化师
     */
    @Field(type = FieldType.Keyword)
    private String optimizer;

    /**
     * 修改时间
     */
    //@Field(type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @Field(type = FieldType.Long)
    private Date modifyTime;
    
//    @TableField(exist = false)
//    private String createTimeFrom;
//    @TableField(exist = false)
//    private String createTimeTo;
//
//    @TableField(exist = false)
//    private String smsCode;
//    @TableField
//    private String transactionId;

    /**
     * 校验包月状态(延迟90分钟) 90分钟退订校验(-1=未校验,0=未包月,1=已包月)
     */
    @Field(type = FieldType.Integer)
    private Integer verifyStatus;
    /**
     * 校验包月状态(延迟903天)* 3天退订校验(-1=未校验,0=未包月,1=已包月)
     */
    @Field(type = FieldType.Integer)
    private Integer verifyStatusDaily;

    /**
     * 运营商
     */
    @Field(type = FieldType.Keyword)
    private java.lang.String isp;
    /**版权id*/
    @Field(type = FieldType.Keyword)
    private java.lang.String copyrightId;
    /**内容id*/
    @Field(type = FieldType.Keyword)
    private java.lang.String contentId;
    /**电信铃音编码*/
    @Field(type = FieldType.Keyword)
    private java.lang.String dxToneCode;
    /**联通铃音id*/
    @Field(type = FieldType.Keyword)
    private java.lang.String ltRingId;
    /**运营商一键下单订单号*/
    @Field(type = FieldType.Keyword)
    private java.lang.String ispOrderNo;
    /**免费订铃声的结果*/
    @Field(type = FieldType.Keyword)
    private java.lang.String extra;

    //通过bean复制需要手动设置 使用feedbackStatusCaitu
    //private java.lang.Integer tuiaFeedbackStatus;


}
