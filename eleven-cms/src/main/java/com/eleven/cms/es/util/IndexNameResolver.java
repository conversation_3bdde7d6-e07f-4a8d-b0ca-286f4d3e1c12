package com.eleven.cms.es.util;

import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @datetime 2024/12/25 14:25
 */
// 索引名解析器
@Configuration
public class IndexNameResolver {

    public String resolveIndexName(String prefix) {
        // 动态生成索引名的逻辑，例如根据年月来生成
        String yearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        return prefix + "_" + yearMonth;
    }
}
