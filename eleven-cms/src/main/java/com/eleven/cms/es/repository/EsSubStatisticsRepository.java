package com.eleven.cms.es.repository;

import com.eleven.cms.es.entity.EsSubStatistics;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: cai lei
 * @create: 2021-07-15 11:16
 */
@Repository
public interface EsSubStatisticsRepository extends ElasticsearchRepository<EsSubStatistics, String> {
    List<EsSubStatistics> findByStatsMonthAndSubMonthAndChannel(String statsMonth, String subMonth, String channelCode);
    int countByStatsMonthAndChannel(String statsMonth, String channelCode);
}
