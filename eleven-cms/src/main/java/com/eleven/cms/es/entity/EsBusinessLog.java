package com.eleven.cms.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * @author: cai lei
 * @create: 2021-07-27 15:36
 */
@Data
@Document(indexName = "business_log", type = "_doc",createIndex = false, shards = 1, replicas = 0)
public class EsBusinessLog {

    @Id
    private String id;

    /**
     * 接口地址
     */
    @Field(type = FieldType.Keyword)
    private String url;

    /**
     * 手机号
     */
    @Field(type = FieldType.Keyword)
    private String mobile;

    /**
     * 指纹
     */
    @Field(type = FieldType.Keyword)
    private String finger;

    /**
     * 创建日期
     */
    @Field(type = FieldType.Long)
    private Date createTime;

    /**
     * 动作
     */
    @Field(type = FieldType.Keyword)
    private String action;

    /**
     * 请求
     */
    @Field(type = FieldType.Keyword)
    private String request;

    /**
     * 响应
     */
    @Field(type = FieldType.Keyword)
    private String response;

    /**
     * 内容
     */
    @Field(type = FieldType.Keyword)
    private String content;

    /**
     * 备注
     */
    @Field(type = FieldType.Keyword)
    private String remake;


    /**
     * 保留字段1
     */
    @Field(type = FieldType.Keyword)
    private String extend1;

    /**
     * 保
     * 留字段2
     */
    @Field(type = FieldType.Keyword)
    private String extend2;

    /**
     * 保留字段3
     */
    @Field(type = FieldType.Keyword)
    private String extend3;

}
