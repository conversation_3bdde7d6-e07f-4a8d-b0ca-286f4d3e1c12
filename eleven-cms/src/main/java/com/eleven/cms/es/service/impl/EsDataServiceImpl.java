package com.eleven.cms.es.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.eleven.cms.entity.Qywx;
import com.eleven.cms.entity.SubChannel;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.*;
import com.eleven.cms.es.repository.*;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.es.util.IdUtil;
import com.eleven.cms.mapper.SubscribeMapper;
import com.eleven.cms.remote.ComplaintService;
import com.eleven.cms.service.ISubChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.support.replication.ReplicationResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.TopHits;
import org.elasticsearch.search.collapse.CollapseBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SourceFilter;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_MONTH_VERIFY_INIT;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2021-07-15 11:58
 */
@Service
@Slf4j
public class EsDataServiceImpl implements IEsDataService {

    @Autowired
    EsSubscribeRepository esSubscribeRepository;

    @Autowired
    EsOperationSmsRepository esOperationSmsRepository;

    @Autowired
    EsUnsubLogRepository esUnsubLogRepository;

    @Autowired
    SubscribeMapper subscribeMapper;

    @Autowired
    ISubChannelService subChannelService;

    @Autowired
    RestHighLevelClient restHighLevelClient;

    @Autowired
    ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    ComplaintService complaintService;

    @Autowired
    EsBusinessLogRepository esBusinessLogRepository;
    @Autowired
    EsOrderFailLogRepository esOrderFailLogRepository;
    @Autowired
    EsQywxRepository esQywxRepository;
    @Autowired
    EsQywxActionRepository esQywxActionRepository;

    public static final ObjectMapper mapper = new ObjectMapper().registerModule(new JavaTimeModule())
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .enable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    public static final String INDEX_SUBSCRIBE = "subscribe";
    public static final String INDEX_SUBSCRIBE_WILDCARD = "subscribe*";
    public static final String INDEX_FAIL_WILDCARD = "order_fail_log*";
    public static final IndexCoordinates INDEX_COORDINATES = IndexCoordinates.of(INDEX_SUBSCRIBE_WILDCARD);
    public static final IndexCoordinates INDEX__FAIL = IndexCoordinates.of(INDEX_FAIL_WILDCARD);
    //ObjectMapper mapper =   new ObjectMapper().;



    @Override
    @Async
    public void saveOrUpdateSubscribeAsync(Subscribe subscribe) {
        saveOrUpdateSubscribe(subscribe);
    }

    @Override
    public void saveOrUpdateSubscribe(Subscribe subscribe) {
        if (subscribe.getId() != null) {
            subscribe = subscribeMapper.selectById(subscribe.getId());
            EsSubscribe esSubscribe = new EsSubscribe();
            BeanUtils.copyProperties(subscribe, esSubscribe);
//            esSubscribe.setProvinceCode(subscribe.getOwner());
            final SubChannel subChannel = subChannelService.findSubChannel(subscribe.getSubChannel());
            if(subChannel != null){
                esSubscribe.setAdPlatform(subChannel.getAdPlatform());
                esSubscribe.setOptimizer(subChannel.getOptimizer());
            }
            esSubscribe.setFmtCreateTime(subscribe.getCreateTime());
            esSubscribe.setUserAgent("");
            //esSubscribe.setSource("");
            Integer feedbackStatusCaitu = this.getFeedbackStatusById(String.valueOf(subscribe.getId()));
            if (feedbackStatusCaitu != null) {
                esSubscribe.setFeedbackStatusCaitu(feedbackStatusCaitu);
            }
            try {
                IndexRequest indexRequest = new IndexRequest(INDEX_SUBSCRIBE, "_doc", subscribe.getId());
                //String json = JSON.toJSONString(esSubscribe);
                String json = mapper.writeValueAsString(esSubscribe);
//                if ("SXYD_JBAICL".equals(subscribe.getChannel())) {
//                    log.info("saveOrUpdateSubscribe-SXYD_JBAICL, mobile：{}, json：{}", subscribe.getMobile(), json);
//                }
                indexRequest.source(json, XContentType.JSON);
                indexRequest.timeout("1s");
                IndexResponse indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
                ReplicationResponse.ShardInfo shardInfo = indexResponse.getShardInfo();
                if (shardInfo.getFailed() > 0) {
                    log.error("同步数据到es失败,失败原因:{}", shardInfo.getFailures()[0].reason());
                }
                //log.info("数据同步到es,id:{},更新结果:{}", subscribe.getId(), shardInfo.getSuccessful() == shardInfo.getTotal());
            } catch (Exception e) {
                log.error("业务订购写入es错误,错误文档:{},错误信息:{}", "subscribe", e.getMessage());
            }
        }
    }

    @Override
    public void batchSaveOrUpdateSubscribe(List<Subscribe> list) {
        BulkRequest bulkRequest = new BulkRequest();
        list.forEach(subscribe -> {
            EsSubscribe esSubscribe = new EsSubscribe();
            BeanUtils.copyProperties(subscribe, esSubscribe);
//            esSubscribe.setProvinceCode(subscribe.getOwner());
            final SubChannel subChannel = subChannelService.findSubChannel(subscribe.getSubChannel());
            if(subChannel != null){
                esSubscribe.setAdPlatform(subChannel.getAdPlatform());
                esSubscribe.setOptimizer(subChannel.getOptimizer());
            }
            esSubscribe.setFmtCreateTime(subscribe.getCreateTime());
            esSubscribe.setUserAgent("");
            //esSubscribe.setSource("");
            EsSubscribe oldEsSubscribe = esSubscribeRepository.findById(subscribe.getId()).get();
            esSubscribe.setFeedbackStatusCaitu(oldEsSubscribe.getFeedbackStatusCaitu());
//            esSubscribe.setFeedbackStatusCaitu(subscribe.getTuiaFeedbackStatus());
            IndexRequest indexRequest = new IndexRequest(INDEX_SUBSCRIBE, "_doc", subscribe.getId());
            //String json = JSON.toJSONString(esSubscribe);
            String json = null;
            try {
                json = mapper.writeValueAsString(esSubscribe);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            log.info("同步数据:{}", json);
            indexRequest.source(json, XContentType.JSON);
            indexRequest.timeout("1s");
            bulkRequest.add(indexRequest);
        });
        try {
            BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                for (BulkItemResponse item : bulkResponse.getItems()) {
                    if (item.isFailed()) {
                        log.error("es批量同步数据异常,id:{}原因:{}", item.getId(), item.getFailureMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("es批量同步数据异常", e);
        }
    }


    @Override
    @Async
    public void saveEsSendSms(Subscribe subscribe) {
        if (subscribe != null) {
            EsOperationSms esSendSms = new EsOperationSms();
            esSendSms.setId(IdUtil.generateId());
            esSendSms.setChannel(subscribe.getChannel());
            esSendSms.setSubChannel(subscribe.getSubChannel());
            esSendSms.setUserAgent(subscribe.getUserAgent());
            esSendSms.setIp(subscribe.getIp());
            esSendSms.setMobile(subscribe.getMobile());
            esSendSms.setOperationTime(new Date());
            esSendSms.setOperationType("1"); //发送短信
            esSendSms.setIsp("1"); //只支持移动
            //获取下发验证码
            String key = BizConstant.SMS_CODE_KEY_PREFIX + subscribe.getMobile();
            //            String smsCode = (String) redisUtil.get(key);
            //            esSendSms.setCode(smsCode);
            try {
                esOperationSmsRepository.save(esSendSms);
            } catch (Exception e) {
                log.error("发送验证码写入es错误,错误文档:{},错误信息:{}", "operation_sms", e.getMessage());
            }
        }

    }

    @Override
    public void saveEsSubmitSms(Subscribe subscribe) {
        if (subscribe != null) {
            EsOperationSms esSubmitSms = new EsOperationSms();
            esSubmitSms.setId(IdUtil.generateId());
            esSubmitSms.setChannel(subscribe.getChannel());
            esSubmitSms.setSubChannel(subscribe.getSubChannel());
            esSubmitSms.setUserAgent(subscribe.getUserAgent());
            esSubmitSms.setIp(subscribe.getIp());
            esSubmitSms.setMobile(subscribe.getMobile());
            esSubmitSms.setOperationTime(new Date());
            esSubmitSms.setOperationType("2"); //提交短信
            esSubmitSms.setIsp("1"); //只支持移动
            esSubmitSms.setCode(subscribe.getSmsCode());
            try {
                esOperationSmsRepository.save(esSubmitSms);
            } catch (Exception e) {
                log.error("提交验证码写入es错误,错误文档:{},错误信息:{}", "operation_sms", e.getMessage());
            }
        }
    }

    @Override
    @Async
    public void syncEsData() {
        log.info("开始同步es数据");
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        int i = 1;
        while (i > 0) {
            BoolQueryBuilder builder = QueryBuilders.boolQuery();
            buildQuery(builder);
            NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                    withSort(SortBuilders.fieldSort("createTime").order(SortOrder.ASC)).
                    withQuery(builder).build();
            nativeSearchQuery.setTrackTotalHits(true);
            nativeSearchQuery.setMaxResults(500);
            SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
            List<EsSubscribe> list = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
            i = list.size();
            log.info("本次查询共需要同步数据{}条", i);
            List<String> ids = list.stream().map(EsSubscribe::getId).collect(Collectors.toList());
            if (ids.size() > 0) {
                List<Subscribe> subscribes = subscribeService.lambdaQuery().in(Subscribe::getId, ids).list();
                batchSaveOrUpdateSubscribe(subscribes);
            }
        }
        log.info("结束同步es数据");
    }

    private void buildQuery(BoolQueryBuilder builder) {
        builder.must(QueryBuilders.rangeQuery("createTime").
                gte(getSixMonthStart()).
                lte(getSixMonthEnd()));
        builder.must(QueryBuilders.termQuery("status", 1));
        builder.must(QueryBuilders.termQuery("isp", "1"));
        builder.must(QueryBuilders.termQuery("price", -1));
        builder.mustNot(QueryBuilders.termsQuery("bizType.keyword", "MEMBER_WCY", "BAIDU", "ChinaMobile", "CARD", "MEMBER_XMSG", "COMIC", "READ"));

    }

    /**
     * 获取6个月开始时间时间戳
     */
    public static Long getSixMonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) - 6, 1, 0, 0, 0);
        return calendar.getTimeInMillis();
    }


    /**
     * 获取6个月开始时间时间戳
     */
    public static Long getSixMonthEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 0, 0, 0, 0);
        return calendar.getTimeInMillis();
    }


    @Override
    public List<Map<String, Object>> queryAllBizByMobile(String mobile) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("mobile", mobile));
        builder.must(QueryBuilders.termQuery("status", "1"));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                withQuery(builder).build();
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
        List<EsSubscribe> list = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        List<Map<String, Object>> result = new ArrayList<>();
        list.forEach(esSubscribe -> {
            Map<String, Object> dataMap = new HashMap();
            dataMap.put("mobile", mobile);
            dataMap.put("channel", esSubscribe.getChannel());
            dataMap.put("bizType", esSubscribe.getBizType());
            dataMap.put("province", esSubscribe.getProvince());
            dataMap.put("city", esSubscribe.getCity());
            dataMap.put("isp", esSubscribe.getIsp());
            dataMap.put("serviceId", esSubscribe.getServiceId());
            dataMap.put("createTime", DateUtils.datetimeFormat.get().format(esSubscribe.getCreateTime()));
            try {
                dataMap.put("orderStatus", complaintService.queryOrderStatus(mobile, esSubscribe.getChannel(), esSubscribe.getBizType(), esSubscribe.getServiceId()));
            } catch (Exception e) {
                log.warn("查询用户再订业务异常,手机号:{},渠道号:{},业务编码:{}", mobile, esSubscribe.getChannel(), esSubscribe.getBizType());
                dataMap.put("orderStatus", BizConstant.SUBSCRIBE_STATUS_INIT);
            }
            result.add(dataMap);
        });
        Collections.sort(result, (o1, o2) -> {
            if (((Integer) o2.get("orderStatus")).compareTo((Integer) o1.get("orderStatus")) != 0) {
                return ((Integer) o2.get("orderStatus")).compareTo((Integer) o1.get("orderStatus"));
            }


            return ((String) o2.get("createTime")).compareTo((String) o1.get("createTime"));
        });
        //        result.sort(Comparator.comparingInt(e -> Integer.parseInt(e.get("orderStatus").toString())));
        //        Collections.reverse(result);
        return result;
    }

    /**
     * 根据手机号查询最近3天订购的业务
     * @param mobile
     * @param recentDays 最近几天
     * @return
     */
    @Override
    public List<EsSubscribe> queryRecentAllBizByMobile(String mobile, long recentDays) {
        if(recentDays<1){
            return Lists.newArrayList();
        }
        LocalDate localDate = LocalDate.now();
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("mobile", mobile));
        builder.must(QueryBuilders.termQuery("status", "1"));
        //查询最近3天订购的业务
        builder.filter(QueryBuilders.rangeQuery("fmtCreateTime").
                        from(localDate.minusDays(recentDays-1).toString()).
                        to(localDate.toString())
                        .format("yyyy-MM-dd"));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                withQuery(builder).build();
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);

        return searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
    }

    @Override
    @Async
    public void saveUnsubLogAsync(EsUnsubLog esUnsubLog) {
        try {
            IndexRequest indexRequest = new IndexRequest("unsub_log", "_doc", esUnsubLog.getId());
            //String json = JSON.toJSONString(esUnsubLog);
            String json = mapper.writeValueAsString(esUnsubLog);
            indexRequest.source(json, XContentType.JSON);
            indexRequest.timeout("1s");
            IndexResponse indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            ReplicationResponse.ShardInfo shardInfo = indexResponse.getShardInfo();
            if (shardInfo.getFailed() > 0) {
                log.error("退订日志数据写入es失败,失败原因:{}", shardInfo.getFailures()[0].reason());
            }
        } catch (Exception e) {
            log.error("退订日志数据写入es错误,错误文档:{},错误信息:{}", "unsub_log", e.getMessage());
        }
    }

    @Override
    public Integer getFeedbackStatusById(String id)  {
        //构建搜索请求对象
//        SearchRequest searchRequest = new SearchRequest(INDEX_SUBSCRIBE);
////        //设置搜索对象的类型
////        searchRequest.types("_doc");
//        //构建搜索源对象
//        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        //搜索源搜索方式
//        searchSourceBuilder.query(QueryBuilders.matchQuery("id",id));
//        //过滤条件 第一个要显示的字段,第二个不显示的
//        searchSourceBuilder.fetchSource(new String[]{"feedbackStatusCaitu"},new String[]{});
//        //向搜索请求对象中设置搜索源
//        searchRequest.source(searchSourceBuilder);
        //执行搜索,向ES发起Http请求,获得结果对象
//        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
//        //搜索结果
//        SearchHits hits = searchResponse.getHits();
//        //得到匹配度高的文档
//        SearchHit[] searchHits = hits.getHits();
//        for (SearchHit hit : searchHits) {
//            //源文档内容
//            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
//            Integer feedbackStatusCaitu = (Integer) sourceAsMap.get("feedbackStatusCaitu");
////            return feedbackStatusCaitu;
//        }
        GetRequest request = new GetRequest(INDEX_SUBSCRIBE, id);
        FetchSourceContext fetchSourceContext = new FetchSourceContext(true, new String[]{"feedbackStatusCaitu"}, null);
        request.fetchSourceContext(fetchSourceContext);
        try {
            GetResponse response = restHighLevelClient.get(request, RequestOptions.DEFAULT);
            if (response.isExists()) {
                return (Integer) response.getSourceAsMap().get("feedbackStatusCaitu");
            }
            return null;
        } catch (IOException e) {
            log.error("查询id异常", e);
            return null;
        }



        //ObjectMapper mapper =   new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //mapper.registerModule(new JavaTimeModule());
        //mapper.enable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        //UpdateRequest updateRequest = new UpdateRequest(INDEX_SUBSCRIBE, "487816");
        //Subscribe subscribe = new Subscribe();
        //subscribe.setResult("黑名单号码更新+2");
        //subscribe.setOpenTime(LocalDateTime.now());
        //try {
        //    String source = mapper.writeValueAsString(subscribe);
        //    System.out.println(source);
        //    updateRequest.doc(source,XContentType.JSON);
        //} catch (JsonProcessingException e) {
        //    e.printStackTrace();
        //}
        //// 执行请求
        //try {
        //    UpdateResponse updateResponse = restHighLevelClient.update( updateRequest, RequestOptions.DEFAULT);
        //    System.out.println(JSON.toJSONString(updateResponse));
        //} catch (IOException e) {
        //    e.printStackTrace();
        //}
        //finally {
        //    return null;
        //}
    }

    /**
     * 根据id更新EsSubscribe状态
     * @param subscriberId
     * @param status
     * @param result
     */
    @Override
    public void updateSubscribeStatusResultById(String subscriberId, Integer status, String result)  {
        UpdateRequest updateRequest = new UpdateRequest(INDEX_SUBSCRIBE, subscriberId);
        EsSubscribe esSubscribe = new EsSubscribe();
        esSubscribe.setStatus(status);
        esSubscribe.setResult(result);
        try {
            String source = mapper.writeValueAsString(esSubscribe);
            updateRequest.doc(source,XContentType.JSON);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        // 执行请求
        try {
            UpdateResponse updateResponse = restHighLevelClient.update( updateRequest, RequestOptions.DEFAULT);
            log.info("根据id更新EsSubscribe状态,响应:{}",updateResponse);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据id更新EsSubscribe包月校验状态
     * @param subscriberId
     * @param verfyStatus
     */
    @Override
    public void updateSubscribeVerfyStatusById(String subscriberId, Integer verfyStatus)  {
        try {
            UpdateRequest updateRequest = new UpdateRequest(INDEX_SUBSCRIBE, subscriberId).doc(XContentType.JSON,"price", verfyStatus);
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.warn("根据id更新EsSubscribe包月校验状态,subscriberId:{},verfyStatus:{},异常:{}",subscriberId,verfyStatus, e.getMessage());
        }
    }

    @Override
    public void removeSubscribeById(String id) {
        if (StringUtils.isNotBlank(id)) {
            esSubscribeRepository.deleteById(id);
        }
    }

    public BulkByScrollResponse updateByQuery(String index, QueryBuilder query, Map<String, Object> document) {
        UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest(index);
        updateByQueryRequest.setQuery(query);
        StringBuilder script = new StringBuilder();
        Set<String> keys = document.keySet();
        for (String key : keys) {
            Object value = document.get(key);
            String appendValue = value instanceof String ? "'" + value + "'" : value.toString();
            script.append("ctx._source.").append(key).append("=").append(appendValue).append(";");
        }
        updateByQueryRequest.setScript(new Script(script.toString()));
        try {
            return restHighLevelClient.updateByQuery(updateByQueryRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.warn("es条件更新异常",e);
        }
        return null;
    }

    @Override
    public void transTestEsSub(Subscribe subscribe) {
        subscribe.setResult("测试数据");
        TestEsSub testEsSub = new TestEsSub();
        BeanUtils.copyProperties(subscribe, testEsSub);
        final SubChannel subChannel = subChannelService.findSubChannel(subscribe.getSubChannel());
        if(subChannel != null){
            //testEsSub.setAdPlatform(subChannel.getAdPlatform());
            testEsSub.setOptimizer(subChannel.getOptimizer());
        }
        testEsSub.setFmtCreateTime(subscribe.getCreateTime());
        testEsSub.setUserAgent("");
        //testEsSub.setSource("");
        try {
            IndexRequest indexRequest = new IndexRequest("test_sub", "_doc", subscribe.getId());
            //String json = JSON.toJSONString(testEsSub);
            String json = mapper.writeValueAsString(testEsSub);
            indexRequest.source(json, XContentType.JSON);
            indexRequest.timeout("1s");
            IndexResponse indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            ReplicationResponse.ShardInfo shardInfo = indexResponse.getShardInfo();
            if (shardInfo.getFailed() > 0) {
                log.error("同步数据到es失败,失败原因:{}", shardInfo.getFailures()[0].reason());
            }
            //log.info("数据同步到es,id:{},更新结果:{}", subscribe.getId(), shardInfo.getSuccessful() == shardInfo.getTotal());
        } catch (Exception e) {
            log.error("业务订购写入es错误,错误文档:{},错误信息:{}", "test_sub", e.getMessage());
        }
    }

    @Override
    public void transTestEsOrder(Subscribe subscribe) {
        subscribe.setResult("测试数据");
        TestEsOrder testEsOrder = new TestEsOrder();
        BeanUtils.copyProperties(subscribe, testEsOrder);
        final SubChannel subChannel = subChannelService.findSubChannel(subscribe.getSubChannel());
        if(subChannel != null){
            //testEsOrder.setAdPlatform(subChannel.getAdPlatform());
            testEsOrder.setOptimizer(subChannel.getOptimizer());
        }
        testEsOrder.setFmtCreateTime(subscribe.getCreateTime());
        testEsOrder.setUserAgent("");
        //testEsOrder.setSource("");
        try {
            IndexRequest indexRequest = new IndexRequest("test_order", "_doc", subscribe.getId());
            //String json = JSON.toJSONString(testEsOrder);
            String json = mapper.writeValueAsString(testEsOrder);
            log.info("数据转json:{}",json);
            indexRequest.source(json, XContentType.JSON);
            indexRequest.timeout("1s");
            IndexResponse indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            ReplicationResponse.ShardInfo shardInfo = indexResponse.getShardInfo();
            if (shardInfo.getFailed() > 0) {
                log.error("同步数据到es失败,失败原因:{}", shardInfo.getFailures()[0].reason());
            }
            //log.info("数据同步到es,id:{},更新结果:{}", subscribe.getId(), shardInfo.getSuccessful() == shardInfo.getTotal());
        } catch (Exception e) {
            log.error("业务订购写入es错误,错误文档:{},错误信息:{}", "test_order", e.getMessage());
        }
    }

    @Override
    public void modifyAndfindTestEsOrder(String id) {
        try {
            UpdateRequest updateRequest = new UpdateRequest("test_order", id);
            updateRequest.doc(XContentType.JSON,"optimizer","eleven","serviceId","9527");
            UpdateResponse updateResponse = restHighLevelClient.update( updateRequest, RequestOptions.DEFAULT);
            log.info("根据id更新EsSubscribe状态,响应:{}",updateResponse);
            final TestEsOrder esOrder = elasticsearchRestTemplate.get(id, TestEsOrder.class);
            System.out.println(esOrder);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public EsSubscribe searchById(String id) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("id", id));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).build();
        SearchHit<EsSubscribe> searchHit = elasticsearchRestTemplate.searchOne(nativeSearchQuery, EsSubscribe.class);
        return searchHit == null ? null : searchHit.getContent();
    }

    /**
     * 根据手机号和渠道号列表查询全部订购成功数据
     * @param mobile
     * @param channelList
     * @return
     */
    @Override
    public List<EsSubscribe> findEsSubscribeByMobileAndChannels(String mobile, List<String> channelList) {
        //需要查询的字段
        String[] includes = {"mobile", "bizType", "channel", "status", "serviceId"};

        //构建搜索条件
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("mobile", mobile));
        //渠道号列表查询
        if(channelList!=null && !channelList.isEmpty()){
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            channelList.forEach(channel -> {
                shouldQuery.should(QueryBuilders.termQuery("channel.keyword", channel));
            });
            builder.must(shouldQuery);
        }
        builder.must(QueryBuilders.termQuery("status", 1));

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
                    @Override
                    public String[] getIncludes() {
                        return includes;
                    }
                    @Override
                    public String[] getExcludes() {
                        return new String[0];
                    }
                }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        //渠道号去重
        nativeSearchQuery.setCollapseBuilder(new CollapseBuilder("channel.keyword"));
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(100000);
        SearchHits<EsSubscribe> searchHits =elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.warn("渠道订购[渠道号查询]-根据手机号和渠道号列表查询全部订购成功数据-暂无订购数据=>手机号:{},渠道号:{}",mobile,channelList);
            return null;
        }

        return esSubscribeList;
    }


    /**
     * 根据手机号和渠道号列表查询es实时订购数据
     * @param mobile
     * @param channelList
     * @return
     */
    @Override
    public List<EsSubscribe> findTodayEsSubscribeByMobileAndChannels(String mobile, List<String> channelList) {
        //需要查询的字段
        String[] includes = {"mobile", "bizType", "channel", "status","serviceId","createTime"};

        //构建搜索条件
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("mobile", mobile));
        builder.must(QueryBuilders.rangeQuery("createTime")
                .gte(DateUtil.localDateTimeToDate(DateUtil.handleSqlDateStartTime(LocalDateTime.now())).getTime())
                .lte(DateUtil.localDateTimeToDate(DateUtil.handleSqlDateEndTime(LocalDateTime.now())).getTime()));
        //渠道号列表查询
        if(channelList!=null && !channelList.isEmpty()){
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            channelList.forEach(channel -> {
                shouldQuery.should(QueryBuilders.termQuery("channel.keyword", channel));
            });
            builder.must(shouldQuery);
        }
        builder.must(QueryBuilders.termQuery("status", 1));

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
                    @Override
                    public String[] getIncludes() {
                        return includes;
                    }
                    @Override
                    public String[] getExcludes() {
                        return new String[0];
                    }
                }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        //渠道号去重
        nativeSearchQuery.setCollapseBuilder(new CollapseBuilder("channel.keyword"));
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(100000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.warn("渠道订购[渠道号查询]-根据手机号和渠道号列表查询es当天订购数据-暂无订购数据=>手机号:{},渠道号:{}",mobile,channelList);
            return null;
        }
        return esSubscribeList;
    }

    /**
     * 根据手机号和渠道号查询es订购数据
     * @param mobile
     * @param channel
     * @return
     */
    @Override
    public EsSubscribe findRecentEsSubscribeByMobileAndChannel(String mobile, String channel)  {
        //需要查询的字段
        String[] includes = {"mobile", "bizType", "channel", "status", "serviceId", "subChannel","province","openTime"};

        //构建搜索条件
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("mobile", mobile));
        builder.must(QueryBuilders.termQuery("channel.keyword",channel));
        builder.must(QueryBuilders.termQuery("status", 1));

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
                    @Override
                    public String[] getIncludes() {
                        return includes;
                    }

                    @Override
                    public String[] getExcludes() {
                        return new String[0];
                    }
                }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        //渠道号去重
        nativeSearchQuery.setCollapseBuilder(new CollapseBuilder("channel.keyword"));
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(100000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.warn("渠道订购[渠道号查询]-根据手机号和渠道号查询es最新订购数据-暂无订购数据=>手机号:{},渠道号:{}",mobile,channel);
            return null;
        }
        return esSubscribeList.get(0);
    }

    /**
     * 保存退订日志
     * @param esSubscribe
     * @param respMsg
     * @param action
     */
    @Override
    public void saveUnsubLog(EsSubscribe esSubscribe, String respMsg, String action){
        final EsUnsubLog esUnsubLog = new EsUnsubLog();
        esUnsubLog.setMobile(esSubscribe.getMobile());
        esUnsubLog.setBizType(esSubscribe.getBizType());
        esUnsubLog.setChannel(esSubscribe.getChannel());
        esUnsubLog.setResponse(respMsg);
        esUnsubLog.setAction(action);
        esUnsubLog.setCreateTime(new Date());
        esUnsubLogRepository.save(esUnsubLog);
    }

    @Override
    public List<EsSubStatistics> subscribeStatisticsLast6MonthByChannel(LocalDate startDay, LocalDate endDay, String channelCode) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                        from(startDay.toString()).
                        to(endDay.toString())
                .format("yyyy-MM-dd"));
        builder.must(QueryBuilders.termQuery("channel.keyword", channelCode));
        builder.must(QueryBuilders.termQuery("status", SUBSCRIBE_STATUS_SUCCESS));
        builder.must(QueryBuilders.termQuery("isp.keyword", MobileRegionResult.ISP_YIDONG));
        TermsAggregationBuilder tab = AggregationBuilders.terms("subChannelGroup").field("subChannel.keyword").size(1000)
                .subAggregation(AggregationBuilders.terms("provinceGroup").field("province.keyword").size(1000)
                        .subAggregation(AggregationBuilders.terms("verifyStatusGroup").field("price")
                                .order(BucketOrder.key(true))));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).
                addAggregation(tab).
                withSourceFilter(new FetchSourceFilter(new String[]{""}, new String[]{""})).  //不查询任何结果
                build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(0); //不查询具体数据
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
        List<? extends Terms.Bucket> subChannelBuckets = ((Terms) searchHits.getAggregations().asMap().get("subChannelGroup")).getBuckets();
        List<EsSubStatistics> statisticsByChannel = new ArrayList<>();
        subChannelBuckets.forEach(subChannelBucket -> {
            List<? extends Terms.Bucket> provinceBuckets = ((Terms) subChannelBucket.getAggregations().asMap().get("provinceGroup")).getBuckets();
            provinceBuckets.forEach(provinceBucket -> {
                EsSubStatistics ss = new EsSubStatistics();
                ss.setChannel(channelCode);
                ss.setBizType(BizConstant.getBizTypeByMiguChannel(channelCode));
                ss.setSubChannel(subChannelBucket.getKeyAsString());
                ss.setProvince(provinceBucket.getKeyAsString());
                ss.setTotal(provinceBucket.getDocCount());
                ss.setSubMonth(YearMonth.from(startDay).toString());
                ss.setStatsMonth(YearMonth.now().toString());
                List<? extends Terms.Bucket> verifyStatusBuckets = ((Terms) provinceBucket.getAggregations().asMap().get("verifyStatusGroup")).getBuckets();
                verifyStatusBuckets.forEach(verifyStatusBucket -> {
                    if(Integer.parseInt(verifyStatusBucket.getKeyAsString()) == 1){
                        ss.setSucc(verifyStatusBucket.getDocCount());
                    }
                });
                statisticsByChannel.add(ss);
            });
        });

        return statisticsByChannel;
    }

    @Override
    public List<EsSubStatistics> subscribeStatisticsLast6MonthAllChannel(LocalDate startDay, LocalDate endDay) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                        from(startDay.toString()).
                        to(endDay.toString())
                .format("yyyy-MM-dd"));
        builder.must(QueryBuilders.termQuery("status", SUBSCRIBE_STATUS_SUCCESS));
        builder.must(QueryBuilders.termQuery("isp.keyword", MobileRegionResult.ISP_YIDONG));
        TermsAggregationBuilder tab = AggregationBuilders.terms("channelGroup").field("channel.keyword").size(1000)
                        .subAggregation(AggregationBuilders.terms("verifyStatusGroup").field("price")
                                .order(BucketOrder.key(true)));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).
                addAggregation(tab).
                withSourceFilter(new FetchSourceFilter(new String[]{""}, new String[]{""})).  //不查询任何结果
                build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(0); //不查询具体数据
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
        List<? extends Terms.Bucket> channelBuckets = ((Terms) searchHits.getAggregations().asMap().get("channelGroup")).getBuckets();
        List<EsSubStatistics> statisticsByChannel = new ArrayList<>();
        channelBuckets.forEach(channelBucket -> {
                EsSubStatistics ss = new EsSubStatistics();
                ss.setChannel(channelBucket.getKeyAsString());
                ss.setBizType(BizConstant.getBizTypeByMiguChannel(channelBucket.getKeyAsString()));
                ss.setTotal(channelBucket.getDocCount());
                ss.setSubMonth(YearMonth.from(startDay).toString());
                ss.setStatsMonth(YearMonth.now().toString());
                List<? extends Terms.Bucket> verifyStatusBuckets = ((Terms) channelBucket.getAggregations().asMap().get("verifyStatusGroup")).getBuckets();
                verifyStatusBuckets.forEach(verifyStatusBucket -> {
                    if(Integer.parseInt(verifyStatusBucket.getKeyAsString()) == 1){
                        ss.setSucc(verifyStatusBucket.getDocCount());
                    }
                });
                statisticsByChannel.add(ss);
        });

        return statisticsByChannel;
    }

    /**
     * 企业彩铃退订统计
     *
     * @param channelCode
     * @return
     */
    @Override
    public Map<Long, Long> subscribeStatisticsQyclVerifyStatusToday(String channelCode) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime")
                        .gte(cn.hutool.core.date.DateUtil.formatLocalDateTime(LocalDate.now().atStartOfDay()))
                        .to(cn.hutool.core.date.DateUtil.formatLocalDateTime(LocalDate.now().atTime(LocalTime.MAX)))
                        .format(DatePattern.NORM_DATETIME_PATTERN));
        builder.must(QueryBuilders.termQuery("channel.keyword", channelCode));
        builder.must(QueryBuilders.termQuery("status", SUBSCRIBE_STATUS_SUCCESS));
        TermsAggregationBuilder tab = AggregationBuilders.terms("verifyStatusGroup").field("verifyStatus").size(10);
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).
                addAggregation(tab).
                withSourceFilter(new FetchSourceFilter(new String[]{""}, new String[]{""})).  //不查询任何结果
                build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(0); //不查询具体数据
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class);
        return ((Terms) searchHits.getAggregations().asMap().get("verifyStatusGroup"))
                .getBuckets()
                .stream()
                //排除未校验的
                .filter(bucket -> (Long) bucket.getKey() > SUBSCRIBE_MONTH_VERIFY_INIT)
                .collect(Collectors.toMap(bucket -> (Long) bucket.getKey(), MultiBucketsAggregation.Bucket::getDocCount));
    }

    /**
     * 进24小时按渠道号分组最近的50个订单是否无订购成功的
     *
     * @return
     */
    @Override
    public Map<String, Boolean> subscribeMonitoAllChannel(long recentHours, int topN) {
        final LocalDateTime now = LocalDateTime.now();
        BoolQueryBuilder builder = QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("fmtCreateTime")
                .gte(cn.hutool.core.date.DateUtil.formatLocalDateTime(now.minusHours(recentHours)))
                .to(cn.hutool.core.date.DateUtil.formatLocalDateTime(now.minusMinutes(3L)))
                .format(DatePattern.NORM_DATETIME_PATTERN));
        TermsAggregationBuilder tab = AggregationBuilders.terms("channelGroup").field("channel.keyword").size(100)
                .subAggregation(AggregationBuilders.topHits("topN")
                        .explain(false)
                        .trackScores(false)
                        .version(false)
                        .size(topN)
                        .from(0)
                        .fetchSource(new String[]{"status"}, null)
                        .sort("fmtCreateTime", SortOrder.DESC));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).
                addAggregation(tab).
                withSourceFilter(new FetchSourceFilter(new String[]{""}, new String[]{""})).  //不查询任何结果
                build();
        nativeSearchQuery.setTrackTotalHits(false);
        nativeSearchQuery.setMaxResults(0); //不查询具体数据
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class);

        return ((Terms) searchHits.getAggregations().asMap().get("channelGroup"))
                .getBuckets()
                .stream()
                //排除未校验的
                .filter(bucket -> bucket.getDocCount() >= topN)
                .collect(Collectors.toMap(
                        bucket -> bucket.getKeyAsString(),
                        bucket -> Arrays.stream(((TopHits) bucket.getAggregations().asMap().get("topN")).getHits().getHits()).map(org.elasticsearch.search.SearchHit::getSourceAsMap).noneMatch(docMap-> SUBSCRIBE_STATUS_SUCCESS.equals(docMap.get("status")))
                ));
    }

    @Override
    public List<EsSubscribe> findLast3MonthEsSubscribeByMobile(String mobile, String channelCode) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime")
                .gte(cn.hutool.core.date.DateUtil.formatLocalDateTime(LocalDate.now().minusMonths(3L).atTime(LocalTime.MIN)))
                .format(DatePattern.NORM_DATETIME_PATTERN));
        builder.must(QueryBuilders.termQuery("mobile.keyword", mobile));
        builder.must(QueryBuilders.termQuery("channel.keyword", channelCode));
        builder.must(QueryBuilders.termQuery("status", SUBSCRIBE_STATUS_SUCCESS));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).build();
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
        return searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
    }

    @Override
    public void saveEsBusinessLog(EsBusinessLog esBusinessLog) {
        esBusinessLogRepository.save(esBusinessLog);
    }

    public static void main(String[] args) throws IOException {
        //final XContentBuilder xContentBuilder = XContentFactory.jsonBuilder().startObject().field("date",
        //        new Date()).field("aaa", "111").endObject();
        //System.out.println("xContentBuilder = " + xContentBuilder.toString());
        //System.out.println("***********".matches("13[6-9]\\d{8}"));
        //System.out.println("***********".matches("13[6-9]\\d{8}"));
        //System.out.println("***********".matches("13[6-9]\\d{8}"));
        //System.out.println("***********".matches("13[6-9]\\d{8}"));
        //System.out.println("***********".matches("13[6-9]\\d{8}"));
        //
        //final HttpUrl parse = HttpUrl.parse(
        //        "https://www.118114.net/h5/marketing/#/verification?channel=M007&phone=gX0EOuhQOcN0dFkccSAjOg".replace("/ #",""));
        //System.out.println(parse.queryParameter("channel"));
        System.out.println(LocalDateTime.now());
        System.out.println(cn.hutool.core.date.DateUtil.formatLocalDateTime(LocalDateTime.now()));
        System.out.println(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        System.out.println(LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.UTC_MS_PATTERN));
        Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 根据订单号和渠道号列表查询es实时订购数据
     * @param orderId
     * @param channelList
     * @return
     */
    @Override
    public List<EsSubscribe> findTodayEsSubscribeByOrderIdAndChannels(String orderId, List<String> channelList) {
        //需要查询的字段
        String[] includes = {"mobile","ispOrderNo", "bizType", "channel", "status","serviceId","createTime"};

        //构建搜索条件
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("ispOrderNo", orderId));
        builder.must(QueryBuilders.rangeQuery("createTime")
                .gte(DateUtil.localDateTimeToDate(DateUtil.handleSqlDateStartTime(LocalDateTime.now())).getTime())
                .lte(DateUtil.localDateTimeToDate(DateUtil.handleSqlDateEndTime(LocalDateTime.now())).getTime()));
        //渠道号列表查询
        if(channelList!=null && !channelList.isEmpty()){
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            channelList.forEach(channel -> {
                shouldQuery.should(QueryBuilders.termQuery("channel.keyword", channel));
            });
            builder.must(shouldQuery);
        }
        builder.must(QueryBuilders.termQuery("status", 1));

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
            @Override
            public String[] getIncludes() {
                return includes;
            }
            @Override
            public String[] getExcludes() {
                return new String[0];
            }
        }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        //渠道号去重
        nativeSearchQuery.setCollapseBuilder(new CollapseBuilder("channel.keyword"));
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(100000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.warn("渠道订购[渠道号查询]-根据订单号和渠道号列表查询当天全部订购成功数据-暂无订购数据=>订单号:{},渠道号:{}",orderId,channelList);
            return null;
        }
        return esSubscribeList;
    }



    /**
     * 根据id更新EsSubscribe3天包月校验
     * @param subscriberId
     * @param verifyStatusDaily
     */
    @Override
    public void updateSubscribeVerfyStatusDailyById(String subscriberId,Integer verifyStatusDaily)  {
        try {
            UpdateRequest updateRequest = new UpdateRequest(INDEX_SUBSCRIBE, subscriberId).doc(XContentType.JSON,"verifyStatusDaily",verifyStatusDaily);
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.warn("根据id更新EsSubscribe3天包月校验,subscriberId:{},verifyStatusDaily:{},异常:{}",subscriberId,verifyStatusDaily, e.getMessage());
        }
    }

    /**
     * 根据id更新EsSubscribe1小时包月校验
     * @param subscriberId
     * @param verfyStatus
     */
    @Override
    public void updateSubVerfyStatusById(String subscriberId, Integer verfyStatus) {
        try {
            UpdateRequest updateRequest = new UpdateRequest(INDEX_SUBSCRIBE, subscriberId).doc(XContentType.JSON,"verifyStatus", verfyStatus);
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.warn("根据id更新EsSubscribe1小时包月校验,subscriberId:{},verfyStatus:{},异常:{}",subscriberId,verfyStatus, e.getMessage());
        }
    }



    /**
     * 根据手机号和渠道号查询es订单
     * @param mobile
     * @param channel
     * @return
     */
    @Override
    public EsSubscribe findEsSubscribeByMobileAndChannel(String mobile, String channel)  {
        //需要查询的字段
        String[] includes = {"mobile", "bizType", "channel", "status", "serviceId", "subChannel","province","openTime","createTime"};

        //构建搜索条件
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("mobile", mobile));
        builder.must(QueryBuilders.termQuery("channel",channel));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
                    @Override
                    public String[] getIncludes() {
                        return includes;
                    }

                    @Override
                    public String[] getExcludes() {
                        return new String[0];
                    }
                }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        //渠道号去重
        nativeSearchQuery.setCollapseBuilder(new CollapseBuilder("channel"));
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(100000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.warn("渠道订购[渠道号查询]-根据手机号和渠道号查询es最新订购数据-暂无订购数据=>手机号:{},渠道号:{}",mobile,channel);
            return null;
        }
        return esSubscribeList.get(0);
    }


    /**
     * 根据手机号和渠道号列表查询es当月订购数据
     * @param mobile
     * @param channel
     * @return
     */
    @Override
    public EsSubscribe findSameMonthEsSubscribeByMobileAndChannel(String mobile,String channel) {
        //需要查询的字段
        String[] includes = {"mobile", "bizType", "channel", "status","serviceId","createTime"};
        LocalDateTime start = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        LocalDateTime end=    LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        //构建搜索条件
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("mobile", mobile));
        builder.must(QueryBuilders.termQuery("channel.keyword",channel));
        builder.must(QueryBuilders.termQuery("status", 1));
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").from(DateUtil.formatSplitTime(start)).to(DateUtil.formatSplitTime(end)).format("yyyy-MM-dd HH:mm:ss"));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
                    @Override
                    public String[] getIncludes() {
                        return includes;
                    }
                    @Override
                    public String[] getExcludes() {
                        return new String[0];
                    }
                }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        //渠道号去重
        nativeSearchQuery.setCollapseBuilder(new CollapseBuilder("channel.keyword"));
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(100000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.warn("渠道订购[渠道号查询]-根据手机号和渠道号查询es当月订购数据-暂无订购数据=>手机号:{},渠道号:{}",mobile,channel);
            return null;
        }
        return esSubscribeList.get(0);
    }



    @Override
    public void saveEsOrderFailLog(EsOrderFailLog esOrderFailLog) {
        esOrderFailLogRepository.save(esOrderFailLog);
    }

    @Override
    public void saveEsQywx(EsQywx esQywx) {
        esQywxRepository.save(esQywx);
    }

    @Override
    public void saveEsQywxAction(EsQywxAction esQywxAction) {
        esQywxActionRepository.save(esQywxAction);
    }



    @Override
    public void updatesOrderFailLogIndex(String index,int size)  {
        try {
            //创建Setting对象
            Settings settings = Settings.builder()
                    .put("max_result_window", size)//es默认是1000
                    .build();
            //创建UpdateSettingsRequest
            UpdateSettingsRequest updateSettingsRequest = new UpdateSettingsRequest(settings,index);
            //执行put
            restHighLevelClient.indices().putSettings(updateSettingsRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }



    /**
     * 根据手机号和业务类型查询es最新订购数据
     * @param mobile
     * @param bizType
     * @return
     */
    @Override
    public EsSubscribe findEsSubscribeByMobileAndBizType(String mobile, String bizType)  {
        //需要查询的字段
        String[] includes = {"mobile", "bizType", "channel", "subChannel"};
        //构建搜索条件
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("mobile", mobile));
        builder.must(QueryBuilders.termQuery("bizType.keyword",bizType));
        builder.must(QueryBuilders.termQuery("status", 1));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
                    @Override
                    public String[] getIncludes() {
                        return includes;
                    }

                    @Override
                    public String[] getExcludes() {
                        return new String[0];
                    }
                }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(100000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.warn("根据手机号和业务类型查询es最新订购数据-暂无订购数据=>手机号:{},业务类型:{}",mobile,bizType);
            return null;
        }
        return esSubscribeList.get(0);
    }

    @Override
    public void saveOrUpdateQywxAsync(Qywx qywx) {
        EsQywx esQywx = new EsQywx();
        BeanUtils.copyProperties(qywx, esQywx);
        Integer reportStatus = this.getReportStatusById(String.valueOf(qywx.getId()));
//        if (reportStatus != null) {
        esQywx.setReportStatus(reportStatus);
//        }
        esQywx.setFmtCreateTime(qywx.getCreateTime());
        try {
            IndexRequest indexRequest = new IndexRequest("qywx", "_doc", qywx.getId());
            //String json = JSON.toJSONString(esSubscribe);
            String json = mapper.writeValueAsString(esQywx);
            indexRequest.source(json, XContentType.JSON);
            indexRequest.timeout("1s");
            IndexResponse indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            ReplicationResponse.ShardInfo shardInfo = indexResponse.getShardInfo();
            if (shardInfo.getFailed() > 0) {
                log.error("同步数据到es失败,失败原因:{}", shardInfo.getFailures()[0].reason());
            }
            //log.info("数据同步到es,id:{},更新结果:{}", subscribe.getId(), shardInfo.getSuccessful() == shardInfo.getTotal());
        } catch (Exception e) {
            log.error("企业微信数据写入es错误,错误文档:{},错误信息:{}", "subscribe", e.getMessage());
        }
    }

    @SneakyThrows
    @Override
    public void updateQywxByAssignFieldById(String id, String field, String value) {
        UpdateRequest updateRequest = new UpdateRequest("qywx", id).doc(XContentType.JSON, field, value);
        restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);

    }


    public Integer getReportStatusById(String id) {
        GetRequest request = new GetRequest("qywx", id);
        FetchSourceContext fetchSourceContext = new FetchSourceContext(true, new String[]{"reportStatus"}, null);
        request.fetchSourceContext(fetchSourceContext);
        try {
            GetResponse response = restHighLevelClient.get(request, RequestOptions.DEFAULT);
            if (response.isExists()) {
                return (Integer) response.getSourceAsMap().get("reportStatus");
            }
            return null;
        } catch (IOException e) {
            log.error("查询id异常", e);
            return null;
        }
    }


    /**
     * 根据id更新EsSubscribe
     * @param subscribe
     */
    @Override
    public void updateSubscribeEs(Subscribe subscribe) {
        try {
            UpdateRequest updateRequest = new UpdateRequest(INDEX_SUBSCRIBE, subscribe.getId())
                    .doc(XContentType.JSON,"status", subscribe.getStatus(),"result", subscribe.getResult(),"openTime", subscribe.getOpenTime().getTime(),"modifyTime", subscribe.getModifyTime().getTime());
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("根据id更新EsSubscribe数据,subscribe:{}-异常",subscribe,e);
        }
    }
}
