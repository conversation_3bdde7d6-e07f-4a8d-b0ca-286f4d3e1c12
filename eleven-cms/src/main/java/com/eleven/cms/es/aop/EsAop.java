package com.eleven.cms.es.aop;

import com.eleven.cms.es.service.IEsDataService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: cai lei
 * @create: 2021-07-20 11:20
 */

//@Slf4j
//@Aspect
//@Component
public class EsAop {

//    private static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    @Autowired
    IEsDataService esDataService;

//    @Pointcut("execution(public * cc.mrbird.febs.biz.service.impl.SubscribeServiceImpl.receiveOrder(..))")
    public void receiveOrderPointCut() {
    };

//    @Pointcut("execution(public * com.baomidou.mybatisplus.extension.service.IService.save(..)) && args(cc.mrbird.febs.biz.entity.Subscribe)")
//    public void saveSubscribePointCut() {
//    };


//    @Before(value = "receiveOrderPointCut()")
    public void receiveOrderBefore(JoinPoint joinPoint) {
//        Object[] args = joinPoint.getArgs();
//        Subscribe subscribe = (Subscribe) args[0];
//        //写入访问记录
//        esDataService.saveEsSubscribeVisit(subscribe);
//        //写入短信提交记录
//        if(StringUtils.isNotBlank(subscribe.getSmsCode())){
//            esDataService.saveEsSubmitSms(subscribe);
//        }
    }

//    @After(value = "saveSubscribePointCut()")
//    public void saveSubscribeAfter(JoinPoint joinPoint) {
//        log.info("saveSubscribe方法执行后......开始");
//        Object[] args = joinPoint.getArgs();
//        Subscribe subscribe = (Subscribe) args[0];
//        esDataService.saveOrUpdateSubscribe(subscribe);
//        log.info("saveSubscribe方法执行后......开始");
//    }



//
//    @After(value = "cPointCut()")
//    public void xxlSubscribeAfter(JoinPoint joinPoint) {
//        log.info("方法执行前执行......开始");
//        Object[] args = joinPoint.getArgs();
//        HttpServletRequest request = (HttpServletRequest) args[0];
//
//        String userAgent = request.getHeader("User-Agent");
//        if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
//            userAgent = userAgent.substring(0, 512);
//        }
//        //System.out.println("userAgent = " + userAgent);
//        String referer = request.getHeader("Referer");
//        //System.out.println("referer = " + referer);
//        String ipAddr = IPUtil.getIpAddr(request);
//        //System.out.println("ipAddr = " + ipAddr);
//        Subscribe subscribe = null;
//        try (ServletInputStream inputStream = request.getInputStream()) {
//            String jsonData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
//            //log.info("渠道订阅请求,加密的内容=>{}",raw);
//            //String jsonData = Sm2Utils.decrypt(raw);
//            log.info("渠道订阅请求=>{}", jsonData);
//            subscribe = mapper.readValue(jsonData, Subscribe.class);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        if (subscribe == null) {
//            subscribe = new Subscribe();
//        }
//        subscribe.setUserAgent(userAgent);
//        subscribe.setReferer(referer);
//        subscribe.setIp(ipAddr);
//        esSubscribeService.saveEsSubscribeVisit(subscribe);
//        log.info("方法执行前执行......结束");
//
//    }


}
