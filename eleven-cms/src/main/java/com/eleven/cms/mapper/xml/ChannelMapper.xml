<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.ChannelMapper">
    <select id="findByPage" resultType="com.eleven.cms.entity.Channel" parameterType="com.eleven.cms.entity.Channel">
        SELECT
            cc.id as id,
            cc.title as title,
            cc.intro as intro,
            cc.status as status,
            cc.validate_status as validateStatus,
            cc.ad_api_bean_name as adApiBeanName,
            cc.ad_id_param_name as adIdParamName,
            cc.ad_js_url as adJsUrl,
            cc.ad_js_eval as adJsEval,
            cc.api_key as apiKey,
            cc.api_url as apiUrl,
            cc.remark as remark,
            cc.create_by as createBy,
            cc.create_time as createTime,
            cc.update_by as updateBy,
            cc.update_time as updateTime,
            cc.sys_org_code as sysOrgCode,
            cc.ad_platform_id as adPlatformId,
            cc.account_id as accountId,
            cc.spread_url as spread_url,
            ap.ad_platform as adPlatform,
            cac.account as account
        FROM cms_channel cc
        LEFT JOIN  cms_ad_platform ap
        ON cc.ad_platform_id = ap.id
        LEFT JOIN  cms_account_cofig cac
        ON cc.account_id = cac.id
        WHERE 1=1
        <if test="dto != null">
            <if test="dto.id != null and dto.id != ''">
                and cc.id = #{dto.id}
            </if>
            <if test="dto.adPlatformId != null and dto.adPlatformId != ''">
                and cc.ad_platform_id = #{dto.adPlatformId}
            </if>
            <if test="dto.accountId != null and dto.accountId != ''">
                and cc.account_id = #{dto.accountId}
            </if>
            <if test="dto.adPlatform != null and dto.adPlatform != ''">
                and ap.ad_platform = #{dto.adPlatform}
            </if>
            <if test="dto.account != null and dto.account != ''">
                and cac.account = #{dto.account}
            </if>
            <if test="dto.intro != null and dto.intro != ''">
                and cc.intro LIKE CONCAT('%',#{dto.intro},'%')
            </if>
            <if test="dto.title != null and dto.title != ''">
                and cc.title LIKE CONCAT('%',#{dto.title},'%')
            </if>
        </if>
        ORDER BY cc.create_time DESC
    </select>
</mapper>