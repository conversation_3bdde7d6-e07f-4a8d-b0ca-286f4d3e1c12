<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.BusinessPackMapper">
    <select id="findByBusinessId" resultType="com.eleven.cms.entity.BusinessPack">
        SELECT service_id,service_api_bean_name
        FROM cms_business_pack
        LEFT JOIN  sys_dict_item ap
        ON service_api_bean_name = item_value
        WHERE business_id in
        <foreach collection="businessId" index="index" item="businessId" open="(" separator="," close=")">
            #{businessId}
        </foreach>
        GROUP BY service_api_bean_name
        order by sort_order asc
    </select>

    <select id="findByServiceId" resultType="com.eleven.cms.entity.BusinessPack">
        SELECT service_id,service_api_bean_name
        FROM cms_business_pack
        WHERE service_id= #{serviceId}
        GROUP BY service_api_bean_name
    </select>
</mapper>
