package com.eleven.cms.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.Music;
import org.apache.ibatis.annotations.Param;
import com.eleven.cms.entity.Column;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 栏目
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
public interface ColumnMapper extends BaseMapper<Column> {

    List<Music> getMusicListById(String id);

    IPage<Column> findByPage(Page<Column> page, @Param("dto") Column entity, @Param("createTimeBegin")Date createTimeBegin, @Param("createTimeEnd") Date createTimeEnd);
}
