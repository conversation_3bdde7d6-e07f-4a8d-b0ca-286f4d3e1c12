package com.eleven.cms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.MusicHotLevel;
import com.eleven.cms.vo.MusicVo;

/**
 * @Description: 人气榜单歌曲
 * @Author: jeecg-boot
 * @Date:   2023-11-21
 * @Version: V1.0
 */
public interface MusicHotLevelMapper extends BaseMapper<MusicHotLevel> {
    IPage<MusicVo> selectMusicHotLevelPage(Page<MusicVo> page);
}
