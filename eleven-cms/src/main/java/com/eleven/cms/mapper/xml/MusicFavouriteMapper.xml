<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.MusicFavouriteMapper">
<select id="matching" parameterType="String" resultType="String">
    SELECT
        ccm2.`column_id`
      FROM
        cms_music_favourite cmf
        LEFT JOIN cms_music cm2
          ON cmf.`copyright_id` = cm2.`copyright_id`
        LEFT JOIN cms_column_music ccm2
          ON cm2.`id` = ccm2.`music_id`
        LEFT JOIN cms_column cc
          ON ccm2.`column_id` = cc.`id`
      WHERE cmf.`mobile` = #{mobile}
        AND cc.`column_class_name` = 'XCX_HOME'
</select>
<select id="findByMobile" resultType="com.eleven.cms.vo.MusicFavouriteVo" parameterType="String">
    SELECT
      cmf.id,
      cmf.copyright_id,
      cmf.mobile,
      cmf.music_name,
      cm.singer_name,
      cm.vrbt_product_id,
      cm.cp_id,
      cm.dy_copyright_id,
      cm.dx_resource_code,
      cm.dx_tone_code,
      cm.lt_ring_id,
      cm.vrbt_img,
      cm.vrbt_video,
      cm.play_count,
      cm.like_count,
      cm.fav_count
    FROM cms_music_favourite cmf
        LEFT JOIN cms_music cm
          ON cmf.`copyright_id` = cm.`copyright_id`
        WHERE cmf.mobile = #{mobile}
</select>
</mapper>