<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.VrbtDiyRingMapper">
    <select id="queryRing" resultType="com.eleven.cms.entity.VrbtDiyRing">
        select r.*,v.audit_status as auditStatus,v.mv_name as ringName from cms_vrbt_diy_ring r
        left join cms_vrbt_diy_video v on r.vrbt_diy_video_id = v.id
        where r.mobile = #{mobile}
    </select>
</mapper>