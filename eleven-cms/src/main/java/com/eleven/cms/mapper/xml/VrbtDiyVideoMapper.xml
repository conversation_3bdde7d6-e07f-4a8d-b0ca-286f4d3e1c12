<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.VrbtDiyVideoMapper">


    <select id="querySettingRingStatus" resultType="java.lang.Integer">

        select             CASE
                               WHEN (report_status = 0 or audit_status = 0
                                   or distribute_status = 0 or setting_status = 0) THEN -1
                               WHEN setting_status = -1 THEN 1
                               WHEN setting_status = 1 THEN 2
                               ELSE 0
                               END AS ring_status from cms_vrbt_diy_video where id = #{id}
    </select>
</mapper>