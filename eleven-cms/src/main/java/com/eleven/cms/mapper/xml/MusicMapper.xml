<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.MusicMapper">

    <select id="findVrbtInfoByVrbtId" resultType="com.eleven.cms.vo.MusicVo">
        SELECT
        m.id as id,
        m.copyright_id as copyrightId,
        m.music_name as music<PERSON>ame,
        m.singer_name as singer<PERSON><PERSON>,
        m.vrbt_product_id as vrbtProductId,
        m.cp_id as cpId,
        m.dy_copyright_id as dyCopyrightId,
        m.dx_resource_code as dxResourceCode,
        m.dx_tone_code as dxToneCode,
        m.lt_ring_id as ltRingId,
        m.vrbt_img as vrbtImg,
        m.vrbt_video as vrbtVideo,
        m.hot_level as hotLevel,
        m.play_count as playCount
        FROM cms_music m
        WHERE m.vrbt_product_id = #{vrbtId}
        LIMIT 1
    </select>
    <select id="findVrbtInfoByCopyrightId" resultType="com.eleven.cms.vo.MusicVo">
        SELECT
            m.id as id,
            m.copyright_id as copyrightId,
            m.music_name as musicName,
            m.singer_name as singerName,
            m.vrbt_product_id as vrbtProductId,
            m.cp_id as cpId,
            m.dy_copyright_id as dyCopyrightId,
            m.dx_resource_code as dxResourceCode,
            m.dx_tone_code as dxToneCode,
            m.lt_ring_id as ltRingId,
            m.vrbt_img as vrbtImg,
            m.vrbt_video as vrbtVideo,
            m.hot_level as hotLevel,
            m.play_count as playCount
        FROM cms_music m
        WHERE m.copyright_id = #{copyrightId}
            LIMIT 1
    </select>
    <update id="playCountClick" parameterType="com.eleven.cms.entity.Music">
        UPDATE cms_music m
        SET m.play_count = IFNULL(m.play_count,0) + #{dto.playCount}
        WHERE m.id = #{dto.id}
    </update>
    <update id="likeCountClick" parameterType="com.eleven.cms.entity.Music">
       UPDATE cms_music m
       SET m.like_count = IFNULL(m.like_count,0) + #{dto.likeCount}
       WHERE m.id = #{dto.id}
    </update>
    <update id="favCountClick" parameterType="com.eleven.cms.entity.Music">
      UPDATE cms_music m
      SET m.fav_count = IFNULL(m.fav_count,0) + #{dto.favCount}
      WHERE m.id = #{dto.id}
    </update>
</mapper>