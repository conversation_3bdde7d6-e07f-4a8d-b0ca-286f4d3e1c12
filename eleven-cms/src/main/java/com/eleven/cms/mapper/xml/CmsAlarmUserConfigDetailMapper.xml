<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.CmsAlarmUserConfigDetailMapper">

    <select id="queryPageList" resultType="com.eleven.cms.entity.CmsAlarmUserConfigDetail">
        SELECT
            a.id,
            a.alarm_user_config_id,
            a.enable_sms,
            a.enable_vx,
            a.create_time,
            a.alarm_user_id,
            b.real_name
        FROM
            cms_alarm_user_config_detail a
        LEFT JOIN cms_alarm_user b ON a.alarm_user_id = b.id
        WHERE a.alarm_user_config_id = #{cmsAlarmUserConfigDetail.alarmUserConfigId}
        <if test="cmsAlarmUserConfigDetail.realName != null and cmsAlarmUserConfigDetail.realName != ''">
            AND b.real_name = #{cmsAlarmUserConfigDetail.realName}
        </if>
        <if test="cmsAlarmUserConfigDetail.enableSms != null and cmsAlarmUserConfigDetail.enableSms != ''">
            AND a.enable_sms = #{cmsAlarmUserConfigDetail.enableSms}
        </if>
        <if test="cmsAlarmUserConfigDetail.enableVx != null and cmsAlarmUserConfigDetail.enableVx != ''">
            AND a.enable_vx = #{cmsAlarmUserConfigDetail.enableVx}
        </if>
    </select>

    <select id="getAlarmUserList" resultType="com.eleven.cms.entity.CmsAlarmUserConfigDetailDto">
        SELECT
        b.phone as phone,
        b.open_id as openId,
        a.enable_sms as enableSms,
        a.enable_vx as enableVx
        FROM
        cms_alarm_user_config_detail a
        JOIN cms_alarm_user b ON a.alarm_user_id = b.id
        JOIN cms_alarm_user_config c ON a.alarm_user_config_id = c.id

        WHERE
        c.channel = #{channelCode}
        AND c.alarm_code = #{alarmSceneCode}

    </select>
</mapper>