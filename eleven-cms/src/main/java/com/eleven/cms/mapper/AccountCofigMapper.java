package com.eleven.cms.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AccountCofig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账号配置表 Mapper
 *
 * <AUTHOR>
 * @date 2021-06-02 16:57:09
 */
public interface AccountCofigMapper extends BaseMapper<AccountCofig> {

    IPage<AccountCofig> findByPage(Page<AccountCofig> page, AccountCofig dto);

    List<AccountCofig> findAccountCofigs(@Param("dto") AccountCofig dto);

}
