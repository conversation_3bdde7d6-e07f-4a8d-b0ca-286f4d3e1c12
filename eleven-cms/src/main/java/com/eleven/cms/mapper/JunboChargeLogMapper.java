package com.eleven.cms.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.dto.BusinessChargeDto;
import com.eleven.cms.dto.RightsDto;
import com.eleven.cms.dto.WoReadJunboChargeLogDto;
import com.eleven.cms.dto.WoReadQueryDto;
import com.eleven.cms.entity.JunboChargeLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 俊博直充记录
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@DS("master")
public interface JunboChargeLogMapper extends BaseMapper<JunboChargeLog> {

    List<BusinessChargeDto> findByPage(@Param("dto") BusinessChargeDto dto);

    List<RightsDto> getServiceList();

    List<RightsDto> findByServiceId(@Param("dto") RightsDto dto);



    List <WoReadJunboChargeLogDto> findByMobile(@Param("dto") WoReadQueryDto dto);
}
