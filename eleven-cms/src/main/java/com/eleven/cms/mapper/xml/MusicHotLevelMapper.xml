<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.MusicHotLevelMapper">
    <select id="selectMusicHotLevelPage" resultType="com.eleven.cms.vo.MusicVo">
        SELECT
            count(0) as c,
            m.id as id,
            m.copyright_id as copyrightId,
            m.music_name as music<PERSON>ame,
            m.singer_name as singer<PERSON><PERSON>,
            m.vrbt_product_id as vrbtProductId,
            m.cp_id as cpId,
            m.dy_copyright_id as dyCopyrightId,
            m.dx_resource_code as dxResourceCode,
            m.dx_tone_code as dxToneCode,
            m.lt_ring_id as ltRingId,
            m.vrbt_img as vrbtImg,
            m.vrbt_video as vrbtVideo,
            m.hot_level as hotLevel,
            m.play_count as playCount,
            m.like_count as likeCount,
            m.fav_count as favCount,
            m.expiry_date as expiryDate
        FROM cms_music_hot_level cm
        JOIN cms_music m
        ON cm.copyright_id = m.copyright_id
        AND m.status = 1
        group by cm.copyright_id
        ORDER BY c desc
    </select>
</mapper>
