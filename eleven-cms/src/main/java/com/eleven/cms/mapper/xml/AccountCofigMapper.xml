<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.AccountCofigMapper">

    <select id="findByPage" resultType="com.eleven.cms.entity.AccountCofig">
        SELECT
            ac.id,
            ac.ad_platform_id,
            ac.account,
            ac.config,
            ac.remark,
            ac.spare_field,
            ac.create_by,
            ac.create_time,
            ac.update_by,
            ac.update_time,
            ap.ad_platform
        FROM cms_account_cofig ac
        LEFT JOIN  ad_platform ap
        ON ac.ad_platform_id = ap.id
        WHERE 1=1
        <if test="dto != null">
        <if test="dto.id != null and dto.id != ''">
            and ac.id = #{dto.id}
        </if>
        <if test="dto.adPlatformId != null and dto.adPlatformId != ''">
            and ac.ad_platform_id = #{dto.adPlatformId}
        </if>
        </if>
    </select>
    <select id="findAccountCofigs" resultType="com.eleven.cms.entity.AccountCofig" parameterType="com.eleven.cms.entity.AccountCofig">
        SELECT
        ac.id,
        ac.ad_platform_id,
        ac.account,
        ac.config,
        ac.remark,
        ac.spare_field,
        ac.create_by,
        ac.create_time,
        ac.update_by,
        ac.update_time,
        ap.ad_platform
        FROM cms_account_cofig ac
        LEFT JOIN  cms_ad_platform ap
        ON ac.ad_platform_id = ap.id
        WHERE 1=1
        <if test="dto != null">
            <if test="dto.id != null and dto.id != ''">
                and ac.id = #{dto.id}
            </if>
            <if test="dto.adPlatformId != null and dto.adPlatformId != ''">
                and ac.ad_platform_id = #{dto.adPlatformId}
            </if>
        </if>
    </select>
</mapper>
