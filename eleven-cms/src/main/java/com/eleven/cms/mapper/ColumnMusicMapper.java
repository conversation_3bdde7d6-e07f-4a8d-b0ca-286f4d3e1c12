package com.eleven.cms.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.ColumnMusic;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.entity.ColumnMusicVo;
import com.eleven.cms.vo.MusicVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 栏目歌曲
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
public interface ColumnMusicMapper extends BaseMapper<ColumnMusic> {

	public boolean deleteByMainId(@Param("mainId") String mainId);

	public List<ColumnMusic> selectByMainId(@Param("mainId") String mainId);

    IPage<MusicVo> selectColumnMusicVoPage(Page<MusicVo> page, @Param("columnId") String columnId);

	List<ColumnMusicVo> selectByMainIdNew(@Param("mainId") String mainId);

	IPage<MusicVo> selectHomeColumnMusicVoPage(Page<MusicVo> page, @Param("columnId") String columnId, @Param("tag") String tag);

    MusicVo selectRandomMusicByColumn();

}
