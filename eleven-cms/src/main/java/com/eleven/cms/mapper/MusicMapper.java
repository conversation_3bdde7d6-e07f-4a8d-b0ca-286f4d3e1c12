package com.eleven.cms.mapper;

import java.util.List;

import com.eleven.cms.vo.MusicVo;
import org.apache.ibatis.annotations.Param;
import com.eleven.cms.entity.Music;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 歌曲
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
public interface MusicMapper extends BaseMapper<Music> {

    MusicVo findVrbtInfoByVrbtId(String vrbtId);

    MusicVo findVrbtInfoByCopyrightId(String copyrightId);

    void playCountClick(@Param("dto") Music music);

    void likeCountClick(@Param("dto") Music music);

    void favCountClick(@Param("dto") Music music);
}
