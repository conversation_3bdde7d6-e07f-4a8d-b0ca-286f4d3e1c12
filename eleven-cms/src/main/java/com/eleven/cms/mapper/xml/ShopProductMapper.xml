<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.ShopProductMapper">
    <resultMap type="com.eleven.cms.entity.ShopProduct" id="AllMap">
        <result property="id" column="id"/>
        <collection property="shopProductRightsList" ofType="com.eleven.cms.entity.ShopProductRights"
                    select="queryShopProductRightsList"  column="id"><!-- 主表colum的id 传递给子表查询-->
        </collection>
    </resultMap>

    <select id="queryShopProductList"  resultMap="AllMap">
        SELECT
            id as id,
            product_name,
            product_img
        FROM cms_shop_product
        WHERE is_online=1
        <if test="productClass !=null and productClass != ''">
            and product_class = #{productClass}
        </if>
        ORDER BY sort_order asc
    </select>


    <select id="queryShopProductRightsList" resultType="com.eleven.cms.entity.ShopProductRights">
        SELECT
            rights_name,
            rights_id,
            product_price,
            original_price,
            remark
        FROM cms_shop_product_rights WHERE  is_online=1 and shop_id = #{shopId}
        ORDER BY sort_order asc
    </select>

</mapper>
