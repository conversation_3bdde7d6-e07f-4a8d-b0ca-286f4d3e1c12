package com.eleven.cms.mapper;

import java.util.List;
import com.eleven.cms.entity.OutsideBusinessConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: cms_outside_business_config
 * @Author: jeecg-boot
 * @Date:   2023-10-10
 * @Version: V1.0
 */
public interface OutsideBusinessConfigMapper extends BaseMapper<OutsideBusinessConfig> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<OutsideBusinessConfig> selectByMainId(@Param("mainId") String mainId);
}
