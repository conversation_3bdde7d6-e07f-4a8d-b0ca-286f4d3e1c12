<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.VisitLogMapper">
    <select id="findByPage" resultType="com.eleven.cms.entity.VisitLog">
        SELECT
          DATE(create_time) AS create_date,
          sub_channel,page,extra, COUNT(DISTINCT finger) AS uv,
          COUNT(*) AS pv
        FROM cms_visit_log vl WHERE 1=1
        <if test="createTimeBegin != null">
            and vl.create_time &gt;= #{createTimeBegin}
        </if>
        <if test="createTimeEnd != null">
            and vl.create_time &lt;= #{createTimeEnd}
        </if>
        <if test="dto != null">
            <if test="dto.page != null and dto.page != ''">
                and vl.page = #{dto.page}
            </if>
            <if test="dto.extra != null and dto.extra != ''">
                and vl.extra = #{dto.extra}
            </if>
        </if>

    </select>
</mapper>
