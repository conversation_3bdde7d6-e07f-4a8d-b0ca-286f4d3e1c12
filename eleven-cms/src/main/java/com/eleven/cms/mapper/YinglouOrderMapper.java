package com.eleven.cms.mapper;

import java.util.List;

import com.eleven.cms.entity.RightsPack;
import com.eleven.cms.vo.RightsPackDto;
import com.eleven.cms.vo.YinglouOrderDto;
import org.apache.ibatis.annotations.Param;
import com.eleven.cms.entity.YinglouOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 影楼订单表
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
public interface YinglouOrderMapper extends BaseMapper<YinglouOrder> {


    List<YinglouOrderDto> selectOrderListByUser(@Param("order") YinglouOrderDto order);


    Integer selectCountByUser(@Param("order") YinglouOrderDto order);

    Integer selectMajorUserOrderTotalByUser(@Param("order") YinglouOrderDto order);

}
