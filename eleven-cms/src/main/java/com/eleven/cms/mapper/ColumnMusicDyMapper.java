package com.eleven.cms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.ColumnMusicDy;
import com.eleven.cms.entity.ColumnMusicDyVo;
import com.eleven.cms.vo.MusicDyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 订阅包栏目歌曲
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
public interface ColumnMusicDyMapper extends BaseMapper<ColumnMusicDy> {

	public boolean deleteByMainId(@Param("mainId") String mainId);

	public List<ColumnMusicDy> selectByMainId(@Param("mainId") String mainId);


	IPage<MusicDyVo> selectColumnMusicDyVoPage(Page<MusicDyVo> page, @Param("columnId") String columnId);

    List<ColumnMusicDyVo> selectByMainIdNew(@Param("mainId") String mainId);
}
