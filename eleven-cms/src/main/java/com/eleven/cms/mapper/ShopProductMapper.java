package com.eleven.cms.mapper;

import java.util.List;

import com.eleven.cms.entity.RightsPack;
import org.apache.ibatis.annotations.Param;
import com.eleven.cms.entity.ShopProduct;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 商城产品列表
 * @Author: jeecg-boot
 * @Date:   2023-09-19
 * @Version: V1.0
 */
public interface ShopProductMapper extends BaseMapper<ShopProduct> {

  List<ShopProduct>  queryShopProductList(@Param("productClass") String productClass);

}
