package com.eleven.cms.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.eleven.cms.es.entity.EsSubStatistics;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 落地页业务配置 Mapper
 *
 * <AUTHOR>
 * @date 2023-08-08 14:42:58
 */
@DS("member")
public interface MemberCommonMapper {
    //@Select("<script> " +
    //        " SELECT channel, biz_type as bizType, COUNT(*) AS total, COUNT(IF(price=1,1,NULL)) AS succ FROM t_biz_subscribe " +
    //        " WHERE create_time BETWEEN #{startDay} AND #{endDay} " +
    //        " AND STATUS = 1  " +
    //        " AND isp = '1' " +
    //        " GROUP BY channel " +
    //        " </script>")
    //List<SubscribeStatistics> statisticsAllChannel(@Param("startDay") LocalDateTime startDay, @Param("endDay")LocalDateTime endDay);

    @Select("<script> " +
            " SELECT channel, sub_channel as subChannel, province, COUNT(*) AS total, COUNT(IF(price=1,1,NULL)) AS succ FROM t_biz_subscribe " +
            " WHERE create_time BETWEEN #{startDay} AND #{endDay} " +
            " AND channel = #{channelCode} " +
            " AND STATUS = 1  " +
            " AND isp = '1' " +
            " GROUP BY sub_channel,province " +
            " </script>")
    List<EsSubStatistics> statisticsByChannel(@Param("startDay") LocalDateTime startDay, @Param("endDay")LocalDateTime endDay, @Param("channelCode")String channelCode);
}
