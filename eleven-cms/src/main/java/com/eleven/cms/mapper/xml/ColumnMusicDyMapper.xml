<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.ColumnMusicDyMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE
		FROM  cms_column_music_dy
		WHERE
			 column_id = #{mainId} 	</delete>

	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.eleven.cms.entity.ColumnMusicDy">
		SELECT *
		FROM  cms_column_music_dy
		WHERE
			 column_id = #{mainId} 	</select>


	<select id="selectColumnMusicDyVoPage" resultType="com.eleven.cms.vo.MusicDyVo">
		SELECT
			m.id as id,
			m.copyright_id as copyrightId,
			m.music_name as music<PERSON>ame,
			m.singer_name as singer<PERSON><PERSON>,
			m.vrbt_product_id as vrbtProductId,
			m.cp_id as cpId,
			m.dx_resource_code as dxResourceCode,
			m.dx_tone_code as dxToneCode,
			m.lt_ring_id as ltRingId,
			m.vrbt_img as vrbtImg,
			m.vrbt_video as vrbtVideo,
			m.hot_level as hotLevel,
			m.play_count as playCount,
			m.like_count as likeCount,
			m.fav_count as favCount,
			m.expiry_date as expiryDate
		FROM cms_column_music_dy cm
				 JOIN  cms_music_dy m
					   ON cm.music_id = m.id
		WHERE cm.column_id = #{columnId}
		  AND m.status = 1
		ORDER BY cm.priority
	</select>


	<select id="selectByMainIdNew" parameterType="java.lang.String" resultType="com.eleven.cms.entity.ColumnMusicDyVo">
		SELECT ccm.*,cm.copyright_id
		FROM  cms_column_music_dy ccm
				  LEFT JOIN cms_music_dy cm
							ON ccm.music_id = cm.id
		WHERE
			ccm.column_id = #{mainId}
		order by priority
	</select>
</mapper>
