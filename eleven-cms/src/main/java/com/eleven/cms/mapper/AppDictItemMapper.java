package com.eleven.cms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.entity.AppDictItem;
import com.eleven.cms.vo.AppDictItemVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
public interface AppDictItemMapper extends BaseMapper<AppDictItem> {
    @Select("SELECT * FROM sys_dict_item WHERE DICT_ID = #{mainId} order by sort_order asc, item_value asc")
    List<AppDictItem> selectItemsByMainId(String mainId);


    @Select(value = " select s.item_value as \"value\",s.item_text as \"text\",s.description,s.sort_order as sortOrder,s.status from sys_dict_item s\n" +
            "        where dict_id = (select id from sys_dict where dict_code = #{code})\n" +
            "        order by s.sort_order asc")
    List<AppDictItemVO> getDictItemByCode(@Param("code") String code);
}
