package com.eleven.cms.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AccountCofig;
import org.apache.ibatis.annotations.Param;
import com.eleven.cms.entity.VisitLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 页面访问日志
 * @Author: jeecg-boot
 * @Date:   2020-10-26
 * @Version: V1.0
 */
public interface VisitLogMapper extends BaseMapper<VisitLog> {
    IPage<VisitLog> findByPage(Page<VisitLog> page, VisitLog dto,@Param("createTimeBegin") Date createTimeBegin, @Param("createTimeEnd") Date createTimeEnd);
}
