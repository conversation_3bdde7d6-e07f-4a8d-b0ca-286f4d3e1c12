package com.eleven.cms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.RightsPack;
import com.eleven.cms.vo.RightsPackDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 会员权益业务关联
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
public interface RightsPackMapper extends BaseMapper<RightsPack> {

	public boolean deleteByMainId(@Param("mainId") String mainId);

	public List<RightsPack> selectByMainId(@Param("mainId") String mainId);

	List<RightsPack> selectRightsPackList(@Param("packId") String packId);

	IPage<RightsPack> selectRightsPackPage(Page<RightsPack> page, @Param("rightsPack") RightsPack rightsPack);

	List<RightsPackDto> wholeMemberRightsList(String serviceId);

	List<RightsPackDto> packList(@Param("ids")List<String> ids);



	List<RightsPackDto> aliPayRightsList(@Param("serviceId")List<String> serviceId);


	List<RightsPackDto> getMiguPackList(String packName);

	List<RightsPackDto> queryRightsList(@Param("serviceId")String serviceId,@Param("packId")List<String> packId);

	List<RightsPackDto> queryWebRightsList(@Param("serviceId")String serviceId,@Param("packId")List<String> packId);

	List<RightsPackDto> queryRightsListById(String id);
}
