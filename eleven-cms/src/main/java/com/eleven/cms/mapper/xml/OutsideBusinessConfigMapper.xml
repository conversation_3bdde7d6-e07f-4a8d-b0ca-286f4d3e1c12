<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.OutsideBusinessConfigMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  cms_outside_business_config 
		WHERE
			 outside_config_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.eleven.cms.entity.OutsideBusinessConfig">
		SELECT * 
		FROM  cms_outside_business_config
		WHERE
			 outside_config_id = #{mainId} 	</select>
</mapper>
