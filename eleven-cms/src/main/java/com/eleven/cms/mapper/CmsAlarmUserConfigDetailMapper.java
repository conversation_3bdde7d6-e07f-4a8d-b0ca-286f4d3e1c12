package com.eleven.cms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eleven.cms.entity.CmsAlarmUserConfigDetail;
import com.eleven.cms.entity.CmsAlarmUserConfigDetailDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: cms_alarm_user_config_detail
 * @Author: jeecg-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
public interface CmsAlarmUserConfigDetailMapper extends BaseMapper<CmsAlarmUserConfigDetail> {

    /**
     * 查询分页列表
     * @param page page
     * @param cmsAlarmUserConfigDetail cmsAlarmUserConfigDetail
     * @return IPage<CmsAlarmUserConfigDetail>
     */
    IPage<CmsAlarmUserConfigDetail> queryPageList(IPage<CmsAlarmUserConfigDetail> page, CmsAlarmUserConfigDetail cmsAlarmUserConfigDetail);

    List<CmsAlarmUserConfigDetailDto> getAlarmUserList(@Param("channelCode") String channelCode, @Param("alarmSceneCode") String alarmSceneCode);
}
