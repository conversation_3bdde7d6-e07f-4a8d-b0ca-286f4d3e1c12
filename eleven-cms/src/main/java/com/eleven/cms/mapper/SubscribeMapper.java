package com.eleven.cms.mapper;

import java.time.LocalDateTime;
import java.util.List;

import com.eleven.cms.dto.BusinessOrderLog;
import com.eleven.cms.es.entity.EsSubStatistics;
import org.apache.ibatis.annotations.Param;
import com.eleven.cms.entity.Subscribe;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
 * @Description: 渠道订阅
 * @Author: jeecg-boot
 * @Date:   2020-09-23
 * @Version: V1.0
 */
public interface SubscribeMapper extends BaseMapper<Subscribe> {
    List<BusinessOrderLog> getBusinessOrderLogList(@Param("businessOrderLog") BusinessOrderLog businessOrderLog);

    Integer getBusinessOrderLogCount(@Param("businessOrderLog") BusinessOrderLog businessOrderLog);

    @Select("<script> " +
            " SELECT channel, biz_type as bizType, COUNT(*) AS total, COUNT(IF(price=1,1,NULL)) AS succ FROM cms_subscribe " +
            " WHERE create_time BETWEEN #{startDay} AND #{endDay} " +
            " AND STATUS = 1  " +
            " AND isp = '1' " +
            " GROUP BY channel " +
            " </script>")
    List<EsSubStatistics> statisticsAllChannel(@Param("startDay")LocalDateTime startDay, @Param("endDay")LocalDateTime endDay);

    @Select("<script> " +
            " SELECT channel, sub_channel as subChannel, province, COUNT(*) AS total, COUNT(IF(price=1,1,NULL)) AS succ FROM cms_subscribe " +
            " WHERE create_time BETWEEN #{startDay} AND #{endDay} " +
            " AND channel = #{channelCode} " +
            " AND STATUS = 1  " +
            " AND isp = '1' " +
            " GROUP BY sub_channel,province " +
            " </script>")
    List<EsSubStatistics> statisticsByChannel(@Param("startDay")LocalDateTime startDay, @Param("endDay")LocalDateTime endDay, @Param("channelCode")String channelCode);

    List<Subscribe> querySubscribeList();

}
