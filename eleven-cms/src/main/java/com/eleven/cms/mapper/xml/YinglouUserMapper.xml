<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.YinglouUserMapper">
    <select id="findMajorUserByLevels" resultType="com.eleven.cms.dto.YinglouUserDto">
        SELECT user_name AS optionKey,user_alias AS optionValue
        FROM cms_yinglou_user
        where levels in(0,1) ORDER BY user_alias
    </select>
</mapper>
