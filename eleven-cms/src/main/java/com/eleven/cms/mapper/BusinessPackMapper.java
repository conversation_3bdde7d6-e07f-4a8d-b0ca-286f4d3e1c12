package com.eleven.cms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.entity.BusinessPack;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 业务渠道权益关联
 * @Author: jeecg-boot
 * @Date:   2022-12-19
 * @Version: V1.0
 */
public interface BusinessPackMapper extends BaseMapper<BusinessPack> {
    List<BusinessPack> findByBusinessId(@Param("businessId")List<String> businessId);


    List<BusinessPack> findByServiceId(String serviceId);
}
