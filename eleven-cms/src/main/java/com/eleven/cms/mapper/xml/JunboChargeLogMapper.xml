<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.JunboChargeLogMapper">

    <select id="findByPage" resultType="com.eleven.cms.dto.BusinessChargeDto">
        SELECT cjcl.province,cjcl.city,cjcl.service_id AS serviceId,cmp.title_name AS serviceName,COUNT(0) AS sumCount,cjcl.remark,cjcl.coupon_name AS couponName,cjcl.coupon_price AS couponPrice,SUM(cjcl.coupon_price) AS sumMoney
        FROM cms_junbo_charge_log cjcl left join cms_migu_pack cmp ON cmp.service_id=cjcl.service_id
        WHERE 1=1
        <if test="dto.remark != null and dto.remark != ''">
            and cjcl.remark = #{dto.remark}
        </if>
        <if test="dto.serviceId != null and dto.serviceId != ''">
            and cjcl.service_id = #{dto.serviceId}
        </if>
        <if test="dto.couponId != null and dto.couponId != ''">
            and cjcl.coupon_id = #{dto.couponId}
        </if>
        <if test="dto.status != null and dto.status != ''">
            and cjcl.status = #{dto.status}
        </if>
        <if test="dto.province != null and dto.province != ''">
            and cjcl.province = #{dto.province}
        </if>
        <if test="dto.city != null and dto.city != ''">
            and cjcl.city = #{dto.city}
        </if>
        <if test="dto.createTimeBegin != null">
            and cjcl.create_time &gt;= #{dto.createTimeBegin}
        </if>
        <if test="dto.createTimeEnd != null">
            and cjcl.create_time &lt;= #{dto.createTimeEnd}
        </if>
        group by cjcl.service_id,cjcl.coupon_id ORDER BY cmp.title_name
    </select>


    <select id="findByServiceId" resultType="com.eleven.cms.dto.RightsDto">
        SELECT cjcl.coupon_id as optionKey,cjcl.coupon_name as optionValue
        FROM cms_member_rights cmr inner join cms_junbo_charge_log cjcl ON cjcl.coupon_id=cmr.coupon_id
        WHERE 1=1
        <if test="dto.serviceId != null and dto.serviceId != ''">
            and cjcl.service_id = #{dto.serviceId}
        </if>
        group by cjcl.coupon_id order by cjcl.coupon_name
    </select>


    <select id="getServiceList" resultType="com.eleven.cms.dto.RightsDto">
        SELECT cmp.service_id AS optionKey,cmp.title_name AS optionValue
        FROM cms_migu_pack cmp inner join cms_junbo_charge_log cjcl ON cjcl.service_id=cmp.service_id
        GROUP BY cjcl.service_id ORDER BY cmp.title_name
    </select>



    <select id="findByMobile" resultType="com.eleven.cms.dto.WoReadJunboChargeLogDto">
        SELECT mobile as useraccount,coupon_name as rightName,status as status,create_time as createtimes
        FROM cms_junbo_charge_log
        WHERE status=1
        <if test="dto.mobile != null and dto.mobile != ''">
            and mobile = #{dto.mobile}
        </if>
        <if test="dto.date != null and dto.date != ''">
            and create_time &gt;= #{dto.date}
        </if>
        order by create_time asc
    </select>


</mapper>
