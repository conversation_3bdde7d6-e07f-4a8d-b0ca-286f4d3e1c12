<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.YinglouOrderMapper">

    <select id="selectOrderListByUser" resultType="com.eleven.cms.vo.YinglouOrderDto">
        SELECT pay_order_time AS payOrderTime,
        COUNT(0) AS totalPayOrder FROM `cms_yinglou_order` WHERE pay_status=1
        <if test="order.payTime !=null and order.payTime != '' ">
            AND pay_time BETWEEN #{order.payTimeBegin} AND #{order.payTimeEnd}
        </if>
        and pay_user_name = #{order.payUserName}
        GROUP BY pay_order_time
        ORDER BY pay_order_time asc
    </select>


    <select id="selectCountByUser" resultType="java.lang.Integer">
        SELECT COUNT(0) AS totalPayOrder FROM `cms_yinglou_order` WHERE pay_status=1
        <if test="order.payTime !=null and order.payTime != '' ">
            AND pay_time BETWEEN #{order.payTimeBegin} AND #{order.payTimeEnd}
        </if>
        and pay_user_name = #{order.payUserName}
    </select>

    <select id="selectMajorUserOrderTotalByUser" resultType="java.lang.Integer">
        SELECT COUNT(0) AS totalPayOrder FROM `cms_yinglou_order` WHERE pay_status=1
        <if test="order.payTime !=null and order.payTime != '' ">
            AND pay_time BETWEEN #{order.payTimeBegin} AND #{order.payTimeEnd}
        </if>
        and major_user = #{order.payUserName}
    </select>

</mapper>
