<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.VrbtAppUserMapper">
    <update id="updateVrbtAppUser" parameterType="com.eleven.cms.entity.VrbtAppUser">
        UPDATE cms_vrbt_app_user
        SET
        <if test="user.subStatus != null">
            sub_status =#{user.subStatus},
        </if>
        <if test="user.signInDay != null">
            sign_in_day =#{user.signInDay},
        </if>
        <if test="user.totalPoints != null">
            total_points =#{user.totalPoints},
        </if>
            version = version + 1,
            modify_time =now()
        WHERE id = #{user.id} AND version =#{user.version}
    </update>
</mapper>

