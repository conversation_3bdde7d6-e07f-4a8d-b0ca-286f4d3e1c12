<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.SubscribeMapper">
    <select id="getBusinessOrderLogList" parameterType="com.eleven.cms.dto.BusinessOrderLog" resultType="com.eleven.cms.dto.BusinessOrderLog">
        SELECT DISTINCT co.mobile 'mobile',co.sub_channel 'subChannel' FROM cms_subscribe co
        WHERE co.create_time &gt;= #{businessOrderLog.createTimeFrom} and co.create_time &lt;= #{businessOrderLog.createTimeTo}
        And  co.channel = #{businessOrderLog.channel}
        And  co.isp = #{businessOrderLog.isp}
        AND co.status = '1'order by co.create_time asc;
    </select>

    <select id="getBusinessOrderLogCount" parameterType="com.eleven.cms.dto.BusinessOrderLog" resultType="java.lang.Integer">
        SELECT count(0) FROM cms_subscribe co
        WHERE co.create_time &gt;= #{businessOrderLog.createTimeFrom} and co.create_time &lt;= #{businessOrderLog.createTimeTo}
          And  co.channel = #{businessOrderLog.channel}
          And  co.isp = #{businessOrderLog.isp}
          And  co.isp = #{businessOrderLog.mobile}
          AND co.status = '1';
    </select>


    <select id="querySubscribeList"  resultType="com.eleven.cms.entity.Subscribe">
       SELECT * FROM cms_subscribe WHERE create_time
       BETWEEN '2024-01-21 00:00:00' AND '2024-01-21 23:59:59' AND channel = '014X04C' AND province = '山东' AND STATUS = 1 AND isp = '1';
    </select>
</mapper>
