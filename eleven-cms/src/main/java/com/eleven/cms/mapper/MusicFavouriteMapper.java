package com.eleven.cms.mapper;

import java.util.List;

import com.eleven.cms.entity.Music;
import com.eleven.cms.vo.MusicFavouriteVo;
import org.apache.ibatis.annotations.Param;
import com.eleven.cms.entity.MusicFavourite;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: cms_music_favourite
 * @Author: jeecg-boot
 * @Date:   2022-03-01
 * @Version: V1.0
 */
public interface MusicFavouriteMapper extends BaseMapper<MusicFavourite> {

    List<String> matching(String mobile);

    List<MusicFavouriteVo> findByMobile(String mobile);
}
