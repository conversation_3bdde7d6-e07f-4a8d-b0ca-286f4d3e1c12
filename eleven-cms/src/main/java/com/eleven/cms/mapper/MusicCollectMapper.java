package com.eleven.cms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.MusicCollect;
import com.eleven.cms.vo.MusicVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 歌曲收藏
 * @Author: jeecg-boot
 * @Date:   2023-11-21
 * @Version: V1.0
 */
public interface MusicCollectMapper extends BaseMapper<MusicCollect> {

    IPage<MusicVo> selectMusicCollectPage(Page<MusicVo> page, @Param("mobile") String mobile);


}
