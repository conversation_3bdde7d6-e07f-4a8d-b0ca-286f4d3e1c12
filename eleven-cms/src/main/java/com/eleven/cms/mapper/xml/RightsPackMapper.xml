<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.RightsPackMapper">
	<resultMap type="com.eleven.cms.vo.RightsPackDto" id="AllMap">
		<result property="id" column="id"/>
		<collection property="rightsPackList" ofType="com.eleven.cms.vo.RightsPackList"
					select="getRightsPackList"  column="id"><!-- 主表colum的id 传递给子表查询-->
		</collection>
	</resultMap>
	<resultMap type="com.eleven.cms.vo.RightsPackDto" id="AliPayAllMap">
		<result property="id" column="id"/>
		<collection property="rightsPackList" ofType="com.eleven.cms.vo.RightsPackList"
					select="getAlipayRightsPackList"  column="id"><!-- 主表colum的id 传递给子表查询-->
		</collection>
	</resultMap>
	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE FROM cms_rights_pack WHERE pack_id = #{mainId}
	</delete>

	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.eleven.cms.entity.RightsPack">
		SELECT * FROM  cms_rights_pack WHERE pack_id = #{mainId} ORDER BY id
	</select>


	<select id="selectRightsPackList" resultType="com.eleven.cms.entity.RightsPack">
		SELECT
			cr.pack_id as packId,
			cr.member_id as memberId,
			cm.rights_name as rightsName,
			cm.rights_id as rightsId,
			cm.remark as remark,
			cm.product_price as productPrice,
			cm.company_owner as companyOwner
		FROM cms_rights_pack cr inner JOIN  cms_member_rights cm
		ON cr.member_id =cm.id
		WHERE cr.pack_id = #{packId} and cm.rights_switchs=1
		ORDER BY cr.priority
	</select>

	<select id="selectRightsPackPage" resultType="com.eleven.cms.entity.RightsPack">
		SELECT
		    cr.pack_id as packId,
			cm.rights_name as rightsName,
			cm.coupon_id as couponId,
			cm.remark as remark,
			cm.product_price as productPrice,
			cm.company_owner as companyOwner
		FROM cms_rights_pack cr inner JOIN  cms_member_rights cm
		ON cr.member_id =cm.id
		WHERE cr.pack_id = #{rightsPack.packId} and cm.rights_switchs=1
		<if test="rightsPack.rightsName !=null and rightsPack.rightsName != ''">
			and cm.rights_name LIKE concat(concat('%',#{rightsPack.rightsName}),'%')
		</if>
		ORDER BY cr.priority
	</select>

	<select id="wholeMemberRightsList" parameterType="java.lang.String" resultMap="AllMap">
		SELECT
		id as id,
		service_Id as  serviceId,
		title_Name as titleName,
		pack_Name as packName,
		remark as remark,
		provide_person_name as providePersonName,
		product_img as productImg
		FROM cms_migu_pack
        WHERE service_id = #{serviceId}
		ORDER BY id asc
	</select>


	<select id="packList"  resultMap="AllMap">
		SELECT
			id as id,
			service_Id as  serviceId,
			title_Name as titleName,
			pack_Name as packName,
			remark as remark,
			provide_person_name as providePersonName,
			product_img as productImg
		FROM cms_migu_pack
		WHERE id in
		<foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
		ORDER BY id asc
	</select>


	<select id="getRightsPackList" resultType="com.eleven.cms.vo.RightsPackList">
		SELECT
			cm.rights_name as rightsName,
			cm.rights_id as rightsId,
			cm.coupon_id as couponId,
			cm.remark as remark,
			cm.product_price as productPrice,
			is_account as isAccount,
			cm.original_price as originalPrice,
			cm.rights_switchs as rightsSwitchs,
			recharge_state as rechargeState,
			cm.company_owner as companyOwner
		FROM cms_rights_pack cr inner JOIN  cms_member_rights cm
		ON cr.member_id =cm.id
		WHERE cr.pack_id = #{packId} and cm.rights_switchs=1
		ORDER BY cr.priority
	</select>

	<select id="getMiguPackList" parameterType="java.lang.String" resultType="com.eleven.cms.vo.RightsPackDto">
		SELECT
			id as id,
			title_Name as titleName,
			pack_Name as packName,
			remark as remark,
			product_img as productImg
		FROM cms_migu_pack
		WHERE pack_Name = #{packName}
		ORDER BY id asc
	</select>



	<select id="aliPayRightsList" resultMap="AliPayAllMap">
		SELECT
			id as id,
			service_Id as  serviceId,
			pack_Name as packName
		FROM cms_migu_pack
		WHERE service_id in
		<foreach collection="serviceId" index="index" item="serviceId" open="(" separator="," close=")">
			#{serviceId}
		</foreach>
		ORDER BY id asc
	</select>

	<select id="getAlipayRightsPackList" resultType="com.eleven.cms.vo.RightsPackList">
		SELECT
			cm.rights_name as rightsName,
			cm.rights_id as rightsId,
			cm.remark as remark,
			cm.product_price as productPrice,
			is_account as isAccount,
			cm.company_owner as companyOwner
		FROM cms_rights_pack cr inner JOIN  cms_member_rights cm
											ON cr.member_id =cm.id
		WHERE cr.pack_id = #{packId} and cm.rights_switchs=1
		ORDER BY cr.priority
	</select>



<!--	<select id="queryRightsList"  resultMap="AllMap">
		SELECT
			id as id,
			service_Id as  serviceId,
			title_Name as titleName,
			pack_Name as packName,
			remark as remark,
			provide_person_name as providePersonName,
			product_img as productImg
		FROM cms_migu_pack
		WHERE service_id = #{serviceId} and is_valid=1
		ORDER BY id asc
	</select>-->


	<!--<select id="queryWebRightsList" resultMap="AllMap">
		SELECT
			id as id,
			service_Id as  serviceId,
			title_Name as titleName,
			pack_Name as packName,
			remark as remark,
			provide_person_name as providePersonName,
			product_img as productImg
		FROM cms_migu_pack
		WHERE service_id = #{serviceId} and is_display=1
		ORDER BY id asc
	</select>-->


	<select id="queryRightsListById"  resultMap="AllMap">
		SELECT
			id as id,
			service_Id as  serviceId,
			title_Name as titleName,
			pack_Name as packName,
			remark as remark,
			provide_person_name as providePersonName,
			product_img as productImg
		FROM cms_migu_pack
		WHERE id = #{id}
		ORDER BY id asc
	</select>



	<select id="queryWebRightsList" resultMap="AllMap">
		SELECT
			id as id,
			service_Id as  serviceId,
			title_Name as titleName,
			pack_Name as packName,
			remark as remark,
			provide_person_name as providePersonName,
			product_img as productImg
		FROM cms_migu_pack
		WHERE service_id = #{serviceId} and id in
		<foreach collection="packId" index="index" item="packId" open="(" separator="," close=")">
			#{packId}
		</foreach>
		and is_display=1
		ORDER BY id asc
	</select>


	<select id="queryRightsList"  resultMap="AllMap">
		SELECT
			id as id,
			service_Id as  serviceId,
			title_Name as titleName,
			pack_Name as packName,
			remark as remark,
			provide_person_name as providePersonName,
			product_img as productImg
		FROM cms_migu_pack
		WHERE service_id = #{serviceId}
		and id in
		<foreach collection="packId" index="index" item="packId" open="(" separator="," close=")">
			#{packId}
		</foreach>
		and is_valid=1
		ORDER BY id asc
	</select>

</mapper>
