<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.CouponCodeMapper">
    <update id="updateBatch" parameterType="java.util.List">
        UPDATE cms_coupon_code
        SET
        status = 5,
        remark ='手动设置已过期',
        update_time =now()
        WHERE coupon_code in
        <foreach collection="codes" index="index" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and status!=5
    </update>
</mapper>
