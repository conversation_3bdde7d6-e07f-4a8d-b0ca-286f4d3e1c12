<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.ColumnMapper">

    <select id="getMusicListById" parameterType="java.lang.String" resultType="com.eleven.cms.entity.Music">
        SELECT m.* FROM
        cms_column_music cm
        JOIN  cms_music m
        ON cm.music_id = m.id
        WHERE cm.column_id = #{columnId}
        ORDER BY cm.priority
    </select>
    <select id="findByPage" resultType="com.eleven.cms.entity.Column" parameterType="com.eleven.cms.entity.Column">
        SELECT
            t.id,
            t.title,
            t.priority,
            t.status,
            t.channel,
            t.column_class_name,
            t.is_carousel,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.sys_org_code,
            t.img_url,
            t.video_url,
            t.backdrop_color,
            t.text_introduction,
            t.text_color
        FROM  cms_column t
       where 1=1
        <if test="dto != null">
            <if test="dto.id != null and dto.id != ''">
                and id = #{dto.id}
            </if>
            <if test="dto.title != null and dto.title != ''">
                and title = #{dto.title}
            </if>
            <if test="dto.isCarousel != null and dto.isCarousel != ''">
                and is_carousel = #{dto.isCarousel}
            </if>
            <if test="dto.columnClassName != null and dto.columnClassName != ''">
                and column_class_name = #{dto.columnClassName}
            </if>
        </if>
        <if test="createTimeBegin != null">
            and create_time &gt;= #{createTimeBegin}
        </if>
        <if test="createTimeEnd != null">
            and create_time &lt;= #{createTimeEnd}
        </if>
        ORDER BY column_class_name,create_time DESC
    </select>
</mapper>
