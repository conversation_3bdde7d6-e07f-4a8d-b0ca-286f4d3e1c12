<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.mapper.ProvinceLimitMapper">
    <select id="selectMobileLimit" resultType="java.lang.Integer">
        select mobile_available from cms_province_limit where province_code= #{provinceCode}
    </select>
    <select id="selectUnicomLimit" resultType="java.lang.Integer">
        select unicom_available from cms_province_limit where province_code= #{provinceCode}
    </select>
    <select id="selectTelecomLimit" resultType="java.lang.Integer">
        select telecom_available from cms_province_limit where province_code= #{provinceCode}
    </select>
</mapper>