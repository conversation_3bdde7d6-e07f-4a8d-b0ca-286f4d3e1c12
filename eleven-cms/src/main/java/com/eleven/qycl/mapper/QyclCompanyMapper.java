package com.eleven.qycl.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AccountCofig;
import org.apache.ibatis.annotations.Param;
import com.eleven.qycl.entity.QyclCompany;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: qycl_company
 * @Author: jeecg-boot
 * @Date: 2022-11-09
 * @Version: V1.0
 */
public interface QyclCompanyMapper extends BaseMapper<QyclCompany> {

    Map<String, Object> analysis(@Param("reportDate") String reportDate, @Param("totalFee") Double totalFee);

    List<Map<String, Object>> groupByDate(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("totalFee") Double totalFee);

    Map<String, Object> queryWaitOrderNum(@Param("reportDate") String reportDate, @Param("totalFee") Double totalFee);

    IPage<QyclCompany> findByPage(Page<QyclCompany> page, @Param("dto") QyclCompany dto);

    Long findByPage_COUNT(@Param("dto") QyclCompany dto);


}
