<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.qycl.mapper.DataCollectMapper">

    <select id="subList" resultType="com.eleven.cms.dto.DataCollectSubDto">
        SELECT mobile,channel,province FROM qycl_data_notify_log WHERE create_time &gt;= #{dto.createTimeBegin}  and create_time &lt;= #{dto.createTimeEnd} and status=1 GROUP BY mobile
    </select>

    <select id="findDataCollectCount" resultType="com.eleven.cms.dto.DataCollectDto">
        SELECT sum(sub) as sub,sum(un_sub) as unSub,sum(verify_status) as verifyStatus FROM qycl_data_collect WHERE execute_date = #{dto.executeDate}
    </select>

    <select id="verifyStatusCount" resultType="java.lang.Integer">
        SELECT count(0) FROM qycl_data_notify_log WHERE create_time &gt;= #{dto.createTimeBegin}  and create_time &lt;= #{dto.createTimeEnd} and mobile = #{dto.mobile} and verify_status=0
    </select>

    <select id="findProvinceList" resultType="com.eleven.cms.dto.DataCollectDto">
        SELECT SUM(sub) as sub,SUM(un_sub) as unSub,sum(verify_status) as verifyStatus,province as type FROM qycl_data_collect
        <where>
            <if test="dto != null ">
                <if test="dto.updateTimeBegin != null and dto.updateTimeBegin != ''">
                    and execute_date &gt;= #{dto.updateTimeBegin}
                </if>
                <if test="dto.updateTimeEnd != null and dto.updateTimeEnd != ''">
                    and execute_date &lt;= #{dto.updateTimeEnd}
                </if>
                <if test="dto.province != null and dto.province != ''">
                    and province = #{dto.province}
                </if>
                <if test="dto.channel != null and dto.channel != ''">
                    and channel = #{dto.channel}
                </if>
            </if>
        </where>
        group by province
        ORDER BY CONVERT(province USING gbk) ASC
    </select>
    <select id="findExecuteDateList" resultType="com.eleven.cms.dto.DataCollectDto">
        SELECT SUM(sub) as sub,SUM(un_sub) as unSub,sum(verify_status) as verifyStatus,DATE_FORMAT(execute_date, '%m-%d') as type FROM qycl_data_collect
        <where>
            <if test="dto != null ">
                <if test="dto.updateTimeBegin != null and dto.updateTimeBegin != ''">
                    and execute_date &gt;= #{dto.updateTimeBegin}
                </if>
                <if test="dto.updateTimeEnd != null and dto.updateTimeEnd != ''">
                    and execute_date &lt;= #{dto.updateTimeEnd}
                </if>
                <if test="dto.province != null and dto.province != ''">
                    and province = #{dto.province}
                </if>
                <if test="dto.channel != null and dto.channel != ''">
                    and channel = #{dto.channel}
                </if>
            </if>
        </where>
        group by execute_date
        ORDER BY execute_date asc
    </select>




    <select id="findByPage" resultType="com.eleven.cms.dto.DataCollectDto">
        SELECT sub as sub,un_sub as unSub,province as province,un_sub_rate as unSubRate,verify_status as verifyStatus,verify_status_rate as verifyStatusRate,channel as channel,execute_date as updateTime  FROM qycl_data_collect
        <where>
            <if test="dto != null ">
                <if test="dto.updateTimeBegin != null and dto.updateTimeBegin != ''">
                    and execute_date &gt;= #{dto.updateTimeBegin}
                </if>
                <if test="dto.updateTimeEnd != null and dto.updateTimeEnd != ''">
                    and execute_date &lt;= #{dto.updateTimeEnd}
                </if>
                <if test="dto.province != null and dto.province != ''">
                    and province = #{dto.province}
                </if>
                <if test="dto.channel != null and dto.channel != ''">
                    and channel = #{dto.channel}
                </if>
            </if>
        </where>
        ORDER BY execute_date desc
    </select>



    <select id="pageDownXlsxDataCollectList" resultType="com.eleven.qycl.entity.DataCollect">
        SELECT sub as sub,un_sub as unSub,province as province,un_sub_rate as unSubRate,verify_status as verifyStatus,verify_status_rate as verifyStatusRate,channel as channel,execute_date as executeDate  FROM qycl_data_collect
        <where>
            <if test="dto != null ">
                <if test="dto.updateTimeBegin != null and dto.updateTimeBegin != ''">
                    and execute_date &gt;= #{dto.updateTimeBegin}
                </if>
                <if test="dto.updateTimeEnd != null and dto.updateTimeEnd != ''">
                    and execute_date &lt;= #{dto.updateTimeEnd}
                </if>
                <if test="dto.province != null and dto.province != ''">
                    and province = #{dto.province}
                </if>
                <if test="dto.channel != null and dto.channel != ''">
                    and channel = #{dto.channel}
                </if>
            </if>
        </where>
        ORDER BY execute_date desc
    </select>




    <select id="downXlsxFindExecuteDateList" resultType="com.eleven.cms.dto.DataCollectDto">
        SELECT SUM(sub) as sub,SUM(un_sub) as unSub,sum(verify_status) as verifyStatus,DATE_FORMAT(execute_date, '%m-%d') as type,channel FROM qycl_data_collect
        <where>
            <if test="dto != null ">
                <if test="dto.updateTimeBegin != null and dto.updateTimeBegin != ''">
                    and execute_date &gt;= #{dto.updateTimeBegin}
                </if>
                <if test="dto.updateTimeEnd != null and dto.updateTimeEnd != ''">
                    and execute_date &lt;= #{dto.updateTimeEnd}
                </if>
                <if test="dto.province != null and dto.province != ''">
                    and province = #{dto.province}
                </if>
                <if test="dto.channel != null and dto.channel != ''">
                    and channel = #{dto.channel}
                </if>
            </if>
        </where>
        group by execute_date
        ORDER BY execute_date asc
    </select>

</mapper>
