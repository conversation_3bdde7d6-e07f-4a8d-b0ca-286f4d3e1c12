package com.eleven.qycl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.dto.DataCollectDto;
import com.eleven.cms.dto.DataCollectSubDto;
import com.eleven.cms.dto.DataCollectUnSubDto;
import com.eleven.qycl.entity.DataCollect;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 企业彩铃数据汇总
 * @Author: jeecg-boot
 * @Date:   2024-01-19
 * @Version: V1.0
 */
public interface DataCollectMapper extends BaseMapper<DataCollect> {

    List<DataCollectSubDto> subList(@Param("dto") DataCollectSubDto dto);

    DataCollectDto findDataCollectCount(@Param("dto") DataCollectDto dto);

    List<DataCollectDto> findProvinceList(@Param("dto") DataCollectDto dto);

    List<DataCollectDto> findExecuteDateList(@Param("dto") DataCollectDto dto);

    List<DataCollectDto> findByPage(@Param("dto") DataCollectDto dto);

    Integer verifyStatusCount(@Param("dto") DataCollectUnSubDto dto);

    List<DataCollect> pageDownXlsxDataCollectList(@Param("dto") DataCollectDto dto);

    List<DataCollectDto> downXlsxFindExecuteDateList(@Param("dto") DataCollectDto dto);
}
