<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.qycl.mapper.QyclCompanyMapper">
    <select id="analysis" parameterType="java.util.Date" resultType="java.util.Map">
        select
        date_format(pay_time, '%Y-%m-%d') as payTimeStr,
        count(distinct(c.id)) as sucOrderNum,
        count(if(case when not exists (select 1 from qycl_company_member m where c.open_id = m.open_id and m.qycl_fun
        &lt;&gt; '1' )
        and exists(select 1 from qycl_ring r where c.open_id = r.open_id and r.ring_status is not null) then '已处理' else
        '待处理' end = '已处理',true,null)) as alreadyHandleNum,
        sum((select count(m.id) from qycl_company_member m where c.open_id = m.open_id)) as memberNum,
        sum((select count(m.id) from qycl_company_member m where c.open_id = m.open_id and m.qycl_fun='1')) as
        sucMemberNum
        from qycl_company c
        where c.pay_time is not null
        <if test="reportDate!=null">
            and date_format(c.pay_time, '%Y-%m-%d') = #{reportDate}
        </if>
        <if test="totalFee!= null">
            and exists (select 1 from qycl_order_pay p where p.total_fee = #{totalFee} and c.open_id = p.open_id)
        </if>
        group by date_format(pay_time, '%Y-%m-%d')
        order by pay_time desc
    </select>

    <select id="groupByDate" parameterType="java.lang.String" resultType="java.util.Map">
        select date_format(create_time, '%Y-%m-%d') as payTimeStr
        from qycl_order_pay
        <where>
            <if test="startDate!=null">
                and create_time &gt; #{startDate}
            </if>
            <if test="endDate!=null">
                and create_time &lt; #{endDate}
            </if>
        </where>
        group by date_format(create_time, '%Y-%m-%d')
        order by create_time desc
    </select>

    <select id="queryWaitOrderNum" resultType="java.util.Map">
        select count(*) as waitOrderNum
        from qycl_order_pay
        <where>
            and pay_status = -1
            and date_format(create_time, '%Y-%m-%d') = #{reportDate}
            <if test="totalFee!= null">
                and total_fee = #{totalFee}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="findByPage" resultType="com.eleven.qycl.entity.QyclCompany">
        select c.*, p.total_fee,p.id as order_id,(select count(m.id) from qycl_company_member m where open_id =
        c.open_id group by m.open_id) AS member_count
        from qycl_company c
        left join qycl_order_pay p on c.open_id = p.open_id
        where c.department_id is not null and c.pay_time is not null
        <if test="dto != null">
            <if test="dto.totalFee != null">
                and p.total_fee = #{dto.totalFee}
            </if>
            <if test="dto.orderId != null and dto.orderId !=''">
                and p.order_id = #{dto.orderId}
            </if>
            <if test="dto.mobile != null and dto.mobile !=''">
                and c.mobile = #{dto.mobile}
            </if>
            <if test="dto.title != null and dto.title !=''">
                and c.title = #{dto.title}
            </if>
            <if test="dto.subChannel != null and dto.subChannel !=''">
                and c.sub_channel = #{dto.subChannel}
            </if>
            <if test="dto.companyOwner != null and dto.companyOwner !=''">
                and c.company_owner = #{dto.companyOwner}
            </if>
            <if test="dto.refundStatus != null">
                and c.refund_status = #{dto.refundStatus}
            </if>
            <if test="dto.sourceType != null and dto.sourceType !=''">
                and c.source_type = #{dto.sourceType}
            </if>
            <if test="dto.channel != null and dto.channel !=''">
                and c.channel = #{dto.channel}
            </if>
            <if test="dto.payTime_begin != null">
                and c.pay_time &gt;= #{dto.payTime_begin}
            </if>
            <if test="dto.payTime_end != null">
                and c.pay_time &lt;= #{dto.payTime_end}
            </if>
            <if test="dto.refundTime_begin != null">
                and c.refund_time &gt;= #{dto.refundTime_begin}
            </if>
            <if test="dto.refundTime_end != null">
                and c.refund_time &lt;= #{dto.refundTime_end}
            </if>
        </if>
        order by c.operation_time desc
    </select>

    <select id="findByPage_COUNT" resultType="java.lang.Long">
        select count(*)
        from qycl_company c
        where c.department_id is not null and c.pay_time is not null
        <if test="dto != null">
            <if test="dto.totalFee != null">
                and exists (select 1 from qycl_order_pay p where c.open_id = p.open_id and pay_status = 1 and
                p.total_fee=#{dto.totalFee})
            </if>
            <if test="dto.orderId != null and dto.orderId !=''">
                and exists (select 1 from qycl_order_pay p where c.open_id = p.open_id and pay_status = 1 and
                p.id=#{dto.orderId})
            </if>
            <if test="dto.mobile != null and dto.mobile !=''">
                and c.mobile = #{dto.mobile}
            </if>
            <if test="dto.title != null and dto.title !=''">
                and c.title = #{dto.title}
            </if>
            <if test="dto.subChannel != null and dto.subChannel !=''">
                and c.sub_channel = #{dto.subChannel}
            </if>
            <if test="dto.companyOwner != null and dto.companyOwner !=''">
                and c.company_owner = #{dto.companyOwner}
            </if>
            <if test="dto.refundStatus != null">
                and c.refund_status = #{dto.refundStatus}
            </if>
            <if test="dto.sourceType != null and dto.sourceType !=''">
                and c.source_type = #{dto.sourceType}
            </if>
            <if test="dto.channel != null and dto.channel !=''">
                and c.channel = #{dto.channel}
            </if>
            <if test="dto.payTime_begin != null">
                and c.pay_time &gt;= #{dto.payTime_begin}
            </if>
            <if test="dto.payTime_end != null">
                and c.pay_time &lt;= #{dto.payTime_end}
            </if>
            <if test="dto.refundTime_begin != null">
                and c.refund_time &gt;= #{dto.refundTime_begin}
            </if>
            <if test="dto.refundTime_end != null">
                and c.refund_time &lt;= #{dto.refundTime_end}
            </if>
        </if>
    </select>

</mapper>