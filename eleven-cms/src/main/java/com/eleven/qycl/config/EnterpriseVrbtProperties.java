package com.eleven.qycl.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SystemUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2022/11/10 16:32
 * Desc: 语音合成配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "enterprise.vrbt")
@Slf4j
public class EnterpriseVrbtProperties {

    private String baseUrl;


    /**
     *  集团铃音上传接口地址
     */
    private String submitRingUrl;
    /**
     *  铃音设置接口地址
     */
    private String setDeptRingsByTimeUrl;
    /**
     *  集团铃音数据查询接口地址
     */
    private String searchEcRingUrl;
    /**
     * 集团部门铃音设置查询接口地址
     */
    private String searchEcRingSettingUrl;
    /**
     * 开启/关闭企业视频彩铃播放功能接口地址
     */
    private String updateVideoFuncUrl;
    /**
     * 集团部门操作接口（新增/删除/修改部门）地址
     */
    private String ecOperationUrl;
    /**
     * 部门数据查询接口地址
     */
    private String searchEcDepartmentsUrl;
    /**
     *  内容版企业成员添加接口地址
     */
    private String addContentMembersUrl;
    /**
     * 内容版企业成员删除接口地址
     */
    private String deleteContentMembersUrl;
    /**
     * 内容版企业成员查询接口地址
     */
    private String queryContentMembersUrl;
    /**
     * 短信内容链接
     */
    private String smsContentUrl;


    /**
     * 告警手机号集合
     */
    private List<String> warnMobiles;

//    private Map<String,EnterpriseVrbtConfig> channelConfigMap;

    private Integer unionLimit;
    private List<String> unionLimitProvince;
    private List<String> unionLimitChannel;
    private List<String> unionLimitMobile;


//    public EnterpriseVrbtConfig getEnterpriseVrbtConfigByCompanyOwner(String company) {
//        EnterpriseVrbtConfig enterpriseVrbtConfig = channelConfigMap.get(company);
//        if (enterpriseVrbtConfig == null) {
//            log.error("渠道号:{}未找到相关配置", company);
//            throw new JeecgBootException("无效的渠道号");
//        }
//        return enterpriseVrbtConfig;
//    }
}

