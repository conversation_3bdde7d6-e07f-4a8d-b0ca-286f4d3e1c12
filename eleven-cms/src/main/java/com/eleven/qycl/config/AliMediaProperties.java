package com.eleven.qycl.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "ali.media")
public class AliMediaProperties {

    public static final int JOB_QUEUE_TAG_QYCL = 1;
    public static final int JOB_QUEUE_TAG_DIY = 2;
    public static final int JOB_QUEUE_TAG_MANUAL = 3;
    public static final int JOB_QUEUE_TAG_YINGLOU = 4;
    public static final int JOB_QUEUE_TAG_HYAI = 5;
    public static final int JOB_QUEUE_TAG_TXAI = 6;
    public static final int JOB_QUEUE_TAG_TXAI_FACE = 7;
    public static final int JOB_QUEUE_TAG_TXAI_ACROSS_FACE = 8;
    public static final int JOB_QUEUE_TAG_TALK_SHOW = 9;



    private String accessKeyId;
    private String accessKeySecret;
    private String jobCallbackMnsQueue;
    private String jobCallbackMnsNotifyUrl;
    private String jobCallbackDiyMnsQueue;
    private String jobCallbackDiyMnsNotifyUrl;
    private String jobCallbackManualMnsQueue;
    private String jobCallbackManualMnsNotifyUrl;
    private String jobCallbackYinglouMnsQueue;
    private String jobCallbackYinglouMnsNotifyUrl;
    private String jobCallbackHYAIMnsQueue;
    private String jobCallbackHYAIMnsNotifyUrl;
    private String jobCallbackTxAiFaceMnsQueue;
    private String jobCallbackTxAiFaceMnsNotifyUrl;
    private String jobCallbackTxAiAcrossFaceMnsQueue;
    private String jobCallbackTxAiAcrossFaceMnsNotifyUrl;
    private String jobCallbackTxaiMnsQueue;
    private String jobCallbackTxaiMnsNotifyUrl;

    private String jobCallbackTalkShowMnsQueue;
    private String jobCallbackTalkShowMnsNotifyUrl;

    private String mnsEndpoint;
    private String regionId;
    private String bucketName;
    private String userDataDir;
    private String iproductionDir;
    private String mediaProduceDir;
    private String bgMusicUrl;
    private int outputVideoHeight;
    private int outputVideoWidth;
    private String staticDomain;

    public String determineJobCallbackMnsQueueByTag(int jobQueueTag) {
        String queueName = "";
        switch (jobQueueTag) {
            case JOB_QUEUE_TAG_QYCL:
                queueName = jobCallbackMnsQueue;
                break;
            case JOB_QUEUE_TAG_DIY:
                queueName = jobCallbackDiyMnsQueue;
                break;
            case JOB_QUEUE_TAG_MANUAL:
                queueName = jobCallbackManualMnsQueue;
                break;
            case JOB_QUEUE_TAG_YINGLOU:
                queueName = jobCallbackYinglouMnsQueue;
                break;
            case JOB_QUEUE_TAG_HYAI:
                queueName = jobCallbackHYAIMnsQueue;
                break;
            case JOB_QUEUE_TAG_TXAI:
                queueName = jobCallbackTxaiMnsQueue;
                break;
            case JOB_QUEUE_TAG_TXAI_FACE:
                queueName = jobCallbackTxAiFaceMnsQueue;
                break;
            case JOB_QUEUE_TAG_TXAI_ACROSS_FACE:
                queueName = jobCallbackTxAiAcrossFaceMnsQueue;
                break;
            case JOB_QUEUE_TAG_TALK_SHOW:
                queueName = jobCallbackTalkShowMnsQueue;
                break;
        }
        return queueName;
    }

    public String determineJobCallbackMnsNotifyUrlByTag(int jobQueueTag) {
        String notifyUrl = "";
        switch (jobQueueTag) {
            case JOB_QUEUE_TAG_QYCL:
                notifyUrl = jobCallbackMnsNotifyUrl;
                break;
            case JOB_QUEUE_TAG_DIY:
                notifyUrl = jobCallbackDiyMnsNotifyUrl;
                break;
            case JOB_QUEUE_TAG_MANUAL:
                notifyUrl = jobCallbackManualMnsNotifyUrl;
                break;
            case JOB_QUEUE_TAG_YINGLOU:
                notifyUrl = jobCallbackYinglouMnsNotifyUrl;
                break;
            case JOB_QUEUE_TAG_HYAI:
                notifyUrl = jobCallbackHYAIMnsNotifyUrl;
                break;
            case JOB_QUEUE_TAG_TXAI_FACE:
                notifyUrl = jobCallbackTxAiFaceMnsNotifyUrl;
                break;
            case JOB_QUEUE_TAG_TXAI_ACROSS_FACE:
                notifyUrl = jobCallbackTxAiAcrossFaceMnsNotifyUrl;
                break;
            case JOB_QUEUE_TAG_TALK_SHOW:
                notifyUrl = jobCallbackTalkShowMnsNotifyUrl;
                break;
        }
        return notifyUrl;
    }
}