package com.eleven.qycl.config;

import lombok.Data;
import org.apache.commons.lang3.SystemUtils;

import java.io.File;

/**
 * @author: cai lei
 * @create: 2023-06-09 14:26
 */
@Data
public class EnterpriseVrbtConfig {

    private String orderId;
    private String uniqueAccId;
    private String accPassword;
    private String contactPhone;
    /**
     * 部门管理员手机号（新增必传）
     */
    private String adminMsisdn;
    private String ringCopyrightPathWindows;
    private String ringCopyrightPathLinux;


    /**
     * 铃音版权文件
     * @return
     */
    public File getRingCopyrightFile() {
        return new File(SystemUtils.IS_OS_WINDOWS ? ringCopyrightPathWindows : ringCopyrightPathLinux);
    }

}
