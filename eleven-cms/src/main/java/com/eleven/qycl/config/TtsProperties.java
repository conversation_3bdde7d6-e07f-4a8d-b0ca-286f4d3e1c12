package com.eleven.qycl.config;

import lombok.Data;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Author: <EMAIL>
 * Date: 2022/11/10 16:32
 * Desc: 语音合成配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "tts")
public class TtsProperties {

    private String secretId;
    private String secretKey;
    private String audioFileBaseDirWindows;
    private String audioFileBaseDirLinux;
    private String backgroundAudioPathWindows;
    private String backgroundAudioPathLinux;
    private String templateVideoPathWindows;
    private String templateVideoPathLinux;
    private String vrbtFilePathLinux;
    private String vrbtFilePathWindows;

    public String getAudioFileBaseDir() {
        return SystemUtils.IS_OS_WINDOWS ? audioFileBaseDirWindows : audioFileBaseDirLinux;
    }

    public String getBackgroundAudioPath(){
        return SystemUtils.IS_OS_WINDOWS ? backgroundAudioPathWindows : backgroundAudioPathLinux;
    }

    public String getTemplateVideoPath(){
        return SystemUtils.IS_OS_WINDOWS ? templateVideoPathWindows : templateVideoPathLinux;
    }

    public String getVrbtFilePath(){
        return SystemUtils.IS_OS_WINDOWS ? vrbtFilePathWindows : vrbtFilePathLinux;
    }
}

