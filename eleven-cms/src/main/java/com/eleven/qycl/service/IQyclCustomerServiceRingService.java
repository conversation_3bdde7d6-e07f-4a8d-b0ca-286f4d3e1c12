package com.eleven.qycl.service;

import com.eleven.qycl.entity.BackgroundMusic;
import com.eleven.qycl.entity.QyclCustomerServiceRing;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.qycl.entity.VoiceGender;

/**
 * @Description: qycl_customer_service_ring
 * @Author: jeecg-boot
 * @Date: 2024-07-01
 * @Version: V1.0
 */
public interface IQyclCustomerServiceRingService extends IService<QyclCustomerServiceRing> {

    String createRing(String ringTxt, String companyTitle, String mobile, VoiceGender voiceGender, String[] imageArray, BackgroundMusic backgroundMusic);

    void aliTtsJobFinishHandle(String ttsJobId);

    void aliVideoJobFinishHandle(String videoJobId, String videoPath);

}
