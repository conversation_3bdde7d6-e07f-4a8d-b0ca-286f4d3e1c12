package com.eleven.qycl.service.impl;

import com.eleven.qycl.entity.QyclQa;
import com.eleven.qycl.mapper.QyclQaMapper;
import com.eleven.qycl.service.IQyclQaService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: qycl_qa
 * @Author: jeecg-boot
 * @Date:   2023-05-23
 * @Version: V1.0
 */
@Service
public class QyclQaServiceImpl extends ServiceImpl<QyclQaMapper, QyclQa> implements IQyclQaService {


    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_COMMON,key = "'qa:'+ #root.methodName",unless = "#result==null")
    @Override
    public List<QyclQa> listByType(String type) {
        return this.lambdaQuery().select(QyclQa::getQuestion, QyclQa::getAnswer).eq(QyclQa::getType, type).orderByAsc(QyclQa::getSortOrder).list();
    }
}
