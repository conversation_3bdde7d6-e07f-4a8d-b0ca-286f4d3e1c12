package com.eleven.qycl.service;

import com.eleven.qycl.entity.BackgroundMusic;
import com.eleven.qycl.entity.QyclRing;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.qycl.entity.VoiceGender;
import com.fasterxml.jackson.databind.JsonNode;
import org.jeecg.common.api.vo.Result;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @Description: qycl_ring
 * @Author: jeecg-boot
 * @Date: 2022-11-09
 * @Version: V1.0
 */
public interface IQyclRingService extends IService<QyclRing> {

    List<QyclRing> getListByOpenIdAndPaySuccess(String openId);

    List<QyclRing> getListByOpenId(String openId);

    QyclRing getOriginalByIdAndOpenId(String ringId,String openId);

    void modifyRing(QyclRing originalRing,QyclRing modifyRing);

    void modifyNewRing(QyclRing originalRing, QyclRing modifyRing, Integer ringType, VoiceGender voiceGender, String[] imageArray,BackgroundMusic backgroundMusic);

    Map<String, String> createRing(String ringTxt, String companyTitle, String openId, String mobile, String companyOwner);

    Map<String, String> createNewRing(String ringTxt, String companyTitle, String openId, String mobile, Integer ringType, VoiceGender voiceGender, String[] imageArray, BackgroundMusic backgroundMusic, String subChannel, String companyOwner);


    Result<?> ringPay(String title, String companyTitle, String filePath, String openId, String ringTxt, String ringId, String source, String sourceType, HttpServletRequest req, String companyOwner);

    Result<?> sdkCallback(String mobile, String title, String openId, String ringId, String resCode, String resMsg, String source, String sourceType, Integer ringType, String crack, HttpServletRequest req, String companyOwner, String channel);

    Result useRing(String ringId);

    int deleteByOpenId(String openId);

    void deleteInvalidRing();

    void busModifyRing(QyclRing originalRing);

    void submitRingNotify(JsonNode jsonNode);

    void submitRing(QyclRing qyclRing,String departmentId,String channel);

    void aliTtsJobFinishHandle(String ttsJobId);

    void aliVideoJobFinishHandle(String videoJobId,String videoPath);

    void settingRing(String mobile, String copyrightId, String openId, String companyOwner);

    void settingRingDiy(String mobile,String ringId,String openId);

    QyclRing randomSettingRing(String mobile,String openId);

    QyclRing randomSettingQyclRing(String mobile,String openId);

    void webSdkCallback(String mobile, String openId, String ringId, String departmentId, String companyOwner) throws IOException;

    void webSdkPersonCallback(String mobile, String openId, String departmentId, String companyOwner) throws IOException;

    void webSdkQyclCallback(String mobile, String openId, String departmentId, String companyOwner) throws IOException;

    String createTemplateRing(String mobile, String openId, String templateId, String clipsParam, String subChannel, String companyOwner);

    String createQyclTemplateRing(String mobile, String openId, String templateId, String clipsParam, String subChannel, String companyOwner);

    String createVideoRing(String mobile, String openId, String videoPath, String subChannel, String companyOwner);

    String createVideoRingApp(String mobile, String openId, String videoPath, String subChannel, String companyOwner);

    QyclRing getByRingId(String ringId);

    String createQyclDiyRing(String ringTxt, String companyTitle, String openId, String mobile, VoiceGender voiceGender, String[] imageArray, String subChannel, String companyOwner);




}
