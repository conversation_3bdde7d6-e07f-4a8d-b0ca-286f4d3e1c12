package com.eleven.qycl.service.impl;

import com.eleven.qycl.entity.QyclRingColumn;
import com.eleven.qycl.mapper.QyclRingColumnMapper;
import com.eleven.qycl.service.IQyclRingColumnService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: qycl_ring_column
 * @Author: jeecg-boot
 * @Date:   2023-04-21
 * @Version: V1.0
 */
@Service
public class QyclRingColumnServiceImpl extends ServiceImpl<QyclRingColumnMapper, QyclRingColumn> implements IQyclRingColumnService {

    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_COMMON,key = "'ringColumn:'+ #root.methodName",unless = "#result==null")
    @Override
    public List<QyclRingColumn> ringColumn() {
        return this.lambdaQuery()
                .eq(QyclRingColumn::getStatus,1)
                .eq(QyclRingColumn::getType,1)
                .orderByAsc(QyclRingColumn::getOrderBy)
                .list();
    }

    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_COMMON,key = "'ringColumn:'+ #root.methodName",unless = "#result==null")
    @Override
    public List<QyclRingColumn> ringTemplateColumn() {
        return this.lambdaQuery()
                .eq(QyclRingColumn::getStatus,1)
                .eq(QyclRingColumn::getType,2)
                .orderByAsc(QyclRingColumn::getOrderBy)
                .list();
    }

    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_COMMON,key = "'ringColumn:'+ #root.methodName",unless = "#result==null")
    @Override
    public List<QyclRingColumn> ringDiyTemplateColumn() {
        return this.lambdaQuery()
                .eq(QyclRingColumn::getStatus,1)
                .eq(QyclRingColumn::getType,3)
                .orderByAsc(QyclRingColumn::getOrderBy)
                .list();
    }
}
