package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.QyclWXPayPropertiesConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.WechatComplain;
import com.eleven.cms.queue.QyclDelayedMessage;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.qycl.config.TtsProperties;
import com.eleven.qycl.entity.*;
import com.eleven.qycl.mapper.QyclOrderPayMapper;
import com.eleven.qycl.service.*;
import com.eleven.qycl.util.QyclConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.queue.RedisDelayedQueueManager.*;

/**
 * @Description: qycl_order_pay
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Slf4j
@Service
public class QyclOrderPayServiceImpl extends ServiceImpl<QyclOrderPayMapper, QyclOrderPay> implements IQyclOrderPayService {
    //订单金额
    private static final Double TOTAL_FEE=88.00;

    //不需要支付订单金额
    private static final Double NOT_PATMENT_TOTAL_FEE = 0d;

    //未支付
    private static final Integer NO_PAY_STATUS=-1;
    //已支付
    private static final Integer PAY_STATUS_SUCCESS=1;

    @Autowired
    private IQyclRingService qyclRingService;
    @Autowired
    private IWechatComplainService wechatComplainService;
    @Autowired
    private IQyclCompanyService qyclCompanyService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IQyclCompanyMemberService qyclCompanyMemberService;
    @Autowired
    QyclWXPayPropertiesConfig wxPayPropertiesConfig;
    @Autowired
    TtsService ttsService;

    @Autowired
    private IQyclWxpayService wxpayService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;
    @Autowired
    TtsProperties ttsProperties;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    //退款失败
    private static final Integer REFUND_FAIL=2;
    //未退款
    private static final Integer NOT_REFUND=0;
    //退款成功
    private static final Integer REFUND_SUCCESS=1;
    //退款中
    private static final Integer REFUND_PREPARE=-1;


    @Override
    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_WECHAT_PAY_ORDER_CACHE,key = "#openId + '-' + #mobile",unless = "#result==null")
    public String savePay(String openId, String companyTitle, String mobile, String companyOwner) {
        QyclOrderPay order=new QyclOrderPay();
        order.setOpenId(openId);
        order.setCompanyTitle(companyTitle);
        order.setMobile(mobile);
        order.setTotalFee(TOTAL_FEE);
        order.setPayStatus(NO_PAY_STATUS);
        order.setCreateTime(new Date());
        order.setCompanyOwner(companyOwner);
        this.save(order);
        return order.getId();
    }
    @Override
    public QyclOrderPay queryOrder(String orderId){
        return this.lambdaQuery().eq(QyclOrderPay::getId,orderId).orderByDesc(QyclOrderPay::getCreateTime).last("limit 1").one();
    }


    @Override
    public void updatePayStatus(String outTradeNo,String transactionId,Integer status,QyclOrderPay qyclOrderPay) {
        Boolean orderPay = this.lambdaUpdate()
                .eq(QyclOrderPay::getId, outTradeNo)
                .eq(QyclOrderPay::getPayStatus, NO_PAY_STATUS)
                .set(QyclOrderPay::getPayStatus, status)
                .set(QyclOrderPay::getOutTradeNo, transactionId)
                .set(QyclOrderPay::getNotifyTime, new Date())
                .set(QyclOrderPay::getUpdateTime, new Date()).update();

        if (status.equals(1) && orderPay) {
            //加入延时队列
            redisDelayedQueueManager.addQycl(QyclDelayedMessage.builder().mobile(qyclOrderPay.getMobile()).openId(qyclOrderPay.getOpenId()).msg("企业彩铃未开通包月用户").tag(
                    MESSAG_EXTRA_30_MIN).build(), 30, TimeUnit.MINUTES);
            QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, qyclOrderPay.getOpenId()).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
            QyclRing qyclRing = qyclRingService.lambdaQuery().eq(QyclRing::getOrderId, outTradeNo).eq(QyclRing::getOrderPayStatus, NO_PAY_STATUS).orderByDesc(QyclRing::getCreateTime).last("limit 1").one();
            String videoPath = ttsService.genVideoRelativePath();
            if(qyclRing!=null){
                ttsService.mergeVideo(qyclRing.getFilePath(), videoPath);
            }
            qyclRingService.lambdaUpdate()
                    .eq(QyclRing::getOrderId, outTradeNo)
                    .eq(QyclRing::getOrderPayStatus, NO_PAY_STATUS)
                    .set(QyclRing::getOrderPayStatus, status)
                    .set(QyclRing::getVideoPath, videoPath).update();
            if (qyclCompany != null) {
                //创建部门(替换掉特殊字符)
                String departmentName = qyclCompany.getTitle().replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
                departmentName = StringUtils.substring(departmentName, 0, 19);
                final EntVrbtResult departmentEntVrbtResult = enterpriseVrbtService.ecOperationByChannel("0", departmentName + qyclCompany.getMobile(), "", qyclCompany.getCompanyOwner(),qyclCompany.getChannel());
                String departmentId = null;
                if (departmentEntVrbtResult.getData() != null) {
                    departmentId = departmentEntVrbtResult.getData().at("/departmentId").asText();
                    qyclCompany.setDepartmentId(departmentId);
                    qyclCompanyService.updateById(qyclCompany);
                }
//                //添加成员
//                if (StringUtils.isNotEmpty(departmentId)) {
//                    enterpriseVrbtService.addContentMembers(departmentId, qyclCompany.getMobile());
//                }
                QyclCompanyMember companyMember = qyclCompanyMemberService.lambdaQuery().eq(QyclCompanyMember::getOpenId, qyclOrderPay.getOpenId()).eq(QyclCompanyMember::getMobile, qyclCompany.getMobile()).orderByDesc(QyclCompanyMember::getCreateTime).last("limit 1").one();
                if (companyMember == null) {
                    QyclCompanyMember qycl = new QyclCompanyMember();
                    qycl.setOpenId(qyclOrderPay.getOpenId());
                    qycl.setMobile(qyclCompany.getMobile());
                    qycl.setCompanyTitle(qyclCompany.getTitle());
                    qyclCompanyMemberService.save(qycl);
                } else {
                    qyclCompanyMemberService.lambdaUpdate()
                            .eq(QyclCompanyMember::getId, companyMember.getId())
                            .set(QyclCompanyMember::getOpenId, qyclOrderPay.getOpenId())
                            .set(QyclCompanyMember::getMobile, qyclCompany.getMobile())
                            .set(QyclCompanyMember::getCompanyTitle, qyclCompany.getTitle()).update();
                }
                //更新企业表支付时间
                qyclCompany.setPayTime(new Date());
                //设置操作时间
                qyclCompany.setOperationTime(new Date());
                qyclCompanyService.updateById(qyclCompany);
                //上传铃声
                qyclRing = qyclRingService.getById(qyclRing.getId());
                if (StringUtils.isNotEmpty(departmentId) && QyclConstant.RING_MAKE_COMPLETE.equals(qyclRing.getRingMakeStatus())) {
                    qyclRingService.submitRing(qyclRing, departmentId,qyclCompany.getChannel());
                }
            }
        }
        //修改sub表状态
        Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, outTradeNo).ne(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
        if(subscribe != null ){
            //订阅成功
            if(status.equals(1) && orderPay){
                subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
                subscribe.setOpenTime(new Date());
                subscribe.setResult("付费成功！");
            }else{
                subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                subscribe.setResult("付费失败！");
            }
            subscribeService.updateSubscribeDbAndEs(subscribe);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
        }


    }

    @Override
    public String saveNotPaymentOrder(String openId, String companyTitle, String mobile, String companyOwner) {
        QyclOrderPay order = new QyclOrderPay();
        order.setOpenId(openId);
        order.setCompanyTitle(companyTitle);
        order.setMobile(mobile);
        order.setTotalFee(NOT_PATMENT_TOTAL_FEE);
        order.setPayStatus(NO_PAY_STATUS);
        order.setCreateTime(new Date());
        this.save(order);
        return order.getId();
    }

    @Override
    public Result wechatRefund(String outTradeNo, String refund){
        String refundOrderNo = IdWorker.get32UUID();
        QyclOrderPay orderPay=this.lambdaQuery()
                .eq(QyclOrderPay::getId, outTradeNo)
                .eq(QyclOrderPay::getPayStatus, PAY_STATUS_SUCCESS)
                .eq(QyclOrderPay::getRefundStatus,NOT_REFUND)
                .orderByDesc(QyclOrderPay::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        Double refunds=Double.valueOf(refund);
        Double totalFee=orderPay.getTotalFee();
        if(refunds>totalFee){
            return Result.error("退款金额大于支付金额！");
        }
        if(StringUtils.isBlank(orderPay.getAppId()) || StringUtils.isBlank(orderPay.getMchId()) ){
            return Result.error("退款订单暂不支持！");
        }
        //退款金额
        String refundAmount= BigDecimal.valueOf(refunds).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
        //订单金额
        String totalAmount=BigDecimal.valueOf(totalFee).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
        if(StringUtils.equalsAny(orderPay.getTradeType(),BizConstant.TRADE_TYPE_HTML,BizConstant.TRADE_TYPE_WECHAT)){
            FebsResponse febsRefund=wxpayService.wechatRefund(orderPay.getId(),refundOrderNo,refundAmount,totalAmount,orderPay.getAppId(),orderPay.getMchId());
            if(febsRefund.isOK()){
                upadateRefundStatus(outTradeNo, refund, refundOrderNo, "正在退款！", REFUND_PREPARE,orderPay.getOpenId());
                return Result.ok("正在退款！");
            }
        }else if(org.apache.commons.lang3.StringUtils.equalsAny(orderPay.getTradeType(),BizConstant.TRADE_TYPE_TIKTOK)){
            try{
                FebsResponse febsRefund=wxpayService.dyRefund(orderPay.getId(),refundOrderNo,refundAmount,orderPay.getAppId(),orderPay.getMchId());
                if(febsRefund.isOK()){
                    upadateRefundStatus(outTradeNo, refund, refundOrderNo, "正在退款！", REFUND_PREPARE,orderPay.getOpenId());
                    return Result.ok("正在退款！");
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        upadateRefundStatus(outTradeNo, refund, refundOrderNo, "退款失败！", REFUND_FAIL,orderPay.getOpenId());
        return Result.error("退款失败！");
    }

    /**
     * 更新退款状态和备注
     * @param outTradeNo
     * @param refundAmount
     * @param refundOrderNo
     * @param refundRemark
     * @param refundStatus
     * @param openId
     */
    private void upadateRefundStatus(String outTradeNo, String refundAmount, String refundOrderNo, String refundRemark, Integer refundStatus,String openId) {
        this.lambdaUpdate()
                .eq(QyclOrderPay::getId, outTradeNo)
                .set(QyclOrderPay::getRefundStatus, refundStatus)
                .set(QyclOrderPay::getRefundOrderNo, refundOrderNo)
                .set(QyclOrderPay::getRefundAmount, refundAmount)
                .set(QyclOrderPay::getRefundRemark, refundRemark)
                .set(QyclOrderPay::getRefundTime, new Date())
                .set(QyclOrderPay::getUpdateTime, new Date()).update();




        qyclCompanyService.lambdaUpdate()
                .eq(QyclCompany::getOpenId, openId)
                .set(QyclCompany::getRefundStatus, refundStatus)
                .set(QyclCompany::getRefundOrderNo, refundOrderNo)
                .set(QyclCompany::getRefundAmount, refundAmount)
                .set(QyclCompany::getRefundRemark, refundRemark)
                .set(QyclCompany::getRefundTime, new Date())
                .set(QyclCompany::getUpdateTime, new Date())
                .update();

        wechatComplainService.lambdaUpdate().eq(WechatComplain::getOutTradeNo, outTradeNo).set(WechatComplain::getRefundStatus, refundStatus).update();

    }
    @Override
    public void upadateRefundStatus(String refundOrderNo,Integer refundStatus,String refundRemark) {

        this.lambdaUpdate()
                .eq(QyclOrderPay::getRefundOrderNo, refundOrderNo)
                .set(QyclOrderPay::getRefundStatus, refundStatus)
                .set(QyclOrderPay::getRefundRemark, refundRemark)
                .set(QyclOrderPay::getRefundTime, new Date())
                .set(QyclOrderPay::getUpdateTime, new Date())
                .update();

        qyclCompanyService.lambdaUpdate()
                .eq(QyclCompany::getRefundOrderNo, refundOrderNo)
                .set(QyclCompany::getRefundStatus, refundStatus)
                .set(QyclCompany::getRefundRemark, refundRemark)
                .set(QyclCompany::getRefundTime, new Date())
                .set(QyclCompany::getUpdateTime, new Date())
                .update();



        QyclOrderPay orderPay=this.lambdaQuery()
                .eq(QyclOrderPay::getRefundOrderNo, refundOrderNo)
                .orderByDesc(QyclOrderPay::getCreateTime).last("limit 1").one();

        if(orderPay!=null){
            wechatComplainService.lambdaUpdate().eq(WechatComplain::getOutTradeNo, orderPay.getId()).set(WechatComplain::getRefundStatus, refundStatus).update();
        }

        if(refundStatus.equals(REFUND_SUCCESS) && orderPay!=null){
            //设置渠道订单描述
            Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, orderPay.getId()).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
            if(subscribe != null && refundStatus.equals(REFUND_SUCCESS) ){
                subscribe.setResult("已退款！");
                subscribeService.updateSubscribeDbAndEs(subscribe);
            }
        }

    }
}
