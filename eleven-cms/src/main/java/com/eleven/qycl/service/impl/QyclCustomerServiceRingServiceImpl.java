package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.entity.BackgroundMusic;
import com.eleven.qycl.entity.QyclCustomerServiceRing;
import com.eleven.qycl.entity.QyclRing;
import com.eleven.qycl.entity.VoiceGender;
import com.eleven.qycl.mapper.QyclCustomerServiceRingMapper;
import com.eleven.qycl.service.AliMediaService;
import com.eleven.qycl.service.IQyclCustomerServiceRingService;
import com.eleven.qycl.util.QyclConstant;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: qycl_customer_service_ring
 * @Author: jeecg-boot
 * @Date: 2024-07-01
 * @Version: V1.0
 */
@Service
public class QyclCustomerServiceRingServiceImpl extends ServiceImpl<QyclCustomerServiceRingMapper, QyclCustomerServiceRing> implements IQyclCustomerServiceRingService {

    @Autowired
    AliMediaService aliMediaService;
    @Autowired
    AliMediaProperties aliMediaProperties;

    private String regionId;
    private String bucket;

    @PostConstruct
    public void init() {
        regionId = aliMediaProperties.getRegionId();
        bucket = aliMediaProperties.getBucketName();
    }

    @Override
    public String createRing(String ringTxt, String companyTitle, String mobile, VoiceGender voiceGender, String[] imageArray, BackgroundMusic backgroundMusic) {
        QyclCustomerServiceRing qyclCustomerServiceRing = new QyclCustomerServiceRing();
        qyclCustomerServiceRing.setMobile(mobile);
        qyclCustomerServiceRing.setCompanyTitle(companyTitle);
        qyclCustomerServiceRing.setRingTxt(ringTxt);
        qyclCustomerServiceRing.setImageUrls(StringUtils.join(imageArray, ","));
        qyclCustomerServiceRing.setRingMakeStatus(QyclConstant.RING_MAKE_IN);
        if (StringUtils.isEmpty(ringTxt)) {
            String urlPre = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/";
            List<String> imageList = Arrays.stream(imageArray).map(item -> urlPre + item).collect(Collectors.toList());
            String videoJobId = aliMediaService.produceVideo(AliMediaProperties.JOB_QUEUE_TAG_MANUAL, imageList, backgroundMusic != null ? backgroundMusic.getMusicPath() : "", "", "", "");
            qyclCustomerServiceRing.setAliVideoJobId(videoJobId);
        } else {
            String jobId = aliMediaService.tts(AliMediaProperties.JOB_QUEUE_TAG_MANUAL, qyclCustomerServiceRing.getRingTxt(), voiceGender, ImmutableMap.of());
            qyclCustomerServiceRing.setAliTtsJobId(jobId);
        }
        this.save(qyclCustomerServiceRing);
        return qyclCustomerServiceRing.getId();
    }

    @Override
    public void aliTtsJobFinishHandle(String ttsJobId) {
        QyclCustomerServiceRing qyclCustomerServiceRing = lambdaQuery().eq(QyclCustomerServiceRing::getAliTtsJobId, ttsJobId).one();
        if (qyclCustomerServiceRing == null) {
            return;
        }
        Pair<String, String> pair = aliMediaService.fetchTtsResult(ttsJobId, ImmutableMap.of());
        String audioUrl = pair.getLeft();
        String subtitleContent = pair.getRight();
        String[] imageUrlArray = qyclCustomerServiceRing.getImageUrls().split(",");
        String urlPre = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/";
        List<String> imageList = Arrays.stream(imageUrlArray).map(item -> urlPre + item).collect(Collectors.toList());
        String videoJobId = aliMediaService.produceVideo(AliMediaProperties.JOB_QUEUE_TAG_MANUAL, imageList, qyclCustomerServiceRing.getBgmUrl(), audioUrl, subtitleContent, "");
        lambdaUpdate().set(QyclCustomerServiceRing::getVideoPath, audioUrl).set(QyclCustomerServiceRing::getAliVideoJobId, videoJobId).eq(QyclCustomerServiceRing::getId, qyclCustomerServiceRing.getId()).update();
    }

    @Override
    public void aliVideoJobFinishHandle(String videoJobId, String videoPath) {
        QyclCustomerServiceRing qyclCustomerServiceRing = lambdaQuery().eq(QyclCustomerServiceRing::getAliVideoJobId, videoJobId).one();
        if (qyclCustomerServiceRing == null) {
            return;
        }
        lambdaUpdate().eq(QyclCustomerServiceRing::getId, qyclCustomerServiceRing.getId()).set(QyclCustomerServiceRing::getRingMakeStatus, QyclConstant.RING_MAKE_COMPLETE).set(QyclCustomerServiceRing::getVideoPath, videoPath).update();
    }
}
