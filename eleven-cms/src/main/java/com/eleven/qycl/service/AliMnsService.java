package com.eleven.qycl.service;

import cn.hutool.core.util.StrUtil;
import com.aliyun.mns.client.CloudAccount;
import com.aliyun.mns.client.CloudQueue;
import com.aliyun.mns.client.MNSClient;
import com.aliyun.mns.common.ClientException;
import com.aliyun.mns.common.ServiceException;
import com.aliyun.mns.model.Message;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.eleven.cms.aiunion.entity.AiUnionRecord;
import com.eleven.cms.aiunion.service.AiTalkShowService;
import com.eleven.cms.aiunion.service.IAiUnionRecordService;
import com.eleven.cms.aiunion.service.IAiUnionService;
import com.eleven.cms.aiunionkp.entity.KpAiUnionRecord;
import com.eleven.cms.aiunionkp.service.KpIAiUnionRecordService;
import com.eleven.cms.aivrbt.entity.AiRing;
import com.eleven.cms.aivrbt.service.IAiRingService;
import com.eleven.cms.remote.CommonSecurityService;
import com.eleven.cms.service.IVrbtDiyRingService;
import com.eleven.cms.service.IYinglouRingService;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.MiguSecurityUtils;
import com.eleven.cms.zhmb.service.IZhmbRingService;
import com.eleven.qycl.config.AliMediaProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @desc 阿里云MNS消息服务
 */
@Slf4j
@Component
public class AliMnsService {
    public static final String LOG_TAG = "阿里云MNS消息服务";
    @Autowired
    AliMediaProperties aliMediaProperties;
    @Autowired
    IQyclRingService qyclRingService;
    @Autowired
    IVrbtDiyRingService vrbtDiyRingService;
    @Autowired
    IQyclCustomerServiceRingService qyclCustomerServiceRingService;
    @Autowired
    private IAiRingService aiRingService;
    @Autowired
    private IAiUnionRecordService aiUnionRecordService;
    @Autowired
    private KpIAiUnionRecordService kpAiUnionRecordService;
    @Autowired
    private IAiUnionService aiUnionService;
    @Autowired
    IZhmbRingService zhmbRingService;
    @Autowired
    IYinglouRingService yinglouRingService;
    private MNSClient client;
    private ObjectMapper mapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CommonSecurityService commonSecurityService;


    @Autowired
    AiTalkShowService aiTalkShowService;


    @PostConstruct
    public void init() {
        client = new CloudAccount(aliMediaProperties.getAccessKeyId(), aliMediaProperties.getAccessKeySecret(), aliMediaProperties.getMnsEndpoint()).getMNSClient();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.pullJobQueueMessage();
    }


    /**
     * 处理到期的消息
     */
    public void pullJobQueueMessage() {
        List<Runnable> listeners = Lists.newArrayList();
        listeners.add(hyListener());
        listeners.add(txAiFaceListener());
        listeners.add(txAiAcrossFaceListener());
        listeners.add(talkShowRecord());
        ExecutorService executor = Executors.newFixedThreadPool(listeners.size());
        listeners.forEach(executor::execute);

        //diyListener();
        //manualListener();
        //yinglouListener();
        //txAiListener();
        //listeners.add(listener());
        //listeners.add(diyListener());
        //listeners.add(yinglouListener());
        //listeners.add(manualListener());
        //listeners.add(txAiListener());
   }


    /**
     *
     */

    public Runnable talkShowRecord (){
        log.info("{}-===============轮询启动==============", LOG_TAG);

        return () -> {
            CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackTalkShowMnsQueue());// replace with your queue name
            while (true){
                Message popMsg = queue.popMessage();
                if (popMsg != null) {
                    final String body = popMsg.getMessageBodyAsString();
                    log.info("{}-收到消息,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);

                    final JsonNode root;
                    try {
                        root = mapper.readTree(body);
                        if ("IProduction".equals(root.at("/Type").asText())) {
                            //tts job消息处理
                            final String jobId = root.get("JobId").asText();
                            qyclRingService.aliTtsJobFinishHandle(jobId);
                        } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                            //视频合成job消息处理
                            final JsonNode jobDetail = root.get("MessageBody");
                            final String jobId = jobDetail.get("JobId").asText();
                            final String mediaURL = jobDetail.get("MediaURL").asText();
                            aiTalkShowService.handleJob(jobId, mediaURL);
                            queue.deleteMessage(popMsg.getReceiptHandle());
                        } else {
                            log.info("{}-未知类型的消息,不作处理!,body:{}", LOG_TAG, body);
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            }
        };
    }

    /**
     * 企业彩铃做铃音
     *
     * @return
     */
    public Runnable listener() {
        return () -> {
            log.info("{}-===============轮询启动==============", LOG_TAG);
            try {
                CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackMnsQueue());// replace with your queue name
                //阿里云MNS已设置10秒的长轮询,即10秒未拿到消息服务端会阻塞,不会一直快速轮询
                while (true) {
                    try {
                        Message popMsg = queue.popMessage();
                        if (popMsg != null) {
                            //message handle: 7CDE60DCE2D0737D610C64DAA802A059-MCAxNjc5MjkyNTI3NzcwIDIwMDAwIDAgMSBiam1xbmluZTRzbXEtMDEgMCAw
                            //message body: {"Code":"Success","FunctionName":"AsyncTextToSpeech","JobId":"84c4c569b7a84440bc96db0c974a2261","Message":"Successful.","State":"Success","Type":"IProduction"}
                            //message id: 7CDE60DCE2D0737D610C64DAA802A059
                            //message dequeue count:2

                            //message handle: 7CDE60DCE2D0737D410064DAF96C5BB3-MCAxNjc5MjkyNTI4MDQ0IDIwMDAwIDYgMSBiam1xbmluZTRzbXEtMDMgMCAw
                            //message body: {"EventType":"ProduceMediaComplete","UserId":1431075016168616,"EventTime":"2023-03-20T06:01:12Z","MessageBody":{"Status":"Success","MediaURL":"https://ims-media.oss-cn-beijing.aliyuncs.com/d673803481a3cd07de4bc5113e81eca4.mp4","MediaId":"91b51400c6e471ed828be6f6f6796302","EventType":"ProduceMediaComplete","UserData":"{\"NotifyAddress\":\"ice-callback-queue-ice-mns-job\"}","EventTime":"2023-03-20T06:01:12Z","ProjectId":"0d9a7a6a429b41268435ec9e6702c7fd","ErrorCode":"","ErrorMessage":"","JobId":"4cbdf3433877432d89c1efff21f1bede"}}
                            //message id: 7CDE60DCE2D0737D410064DAF96C5BB3
                            //message dequeue count:2
                            final String body = popMsg.getMessageBodyAsString();
                            log.info("{}-收到消息,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);
                            //System.out.println("message handle: " + popMsg.getReceiptHandle());
                            //System.out.println("message body: " + body);
                            //System.out.println("message id: " + popMsg.getMessageId());
                            //System.out.println("message dequeue count:" + popMsg.getDequeueCount());
                            //<<to add your special logic.>>
                            final JsonNode root = mapper.readTree(body);
                            if ("IProduction".equals(root.at("/Type").asText())) {
                                //tts job消息处理
                                final String jobId = root.get("JobId").asText();
                                qyclRingService.aliTtsJobFinishHandle(jobId);
                            } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                                //视频合成job消息处理
                                final JsonNode jobDetail = root.get("MessageBody");
                                final String jobId = jobDetail.get("JobId").asText();
                                final String mediaURL = jobDetail.get("MediaURL").asText();
                                qyclRingService.aliVideoJobFinishHandle(jobId, mediaURL);
                            } else {
                                log.info("{}-未知类型的消息,不作处理!,body:{}", LOG_TAG, body);
                            }
                            //remember to  delete message when consume message successfully.
                            queue.deleteMessage(popMsg.getReceiptHandle());
                            //System.out.println("delete message successfully.\n");
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            } catch (ClientException ce) {
                log.info("{}-Something wrong with the network connection between client and MNS service." + "Please check your network and DNS availablity.", LOG_TAG, ce);
            } catch (ServiceException se) {
             /*
            you can get more MNS service error code in following link.
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html?spm=5176.docmns/api_reference/error_code/error_response
            */
                if ("QueueNotExist".equals(se.getErrorCode())) {
                    log.info("{}-Queue is not exist.Please create queue before use", LOG_TAG, se);
                } else if ("TimeExpired".equals(se.getErrorCode())) {
                    log.info("{}-The request is time expired. Please check your local machine timeclock", LOG_TAG, se);
                }
            } catch (Exception e) {
                log.info("{}-Unknown exception happened!", LOG_TAG, e);
            }

        };
    }

    public Runnable diyListener() {
        return () -> {
            log.info("{}-===============轮询启动diy==============", LOG_TAG);
            try {
                CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackDiyMnsQueue());// replace with your queue name
                //阿里云MNS已设置10秒的长轮询,即10秒未拿到消息服务端会阻塞,不会一直快速轮询
                while (true) {
                    try {
                        Message popMsg = queue.popMessage();
                        if (popMsg != null) {
                            //message handle: 7CDE60DCE2D0737D610C64DAA802A059-MCAxNjc5MjkyNTI3NzcwIDIwMDAwIDAgMSBiam1xbmluZTRzbXEtMDEgMCAw
                            //message body: {"Code":"Success","FunctionName":"AsyncTextToSpeech","JobId":"84c4c569b7a84440bc96db0c974a2261","Message":"Successful.","State":"Success","Type":"IProduction"}
                            //message id: 7CDE60DCE2D0737D610C64DAA802A059
                            //message dequeue count:2

                            //message handle: 7CDE60DCE2D0737D410064DAF96C5BB3-MCAxNjc5MjkyNTI4MDQ0IDIwMDAwIDYgMSBiam1xbmluZTRzbXEtMDMgMCAw
                            //message body: {"EventType":"ProduceMediaComplete","UserId":1431075016168616,"EventTime":"2023-03-20T06:01:12Z","MessageBody":{"Status":"Success","MediaURL":"https://ims-media.oss-cn-beijing.aliyuncs.com/d673803481a3cd07de4bc5113e81eca4.mp4","MediaId":"91b51400c6e471ed828be6f6f6796302","EventType":"ProduceMediaComplete","UserData":"{\"NotifyAddress\":\"ice-callback-queue-ice-mns-job\"}","EventTime":"2023-03-20T06:01:12Z","ProjectId":"0d9a7a6a429b41268435ec9e6702c7fd","ErrorCode":"","ErrorMessage":"","JobId":"4cbdf3433877432d89c1efff21f1bede"}}
                            //message id: 7CDE60DCE2D0737D410064DAF96C5BB3
                            //message dequeue count:2
                            final String body = popMsg.getMessageBodyAsString();
                            log.info("{}-收到消息diy,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);
                            //System.out.println("message handle: " + popMsg.getReceiptHandle());
                            //System.out.println("message body: " + body);
                            //System.out.println("message id: " + popMsg.getMessageId());
                            //System.out.println("message dequeue count:" + popMsg.getDequeueCount());
                            //<<to add your special logic.>>
                            final JsonNode root = mapper.readTree(body);
                            if ("IProduction".equals(root.at("/Type").asText())) {
                                //tts job消息处理
                                final String jobId = root.get("JobId").asText();
                                //qyclRingService.aliTtsJobFinishHandle(jobId);
                                // TODO: 2023/7/19 处理铃音diy语音合成回执
                            } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                                //视频合成job消息处理
                                final JsonNode jobDetail = root.get("MessageBody");
                                final String jobId = jobDetail.get("JobId").asText();
                                final String mediaURL = jobDetail.get("MediaURL").asText();
                                vrbtDiyRingService.aliVideoJobFinishHandle(jobId, mediaURL);

                                //qyclRingService.aliVideoJobFinishHandle(jobId, mediaURL);
                                // TODO: 2023/7/19 处理铃音diy视频合成回执
                            } else {
                                log.info("{}-未知类型的消息diy,不作处理!,body:{}", LOG_TAG, body);
                            }
                            //remember to  delete message when consume message successfully.
                            queue.deleteMessage(popMsg.getReceiptHandle());
                            //System.out.println("delete message successfully.\n");
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            } catch (ClientException ce) {
                log.info("{}-Something wrong with the network connection between client and MNS service." + "Please check your network and DNS availablity.", LOG_TAG, ce);
            } catch (ServiceException se) {
             /*
            you can get more MNS service error code in following link.
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html?spm=5176.docmns/api_reference/error_code/error_response
            */
                if ("QueueNotExist".equals(se.getErrorCode())) {
                    log.info("{}-Queue is not exist.Please create queue before use", LOG_TAG, se);
                } else if ("TimeExpired".equals(se.getErrorCode())) {
                    log.info("{}-The request is time expired. Please check your local machine timeclock", LOG_TAG, se);
                }
            } catch (Exception e) {
                log.info("{}-Unknown exception happened!", LOG_TAG, e);
            }

        };
    }

    public Runnable manualListener() {
        //需要客服手动设置铃音的
        return () -> {
            log.info("{}-===============轮询启动-客服铃音==============", LOG_TAG);
            try {
                CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackManualMnsQueue());// replace with your queue name
                //阿里云MNS已设置10秒的长轮询,即10秒未拿到消息服务端会阻塞,不会一直快速轮询
                while (true) {
                    try {
                        Message popMsg = queue.popMessage();
                        if (popMsg != null) {
                            //message handle: 7CDE60DCE2D0737D610C64DAA802A059-MCAxNjc5MjkyNTI3NzcwIDIwMDAwIDAgMSBiam1xbmluZTRzbXEtMDEgMCAw
                            //message body: {"Code":"Success","FunctionName":"AsyncTextToSpeech","JobId":"84c4c569b7a84440bc96db0c974a2261","Message":"Successful.","State":"Success","Type":"IProduction"}
                            //message id: 7CDE60DCE2D0737D610C64DAA802A059
                            //message dequeue count:2

                            //message handle: 7CDE60DCE2D0737D410064DAF96C5BB3-MCAxNjc5MjkyNTI4MDQ0IDIwMDAwIDYgMSBiam1xbmluZTRzbXEtMDMgMCAw
                            //message body: {"EventType":"ProduceMediaComplete","UserId":1431075016168616,"EventTime":"2023-03-20T06:01:12Z","MessageBody":{"Status":"Success","MediaURL":"https://ims-media.oss-cn-beijing.aliyuncs.com/d673803481a3cd07de4bc5113e81eca4.mp4","MediaId":"91b51400c6e471ed828be6f6f6796302","EventType":"ProduceMediaComplete","UserData":"{\"NotifyAddress\":\"ice-callback-queue-ice-mns-job\"}","EventTime":"2023-03-20T06:01:12Z","ProjectId":"0d9a7a6a429b41268435ec9e6702c7fd","ErrorCode":"","ErrorMessage":"","JobId":"4cbdf3433877432d89c1efff21f1bede"}}
                            //message id: 7CDE60DCE2D0737D410064DAF96C5BB3
                            //message dequeue count:2
                            final String body = popMsg.getMessageBodyAsString();
                            log.info("{}-收到消息,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);
                            //System.out.println("message handle: " + popMsg.getReceiptHandle());
                            //System.out.println("message body: " + body);
                            //System.out.println("message id: " + popMsg.getMessageId());
                            //System.out.println("message dequeue count:" + popMsg.getDequeueCount());
                            //<<to add your special logic.>>
                            final JsonNode root = mapper.readTree(body);
                            if ("IProduction".equals(root.at("/Type").asText())) {
                                //tts job消息处理
                                final String jobId = root.get("JobId").asText();
                                qyclCustomerServiceRingService.aliTtsJobFinishHandle(jobId);
                            } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                                //视频合成job消息处理
                                final JsonNode jobDetail = root.get("MessageBody");
                                final String jobId = jobDetail.get("JobId").asText();
                                final String mediaURL = jobDetail.get("MediaURL").asText();
                                qyclCustomerServiceRingService.aliVideoJobFinishHandle(jobId, mediaURL);
                            } else {
                                log.info("{}-未知类型的消息,不作处理!,body:{}", LOG_TAG, body);
                            }
                            //remember to  delete message when consume message successfully.
                            queue.deleteMessage(popMsg.getReceiptHandle());
                            //System.out.println("delete message successfully.\n");
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            } catch (ClientException ce) {
                log.info("{}-Something wrong with the network connection between client and MNS service." + "Please check your network and DNS availablity.", LOG_TAG, ce);
            } catch (ServiceException se) {
             /*
            you can get more MNS service error code in following link.
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html?spm=5176.docmns/api_reference/error_code/error_response
            */
                if ("QueueNotExist".equals(se.getErrorCode())) {
                    log.info("{}-Queue is not exist.Please create queue before use", LOG_TAG, se);
                } else if ("TimeExpired".equals(se.getErrorCode())) {
                    log.info("{}-The request is time expired. Please check your local machine timeclock", LOG_TAG, se);
                }
            } catch (Exception e) {
                log.info("{}-Unknown exception happened!", LOG_TAG, e);
            }

        };
    }

    public Runnable yinglouListener() {
        //影楼圈子彩铃铃音制作
        return () -> {
            log.info("{}-===============轮询启动-影楼==============", LOG_TAG);
            try {
                CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackYinglouMnsQueue());// replace with your queue name
                //阿里云MNS已设置10秒的长轮询,即10秒未拿到消息服务端会阻塞,不会一直快速轮询
                while (true) {
                    try {
                        Message popMsg = queue.popMessage();
                        if (popMsg != null) {
                            //message handle: 7CDE60DCE2D0737D610C64DAA802A059-MCAxNjc5MjkyNTI3NzcwIDIwMDAwIDAgMSBiam1xbmluZTRzbXEtMDEgMCAw
                            //message body: {"Code":"Success","FunctionName":"AsyncTextToSpeech","JobId":"84c4c569b7a84440bc96db0c974a2261","Message":"Successful.","State":"Success","Type":"IProduction"}
                            //message id: 7CDE60DCE2D0737D610C64DAA802A059
                            //message dequeue count:2

                            //message handle: 7CDE60DCE2D0737D410064DAF96C5BB3-MCAxNjc5MjkyNTI4MDQ0IDIwMDAwIDYgMSBiam1xbmluZTRzbXEtMDMgMCAw
                            //message body: {"EventType":"ProduceMediaComplete","UserId":1431075016168616,"EventTime":"2023-03-20T06:01:12Z","MessageBody":{"Status":"Success","MediaURL":"https://ims-media.oss-cn-beijing.aliyuncs.com/d673803481a3cd07de4bc5113e81eca4.mp4","MediaId":"91b51400c6e471ed828be6f6f6796302","EventType":"ProduceMediaComplete","UserData":"{\"NotifyAddress\":\"ice-callback-queue-ice-mns-job\"}","EventTime":"2023-03-20T06:01:12Z","ProjectId":"0d9a7a6a429b41268435ec9e6702c7fd","ErrorCode":"","ErrorMessage":"","JobId":"4cbdf3433877432d89c1efff21f1bede"}}
                            //message id: 7CDE60DCE2D0737D410064DAF96C5BB3
                            //message dequeue count:2
                            final String body = popMsg.getMessageBodyAsString();
                            log.info("{}-收到消息,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);
                            //System.out.println("message handle: " + popMsg.getReceiptHandle());
                            //System.out.println("message body: " + body);
                            //System.out.println("message id: " + popMsg.getMessageId());
                            //System.out.println("message dequeue count:" + popMsg.getDequeueCount());
                            //<<to add your special logic.>>
                            final JsonNode root = mapper.readTree(body);
                            if ("IProduction".equals(root.at("/Type").asText())) {
                                //tts job消息处理
                                final String jobId = root.get("JobId").asText();
                                //qyclRingService.aliTtsJobFinishHandle(jobId);
                                log.info("{}-收到tts完成消息,jobId:{}", LOG_TAG, jobId);
                            } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                                //视频合成job消息处理
                                final JsonNode jobDetail = root.get("MessageBody");
                                final String jobId = jobDetail.get("JobId").asText();
                                final String mediaURL = jobDetail.get("MediaURL").asText();
                                log.info("{}-收到视频合成完成消息,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);
                                yinglouRingService.aliRingUpload(jobId, mediaURL);


                            } else {
                                log.info("{}-未知类型的消息,不作处理!,body:{}", LOG_TAG, body);
                            }
                            //remember to  delete message when consume message successfully.
                            queue.deleteMessage(popMsg.getReceiptHandle());
                            //System.out.println("delete message successfully.\n");
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            } catch (ClientException ce) {
                log.info("{}-Something wrong with the network connection between client and MNS service." + "Please check your network and DNS availablity.", LOG_TAG, ce);
            } catch (ServiceException se) {
             /*
            you can get more MNS service error code in following link.
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html?spm=5176.docmns/api_reference/error_code/error_response
            */
                if ("QueueNotExist".equals(se.getErrorCode())) {
                    log.info("{}-Queue is not exist.Please create queue before use", LOG_TAG, se);
                } else if ("TimeExpired".equals(se.getErrorCode())) {
                    log.info("{}-The request is time expired. Please check your local machine timeclock", LOG_TAG, se);
                }
            } catch (Exception e) {
                log.info("{}-Unknown exception happened!", LOG_TAG, e);
            }

        };
    }

    /**
     * 监听海艺铃音合成
     */
    public Runnable hyListener() {
        return () -> {
            log.info("{}-===============轮询启动-海艺==============", LOG_TAG);
            try {
                CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackHYAIMnsQueue());// replace with your queue name
                //阿里云MNS已设置10秒的长轮询,即10秒未拿到消息服务端会阻塞,不会一直快速轮询
                while (true) {
                    try {
                        Message popMsg = queue.popMessage();
                        if (popMsg != null) {
                            //message handle: 7CDE60DCE2D0737D610C64DAA802A059-MCAxNjc5MjkyNTI3NzcwIDIwMDAwIDAgMSBiam1xbmluZTRzbXEtMDEgMCAw
                            //message body: {"Code":"Success","FunctionName":"AsyncTextToSpeech","JobId":"84c4c569b7a84440bc96db0c974a2261","Message":"Successful.","State":"Success","Type":"IProduction"}
                            //message id: 7CDE60DCE2D0737D610C64DAA802A059
                            //message dequeue count:2

                            //message handle: 7CDE60DCE2D0737D410064DAF96C5BB3-MCAxNjc5MjkyNTI4MDQ0IDIwMDAwIDYgMSBiam1xbmluZTRzbXEtMDMgMCAw
                            //message body: {"EventType":"ProduceMediaComplete","UserId":1431075016168616,"EventTime":"2023-03-20T06:01:12Z","MessageBody":{"Status":"Success","MediaURL":"https://ims-media.oss-cn-beijing.aliyuncs.com/d673803481a3cd07de4bc5113e81eca4.mp4","MediaId":"91b51400c6e471ed828be6f6f6796302","EventType":"ProduceMediaComplete","UserData":"{\"NotifyAddress\":\"ice-callback-queue-ice-mns-job\"}","EventTime":"2023-03-20T06:01:12Z","ProjectId":"0d9a7a6a429b41268435ec9e6702c7fd","ErrorCode":"","ErrorMessage":"","JobId":"4cbdf3433877432d89c1efff21f1bede"}}
                            //message id: 7CDE60DCE2D0737D410064DAF96C5BB3
                            //message dequeue count:2
                            final String body = popMsg.getMessageBodyAsString();
                            log.info("{}-收到消息,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);
                            //System.out.println("message handle: " + popMsg.getReceiptHandle());
                            //System.out.println("message body: " + body);
                            //System.out.println("message id: " + popMsg.getMessageId());
                            //System.out.println("message dequeue count:" + popMsg.getDequeueCount());
                            //<<to add your special logic.>>
                            final JsonNode root = mapper.readTree(body);
                            if ("IProduction".equals(root.at("/Type").asText())) {
                                //tts job消息处理
                                final String jobId = root.get("JobId").asText();
                                //qyclRingService.aliTtsJobFinishHandle(jobId);
                                log.info("{}-收到tts完成消息,jobId:{}", LOG_TAG, jobId);
                            } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                                //视频合成job消息处理
                                final JsonNode jobDetail = root.get("MessageBody");
                                final String jobId = jobDetail.get("JobId").asText();
                                final String mediaURL = jobDetail.get("MediaURL").asText();
                                log.info("{}-收到视频合成完成消息,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);

                                boolean flag = aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                                        .set(AiRing::getRingUrl, mediaURL)
                                        .set(AiRing::getRingMakeStatus, 1)
                                        .eq(AiRing::getJobId, jobId));
                                if (flag) {
                                    log.info("{}-更新ai_ring成功,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);
                                } else {
                                    log.error("{}-更新ai_ring失败,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);
                                }
                            } else {
                                log.info("{}-未知类型的消息,不作处理!,body:{}", LOG_TAG, body);
                            }
                            //remember to  delete message when consume message successfully.
                            queue.deleteMessage(popMsg.getReceiptHandle());
                            //System.out.println("delete message successfully.\n");
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            } catch (ClientException ce) {
                log.info("{}-Something wrong with the network connection between client and MNS service." + "Please check your network and DNS availablity.", LOG_TAG, ce);
            } catch (ServiceException se) {
             /*
            you can get more MNS service error code in following link.
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html?spm=5176.docmns/api_reference/error_code/error_response
            */
                if ("QueueNotExist".equals(se.getErrorCode())) {
                    log.info("{}-Queue is not exist.Please create queue before use", LOG_TAG, se);
                } else if ("TimeExpired".equals(se.getErrorCode())) {
                    log.info("{}-The request is time expired. Please check your local machine timeclock", LOG_TAG, se);
                }
            } catch (Exception e) {
                log.info("{}-Unknown exception happened!", LOG_TAG, e);
            }
        };
    }

    /**
     * 监听腾讯云换脸铃音合成
     */
    public Runnable txAiFaceListener() {
        return () -> {
            log.info("{}-===============轮询启动-腾讯ai换脸==============", LOG_TAG);
            try {
                CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackTxAiFaceMnsQueue());// replace with your queue name
                //阿里云MNS已设置10秒的长轮询,即10秒未拿到消息服务端会阻塞,不会一直快速轮询
                while (true) {
                    try {
                        Message popMsg = queue.popMessage();
                        if (popMsg != null) {
                            //message handle: 7CDE60DCE2D0737D610C64DAA802A059-MCAxNjc5MjkyNTI3NzcwIDIwMDAwIDAgMSBiam1xbmluZTRzbXEtMDEgMCAw
                            //message body: {"Code":"Success","FunctionName":"AsyncTextToSpeech","JobId":"84c4c569b7a84440bc96db0c974a2261","Message":"Successful.","State":"Success","Type":"IProduction"}
                            //message id: 7CDE60DCE2D0737D610C64DAA802A059
                            //message dequeue count:2

                            //message handle: 7CDE60DCE2D0737D410064DAF96C5BB3-MCAxNjc5MjkyNTI4MDQ0IDIwMDAwIDYgMSBiam1xbmluZTRzbXEtMDMgMCAw
                            //message body: {"EventType":"ProduceMediaComplete","UserId":1431075016168616,"EventTime":"2023-03-20T06:01:12Z","MessageBody":{"Status":"Success","MediaURL":"https://ims-media.oss-cn-beijing.aliyuncs.com/d673803481a3cd07de4bc5113e81eca4.mp4","MediaId":"91b51400c6e471ed828be6f6f6796302","EventType":"ProduceMediaComplete","UserData":"{\"NotifyAddress\":\"ice-callback-queue-ice-mns-job\"}","EventTime":"2023-03-20T06:01:12Z","ProjectId":"0d9a7a6a429b41268435ec9e6702c7fd","ErrorCode":"","ErrorMessage":"","JobId":"4cbdf3433877432d89c1efff21f1bede"}}
                            //message id: 7CDE60DCE2D0737D410064DAF96C5BB3
                            //message dequeue count:2
                            final String body = popMsg.getMessageBodyAsString();
                            log.info("{}-收到消息,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);
                            //System.out.println("message handle: " + popMsg.getReceiptHandle());
                            //System.out.println("message body: " + body);
                            //System.out.println("message id: " + popMsg.getMessageId());
                            //System.out.println("message dequeue count:" + popMsg.getDequeueCount());
                            //<<to add your special logic.>>
                            final JsonNode root = mapper.readTree(body);
                            if ("IProduction".equals(root.at("/Type").asText())) {
                                //tts job消息处理
                                final String jobId = root.get("JobId").asText();
                                //qyclRingService.aliTtsJobFinishHandle(jobId);
                                log.info("{}-收到tts完成消息,jobId:{}", LOG_TAG, jobId);
                            } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                                //视频合成job消息处理
                                final JsonNode jobDetail = root.get("MessageBody");
                                final String jobId = jobDetail.get("JobId").asText();
                                final String mediaURL = jobDetail.get("MediaURL").asText();
                                log.info("{}-收到视频合成完成消息,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);

                                boolean flag = aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                                        .set(AiRing::getRingUrl, mediaURL)
                                        .set(AiRing::getRingMakeStatus, 1)
                                        .eq(AiRing::getJobId, jobId));
                                if (flag) {
                                    log.info("{}-更新ai_ring成功,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);
                                } else {
                                    log.error("{}-更新ai_ring失败,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);
                                }
                            } else {
                                log.info("{}-未知类型的消息,不作处理!,body:{}", LOG_TAG, body);
                            }
                            //remember to  delete message when consume message successfully.
                            queue.deleteMessage(popMsg.getReceiptHandle());
                            //System.out.println("delete message successfully.\n");
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            } catch (ClientException ce) {
                log.info("{}-Something wrong with the network connection between client and MNS service." + "Please check your network and DNS availablity.", LOG_TAG, ce);
            } catch (ServiceException se) {
             /*
            you can get more MNS service error code in following link.
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html?spm=5176.docmns/api_reference/error_code/error_response
            */
                if ("QueueNotExist".equals(se.getErrorCode())) {
                    log.info("{}-Queue is not exist.Please create queue before use", LOG_TAG, se);
                } else if ("TimeExpired".equals(se.getErrorCode())) {
                    log.info("{}-The request is time expired. Please check your local machine timeclock", LOG_TAG, se);
                }
            } catch (Exception e) {
                log.info("{}-Unknown exception happened!", LOG_TAG, e);
            }
        };
    }

    /**
     * 监听腾讯云换脸铃音合成
     */
    public Runnable txAiAcrossFaceListener() {
        return () -> {
            log.info("{}-===============轮询启动-腾讯ai穿越换脸==============", LOG_TAG);
            try {
                CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackTxAiAcrossFaceMnsQueue());// replace with your queue name
                //阿里云MNS已设置10秒的长轮询,即10秒未拿到消息服务端会阻塞,不会一直快速轮询
                while (true) {
                    try {
                        Message popMsg = queue.popMessage();
                        if (popMsg != null) {
                            //message handle: 7CDE60DCE2D0737D610C64DAA802A059-MCAxNjc5MjkyNTI3NzcwIDIwMDAwIDAgMSBiam1xbmluZTRzbXEtMDEgMCAw
                            //message body: {"Code":"Success","FunctionName":"AsyncTextToSpeech","JobId":"84c4c569b7a84440bc96db0c974a2261","Message":"Successful.","State":"Success","Type":"IProduction"}
                            //message id: 7CDE60DCE2D0737D610C64DAA802A059
                            //message dequeue count:2

                            //message handle: 7CDE60DCE2D0737D410064DAF96C5BB3-MCAxNjc5MjkyNTI4MDQ0IDIwMDAwIDYgMSBiam1xbmluZTRzbXEtMDMgMCAw
                            //message body: {"EventType":"ProduceMediaComplete","UserId":1431075016168616,"EventTime":"2023-03-20T06:01:12Z","MessageBody":{"Status":"Success","MediaURL":"https://ims-media.oss-cn-beijing.aliyuncs.com/d673803481a3cd07de4bc5113e81eca4.mp4","MediaId":"91b51400c6e471ed828be6f6f6796302","EventType":"ProduceMediaComplete","UserData":"{\"NotifyAddress\":\"ice-callback-queue-ice-mns-job\"}","EventTime":"2023-03-20T06:01:12Z","ProjectId":"0d9a7a6a429b41268435ec9e6702c7fd","ErrorCode":"","ErrorMessage":"","JobId":"4cbdf3433877432d89c1efff21f1bede"}}
                            //message id: 7CDE60DCE2D0737D410064DAF96C5BB3
                            //message dequeue count:2
                            final String body = popMsg.getMessageBodyAsString();
                            log.info("{}-收到消息,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);
                            //System.out.println("message handle: " + popMsg.getReceiptHandle());
                            //System.out.println("message body: " + body);
                            //System.out.println("message id: " + popMsg.getMessageId());
                            //System.out.println("message dequeue count:" + popMsg.getDequeueCount());
                            //<<to add your special logic.>>
                            final JsonNode root = mapper.readTree(body);
                            if ("IProduction".equals(root.at("/Type").asText())) {
                                //tts job消息处理
                                final String jobId = root.get("JobId").asText();
                                //qyclRingService.aliTtsJobFinishHandle(jobId);
                                log.info("{}-收到tts完成消息,jobId:{}", LOG_TAG, jobId);
                            } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                                //视频合成job消息处理
                                final JsonNode jobDetail = root.get("MessageBody");
                                final String jobId = jobDetail.get("JobId").asText();
                                final String mediaURL = jobDetail.get("MediaURL").asText();
                                log.info("{}-收到视频合成完成消息,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);

                                    String businessForm = (String) redisUtil.get("cms:cache:aiunion:jobBusiness:" + jobId);
                                    if (StrUtil.isNotBlank(businessForm)) {
                                        String recordId = Optional.ofNullable(kpAiUnionRecordService.getOne(new LambdaQueryWrapper<KpAiUnionRecord>()
                                                .select(KpAiUnionRecord::getId)
                                                .eq(KpAiUnionRecord::getTaskId, jobId))).map(e -> e.getId()).orElse(null);
                                        if (StrUtil.isNotBlank(recordId)) {
                                            kpAiUnionRecordService.update(new LambdaUpdateWrapper<KpAiUnionRecord>()
                                                    .set(KpAiUnionRecord::getVideoUrl, mediaURL)
                                                    .eq(KpAiUnionRecord::getId, recordId));
                                            kpAiUnionSecurity(recordId, mediaURL);
                                            redisUtil.del("cms:cache:aiunion:jobBusiness:" + jobId);
                                        }else{
                                            log.warn("mns队列中jobid：{},未找到,忽略处理",jobId);
                                        }
                                    } else {
                                        String recordId = Optional.ofNullable(aiUnionRecordService.getOne(new LambdaQueryWrapper<AiUnionRecord>()
                                                .select(AiUnionRecord::getId)
                                                .eq(AiUnionRecord::getTaskId, jobId))).map(e -> e.getId()).orElse(null);
                                        if (StrUtil.isNotBlank(recordId)) {
                                            aiUnionRecordService.update(new LambdaUpdateWrapper<AiUnionRecord>()
                                                    .set(AiUnionRecord::getVideoUrl, mediaURL)
                                                    .eq(AiUnionRecord::getId, recordId));
                                            aiUnionSecurity(recordId, mediaURL);
                                        }else{
                                            log.warn("mns队列中jobid：{},未找到,忽略处理",jobId);
                                        }

                                    }

                            } else {
                                log.info("{}-未知类型的消息,不作处理!,body:{}", LOG_TAG, body);
                            }
                            //remember to  delete message when consume message successfully.
                            queue.deleteMessage(popMsg.getReceiptHandle());
                            //System.out.println("delete message successfully.\n");
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            } catch (ClientException ce) {
                log.info("{}-Something wrong with the network connection between client and MNS service." + "Please check your network and DNS availablity.", LOG_TAG, ce);
            } catch (ServiceException se) {
             /*
            you can get more MNS service error code in following link.
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html?spm=5176.docmns/api_reference/error_code/error_response
            */
                if ("QueueNotExist".equals(se.getErrorCode())) {
                    log.info("{}-Queue is not exist.Please create queue before use", LOG_TAG, se);
                } else if ("TimeExpired".equals(se.getErrorCode())) {
                    log.info("{}-The request is time expired. Please check your local machine timeclock", LOG_TAG, se);
                }
            } catch (Exception e) {
                log.info("{}-Unknown exception happened!", LOG_TAG, e);
            }
        };
    }

    private void aiUnionSecurity(String recordId, String mediaURL) throws InterruptedException {
        // 数据送审并异步获取通知 通知接口/ytcyNotify
        String dataId = "";
        String result = MiguSecurityUtils.pushDataCheck(1, mediaURL);
        JsonNode jsonNode = JacksonUtils.readTree(result);
        if ("000000".equals(jsonNode.at("/code").asText())) {
            dataId = jsonNode.at("/data").get(0).at("/dataId").asText();
            String key = CommonSecurityService.PREFIX_KEY + dataId;
            redisUtil.set(key, 0, 600);
            redisUtil.hset(CommonSecurityService.PREFIX_KEY+"pushed",dataId,recordId);
        } else {
            aiUnionRecordService.update(new LambdaUpdateWrapper<AiUnionRecord>()
                    .set(AiUnionRecord::getVideoStatus, -1)
                    .eq(AiUnionRecord::getId, recordId));
            aiUnionService.handleMiguTaskResult(recordId, false, mediaURL);
            log.error("咪咕安审-数据送审响应失败,recordId:{},jsonNode:{}", recordId, jsonNode);
        }
        // 查询送审结果
    }

    private void kpAiUnionSecurity(String recordId, String mediaURL) throws InterruptedException {
        // 数据送审并异步获取通知 通知接口/ytcyNotify
        String dataId = "";
        String result = MiguSecurityUtils.pushDataCheck(1, mediaURL);
        JsonNode jsonNode = JacksonUtils.readTree(result);
        if ("000000".equals(jsonNode.at("/code").asText())) {
            dataId = jsonNode.at("/data").get(0).at("/dataId").asText();
            String key = CommonSecurityService.PREFIX_KEY + dataId;
            redisUtil.set(key, 0, 600);
            redisUtil.hset(CommonSecurityService.PREFIX_KEY+"pushed:kp",dataId,recordId);
        } else {
            kpAiUnionRecordService.update(new LambdaUpdateWrapper<KpAiUnionRecord>()
                    .set(KpAiUnionRecord::getVideoStatus,  -1)
                    .eq(KpAiUnionRecord::getId, recordId));
            log.error("咪咕安审-数据送审响应失败,jobId:{},jsonNode:{}", recordId, jsonNode);
        }
        // 查询送审结果
    }

    /**
     * 监听腾讯图片风格化铃声合成
     */
    public Runnable txAiListener() {
        return () -> {
            log.info("{}-===============轮询启动tx==============", LOG_TAG);
            try {
                CloudQueue queue = client.getQueueRef(aliMediaProperties.getJobCallbackTxaiMnsQueue());// replace with your queue name
                //阿里云MNS已设置10秒的长轮询,即10秒未拿到消息服务端会阻塞,不会一直快速轮询
                while (true) {
                    try {
                        Message popMsg = queue.popMessage();
                        if (popMsg != null) {
                            //message handle: 7CDE60DCE2D0737D610C64DAA802A059-MCAxNjc5MjkyNTI3NzcwIDIwMDAwIDAgMSBiam1xbmluZTRzbXEtMDEgMCAw
                            //message body: {"Code":"Success","FunctionName":"AsyncTextToSpeech","JobId":"84c4c569b7a84440bc96db0c974a2261","Message":"Successful.","State":"Success","Type":"IProduction"}
                            //message id: 7CDE60DCE2D0737D610C64DAA802A059
                            //message dequeue count:2

                            //message handle: 7CDE60DCE2D0737D410064DAF96C5BB3-MCAxNjc5MjkyNTI4MDQ0IDIwMDAwIDYgMSBiam1xbmluZTRzbXEtMDMgMCAw
                            //message body: {"EventType":"ProduceMediaComplete","UserId":1431075016168616,"EventTime":"2023-03-20T06:01:12Z","MessageBody":{"Status":"Success","MediaURL":"https://ims-media.oss-cn-beijing.aliyuncs.com/d673803481a3cd07de4bc5113e81eca4.mp4","MediaId":"91b51400c6e471ed828be6f6f6796302","EventType":"ProduceMediaComplete","UserData":"{\"NotifyAddress\":\"ice-callback-queue-ice-mns-job\"}","EventTime":"2023-03-20T06:01:12Z","ProjectId":"0d9a7a6a429b41268435ec9e6702c7fd","ErrorCode":"","ErrorMessage":"","JobId":"4cbdf3433877432d89c1efff21f1bede"}}
                            //message id: 7CDE60DCE2D0737D410064DAF96C5BB3
                            //message dequeue count:2
                            final String body = popMsg.getMessageBodyAsString();
                            log.info("{}-收到消息,id:{},dequeue count:{},body:{}", LOG_TAG, popMsg.getMessageId(), popMsg.getDequeueCount(), body);
                            //System.out.println("message handle: " + popMsg.getReceiptHandle());
                            //System.out.println("message body: " + body);
                            //System.out.println("message id: " + popMsg.getMessageId());
                            //System.out.println("message dequeue count:" + popMsg.getDequeueCount());
                            //<<to add your special logic.>>
                            final JsonNode root = mapper.readTree(body);
                            if ("IProduction".equals(root.at("/Type").asText())) {
                                //tts job消息处理
                                final String jobId = root.get("JobId").asText();
                                //qyclRingService.aliTtsJobFinishHandle(jobId);
                                log.info("{}-收到tts完成消息,jobId:{}", LOG_TAG, jobId);
                            } else if ("ProduceMediaComplete".equals(root.at("/EventType").asText())) {
                                //视频合成job消息处理
                                final JsonNode jobDetail = root.get("MessageBody");
                                final String jobId = jobDetail.get("JobId").asText();
                                final String mediaURL = jobDetail.get("MediaURL").asText();
                                zhmbRingService.aliJobFinishHandle(jobId, mediaURL);
                                log.info("{}-收到视频合成完成消息,jobId:{},mediaURL:{}", LOG_TAG, jobId, mediaURL);
                            } else {
                                log.info("{}-未知类型的消息,不作处理!,body:{}", LOG_TAG, body);
                            }
                            //remember to  delete message when consume message successfully.
                            queue.deleteMessage(popMsg.getReceiptHandle());
                            //System.out.println("delete message successfully.\n");
                        }
                    } catch (Exception e) {
                        log.info("{}-消息处理异常!", LOG_TAG, e);
                    }
                }
            } catch (ClientException ce) {
                log.info("{}-Something wrong with the network connection between client and MNS service." + "Please check your network and DNS availablity.", LOG_TAG, ce);
            } catch (ServiceException se) {
             /*
            you can get more MNS service error code in following link.
            https://help.aliyun.com/document_detail/mns/api_reference/error_code/error_code.html?spm=5176.docmns/api_reference/error_code/error_response
            */
                if ("QueueNotExist".equals(se.getErrorCode())) {
                    log.info("{}-Queue is not exist.Please create queue before use", LOG_TAG, se);
                } else if ("TimeExpired".equals(se.getErrorCode())) {
                    log.info("{}-The request is time expired. Please check your local machine timeclock", LOG_TAG, se);
                }
            } catch (Exception e) {
                log.info("{}-Unknown exception happened!", LOG_TAG, e);
            }
        };
    }

    public static void main(String[] args) throws JsonProcessingException {
        String json = "{\n" +
                "\t\"EventType\": \"ProduceMediaComplete\",\n" +
                "\t\"UserId\": 1431075016168616,\n" +
                "\t\"EventTime\": \"2023-03-31T02:52:19Z\",\n" +
                "\t\"MessageBody\": {\n" +
                "\t\t\"Status\": \"Success\",\n" +
                "\t\t\"MediaURL\": \"https://ims-media.oss-cn-beijing.aliyuncs.com/output/20230331/d16afc0dc7e2cf8d34520b9c8b982ebe.mp4\",\n" +
                "\t\t\"MediaId\": \"fd04b610cf6e71ed9a81e7e7c45b6302\",\n" +
                "\t\t\"EventType\": \"ProduceMediaComplete\",\n" +
                "\t\t\"UserData\": \"{\\\"NotifyAddress\\\":\\\"ice-callback-queue-job\\\"}\",\n" +
                "\t\t\"EventTime\": \"2023-03-31T02:52:19Z\",\n" +
                "\t\t\"ProjectId\": \"d36efffb13e340419c2ca3d1b3401017\",\n" +
                "\t\t\"ErrorCode\": \"\",\n" +
                "\t\t\"ErrorMessage\": \"\",\n" +
                "\t\t\"JobId\": \"1342b0ecdd22455b9037b98020906dd5\"\n" +
                "\t}\n" +
                "}";
        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        final JsonNode root = objectMapper.readTree(json);
        System.out.println("IProduction".equals(root.at("/Type").asText()));
    }
}
