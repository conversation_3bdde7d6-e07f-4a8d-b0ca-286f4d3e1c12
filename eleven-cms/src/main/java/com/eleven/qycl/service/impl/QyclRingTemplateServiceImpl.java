package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiFaceTemplate;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiTemplate;
import com.eleven.cms.aivrbt.service.IAiRingColumnAiFaceTemplateService;
import com.eleven.cms.aivrbt.service.IAiRingColumnAiTemplateService;
import com.eleven.qycl.entity.QyclRingTemplate;
import com.eleven.qycl.mapper.QyclRingTemplateMapper;
import com.eleven.qycl.service.AliMediaService;
import com.eleven.qycl.service.IQyclRingTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;

/**
 *
 */
@Service
@Slf4j
public class QyclRingTemplateServiceImpl extends ServiceImpl<QyclRingTemplateMapper, QyclRingTemplate> implements IQyclRingTemplateService {
    @Autowired
    AliMediaService aliMediaService;
    @Autowired
    private IAiRingColumnAiTemplateService aiRingColumnAiTemplateService;
    @Autowired
    private IAiRingColumnAiFaceTemplateService aiFaceTemplateService;

    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_COMMON, key = "'ringTemplate:'+ #root.methodName + ':' + #p0", unless = "#result==null")
    @Override
    public IPage<QyclRingTemplate> ringTemplateListByColumnId(IPage<QyclRingTemplate> page, String columnId) {
        return this.lambdaQuery().eq(QyclRingTemplate::getColumnId, columnId).eq(QyclRingTemplate::getStatus,
                1).orderByAsc(QyclRingTemplate::getOrderBy).page(page);
    }

    /**
     * 从NAS放置模板的共享文件夹创建模板,创建成功后会在该文件夹下产生模板信息json文件(跑的时候测试环境接正式库)
     *
     * @param templateContainerPath
     */
    @Override
    public void generateTemplate(Path templateContainerPath) {
        //final Path templateContainerPath = Paths.get("\\\\*************\\鸿盛天极\\视频彩铃-AE模板");
        try {
            //Files.find(templateContainerPath, 1, (path, basicFileAttributes) -> {
            Files.find(templateContainerPath, 3, (path, basicFileAttributes) -> {
                if (!basicFileAttributes.isDirectory()) {
                    return false;
                }
                //if (path.equals(templateContainerPath)) {
                //    return false;
                //}
                return path.toFile().listFiles((dir, name) -> name.toLowerCase().endsWith(".zip")).length > 0 && path.toFile().listFiles((dir, name) -> name.toLowerCase().endsWith(".json")).length == 0;
            }).forEach(path -> {
                log.info("准备创建模板的文件夹:{}", path);
                final File[] files = path.toFile().listFiles();
                File previewMp4File = null;
                File templateZipFile = null;
                for (int i = 0; i < files.length; i++) {
                    File file = files[i];
                    if (file.getName().toLowerCase().endsWith(".mp4")) {
                        previewMp4File = file;
                    } else if (file.getName().toLowerCase().endsWith(".zip")) {
                        templateZipFile = file;
                    }
                }
                if (previewMp4File != null && templateZipFile != null) {
                    final String templateName = FilenameUtils.removeExtension(previewMp4File.getName());
                    log.info("创建模板=>名称:{},模板zip文件大小:{}M,预览视频文件大小:{}M", templateName, templateZipFile.length() / 1024 / 1024, previewMp4File.length() / 1024 / 1024);
                    try {
                        final String templateId = aliMediaService.createTemplate(templateName, previewMp4File, templateZipFile);
                        final String templateInfo = aliMediaService.waitTemplateCreateAndGenJsonFile(path, templateId);
                        QyclRingTemplate qyclRingTemplate = new QyclRingTemplate();
                        qyclRingTemplate.setTemplateId(templateId);
                        qyclRingTemplate.setRingName(templateName);
                        qyclRingTemplate.setClipsParam(templateInfo);
                        qyclRingTemplate.setStatus(0);
                        qyclRingTemplate.setColumnId("101");
                        qyclRingTemplate.setColumnName("模板铃音");
                        qyclRingTemplate.setOrderBy(99);
                        qyclRingTemplate.setRemark(path.toString());
                        qyclRingTemplate.setCreateTime(new Date());
                        this.save(qyclRingTemplate);
                    } catch (Exception e) {
                        log.info("创建模板的文件夹:{},创建模板异常:", path.getFileName(), e);
                    }
                } else {
                    log.info("文件夹未完整包含模板和mp4:{}", path);
                }
            });

        } catch (IOException e) {
            log.info("从NAS共享文件生成模板信息异常:", e);
        }
    }

    /**
     * 从NAS放置模板的共享文件夹创建模板,创建成功后会在该文件夹下产生模板信息json文件(跑的时候测试环境接正式库)
     *
     * @param templateContainerPath
     */
    @Override
    public void generateAITemplate(Path templateContainerPath) {
        //final Path templateContainerPath = Paths.get("\\\\*************\\鸿盛天极\\视频彩铃-AE模板");
        try {
            //Files.find(templateContainerPath, 1, (path, basicFileAttributes) -> {
            Files.find(templateContainerPath, 3, (path, basicFileAttributes) -> {
                if (!basicFileAttributes.isDirectory()) {
                    return false;
                }
                //if (path.equals(templateContainerPath)) {
                //    return false;
                //}
                return path.toFile().listFiles((dir, name) -> name.toLowerCase().endsWith(".zip")).length > 0 && path.toFile().listFiles((dir, name) -> name.toLowerCase().endsWith(".json")).length == 0;
            }).forEach(path -> {
                log.info("准备创建模板的文件夹:{}", path);
                final File[] files = path.toFile().listFiles();
                File previewMp4File = null;
                File templateZipFile = null;
                for (int i = 0; i < files.length; i++) {
                    File file = files[i];
                    if (file.getName().toLowerCase().endsWith(".mp4")) {
                        previewMp4File = file;
                    } else if (file.getName().toLowerCase().endsWith(".zip")) {
                        templateZipFile = file;
                    }
                }
                if (previewMp4File != null && templateZipFile != null) {
                    final String templateName = FilenameUtils.removeExtension(previewMp4File.getName());
                    log.info("创建模板=>名称:{},模板zip文件大小:{}M,预览视频文件大小:{}M", templateName, templateZipFile.length() / 1024 / 1024, previewMp4File.length() / 1024 / 1024);
                    try {
                        final String templateId = aliMediaService.createTemplate(templateName, previewMp4File, templateZipFile);
                        final String templateInfo = aliMediaService.waitTemplateCreateAndGenJsonFile(path, templateId);

                        AiRingColumnAiTemplate aiRingColumnAiTemplate = new AiRingColumnAiTemplate();
                        aiRingColumnAiTemplate.setTemplateId(templateId);
                        aiRingColumnAiTemplate.setRingName(templateName);
                        aiRingColumnAiTemplate.setClipsParam(templateInfo);
                        aiRingColumnAiTemplate.setStatus(0);
                        aiRingColumnAiTemplate.setColumnId("101");
                        aiRingColumnAiTemplate.setOrderBy(99);
                        aiRingColumnAiTemplate.setRemark(path.toString());
                        aiRingColumnAiTemplate.setCreateTime(new Date());
                        aiRingColumnAiTemplateService.save(aiRingColumnAiTemplate);
                    } catch (Exception e) {
                        log.info("创建模板的文件夹:{},创建模板异常:", path.getFileName(), e);
                    }
                } else {
                    log.info("文件夹未完整包含模板和mp4:{}", path);
                }
            });

        } catch (IOException e) {
            log.info("从NAS共享文件生成模板信息异常:", e);
        }
    }

    /**
     * 从NAS放置模板的共享文件夹创建模板,创建成功后会在该文件夹下产生模板信息json文件(跑的时候测试环境接正式库)
     *
     * @param templateContainerPath
     */
    @Override
    public void generateFaceAITemplate(Path templateContainerPath) {
        //final Path templateContainerPath = Paths.get("\\\\*************\\鸿盛天极\\视频彩铃-AE模板");
        try {
            //Files.find(templateContainerPath, 1, (path, basicFileAttributes) -> {
            Files.find(templateContainerPath, 3, (path, basicFileAttributes) -> {
                if (!basicFileAttributes.isDirectory()) {
                    return false;
                }
                //if (path.equals(templateContainerPath)) {
                //    return false;
                //}
                return path.toFile().listFiles((dir, name) -> name.toLowerCase().endsWith(".zip")).length > 0 && path.toFile().listFiles((dir, name) -> name.toLowerCase().endsWith(".json")).length == 0;
            }).forEach(path -> {
                log.info("准备创建模板的文件夹:{}", path);
                final File[] files = path.toFile().listFiles();
                File previewMp4File = null;
                File templateZipFile = null;
                for (int i = 0; i < files.length; i++) {
                    File file = files[i];
                    if (file.getName().toLowerCase().endsWith(".mp4")) {
                        previewMp4File = file;
                    } else if (file.getName().toLowerCase().endsWith(".zip")) {
                        templateZipFile = file;
                    }
                }
                if (previewMp4File != null && templateZipFile != null) {
                    final String templateName = FilenameUtils.removeExtension(previewMp4File.getName());
                    log.info("创建模板=>名称:{},模板zip文件大小:{}M,预览视频文件大小:{}M", templateName, templateZipFile.length() / 1024 / 1024, previewMp4File.length() / 1024 / 1024);
                    try {
                        final String templateId = aliMediaService.createTemplate(templateName, previewMp4File, templateZipFile);
                        final String templateInfo = aliMediaService.waitTemplateCreateAndGenJsonFile(path, templateId);
                        log.info("创建模板=>id:{},templateInfo:{}", templateId, templateInfo);
                        AiRingColumnAiFaceTemplate aiTemplate = new AiRingColumnAiFaceTemplate();
                        aiTemplate.setTemplateId(templateId);
                        aiTemplate.setRingName(templateName);
                        aiTemplate.setClipsParam(templateInfo);
                        aiTemplate.setStatus(0);
                        aiTemplate.setColumnId("129");
                        aiTemplate.setOrderBy(99);
                        aiTemplate.setAiType(1);
                        aiTemplate.setRemark(path.toString());
                        aiTemplate.setCreateTime(new Date());

//                        aiFaceTemplateService.save(aiTemplate);
                    } catch (Exception e) {
                        log.info("创建模板的文件夹:{},创建模板异常:", path.getFileName(), e);
                    }
                } else {
                    log.info("文件夹未完整包含模板和mp4:{}", path);
                }
            });

        } catch (IOException e) {
            log.info("从NAS共享文件生成模板信息异常:", e);
        }
    }
}
