package com.eleven.qycl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eleven.qycl.entity.QyclRingVideo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: qycl_ring_video
 * @Author: jeecg-boot
 * @Date:   2023-04-21
 * @Version: V1.0
 */
public interface IQyclRingVideoService extends IService<QyclRingVideo> {

    QyclRingVideo getQyclRingVideoByRingId(String ringId);

    QyclRingVideo randomQyclGrRingVideo();

    QyclRingVideo randomQyclRingVideo();

    IPage<QyclRingVideo> ringListByColumnId(IPage<QyclRingVideo> page, String columnId);
}
