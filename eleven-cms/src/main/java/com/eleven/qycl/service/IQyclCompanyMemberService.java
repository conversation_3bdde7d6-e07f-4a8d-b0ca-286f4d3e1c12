package com.eleven.qycl.service;

import com.eleven.qycl.entity.QyclCompanyMember;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * @Description: qycl_company_member
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
public interface IQyclCompanyMemberService extends IService<QyclCompanyMember> {

    List<QyclCompanyMember> getListByOpenId(String openId);

    Integer getMemberCountByOpenId(String openId);

    int deleteByOpenId(String openId);

    void updateQyclFunStatus(String mobile,String qyclFunStatus);

    Result addMember(String openId, String mobiles, String companyOwner);

    QyclCompanyMember getMemberByOpenId(String openId);
}
