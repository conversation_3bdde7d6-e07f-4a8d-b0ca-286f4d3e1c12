package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.dto.DataCollectDto;
import com.eleven.cms.dto.DataCollectSubDto;
import com.eleven.cms.dto.DataCollectUnSubDto;
import com.eleven.cms.entity.DataNotifyLog;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IDataNotifyLogService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.qycl.entity.DataCollect;
import com.eleven.qycl.mapper.DataCollectMapper;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.service.IDataCollectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
 * @Description: 企业彩铃数据汇总
 * @Author: jeecg-boot
 * @Date:   2024-01-19
 * @Version: V1.0
 */
@Slf4j
@Service
public class DataCollectServiceImpl extends ServiceImpl<DataCollectMapper, DataCollect> implements IDataCollectService {
    @Autowired
    IDataNotifyLogService dataNotifyLogService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;
    private final Object lock = new Object();
    @Override
    public void dataCollect() {
        //执行日期
        String executeDate=DateUtil.formatYearMonthDay(LocalDate.now().plusDays(-2).atTime(LocalTime.MIN));

//        Date updateTime=DateUtil.localDateTimeToDate(LocalDate.now().plusDays(-2).atTime(LocalTime.MIN));






        //查询两天前订购数据（根据渠道号和省份分组），并带上执行日期插入数据
        DataCollectSubDto subDto=new DataCollectSubDto();
        String subBegin=DateUtil.formatSplitTime(LocalDate.now().plusDays(-2).atTime(LocalTime.MIN));
        String subEnd=DateUtil.formatSplitTime(LocalDate.now().plusDays(-2).atTime(LocalTime.MAX));
        subDto.setCreateTimeBegin(subBegin);
        subDto.setCreateTimeEnd(subEnd);

        List<DataCollectSubDto> subDtoList=this.baseMapper.subList(subDto);
        if(!subDtoList.isEmpty()){
            synchronized (lock) {
                subDtoList.forEach(item -> {
                    DataCollect collect=this.lambdaQuery().eq(DataCollect::getExecuteDate,executeDate).eq(DataCollect::getChannel,item.getChannel()).eq(DataCollect::getProvince,item.getProvince()).orderByDesc(DataCollect::getCreateTime).last("limit 1").one();

                    DataCollectUnSubDto unSubDto=new DataCollectUnSubDto();
                    unSubDto.setCreateTimeBegin(subBegin);
                    unSubDto.setCreateTimeEnd(subEnd);
                    unSubDto.setMobile(item.getMobile());
                    boolean verifyStatus=this.baseMapper.verifyStatusCount(unSubDto)>0;
                    boolean isMonth = enterpriseVrbtService.verifyMonth(item.getMobile(), item.getChannel());
                    if(collect==null){
                        DataCollect dataCollect=new DataCollect();

                        Integer subCount=isMonth?1:0;
                        Integer unSubCount=!isMonth?1:0;
                        Integer verifyStatusCount=verifyStatus?1:0;




                        /**订购数量*/
                        dataCollect.setSub(String.valueOf(subCount));
                        /**退订数量*/
                        dataCollect.setUnSub(String.valueOf(unSubCount));
                        //60分钟退订数量
                        dataCollect.setVerifyStatus(String.valueOf(verifyStatusCount));

                        /**渠道号*/
                        dataCollect.setChannel(item.getChannel());
                        /**省份*/
                        dataCollect.setProvince(StringUtils.isBlank(item.getProvince())?"未知":item.getProvince());
                        /**执行日期*/
                        dataCollect.setExecuteDate(executeDate);
                        this.baseMapper.insert(dataCollect);
                    }else{

                        Integer subCount=isMonth?(Integer.valueOf(collect.getSub()).intValue()+1):Integer.valueOf(collect.getSub());
                        Integer unSubCount=!isMonth?(Integer.valueOf(collect.getUnSub()).intValue()+1):Integer.valueOf(collect.getUnSub());
                        Integer verifyStatusCount=verifyStatus?(Integer.valueOf(collect.getVerifyStatus()).intValue()+1):Integer.valueOf(collect.getVerifyStatus());
                        this.lambdaUpdate().eq(DataCollect::getId, collect.getId()).set(DataCollect::getSub,String.valueOf(subCount)).set(DataCollect::getUnSub,String.valueOf(unSubCount)).set(DataCollect::getVerifyStatus,String.valueOf(verifyStatusCount)).set(DataCollect::getUpdateTime,new Date()).update();
                    }
                });
            }
        }



        //根据订购数量和退订数量计算退订率
        List<DataCollect> dataCollectList=this.lambdaQuery().select(DataCollect::getId,DataCollect::getSub,DataCollect::getUnSub,DataCollect::getVerifyStatus).eq(DataCollect::getExecuteDate,executeDate).list();
        if(!dataCollectList.isEmpty()){
            synchronized (lock) {
                dataCollectList.forEach(item -> {
                    //计算退订率
                    Integer unSub=Integer.valueOf(StringUtils.isBlank(item.getUnSub())?"0":item.getUnSub());
                    Integer sub=Integer.valueOf(StringUtils.isBlank(item.getSub())?"0":item.getSub());
                    String unSubRate=BigDecimal.valueOf(unSub).divide(BigDecimal.valueOf(sub),new MathContext(4, RoundingMode.DOWN)).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                    //计算1小时退订率
                    Integer verifyStatus=Integer.valueOf(StringUtils.isBlank(item.getVerifyStatus())?"0":item.getVerifyStatus());
                    String verifyStatusRate=BigDecimal.valueOf(verifyStatus).divide(BigDecimal.valueOf(sub),new MathContext(4, RoundingMode.DOWN)).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                    this.lambdaUpdate().eq(DataCollect::getId, item.getId()).set(DataCollect::getUnSubRate,unSubRate).set(DataCollect::getVerifyStatusRate,verifyStatusRate).update();
                });
            }
        }
    }

    @Override
    public void updateDataNotify() {
        String createTime_begin=DateUtil.formatSplitTime(LocalDate.now().plusDays(-1).atTime(LocalTime.MIN));
        String createTime_end=DateUtil.formatSplitTime(LocalDateTime.now());
        List<DataNotifyLog> dataNotifyLogList=dataNotifyLogService.lambdaQuery().select(DataNotifyLog::getId,DataNotifyLog::getMobile).between(DataNotifyLog::getCreateTime,createTime_begin,createTime_end).list();
        if(!dataNotifyLogList.isEmpty()){
            dataNotifyLogList.forEach(item -> {
                MobileRegionResult mobileRegionResult = mobileRegionService.query(item.getMobile());
                if (mobileRegionResult != null) {
                    dataNotifyLogService.lambdaUpdate().eq(DataNotifyLog::getId, item.getId()).set(DataNotifyLog::getProvince,mobileRegionResult.getProvince()).update();
                }
            });
        }

    }

    @Override
    public DataCollectDto findDataCollectCount(DataCollectDto dto) {
        DataCollectDto dataCollectDto=this.baseMapper.findDataCollectCount(dto);
        if(dataCollectDto!=null){

            Integer sub=Integer.valueOf(StringUtils.isBlank(dataCollectDto.getSub())?"0":dataCollectDto.getSub());
            //计算退订率
            Integer unSub=Integer.valueOf(StringUtils.isBlank(dataCollectDto.getUnSub())?"0":dataCollectDto.getUnSub());
            String unSubRate=BigDecimal.valueOf(unSub).divide(BigDecimal.valueOf(sub),new MathContext(4, RoundingMode.DOWN)).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
            dataCollectDto.setUnSubRate(unSubRate+"%");

            //计算1小时退订率
            Integer verifyStatus=Integer.valueOf(StringUtils.isBlank(dataCollectDto.getVerifyStatus())?"0":dataCollectDto.getVerifyStatus());
            String verifyStatusRate=BigDecimal.valueOf(verifyStatus).divide(BigDecimal.valueOf(sub),new MathContext(4, RoundingMode.DOWN)).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
            dataCollectDto.setVerifyStatusRate(verifyStatusRate+"%");
        }
        return dataCollectDto;
    }

    @Override
    public List<DataCollectDto> findProvinceList(DataCollectDto dto) {
        List<DataCollectDto> dataCollectDtoList=this.baseMapper.findProvinceList(dto);
        if(!dataCollectDtoList.isEmpty()){
            dataCollectDtoList.forEach(item -> {
                Integer sub=Integer.valueOf(StringUtils.isBlank(item.getSub())?"0":item.getSub());
                Integer unSub=Integer.valueOf(StringUtils.isBlank(item.getUnSub())?"0":item.getUnSub());
                String  unSubRate=BigDecimal.valueOf(unSub).divide(BigDecimal.valueOf(sub),2,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setUnSubRate(unSubRate);

                item.setSubCount(sub);
                item.setUnSubCount(unSub);
                item.setUnSubRateCount(unSubRate);

                Integer verifyStatus=Integer.valueOf(StringUtils.isBlank(item.getVerifyStatus())?"0":item.getVerifyStatus());
                String  verifyStatusRate=BigDecimal.valueOf(verifyStatus).divide(BigDecimal.valueOf(sub),2,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setVerifyStatusCount(verifyStatus);
                item.setVerifyStatusRate(verifyStatusRate);
                item.setVerifyStatusRateCount(verifyStatusRate);
            });
        }
        return dataCollectDtoList;
    }

    @Override
    public List<DataCollectDto> findExecuteDateList(DataCollectDto dto) {
        List<DataCollectDto> dataCollectDtoList=this.baseMapper.findExecuteDateList(dto);
        if(!dataCollectDtoList.isEmpty()){
            dataCollectDtoList.forEach(item -> {
                Integer sub=Integer.valueOf(StringUtils.isBlank(item.getSub())?"0":item.getSub());
                Integer unSub=Integer.valueOf(StringUtils.isBlank(item.getUnSub())?"0":item.getUnSub());
                String unSubRate=BigDecimal.valueOf(unSub).divide(BigDecimal.valueOf(sub),2,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setUnSubRate(unSubRate);

                item.setSubCount(sub);
                item.setUnSubCount(unSub);
                item.setUnSubRateCount(unSubRate);


                Integer verifyStatus=Integer.valueOf(StringUtils.isBlank(item.getVerifyStatus())?"0":item.getVerifyStatus());
                String  verifyStatusRate=BigDecimal.valueOf(verifyStatus).divide(BigDecimal.valueOf(sub),2,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setVerifyStatusCount(verifyStatus);
                item.setVerifyStatusRate(verifyStatusRate);
                item.setVerifyStatusRateCount(verifyStatusRate);
            });
        }
        return dataCollectDtoList;
    }

    @Override
    public List<DataCollectDto> pageDataCollectList(DataCollectDto dto) {
        List<DataCollectDto> dataCollectDtoList=this.baseMapper.findByPage(dto);
        if(!dataCollectDtoList.isEmpty()){
            dataCollectDtoList.forEach(item -> {
                Integer sub=Integer.valueOf(StringUtils.isBlank(item.getSub())?"0":item.getSub());

                //计算退订率
                Integer unSub=Integer.valueOf(StringUtils.isBlank(item.getUnSub())?"0":item.getUnSub());
                String unSubRate=BigDecimal.valueOf(unSub).divide(BigDecimal.valueOf(sub),4,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setUnSubRate(unSubRate+"%");

                //计算1小时退订率
                Integer verifyStatus=Integer.valueOf(StringUtils.isBlank(item.getVerifyStatus())?"0":item.getVerifyStatus());
                String verifyStatusRate=BigDecimal.valueOf(verifyStatus).divide(BigDecimal.valueOf(sub),4,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setVerifyStatusRate(verifyStatusRate+"%");
            });
        }
        return dataCollectDtoList;
    }


    @Override
    public List<DataCollect> pageDownXlsxDataCollectList(DataCollectDto dto) {
        List<DataCollect> dataCollectList=this.baseMapper.pageDownXlsxDataCollectList(dto);
        if(!dataCollectList.isEmpty()){
            dataCollectList.forEach(item -> {
                Integer sub=Integer.valueOf(StringUtils.isBlank(item.getSub())?"0":item.getSub());

                //计算退订率
                Integer unSub=Integer.valueOf(StringUtils.isBlank(item.getUnSub())?"0":item.getUnSub());
                String unSubRate=BigDecimal.valueOf(unSub).divide(BigDecimal.valueOf(sub),4,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setUnSubRate(unSubRate+"%");

                //计算1小时退订率
                Integer verifyStatus=Integer.valueOf(StringUtils.isBlank(item.getVerifyStatus())?"0":item.getVerifyStatus());
                String verifyStatusRate=BigDecimal.valueOf(verifyStatus).divide(BigDecimal.valueOf(sub),4,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setVerifyStatusRate(verifyStatusRate+"%");
            });
        }
        return dataCollectList;
    }



    @Override
    public List<DataCollectDto> downXlsxFindExecuteDateList(DataCollectDto dto) {
        List<DataCollectDto> dataCollectDtoList=this.baseMapper.downXlsxFindExecuteDateList(dto);
        if(!dataCollectDtoList.isEmpty()){
            dataCollectDtoList.forEach(item -> {
                Integer sub=Integer.valueOf(StringUtils.isBlank(item.getSub())?"0":item.getSub());
                Integer unSub=Integer.valueOf(StringUtils.isBlank(item.getUnSub())?"0":item.getUnSub());
                String unSubRate=BigDecimal.valueOf(unSub).divide(BigDecimal.valueOf(sub),2,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setUnSubRate(unSubRate);

                item.setSubCount(sub);
                item.setUnSubCount(unSub);
                item.setUnSubRateCount(unSubRate);


                Integer verifyStatus=Integer.valueOf(StringUtils.isBlank(item.getVerifyStatus())?"0":item.getVerifyStatus());
                String  verifyStatusRate=BigDecimal.valueOf(verifyStatus).divide(BigDecimal.valueOf(sub),2,RoundingMode.DOWN).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
                item.setVerifyStatusCount(verifyStatus);
                item.setVerifyStatusRate(verifyStatusRate);
                item.setVerifyStatusRateCount(verifyStatusRate);
            });
        }
        return dataCollectDtoList;
    }
}
