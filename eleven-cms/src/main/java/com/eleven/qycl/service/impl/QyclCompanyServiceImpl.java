package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.qycl.entity.*;
import com.eleven.qycl.mapper.QyclCompanyMapper;
import com.eleven.qycl.service.*;
import com.eleven.qycl.util.QyclConstant;
import com.jayway.jsonpath.JsonPath;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: qycl_company
 * @Author: jeecg-boot
 * @Date: 2022-11-09
 * @Version: V1.0
 */
@Service
public class QyclCompanyServiceImpl extends ServiceImpl<QyclCompanyMapper, QyclCompany> implements IQyclCompanyService {

    @Autowired
    IQyclCompanyMemberService qyclCompanyMemberService;
    @Autowired
    IQyclOrderPayService qyclOrderPayService;
    @Autowired
    IQyclRingService qyclRingService;
    @Autowired
    QyclCompanyMapper qyclCompanyMapper;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;

    @Override
    public QyclCompany getContainMemberByOpenId(String openId) {
        QyclCompany qyclCompany = lambdaQuery().eq(QyclCompany::getOpenId, openId).one();
        if (qyclCompany != null) {
            EntVrbtResult entVrbtResult = null;
            if (StringUtils.isNotEmpty(qyclCompany.getDepartmentId())) {
                entVrbtResult = enterpriseVrbtService.queryContentMembersByChannel(qyclCompany.getDepartmentId(), null, qyclCompany.getCompanyOwner(),qyclCompany.getChannel());
            }
            List<QyclCompanyMember> members = qyclCompanyMemberService.getListByOpenId(openId);
            for (QyclCompanyMember member : members) {
                if (entVrbtResult != null && entVrbtResult.getData() != null) {
                    final List<String> userStatusList = JsonPath.read(entVrbtResult.getData().toString(), "$[?(@.billNum == '" + member.getMobile() + "')].userStatus");
                    member.setUserStatus(!userStatusList.isEmpty() ? userStatusList.get(0) : "");
                    if ("02".equals(member.getUserStatus())) {
                        member.setSettingStatus(1);
                    }
//                    if(StringUtils.isNotBlank(member.getUserStatus()) && !"02".equals(member.getUserStatus())){
//                        member.setSettingStatus(-1);
//                    }
                    else {
                        member.setSettingStatus(0);
                    }
                }
            }
            qyclCompany.setMembers(members);
        }
        return qyclCompany;
    }

    @Override
    public QyclCompany findPayCompanyByOpenId(String openId) {
        QyclCompany qyclCompany = this.lambdaQuery().eq(QyclCompany::getOpenId, openId).one();
        List<QyclOrderPay> list = qyclOrderPayService.lambdaQuery().eq(QyclOrderPay::getOpenId, openId).list();
        if(qyclCompany != null){
            qyclCompany.setUpdateStatus(true);
            for (QyclOrderPay qyclOrderPay : list) {
                if(qyclOrderPay.getPayStatus() == 1){
                    qyclCompany.setUpdateStatus(false);
                    break;
                }
            }
        }
        return qyclCompany;
    }

    @Override
    public int deleteByOpenId(String openId) {
        return this.baseMapper.delete(new QueryWrapper<QyclCompany>().lambda().in(QyclCompany::getOpenId, openId));
    }

    @Override
    public Boolean queryHandleStatus(String openId) {
        //查询已经审核成功的铃音条数
        Integer ringCount = qyclRingService.lambdaQuery().eq(QyclRing::getOpenId, openId).isNotNull(QyclRing::getRingStatus).count();
        //查询未审核成功的用户条数
        Integer memberCount = qyclCompanyMemberService.lambdaQuery().eq(QyclCompanyMember::getOpenId, openId).ne(QyclCompanyMember::getQyclFun, "1").count();
        return ringCount > 0 && memberCount == 0;
    }

    @Override
    public List<Map<String, Object>> analysis(Date statrDate, Date endDate, Double totalFee) {
        List<Map<String, Object>> list = qyclCompanyMapper.groupByDate(statrDate, endDate, totalFee);
        list.forEach(map -> {
            String payTimeStr = (String) map.get("payTimeStr");
            Map<String, Object> waitOrderNumMap = qyclCompanyMapper.queryWaitOrderNum(payTimeStr, totalFee);
            if (waitOrderNumMap != null) {
                map.putAll(waitOrderNumMap);
            }
            Map<String, Object> analysisMap = qyclCompanyMapper.analysis(payTimeStr, totalFee);
            if (analysisMap != null) {
                map.putAll(analysisMap);
                Long alreadyHandleNum = (Long) map.get("alreadyHandleNum");
                Long sucOrderNum = (Long) map.get("sucOrderNum");
                map.put("notHandleNum", sucOrderNum - alreadyHandleNum);
            }
        });
        return list;
    }

    @Override
    public String getDepartmentId(String openId) {
        QyclCompany qyclCompany = lambdaQuery().eq(QyclCompany::getOpenId, openId).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        return qyclCompany != null ? qyclCompany.getDepartmentId() : "";
    }

    @Override
    public String getCompanyOwnerByMobile(String mobile) {
        QyclCompany qyclCompany = lambdaQuery().eq(QyclCompany::getMobile, mobile).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        if (qyclCompany == null) {
            return null;
        }
        return StringUtils.isNotBlank(qyclCompany.getCompanyOwner()) ? qyclCompany.getCompanyOwner() : QyclConstant.QYCL_COMPANY_OWNER_YRJY;
    }

    @Override
    public QyclCompany getQyclCompanyByOpenId(String openId) {
        return lambdaQuery().eq(QyclCompany::getOpenId, openId).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
    }

    @Override
    public IPage<QyclCompany> findByPage(Page<QyclCompany> page, QyclCompany qyclCompany) {
        page.setSearchCount(false);
        page.setOptimizeCountSql(false);
        page.setTotal(qyclCompanyMapper.findByPage_COUNT(qyclCompany));
        return qyclCompanyMapper.findByPage(page, qyclCompany);
    }
}
