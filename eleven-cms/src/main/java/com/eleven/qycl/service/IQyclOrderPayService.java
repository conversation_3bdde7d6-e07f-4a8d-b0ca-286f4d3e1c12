package com.eleven.qycl.service;

import com.eleven.qycl.entity.QyclOrderPay;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: qycl_order_pay
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
public interface IQyclOrderPayService extends IService<QyclOrderPay> {
    String savePay(String openId, String companyTitle, String mobile, String companyOwner);

    QyclOrderPay queryOrder(String orderId);

    void updatePayStatus(String outTradeNo,String transactionId,Integer status,QyclOrderPay qyclOrderPay);

    String saveNotPaymentOrder(String openId, String companyTitle, String mobile, String companyOwner);


    Result wechatRefund(String outTradeNo, String refund);

    void upadateRefundStatus(String refundOrderNo,Integer refundStatus,String refundRemark);
}
