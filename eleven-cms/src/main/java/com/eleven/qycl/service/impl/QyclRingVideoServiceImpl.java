package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eleven.qycl.entity.QyclRingVideo;
import com.eleven.qycl.mapper.QyclRingVideoMapper;
import com.eleven.qycl.service.IQyclRingVideoService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: qycl_ring_video
 * @Author: jeecg-boot
 * @Date:   2023-04-21
 * @Version: V1.0
 */
@Service
public class QyclRingVideoServiceImpl extends ServiceImpl<QyclRingVideoMapper, QyclRingVideo> implements IQyclRingVideoService {
    @Autowired
    QyclRingVideoMapper qyclRingVideoMapper;

    @Override
    public QyclRingVideo getQyclRingVideoByRingId(String ringId) {
        return this.lambdaQuery().eq(QyclRingVideo::getRingId, ringId).last("limit 1").one();
    }

    @Override
    public QyclRingVideo randomQyclGrRingVideo() {
        return qyclRingVideoMapper.selectRandomQyclGrRingVideo();
    }

    @Override
    public QyclRingVideo randomQyclRingVideo() {
        return qyclRingVideoMapper.selectRandomQyclRingVideo();
    }

    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_COMMON,key = "'ringVideo:'+ #root.methodName + ':' + #p0",unless = "#result==null")
    @Override
    public IPage<QyclRingVideo> ringListByColumnId(IPage<QyclRingVideo> page, String columnId) {
        return this.lambdaQuery()
                .eq(QyclRingVideo::getColumnId,columnId)
                .eq(QyclRingVideo::getStatus,1)
                .orderByAsc(QyclRingVideo::getOrderBy)
                .page(page);

    }

}
