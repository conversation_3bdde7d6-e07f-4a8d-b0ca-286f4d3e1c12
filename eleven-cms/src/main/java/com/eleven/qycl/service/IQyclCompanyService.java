package com.eleven.qycl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AccountCofig;
import com.eleven.qycl.entity.QyclCompany;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: qycl_company
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
public interface IQyclCompanyService extends IService<QyclCompany> {

    QyclCompany getContainMemberByOpenId(String openId);

    QyclCompany findPayCompanyByOpenId(String openId);

    int deleteByOpenId(String openId);

    Boolean queryHandleStatus(String openId);

    List<Map<String, Object>> analysis(Date statrDate, Date endDate, Double totalFee);

    String getDepartmentId(String openId);

    String getCompanyOwnerByMobile(String mobile);

    QyclCompany getQyclCompanyByOpenId(String openId);

    IPage<QyclCompany> findByPage(Page<QyclCompany> page, QyclCompany qyclCompany);


}
