package com.eleven.qycl.service.impl;

import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.entity.QyclCompanyMember;
import com.eleven.qycl.entity.QyclQywx;
import com.eleven.qycl.mapper.QyclQywxMapper;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.service.IQyclCompanyMemberService;
import com.eleven.qycl.service.IQyclQywxService;
import com.eleven.qycl.util.QyclConstant;
import com.jayway.jsonpath.JsonPath;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.similarities.Lambda;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: qycl_qywx
 * @Author: jeecg-boot
 * @Date: 2024-11-05
 * @Version: V1.0
 */
@Service
public class QyclQywxServiceImpl extends ServiceImpl<QyclQywxMapper, QyclQywx> implements IQyclQywxService {

    @Autowired
    private EnterpriseVrbtService enterpriseVrbtService;

    @Autowired
    private IQyclCompanyMemberService qyclCompanyMemberServie;

    @Override
    public Result createDepartmentAndAddMember(QyclQywx qyclQywx) {
        String departmentName = qyclQywx.getTitle().replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
        departmentName = StringUtils.substring(departmentName, 0, 19);
        final EntVrbtResult departmentEntVrbtResult = enterpriseVrbtService.ecOperationByChannel("0", departmentName, "", QyclConstant.QYCL_COMPANY_OWNER_YRJY,null);
        if (departmentEntVrbtResult.isOK()) {
            String departmentId = departmentEntVrbtResult.getData().at("/departmentId").asText();
            qyclQywx.setDepartmentId(departmentId);
            EntVrbtResult entVrbtResult = enterpriseVrbtService.addContentMembersByChannel(departmentId, QyclConstant.QYCL_COMPANY_OWNER_YRJY,null, qyclQywx.getMobile());
            if (entVrbtResult.isOK()) {
                final List<String> errorMsgList = JsonPath.read(entVrbtResult.getData().toString(), "$.list[?(@.billNum =='" + qyclQywx.getMobile() + "')].errorMsg");
                if (errorMsgList.size() > 0) {
                    enterpriseVrbtService.ecOperationByChannel("1", departmentName, departmentId, QyclConstant.QYCL_COMPANY_OWNER_YRJY,null);
                    return Result.error(errorMsgList.get(0));
                } else {
                    this.save(qyclQywx);
                    this.addMember(qyclQywx.getId(), qyclQywx.getMobile(), qyclQywx.getTitle());
                    return Result.ok();
                }
            } else {
                return Result.error(entVrbtResult.getInfo());
            }
        } else {
            return Result.error(departmentEntVrbtResult.getInfo());
        }
    }

    public void addMember(String qyclQywxId, String mobile, String title) {
        QyclCompanyMember qyclCompanyMember = new QyclCompanyMember();
        qyclCompanyMember.setCompanyTitle(title);
        qyclCompanyMember.setMobile(mobile);
        qyclCompanyMember.setOpenId(qyclQywxId);
        qyclCompanyMemberServie.save(qyclCompanyMember);
    }

    @Override
    public void qyclQywxNotify(String mobile) {
        lambdaUpdate().eq(QyclQywx::getMobile, mobile).set(QyclQywx::getStatus, "1").update();
        qyclCompanyMemberServie.lambdaUpdate().eq(QyclCompanyMember::getMobile, mobile).set(QyclCompanyMember::getQyclFun, "1").update();
    }

    public static void main(String[] args) {
        String str = "{\"fullCapacity\":false,\"list\":[{\"billNum\":\"17260807125\",\"errorMsg\":\"存在于其他企业，详情见【成员归属信息查询】\"}]}";
        final List<String> errorMsgList = JsonPath.read(str, "$.list[?(@.billNum =='17260807125')].errorMsg");
        System.out.println(errorMsgList.get(0));
    }
}
