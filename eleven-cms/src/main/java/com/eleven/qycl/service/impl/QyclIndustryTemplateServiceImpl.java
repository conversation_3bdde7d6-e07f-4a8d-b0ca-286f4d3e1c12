package com.eleven.qycl.service.impl;

import com.eleven.cms.service.IQyclWxpayService;
import com.eleven.qycl.entity.QyclCompany;
import com.eleven.qycl.entity.QyclIndustryTemplate;
import com.eleven.qycl.entity.QyclRing;
import com.eleven.qycl.mapper.QyclIndustryTemplateMapper;
import com.eleven.qycl.service.IQyclCompanyService;
import com.eleven.qycl.service.IQyclIndustryTemplateService;
import com.eleven.qycl.service.IQyclOrderPayService;
import com.eleven.qycl.service.IQyclRingService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: qycl_industry_template
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Service
public class QyclIndustryTemplateServiceImpl extends ServiceImpl<QyclIndustryTemplateMapper, QyclIndustryTemplate> implements IQyclIndustryTemplateService {

    @Override
    public QyclIndustryTemplate createRingTxt(String companyTitle, String title) {
        QyclIndustryTemplate qyclIndustryTemplate = this.lambdaQuery().eq(QyclIndustryTemplate::getTitle, title).one();
        qyclIndustryTemplate.setRingTxt(StringUtils.isEmpty(companyTitle)
                ? qyclIndustryTemplate.getRingTxt().replace("[companyTitle]","某某有限公司")
                : qyclIndustryTemplate.getRingTxt().replace("[companyTitle]",companyTitle));
        return qyclIndustryTemplate;
    }

    @Override
    public List<String> findIndustryTitles() {
        ArrayList<String> list = new ArrayList<>();
        List<QyclIndustryTemplate> industryTemplates = this.lambdaQuery().list();
        for (QyclIndustryTemplate industryTemplate : industryTemplates) {
            list.add(industryTemplate.getTitle());
        }
        return list;
    }
}
