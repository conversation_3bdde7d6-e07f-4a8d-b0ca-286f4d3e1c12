package com.eleven.qycl.service;

import com.aliyun.ice20201109.Client;
import com.aliyun.ice20201109.models.*;
import com.aliyun.mts20140618.models.QueryIProductionJobRequest;
import com.aliyun.mts20140618.models.QueryIProductionJobResponse;
import com.aliyun.mts20140618.models.SubmitIProductionJobRequest;
import com.aliyun.mts20140618.models.SubmitIProductionJobResponse;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.eleven.cms.zhmb.entity.ZhmbImageToVideoTemplate;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.entity.VoiceGender;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.util.RawValue;
import com.google.common.base.Stopwatch;
import com.google.common.base.Strings;
import com.google.common.io.CharStreams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Date: 2022/11/10 16:26
 * Desc: 阿里云媒体服务(包含阿里云MPS[使用了TTS语音合成]和智能媒体服务IMS[使用了云剪辑])
 */
@Service
@Slf4j
public class AliMediaService {

    public static final String LOG_TAG = "阿里云媒体服务api";
    @Autowired
    AliMediaProperties aliMediaProperties;
    @Autowired
    private Environment environment;
    @Autowired
    private ResourceLoader resourceLoader;
    private ObjectMapper mapper;
    private OSS ossClient;
    private com.aliyun.mts20140618.Client mpsClient;
    private Client iceClient;
    private String regionId;
    private String bucket;
    private JsonNode templateJsonNode;

    @PostConstruct
    public void init() {
        regionId = aliMediaProperties.getRegionId();
        bucket = aliMediaProperties.getBucketName();
        String accessKeyId = aliMediaProperties.getAccessKeyId();
        String accessKeySecret = aliMediaProperties.getAccessKeySecret();
        ossClient = new OSSClientBuilder().build("http://oss-" + regionId + ".aliyuncs.com", accessKeyId, accessKeySecret);
        try {
            mpsClient = new com.aliyun.mts20140618.Client(new Config().setAccessKeyId(accessKeyId).setAccessKeySecret(accessKeySecret).setEndpoint("mts." + regionId + ".aliyuncs.com"));
            iceClient = new Client(new Config().setAccessKeyId(accessKeyId).setAccessKeySecret(accessKeySecret).setEndpoint("ice." + regionId + ".aliyuncs.com"));
        } catch (Exception e) {
            log.error("{}-初始化MPS/IMS服务,异常:", LOG_TAG, e);
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 阿里云文字转语音和字幕
     *
     * @param jobQueueTag job任务tag(该参数决定通知的队列)
     * @param text        文字内容
     * @param voiceGender MALE:男声,FEMALE:女声
     * @param wordReplace 针对多音字的替换,在tts前先替换成正确读音,然后字幕再替换回来
     * @return 阿里云媒体处理jobId
     */
    public String tts(int jobQueueTag, String text, VoiceGender voiceGender, Map<String, String> wordReplace) {
        try {
            //针对多音字对文本进行替换
            if (wordReplace != null) {
                for (Map.Entry<String, String> entry : wordReplace.entrySet()) {
                    final String key = entry.getKey();
                    final String value = entry.getValue();
                    if (Strings.isNullOrEmpty(key) || Strings.isNullOrEmpty(value)) {
                        continue;
                    }
                    text = text.replaceAll(key, value);
                }
            }
            String voice = voiceGender.getVoiceName();  //声音名称
            String jobParams = "{\"voice\":\"" + voice + "\",\"format\":\"mp3\",\"sample_rate\":16000}";
            final String dateString = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            final String ttsOutputPath = aliMediaProperties.getIproductionDir() + "/" + dateString;
            String textObject = ttsOutputPath + "/" + DigestUtils.md5Hex(voice + text) + ".txt";
            text = text.replaceAll("[，,.]", "。"); // AsyncTextToSpeech任务用句号进行断句
            putObjectContent(textObject, text);
            String input = "oss://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + textObject;
            SubmitIProductionJobRequest submitIProductionJobRequest = new SubmitIProductionJobRequest();
            submitIProductionJobRequest.setFunctionName("AsyncTextToSpeech");
            submitIProductionJobRequest.setInput(input);
            submitIProductionJobRequest.setOutput("oss://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + ttsOutputPath + "/{source}-{sequenceId}.{resultType}");
            submitIProductionJobRequest.setJobParams(jobParams);
            submitIProductionJobRequest.setNotifyUrl(aliMediaProperties.determineJobCallbackMnsNotifyUrlByTag(jobQueueTag)); //这个方法设置的是mns的消息通知
            //submitIProductionJobRequest.setUserData("{\"NotifyAddress\":\""+notifyUrl+"\"}"); //也收不到回调
            SubmitIProductionJobResponse submitIProductionJobResponse = mpsClient.submitIProductionJob(submitIProductionJobRequest);
            final String jobId = submitIProductionJobResponse.body.jobId;
            log.info("{}-文字转语音服务,提交任务响应,jobId:{},requestId:{}", LOG_TAG, jobId, submitIProductionJobResponse.body.getRequestId());
            return jobId;
        } catch (Exception e) {
            log.info("{}-文字转语音服务,异常:", LOG_TAG, e);
            return null;
        }
    }

    /**
     * 抓取阿里云文字转语音结果
     *
     * @param ttsJobId
     * @param wordReplace
     * @return Pair<String, String> left存语音mp3的oss地址,right存替换后 正确的字幕内容文本
     */
    public Pair<String, String> fetchTtsResult(String ttsJobId, Map<String, String> wordReplace) {
        try {
            QueryIProductionJobRequest queryIProductionJobRequest = new QueryIProductionJobRequest();
            queryIProductionJobRequest.setJobId(ttsJobId);
            QueryIProductionJobResponse queryIProductionJobResponse = mpsClient.queryIProductionJob(queryIProductionJobRequest);
            // 获取生成的音频和字幕
            //{"result":[{"file":"iproduction/20230321/1e5cd0ce5b3653d1c5897a7a6b10f1a0-0.mp3"},{"file":"iproduction/20230321/1e5cd0ce5b3653d1c5897a7a6b10f1a0-1.txt"}]}
            final JsonNode jobResult = mapper.readTree(queryIProductionJobResponse.body.result);
            final String resultJson = jobResult.get("Data").asText();
            final Iterator<JsonNode> elements = mapper.readTree(resultJson).get("result").elements();
            String audioObject = null;
            String subtitleObject = null;
            while (elements.hasNext()) {
                final String file = elements.next().get("file").asText();
                if (StringUtils.endsWith(file, "mp3")) {
                    audioObject = file;
                } else if (StringUtils.endsWith(file, "txt")) {
                    subtitleObject = file;
                }
            }
            if (StringUtils.isEmpty(audioObject) || StringUtils.isEmpty(subtitleObject)) {
                log.info("{}-文字转语音出错,jobId:{}:", LOG_TAG, ttsJobId);
                return null;
            }
            String audioUrl = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + audioObject;
            // 获取字幕内容
            String subtitleContent = getObjectContent(subtitleObject);
            //针对多音字对文本进行替换还原
            if (wordReplace != null) {
                for (Map.Entry<String, String> entry : wordReplace.entrySet()) {
                    final String key = entry.getKey();
                    final String value = entry.getValue();
                    if (Strings.isNullOrEmpty(key) || Strings.isNullOrEmpty(value)) {
                        continue;
                    }
                    subtitleContent = subtitleContent.replaceAll(value, key);
                }
            }

            return Pair.of(audioUrl, subtitleContent);

        } catch (Exception e) {
            log.info("{}-文字转语音结果抓取,jobId:{},异常:", LOG_TAG, ttsJobId, e);
            return null;
        }
    }

    /**
     * 阿里云云剪辑合成视频
     *
     * @param jobQueueTag     job任务tag(该参数决定通知的队列)
     * @param videoUrls       图片或者视频oss地址
     * @param audioUrl        文字转语音的mp3 oss地址
     * @param subtitleContent 文字转语音生成的字幕文本内容
     * @param title           公司名字
     * @return 视频转换jobId
     */
    public String produceVideo(int jobQueueTag, List<String> videoUrls, String bgMusic, String audioUrl, String subtitleContent,
                               String title) {
        try {

            // 组装字幕轨
            int fontSize = 32;
            String fontName = "WenQuanYi Zen Hei Mono";
            String fontColor = "#FFFFFF";
            // 每次循环将视频素材随机，并提交合成任务
            //Collections.shuffle(videoUrls);

            final ArrayNode subtitleTrackClips = mapper.createArrayNode();
            final Iterator<JsonNode> mpsSubtitles = mapper.readTree(subtitleContent).elements();
            final ArrayNode videoTrackClips = mapper.createArrayNode();

            // 字幕距离底部距离
            //float subtitleBottom = 0.25f;
            float subtitleBottom = 0.10f;
            // 随机特效，更多特效见：https://help.aliyun.com/document_detail/207059.html
            //List<String> vfxs = Arrays.asList("heartfireworks", "colorfulradial", "meteorshower", "starry", "colorfulstarry", "moons_and_stars", "flyfire", "starexplosion", "spotfall", "sparklestarfield");
            // 随机转场，更多转场见：https://help.aliyun.com/document_detail/204853.html
            List<String> transitions = Arrays.asList("windowslice", "displacement", "bowTieVertical", "linearblur", "waterdrop", "polka", "wiperight", "gridflip", "hexagonalize", "windowblinds");
            //float transDuration = 0.3f;
            float transDuration = 0.6f;
            double totalDuration = 0f;
            //Collections.shuffle(vfxs);
            while (mpsSubtitles.hasNext()) {
                JsonNode mpsSubtitle = mpsSubtitles.next();
                String content = mpsSubtitle.get("text").asText();
                content = content.replaceAll("。", "");

                double timelineIn = mpsSubtitle.get("begin_time").asDouble() / 1000;
                double timelineOut = mpsSubtitle.get("end_time").asDouble() / 1000;
                totalDuration = timelineOut;
                String subtitleClip = "{\"Content\":\"" + content + "\",\"TimelineIn\":" + timelineIn + ",\"TimelineOut\":" + timelineOut +
                        ",\"Type\":\"Text\",\"X\":0.0,\"Y\":" + subtitleBottom + ",\"Font\":\"" + fontName + "\",\"Alignment\":\"BottomCenter\",\"FontSize\":" + fontSize +
                        ",\"FontColor\":\"" + fontColor + "\",\"OutlineColour\":\"#000000\"}";
                subtitleTrackClips.addRawValue(new RawValue(subtitleClip));
            }
            if (totalDuration < 18f) {
                totalDuration = 18f;
            }

            //图片固定时长轮播
            float duration = 3.0f;
            for (int i = 0; i * duration <= totalDuration; i++) {

                // 随机特效
                //String vfx = vfxs.get(i % vfxs.size());
                //String transition = transitions.get(i % transitions.size());
                //String transition = StringUtils.join(transitions, ",");
                String transition = String.join(",", transitions);
                String url = videoUrls.get(i % videoUrls.size());
                ObjectNode clip = mapper.createObjectNode();
                clip.put("MediaURL", url);
                if (!url.endsWith(".mp4")) {
                    clip.put("Duration", duration + transDuration);
                    clip.put("Type", "Image");
                } else {
                    clip.put("Out", duration + transDuration);
                }
                ArrayNode effects = mapper.createArrayNode();
                // 添加背景模糊
                effects.addRawValue(new RawValue("{\"Type\":\"Background\",\"SubType\":\"Blur\",\"Radius\":0.1}"));
                // 添加氛围类特效
                //effects.addRawValue(new RawValue("{\"Type\":\"VFX\",\"SubType\":\"" + vfx + "\"}"));
                // 视频静音
                //effects.addRawValue(new RawValue("{\"Type\":\"Volume\",\"Gain\":0}"));
                // 添加转场
                effects.addRawValue(new RawValue("{\"Type\":\"Transition\",\"SubType\":\"" + transition + "\",\"Duration\":" + transDuration + "}"));

                clip.put("Effects", effects);
                videoTrackClips.add(clip);
            }

            if (title != null && title.length() > 0) {
                float titleY = 80;
                int titleFontSize = 65;
                String titleFont = "AlibabaPuHuiTi";
                String titleClip = "{\"Type\":\"Text\",\"X\":0,\"Y\":" + titleY + ",\"Font\":\"" + titleFont + "\",\"Content\":\"" + title + "\",\"Alignment\":\"TopCenter\",\"FontSize\":" + titleFontSize + ",\"FontColor\":\"#FFD700\",\"Outline\":4,\"OutlineColour\":\"#000000\",\"FontFace\":{\"Bold\":true,\"Italic\":false,\"Underline\":false}}";
                subtitleTrackClips.addRawValue(new RawValue(titleClip));
            }
            //if (subtitle != null && subtitle.length() > 0) {
            //    float subtitleY = 160;
            //    int subtitleFontSize = 60;
            //    String subtitleFont = "AlibabaPuHuiTi";
            //    String subtitleClip = "{\"Type\":\"Text\",\"X\":0,\"Y\":" + subtitleY + ",\"Font\":\"" + subtitleFont + "\",\"Content\":\"" + subtitle + "\",\"Alignment\":\"TopCenter\",\"FontSize\":" + subtitleFontSize + ",\"FontColorOpacity\":1,\"FontColor\":\"#ffffff\",\"Outline\":2,\"OutlineColour\":\"#000000\",\"FontFace\":{\"Bold\":false,\"Italic\":false,\"Underline\":false}}";
            //    subtitleTrackClips.add(JSONObject.parse(subtitleClip));
            //}

            // 组装音频轨
            if (StringUtils.isBlank(bgMusic)) {
                bgMusic = aliMediaProperties.getBgMusicUrl();
            }
            String audioTrackClips = "";
            if (StringUtils.isBlank(bgMusic)) {
                if (StringUtils.isNotBlank(audioUrl)) {
                    audioTrackClips = "[{\"MediaURL\":\"" + audioUrl + "\"}]";
                }
            } else {
                if (StringUtils.isNotBlank(audioUrl)) {
                    // 两个音频轨，一个人声，一个音乐
                    audioTrackClips = "[{\"MediaURL\":\"" + audioUrl + "\"}]},{\"AudioTrackClips\":[{\"MediaURL\":\"" + bgMusic + "\",\"Effects\":[{\"Type\":\"Volume\",\"Gain\":\"" + 0.2 + "\"}]}]";
                } else {
                    audioTrackClips = "[{\"MediaURL\":\"" + bgMusic + "\",\"Effects\":[{\"Type\":\"Volume\",\"Gain\":\"" + 0.2 + "\"}]}]";
                }
            }

            // 图片轨，用于展示logo
            String logoClip = "";
            //int logoWidth = 32;
            //int logoHeight = 36;
            //if (StringUtils.isNotBlank(logoUrl)) {
            //    logoClip = "{\"ImageURL\":\"" + logoUrl + "\",\"X\":" + logoX + ",\"Y\":"
            //            + logoY + ",\"Width\":\"" + logoWidth + "\",\"Height\":\"" + logoHeight + "\"}";
            //}

            // 拼时间线
            String timeline = "{\"VideoTracks\":[{\"VideoTrackClips\":" + videoTrackClips.toString() + "}]," +
                    "\"SubtitleTracks\":[{\"SubtitleTrackClips\":" + subtitleTrackClips.toString() + "}]," +
                    "\"AudioTracks\":[{\"AudioTrackClips\":" + audioTrackClips + "}]," +
                    "\"ImageTracks\":[{\"ImageTrackClips\":[" + logoClip + "]}]}";
            System.out.println("timeline : " + timeline);

            int height = aliMediaProperties.getOutputVideoHeight();
            int width = aliMediaProperties.getOutputVideoWidth();
            // 提交合成任务
            SubmitMediaProducingJobRequest submitMediaProducingJobRequest = new SubmitMediaProducingJobRequest();
            submitMediaProducingJobRequest.setTimeline(timeline);
            final String dateString = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String mediaURL = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + aliMediaProperties.getMediaProduceDir() + "/" + dateString + "/" + DigestUtils.md5Hex(String.valueOf(Math.random())) + ".mp4";
            submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height + "}");
            //submitIProductionJobRequest.setNotifyUrl(notifyUrl); //智能媒体剪辑不支持在这个方法上设置回调通知
            //submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\""+notifyUrl+"\"}"); //http方式回调
            submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\"" + (aliMediaProperties.determineJobCallbackMnsQueueByTag(jobQueueTag)) + "\"}"); //MNS回调地址（必须以ice-callback开头的消息队列才可以进行回调）
            SubmitMediaProducingJobResponse submitMediaProducingJobResponse = iceClient.submitMediaProducingJob(submitMediaProducingJobRequest);
            final String jobId = submitMediaProducingJobResponse.body.jobId;
            log.info("{}-视频合成服务,提交任务响应,jobId:{},mediaURL:{},timeline:{}", LOG_TAG, jobId, mediaURL, timeline);
            return jobId;
        } catch (Exception e) {
            log.info("{}-视频合成服务,异常:", LOG_TAG, e);
            return null;
        }
    }


    /**
     * 阿里云云剪辑合成视频(AI专用)
     *
     * @param jobQueueTag              job任务tag(该参数决定通知的队列)
     * @param imageUrls                图片或者视频oss地址
     * @param zhmbImageToVideoTemplate 图片风格化模板
     * @return 视频转换jobId
     */
    public String produceVideoForAi(int jobQueueTag, List<String> imageUrls, ZhmbImageToVideoTemplate zhmbImageToVideoTemplate) {
        try {
            // 组装字幕轨
            int fontSize = 32;
            String fontName = "WenQuanYi Zen Hei Mono";
            String fontColor = "#FFFFFF";
            // 每次循环将视频素材随机，并提交合成任务
            //Collections.shuffle(videoUrls);

            final ArrayNode subtitleTrackClips = mapper.createArrayNode();
            final ArrayNode videoTrackClips = mapper.createArrayNode();

            // 字幕距离底部距离
            //float subtitleBottom = 0.25f;
            float subtitleBottom = 0.10f;
            // 随机特效，更多特效见：https://help.aliyun.com/document_detail/207059.html
            //List<String> vfxs = Arrays.asList("heartfireworks", "colorfulradial", "meteorshower", "starry", "colorfulstarry", "moons_and_stars", "flyfire", "starexplosion", "spotfall", "sparklestarfield");
            // 随机转场，更多转场见：https://help.aliyun.com/document_detail/204853.html
            String transition = zhmbImageToVideoTemplate.getTransition();
            String vfx = zhmbImageToVideoTemplate.getVfx();
            String bgMusic = zhmbImageToVideoTemplate.getBgmUrl();
            //float transDuration = 0.3f;
            float transDuration = Float.parseFloat(zhmbImageToVideoTemplate.getTransDuration());
            double totalDuration = imageUrls.size() * 3f;

            //图片固定时长轮播
            float duration = Float.parseFloat(zhmbImageToVideoTemplate.getFirstDuration());
            for (int i = 0; i * duration < totalDuration; i++) {
                if (i > 0) {
                    duration = Float.parseFloat(zhmbImageToVideoTemplate.getSedondDuration());
                }
                // 随机特效
                //String vfx = vfxs.get(i % vfxs.size());
                //String transition = transitions.get(i % transitions.size());
                //String transition = StringUtils.join(transitions, ",");
                String url = imageUrls.get(i % imageUrls.size());
                ObjectNode clip = mapper.createObjectNode();
                clip.put("MediaURL", url);
                if (!url.endsWith(".mp4")) {
                    clip.put("Duration", duration + transDuration);
                    clip.put("Type", "Image");
                } else {
                    clip.put("Out", duration + transDuration);
                }
                ArrayNode effects = mapper.createArrayNode();
                // 添加背景模糊
                effects.addRawValue(new RawValue("{\"Type\":\"Background\",\"SubType\":\"Blur\",\"Radius\":0.1}"));
                // 添加氛围类特效
                effects.addRawValue(new RawValue("{\"Type\":\"VFX\",\"SubType\":\"" + vfx + "\"}"));
                // 视频静音
                //effects.addRawValue(new RawValue("{\"Type\":\"Volume\",\"Gain\":0}"));
                // 添加转场
                effects.addRawValue(new RawValue("{\"Type\":\"Transition\",\"SubType\":\"" + transition + "\",\"Duration\":" + transDuration + "}"));
                clip.put("Effects", effects);
                videoTrackClips.add(clip);
            }


            // 组装音频轨
            if (StringUtils.isBlank(bgMusic)) {
                bgMusic = aliMediaProperties.getBgMusicUrl();
            }
            String audioTrackClips = "[{\"MediaURL\":\"" + bgMusic + "\",\"LoopMode\": true,\"Effects\":[{\"Type\":\"Volume\",\"Gain\":\"" + 0.2 + "\"}]}]";

            // 图片轨，用于展示logo
            String logoClip = "";
            //int logoWidth = 32;
            //int logoHeight = 36;
            //if (StringUtils.isNotBlank(logoUrl)) {
            //    logoClip = "{\"ImageURL\":\"" + logoUrl + "\",\"X\":" + logoX + ",\"Y\":"
            //            + logoY + ",\"Width\":\"" + logoWidth + "\",\"Height\":\"" + logoHeight + "\"}";
            //}

            // 拼时间线
            String timeline = "{\"VideoTracks\":[{\"VideoTrackClips\":" + videoTrackClips.toString() + "}]," +
                    "\"SubtitleTracks\":[{\"SubtitleTrackClips\":" + subtitleTrackClips.toString() + "}]," +
                    "\"AudioTracks\":[{\"AudioTrackClips\":" + audioTrackClips + "}]," +
                    "\"ImageTracks\":[{\"ImageTrackClips\":[" + logoClip + "]}]}";
            System.out.println("timeline : " + timeline);

            int height = aliMediaProperties.getOutputVideoHeight();
            int width = aliMediaProperties.getOutputVideoWidth();
            // 提交合成任务
            SubmitMediaProducingJobRequest submitMediaProducingJobRequest = new SubmitMediaProducingJobRequest();
            submitMediaProducingJobRequest.setTimeline(timeline);
            final String dateString = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String mediaURL = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + aliMediaProperties.getMediaProduceDir() + "/" + dateString + "/" + DigestUtils.md5Hex(String.valueOf(Math.random())) + ".mp4";
            submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height + "}");
            //submitIProductionJobRequest.setNotifyUrl(notifyUrl); //智能媒体剪辑不支持在这个方法上设置回调通知
            //submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\""+notifyUrl+"\"}"); //http方式回调
            submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\"" + (aliMediaProperties.determineJobCallbackMnsQueueByTag(jobQueueTag)) + "\"}"); //MNS回调地址（必须以ice-callback开头的消息队列才可以进行回调）
            SubmitMediaProducingJobResponse submitMediaProducingJobResponse = iceClient.submitMediaProducingJob(submitMediaProducingJobRequest);
            final String jobId = submitMediaProducingJobResponse.body.jobId;
            log.info("{}-视频合成服务,提交任务响应,jobId:{},mediaURL:{},timeline:{}", LOG_TAG, jobId, mediaURL, timeline);
            return jobId;
        } catch (Exception e) {
            log.info("{}-视频合成服务,异常:", LOG_TAG, e);
            return null;
        }
    }

    /**
     * 获取模板信息
     */
    public String fetchTemplateInfo(String templateId) {
        GetTemplateRequest request = new GetTemplateRequest();
        request.setTemplateId(templateId);
        try {
            GetTemplateResponse response = iceClient.getTemplate(request);
            GetTemplateResponseBody.GetTemplateResponseBodyTemplate template = response.getBody().getTemplate();
            return template.getClipsParam();
        } catch (Exception e) {
            log.info("{}-获取模板信息,异常:", LOG_TAG, e);
            return null;
        }
    }

    /**
     * 模板合成
     *
     * @param jobQueueTag job任务tag(该参数决定通知的队列)
     * @param templateId
     * @param clipsParam
     * @return
     */
    public String mergeTemplate(int jobQueueTag, String templateId, String clipsParam) {
        int height = aliMediaProperties.getOutputVideoHeight();
        int width = aliMediaProperties.getOutputVideoWidth();
        SubmitMediaProducingJobRequest submitMediaProducingJobRequest = new SubmitMediaProducingJobRequest();
        submitMediaProducingJobRequest.setTemplateId(templateId);
        submitMediaProducingJobRequest.setClipsParam(clipsParam);
        final String dateString = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String mediaURL = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + aliMediaProperties.getMediaProduceDir() + "/" + dateString + "/" + DigestUtils.md5Hex(String.valueOf(Math.random())) + ".mp4";
        System.out.println("mediaURL = " + mediaURL);
        //输出参数参看 https://help.aliyun.com/document_detail/357745.html?spm=a2c4g.441147.0.0.11bf5664mMawEA#title-4j6-ve7-g31
        //submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height + "}");
        //submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height + ",\"Video\":{\"Codec\":\"H.264\",\"Fps\":22,\"Profile\":\"main\",\"Crf\":23}"+ "}");
        submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height + ",\"Bitrate\":" + 2000 + ",\"Video\":{\"Codec\":\"H.264\",\"Fps\":22,\"Profile\":\"main\",\"Preset\":\"medium\",\"Crf\":20}" + "}");
        submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\"" + (aliMediaProperties.determineJobCallbackMnsQueueByTag(jobQueueTag)) + "\"}"); //MNS回调地址（必须以ice-callback开头的消息队列才可以进行回调）
        try {
            SubmitMediaProducingJobResponse response = iceClient.submitMediaProducingJob(submitMediaProducingJobRequest);
            return response.getBody().getJobId();
        } catch (Exception e) {
            log.info("{}-模板合成,异常:", LOG_TAG, e);
            return null;
        }
    }

    public String createTemplate(String templateName, File previewMp4File, File templateZipFile) throws Exception {
        String templateOssPath = "template/advance/";
        final String mp4FileOssPath = templateOssPath + previewMp4File.getName();
        putObjectFile(mp4FileOssPath, previewMp4File);
        final String mp4FileUrl = "oss://" + bucket + "/" + mp4FileOssPath;
        final String mp4MediaId = registerMedia(mp4FileUrl);
        final String zipFileOssPath = templateOssPath + templateZipFile.getName();
        putObjectFile(zipFileOssPath, templateZipFile);
        //final String zipFileUrl = "oss://" + bucket + "/" + mp4FileOssPath;
        final String zipFileUrl = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + zipFileOssPath;
        AddTemplateRequest request = new AddTemplateRequest();
        request.setType("VETemplate");
        request.setName(templateName);
        request.setConfig("{\"oss_url\":\"" + zipFileUrl + "\"}");
        request.setPreviewMedia(mp4MediaId);
        AddTemplateResponse response = iceClient.addTemplate(request);
        final String templateId = response.getBody().getTemplate().getTemplateId();
        //System.out.println("templateId : " + templateId);
        log.info("创建模板=>名称:{},模板id:{},响应:{}", previewMp4File.getName(), templateId, mapper.writeValueAsString(response));
        return templateId;
    }

    public String waitTemplateCreateAndGenJsonFile(Path templateDirPath, String templateId) {
        String newTemplateInfo = null;
        try {
            Stopwatch stopWatch = Stopwatch.createStarted();
            // 等待合成任务完成
            while (true) {
                final String templateInfo = fetchTemplateInfo(templateId);
                if (StringUtils.isNotEmpty(templateInfo)) {
                    newTemplateInfo = templateInfo.replaceAll("mediaId", "960:540");
                    log.info("模板信息抓取=>templateId:{},,阿里模板解析用时:{},newTemplateInfo:{}", templateId, stopWatch, newTemplateInfo);
                    final String jsonFileName = FilenameUtils.concat(templateDirPath.toAbsolutePath().toString(), templateId + ".json");
                    FileUtils.write(new File(jsonFileName), newTemplateInfo, StandardCharsets.UTF_8);
                    break;
                }
                //
                if (stopWatch.elapsed(TimeUnit.SECONDS) > 60L) {
                    log.info("模板信息抓取=>templateId:{},,阿里模板创建失败:{}", templateId, templateDirPath.toAbsolutePath().toString());
                    final String jsonFileName = FilenameUtils.concat(templateDirPath.toAbsolutePath().toString(), "create_template_error.json");
                    FileUtils.write(new File(jsonFileName), newTemplateInfo, StandardCharsets.UTF_8);
                    break;
                }
                Thread.sleep(1000);
            }

        } catch (Exception e) {
            log.info("等待模板创建异常:", e);
        }
        return newTemplateInfo;
    }

    /**
     * 注册媒资内容
     *
     * @param inputUrl oss地址  如: oss://example-bucket/example.mp4 （此格式默认oss region与服务接入区域一致
     * @return
     * @throws Exception
     */
    public String registerMedia(String inputUrl) throws Exception {
        try {
            RegisterMediaInfoRequest registerMediaInfoRequest = new RegisterMediaInfoRequest();
            //inputUrl如果为oss地址包含region信息就去掉
            //final String newInputUrl = StringUtils.startsWith(inputUrl, "oss:") ? inputUrl.replace(".oss-" + regionId + ".aliyuncs.com/", "/") : inputUrl;
            registerMediaInfoRequest.setInputURL(inputUrl);
            //registerMediaInfoRequest.setMediaType("video");//此字段建议用户按需填写。当InputURL字段是OSS URL时，也支持按照文件后缀自动判断媒资类型（仅限图片、视频、音频文件后缀）
            RegisterMediaInfoResponse registerMediaInfoResponse = iceClient.registerMediaInfo(registerMediaInfoRequest);
            return registerMediaInfoResponse.getBody().getMediaId();
        } catch (TeaException e) {
            //com.aliyun.tea.TeaException: code: 409, The media with the given inputUrl "oss://ims-media/template/advance/高级模板API上传测试.mp4" has already been registered with mediaId "c99364e006a371eeb652f6f6c5596302". request id: 432CF594-A7A0-58DA-9682-18A69060FFE4
            final String message = e.getMessage();
            if (message.contains("registered with mediaId")) {
                return StringUtils.substringBetween(message, "registered with mediaId \"", "\".");
            }
            throw e;
        }
    }

    public void putObjectFile(String object, File file) throws Exception {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, object, file);
        ossClient.putObject(putObjectRequest);
    }

    public List<String> listObjects(String currentDir){
        ListObjectsRequest listObjectsReq = new ListObjectsRequest();
        listObjectsReq.setBucketName(bucket);
        listObjectsReq.setPrefix(currentDir);    // 设置目录前缀
        listObjectsReq.setDelimiter("/");        // 设置目录分隔符
        listObjectsReq.setMaxKeys(100);          // 单次请求最大文件数（1000为上限）

        // 执行请求
        ObjectListing objectListing = ossClient.listObjects(listObjectsReq);
        List<String> keyList = new ArrayList<>();
        for (OSSObjectSummary objectSummary : objectListing.getObjectSummaries()) {
            String key = objectSummary.getKey();
            // 排除目录本身（如果是空目录）
            if (!key.endsWith("/")) {
                System.out.println("文件路径: " + key);
            }
            keyList.add(key);

        }
        return keyList;
    }

    public String putObjectFile(String object, InputStream inputStream) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, object, inputStream);
        ossClient.putObject(putObjectRequest);
        return "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + object;
    }


    public void putObjectContent(String object, String content) throws Exception {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, object, new ByteArrayInputStream(content.getBytes()));
        ossClient.putObject(putObjectRequest);
    }

    public String getObjectContent(String object) throws Exception {
        InputStream stream = getObjectInputStream(object);
        return CharStreams.toString(new InputStreamReader(stream, StandardCharsets.UTF_8));
    }

    public String putObjectBase64(String object, String base64Str) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, object, new ByteArrayInputStream(Base64.getDecoder().decode(base64Str)));
        ossClient.putObject(putObjectRequest);

        return "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + object;
    }


    public String putObjectRemoteUrl(String object, String remoteUrl) throws Exception {
        InputStream inputStream = new URL(remoteUrl).openStream();
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, object, inputStream);
        ossClient.putObject(putObjectRequest);
        return "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + object;
    }

    public String putObjectRemoteUrlCdn(String object, String remoteUrl) throws Exception {
        InputStream inputStream = new URL(remoteUrl).openStream();
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, object, inputStream);
        ossClient.putObject(putObjectRequest);
        return aliMediaProperties.getStaticDomain() + object;
    }


    public InputStream getObjectInputStream(String object) {
        String aliDomain = "aliyuncs.com/";
        if (StringUtils.contains(object, aliDomain)) {
            object = StringUtils.substringAfter(object, aliDomain);
        }
        OSSObject obj = ossClient.getObject(bucket, object);
        return obj.getObjectContent();
    }
    public void deleteObject(String object) {
        try {
            ossClient.deleteObject(bucket, object);
        } catch (Exception e) {
            log.warn("删除oss文件失败:{},{}",bucket, object);
        }

    }
}
