package com.eleven.qycl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.qycl.entity.QyclRingTemplate;

import java.nio.file.Path;

/**
 * @Description: qycl_ring_template
 * @Author: jeecg-boot
 * @Date:   2023-05-18
 * @Version: V1.0
 */
public interface IQyclRingTemplateService extends IService<QyclRingTemplate> {

    IPage<QyclRingTemplate> ringTemplateListByColumnId(IPage<QyclRingTemplate> page, String columnId);

    void generateTemplate(Path templateContainerPath);

    void generateAITemplate(Path templateContainerPath);

    void generateFaceAITemplate(Path templateContainerPath);
}
