package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IQyclWxpayService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.qycl.entity.QyclOrderPayLog;
import com.eleven.qycl.mapper.QyclOrderPayLogMapper;
import com.eleven.qycl.service.IQyclOrderPayLogService;
import com.eleven.qycl.service.TtsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: qycl_order_pay_log
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Slf4j
@Service
public class QyclOrderPayLogServiceImpl extends ServiceImpl<QyclOrderPayLogMapper, QyclOrderPayLog> implements IQyclOrderPayLogService {
    //未支付
    private static final Integer NO_PAY_STATUS=-1;
    //已支付
    private static final Integer PAY_STATUS_SUCCESS=1;
    //支付失败
    private static final Integer PAY_STATUS_FAIL=0;
    //退款失败
    private static final Integer REFUND_FAIL=5;
    //退款成功
    private static final Integer REFUND_SUCCESS=4;
    //退款中
    private static final Integer REFUND_PREPARE=6;
    @Autowired
    TtsService ttsService;
    @Autowired
    private IQyclWxpayService wxpayService;
    @Autowired
    RedisUtil redisUtil;






    @Override
    public String saveNotPaymentOrder(String openId,String companyTitle, String mobile, String companyOwner,String channel,String subChannel,String transactionId) {
        QyclOrderPayLog order = new QyclOrderPayLog();
        order.setOpenId(openId);
        order.setCompanyTitle(companyTitle);
        order.setMobile(mobile);
        order.setPayStatus(NO_PAY_STATUS);
        order.setChannel(channel);
        order.setSubChannel(subChannel);
        order.setCreateTime(new Date());
        order.setTransactionId(transactionId);
        this.save(order);
        return order.getId();
    }

    @Override
    public Result douYinRefund(String outTradeNo, String refund){
        String refundOrderNo = IdWorker.get32UUID();
        //退款失败可以重复退款
        QyclOrderPayLog orderPay=this.lambdaQuery().eq(QyclOrderPayLog::getId, outTradeNo).in(QyclOrderPayLog::getPayStatus, PAY_STATUS_SUCCESS, REFUND_FAIL).orderByDesc(QyclOrderPayLog::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        Double refunds=Double.valueOf(refund);
        Double totalFee=orderPay.getTotalFee();
        if(refunds>totalFee){
            return Result.error("退款金额大于支付金额！");
        }
        if(StringUtils.isBlank(orderPay.getAppId()) || StringUtils.isBlank(orderPay.getMchId()) ){
            return Result.error("退款订单暂不支持！");
        }
        //退款金额
        String refundAmount= BigDecimal.valueOf(refunds).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
        if(StringUtils.equalsAny(orderPay.getTradeType(),BizConstant.TRADE_TYPE_TIKTOK_TRADE)){
            try{
                FebsResponse febsRefund=wxpayService.douYinRefund(orderPay.getOutTradeNo(),refundOrderNo,refundAmount,orderPay.getAppId(),orderPay.getMchId());
//                FebsResponse febsRefund=wxpayService.douYinCreateRefund(orderPay.getId(),orderPay.getOutTradeNo(),refundOrderNo,refundAmount,orderPay.getAppId(),orderPay.getMchId());
                if(febsRefund.isOK()){
                    upadateRefundStatus(outTradeNo, refund, refundOrderNo, "正在退款！", REFUND_PREPARE);
                    return Result.ok("正在退款！");
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        upadateRefundStatus(outTradeNo, refund, refundOrderNo, "退款失败！", REFUND_FAIL);
        return Result.error("退款失败！");
    }

    /**
     * 更新退款状态和备注
     * @param outTradeNo
     * @param refundAmount
     * @param refundOrderNo
     * @param refundRemark
     * @param refundStatus
     */
    @Override
    public void upadateRefundStatus(String outTradeNo, String refundAmount, String refundOrderNo, String refundRemark, Integer refundStatus) {
        this.lambdaUpdate()
                .eq(QyclOrderPayLog::getId, outTradeNo)
                .set(QyclOrderPayLog::getPayStatus, refundStatus)
                .set(QyclOrderPayLog::getRefundOrderNo, refundOrderNo)
                .set(QyclOrderPayLog::getRefundAmount, refundAmount)
                .set(QyclOrderPayLog::getRefundRemark, refundRemark)
                .set(QyclOrderPayLog::getRefundTime, new Date())
                .set(QyclOrderPayLog::getUpdateTime, new Date()).update();
    }


    @Override
    public QyclOrderPayLog queryOrder(String orderId){
        return this.lambdaQuery().eq(QyclOrderPayLog::getId,orderId).eq(QyclOrderPayLog::getPayStatus,NO_PAY_STATUS).orderByDesc(QyclOrderPayLog::getCreateTime).last("limit 1").one();
    }

    @Override
    public void updatePayStatus(String outTradeNo, String transactionId, Integer status) {
       this.lambdaUpdate().eq(QyclOrderPayLog::getId, outTradeNo).eq(QyclOrderPayLog::getPayStatus, NO_PAY_STATUS)
                .set(QyclOrderPayLog::getPayStatus, status)
                .set(QyclOrderPayLog::getOutTradeNo, transactionId)
                .set(QyclOrderPayLog::getNotifyTime, new Date())
                .set(QyclOrderPayLog::getPayTime, new Date())
                .set(QyclOrderPayLog::getUpdateTime, new Date()).update();
    }

    /**
     * 根据渠道订单主键id查询是否支付成功
     * @param transactionId
     * @return
     */
    @Override
    public Boolean queryOrderPaySuccess(String transactionId){
        return this.lambdaQuery().eq(QyclOrderPayLog::getTransactionId,transactionId).eq(QyclOrderPayLog::getPayStatus,PAY_STATUS_SUCCESS).count()>0;
    }


    @Override
    public QyclOrderPayLog queryRefundOrder(String orderId){
        return this.lambdaQuery().eq(QyclOrderPayLog::getOutTradeNo,orderId).eq(QyclOrderPayLog::getPayStatus,REFUND_PREPARE).orderByDesc(QyclOrderPayLog::getCreateTime).last("limit 1").one();
    }

}
