package com.eleven.qycl.service;

import com.eleven.cms.dto.DataCollectDto;
import com.eleven.qycl.entity.DataCollect;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * @Description: 企业彩铃数据汇总
 * @Author: jeecg-boot
 * @Date:   2024-01-19
 * @Version: V1.0
 */
public interface IDataCollectService extends IService<DataCollect> {

    void dataCollect();

    void updateDataNotify();

    DataCollectDto findDataCollectCount(DataCollectDto dto);

    List<DataCollectDto> findProvinceList(DataCollectDto dto);

    List<DataCollectDto> findExecuteDateList(DataCollectDto dto);

    List<DataCollectDto> pageDataCollectList(DataCollectDto dto);

    List<DataCollect> pageDownXlsxDataCollectList(DataCollectDto dto);


    List<DataCollectDto> downXlsxFindExecuteDateList(DataCollectDto dto);
}
