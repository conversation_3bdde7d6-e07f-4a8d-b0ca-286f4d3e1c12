package com.eleven.qycl.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.BillingResult;
import com.eleven.qycl.config.EnterpriseVrbtConfig;
import com.eleven.qycl.config.EnterpriseVrbtProperties;
import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.entity.QyclCompany;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 企业视频彩铃平台接口
 *
 * @author: cai lei
 * @create: 2022-04-07 09:51
 */
@Slf4j
@Service
public class EnterpriseVrbtService {

    @Autowired
    private EnterpriseVrbtProperties enterpriseVrbtProperties;
    @Autowired
    private Environment environment;
    @Autowired
    private Interceptor enterpriseVrbtIntercept;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IDatangSmsService datangSmsService;

    public static final String LOG_TAG = "企业视频彩铃平台api";

    public static final String RING_TYPE_VEDIO = "1";
    public static final String RING_TYPE_AUDIO = "2";

    private OkHttpClient client;
    private ObjectMapper mapper;
    @Autowired
    private IPortCrackConfigService portCrackConfigService;
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    public static final MediaType OCTET_STREAM = MediaType.parse(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE);

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().readTimeout(240L, TimeUnit.SECONDS).writeTimeout(240L, TimeUnit.SECONDS).addInterceptor(enterpriseVrbtIntercept).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            //this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }

        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }


    public void networkTest(){
        Request request = new Request.Builder()
                .url("https://httpbin.org/ip")
                //.post(null)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-网络测试,响应:{}", LOG_TAG,  content);

        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-网络测试,异常:", LOG_TAG, e);
        }
    }

    /**
     * 集团铃音上传接口
     * {"code":"000000","info":"操作成功","data":{"streamNumber":"14153820"}}
     *
     * @param ringType 1-视频  2-音频 使用本类的常量RING_TYPE_VEDIO RING_TYPE_AUDIO
     * @return
     */
    public @Nonnull EntVrbtResult submitRingByChannel(String departmentId, String ringName, File ringFile, String ringType,String companyOwner,String channel) {
        RequestBody ringFileBody = RequestBody.create(OCTET_STREAM, ringFile);
        return ringUpload(departmentId, ringName, ringFileBody, ringType, companyOwner,channel);
    }

    /**
     * 集团铃音上传接口(字节流方式)
     * {"code":"000000","info":"操作成功","data":{"streamNumber":"14153820"}}
     *
     * @param ringType 1-视频  2-音频 使用本类的常量RING_TYPE_VEDIO RING_TYPE_AUDIO
     * @return
     */
    public @Nonnull EntVrbtResult submitRingByCompanyOwner(String departmentId, String ringName, InputStream ringInputStream, String ringType,String companyOwner,String channel) throws IOException {
        //RequestBody ringFileBody = RequestBody.create(OCTET_STREAM, ringFile);
        //byte[] bytes = IOUtils.toByteArray(ringInputStream);
        //RequestBody ringFileBody = RequestBody.create(OCTET_STREAM, bytes);
        RequestBody ringFileBody = OkHttpClientUtils.createInputStreamRequestBody(ringInputStream);
        return ringUpload(departmentId, ringName, ringFileBody, ringType, companyOwner,channel);
    }

    private EntVrbtResult ringUpload(String departmentId, String ringName, RequestBody ringFileBody, String ringType,String companyOwner,String channel) {
//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},集团铃音上传-部门Id:{},ringName:{},异常:", LOG_TAG, companyOwner, departmentId, ringName);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getSubmitRingUrl());
        log.info("{}-companyOwner:{},集团铃音上传-部门Id:{},ringName:{},请求:{}", LOG_TAG, companyOwner, departmentId, ringName, httpUrl.toString());
        final File ringCopyrightFile = enterpriseVrbtConfig.getRingCopyrightFile();
        RequestBody copyrightBody = RequestBody.create(OCTET_STREAM, ringCopyrightFile);
        MultipartBody multipartBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("orderId", enterpriseVrbtConfig.getOrderId())
                .addFormDataPart("departmentId", departmentId)
                //{"success":false,"code":"000004","info":"铃音名称[ringName]不能超过35个字符！"}
                .addFormDataPart("ringName", StringUtils.substring(ringName,0,35))
                //.addFormDataPart("ringFile", DigestUtils.md5Hex(ringName) +".mp4", ringFileBody)
                .addFormDataPart("ringFile", IdWorker.get32UUID() + (EnterpriseVrbtService.RING_TYPE_VEDIO.equals(ringType) ? ".mp4" : ".mp3"), ringFileBody)
                .addFormDataPart("copyrightFile", ringCopyrightFile.getName(), copyrightBody)
                .addFormDataPart("ringType", ringType) //1-视频  2-音频
                //.addFormDataPart("extractAudio", EnterpriseVrbtService.RING_TYPE_VEDIO.equals(ringType) ? "1" : "0") //当ringType为1时可填，默认为0不提取，1为提取
                .addFormDataPart("extractAudio", "0") //当ringType为1时可填，默认为0不提取，1为提取
                .build();
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(multipartBody)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},集团铃音上传-部门Id:{},ringName:{},响应:{}", LOG_TAG, companyOwner, departmentId, ringName, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},集团铃音上传-部门Id:{},ringName:{},异常:", LOG_TAG, companyOwner, departmentId, ringName, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 铃音设置接口
     *
     * @param  ringType 1-视频  2-音频 使用本类的常量RING_TYPE_VEDIO RING_TYPE_AUDIO
     * @return  {"code":"000000","info":"success"}
     */
    public @Nonnull EntVrbtResult setDeptRingsByTimeChannel(String departmentId, String ringType, String companyOwner,String channel, String... ringIds) {
//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},铃音设置-部门Id:{},ringIds:{},异常:", companyOwner, LOG_TAG, departmentId, ringIds);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId())
                .put("departmentId", departmentId)
                .put("startTime", "00:00:00")
                .put("endTime", "23:59:59")
                .put("ringType", ringType) //1-视频  2-音频
                .putPOJO("ringIds", mapper.valueToTree(ringIds));

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getSetDeptRingsByTimeUrl());
        log.info("{}-companyOwner:{},铃音设置-部门Id:{},ringIds:{},请求:{}", LOG_TAG, companyOwner, departmentId, ringIds, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},铃音设置-部门Id:{},ringIds:{},响应:{}",companyOwner, LOG_TAG, departmentId, ringIds, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},铃音设置-部门Id:{},ringIds:{},异常:", companyOwner, LOG_TAG, departmentId, ringIds, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 集团铃音数据查询接口
     *
     * @param
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.QYCL_DEPARTMENT_RING_CACHE,key = " #p0 + ':' + #p1",unless = "#result==null")
    public @Nonnull
    EntVrbtResult searchEcRingByChannel(String departmentId, String companyOwner,String channel) {
//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},集团铃音数据查询-部门Id:{},异常:",  LOG_TAG,companyOwner ,departmentId);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId())
                .put("departmentId", departmentId);

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getSearchEcRingUrl());
        log.info("{}-companyOwner:{},集团铃音数据查询-部门Id:{},请求:{}", LOG_TAG,companyOwner, departmentId, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},集团铃音数据查询-部门Id:{},响应:{}", LOG_TAG,companyOwner, departmentId, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},集团铃音数据查询-部门Id:{},异常:",  LOG_TAG,companyOwner ,departmentId, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 集团部门铃音设置查询接口
     *
     * @param
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.QYCL_DEPARTMENT_USEDRING_CACHE,key = " #p0 + ':' + #p1",unless = "#result==null")
    public @Nonnull
    EntVrbtResult searchEcRingSettingByChannel(String departmentId, String companyOwner,String channel) {

//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},集团部门铃音设置查询-部门Id:{},异常:", LOG_TAG, companyOwner, departmentId);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId())
                .put("departmentId", departmentId);

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getSearchEcRingSettingUrl());
        log.info("{}-companyOwner:{},集团部门铃音设置查询-部门Id:{},请求:{}", LOG_TAG, companyOwner, departmentId, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},集团部门铃音设置查询-部门Id:{},响应:{}", LOG_TAG, companyOwner, departmentId, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},集团部门铃音设置查询-部门Id:{},异常:", LOG_TAG, companyOwner, departmentId, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 开启/关闭企业视频彩铃播放功能接口
     *
     * @param
     * @param  videoRingFunc 企业视频彩铃播放功能 1开启  0关闭
     * @return   {"code":"000000","info":"success"}
     */
    public @Nonnull EntVrbtResult updateVideoFuncByChannel(String departmentId, int videoRingFunc,String companyOwner,String channel) {
//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);

        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},开启/关闭企业视频彩铃播放功能-部门Id:{},videoRingFunc:{},异常:", LOG_TAG, companyOwner,departmentId, videoRingFunc);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId())
                .put("departmentId", departmentId)
                .put("videoRingFunc", videoRingFunc); //企业视频彩铃播放功能 1开启  0关闭

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getUpdateVideoFuncUrl());
        log.info("{}-companyOwner:{},开启/关闭企业视频彩铃播放功能-部门Id:{},videoRingFunc:{},请求:{}", LOG_TAG, companyOwner,departmentId, videoRingFunc, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},开启/关闭企业视频彩铃播放功能-部门Id:{},videoRingFunc:{},响应:{}", LOG_TAG, companyOwner,departmentId, videoRingFunc, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},开启/关闭企业视频彩铃播放功能-部门Id:{},videoRingFunc:{},异常:", LOG_TAG, companyOwner,departmentId, videoRingFunc, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 集团部门操作接口（新增/删除/修改部门）
     *
     * @param  opType 0-新建  1-删除 2-名称变更
     * @param  departmentName 部门名称,添加和修改时必填
     * @param  departmentId 部门ID（删除、修改时必填）
     * @return   新增: {"code":"000000","info":"操作成功","data":{"opType":"0","departmentId":"3099952770730"}}
     *           删除: {"code":"000000","info":"操作成功","data":{"opType":"1","departmentId":"3099952770730"}}
     *           修改: {"code":"000000","info":"操作成功","data":{"opType":"2","departmentId":"3099952770730"}}
     */
    public @Nonnull EntVrbtResult ecOperationByChannel(String opType,String departmentName,String departmentId,String companyOwner,String channel) {

//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},集团部门操作（新增/删除/修改部门）-opType:{},部门名称:{},部门Id:{},异常:", LOG_TAG,companyOwner, opType, departmentName, departmentId);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId())
                .put("opType", opType);
        switch (opType){
            case "0": //新建
                node.put("departmentName", departmentName);
                node.put("adminMsisdn", enterpriseVrbtConfig.getAdminMsisdn());
                node.put("contactPhone", enterpriseVrbtConfig.getContactPhone());
            break;
            case "1": //删除
                node.put("departmentId", departmentId);
                break;
            case "2": //修改
                node.put("departmentId", departmentId);
                node.put("departmentName", departmentName);
                break;
        }

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getEcOperationUrl());
        log.info("{}-companyOwner:{},集团部门操作（新增/删除/修改部门）-opType:{},部门名称:{},部门Id:{},请求:{}", LOG_TAG,companyOwner,opType, departmentName, departmentId, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},集团部门操作（新增/删除/修改部门）-opType:{},部门名称:{},部门Id:{},响应:{}", LOG_TAG,companyOwner, opType, departmentName, departmentId, content);
            EntVrbtResult entVrbtResult = mapper.readValue(content, EntVrbtResult.class);
            if (!entVrbtResult.isOK()) {
                try {
                    this.sendSms(departmentName, content);
                } catch (Exception e) {
                    log.error("创建部门失败发送告警短信错误:{}", e);
                }
            }
            return entVrbtResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},集团部门操作（新增/删除/修改部门）-opType:{},部门名称:{},部门Id:{},异常:", LOG_TAG,companyOwner, opType, departmentName, departmentId, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 部门数据查询接口
     *
     * @return
     */
    public @Nonnull EntVrbtResult searchEcDepartmentsByChannel(String companyOwner,String channel) {

//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},部门数据查询,异常:", LOG_TAG,companyOwner);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId())
                .put("adminMsisdn", enterpriseVrbtConfig.getAdminMsisdn());

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getSearchEcDepartmentsUrl());
        log.info("{}-companyOwner:{},部门数据查询,请求:{}", LOG_TAG,companyOwner, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},部门数据查询,响应:{}", LOG_TAG,companyOwner, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},部门数据查询,异常:", LOG_TAG,companyOwner, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 内容版企业成员添加接口
     * {"code":"000000","info":"操作成功","data":{"fullCapacity":false,"list":[]}}
     * {"code":"000000","info":"操作成功","data":{"fullCapacity":false,"list":[{"billNum":"15072569653","errorMsg":"已存在其他企业"}]}}
     * {"code":"000000","info":"操作成功","data":{"fullCapacity":false,"list":[{"billNum":"13699402402","errorMsg":"已存在订购待确认中的号码，不能重复执行！"}]}}
     * @param billNums 用户手机号码(限制50个号码量)
     * @return
     */
    public @Nonnull EntVrbtResult addContentMembersByChannel(String departmentId, String companyOwner,String channel, String... billNums) {

//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},内容版企业成员添加-部门Id:{},billNums:{},异常:", LOG_TAG,companyOwner, departmentId, billNums);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId())
                .put("departmentId", departmentId)
                .putPOJO("billNums", mapper.valueToTree(billNums));

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getAddContentMembersUrl());
        log.info("{}-companyOwner:{},内容版企业成员添加-部门Id:{},billNums:{},请求:{}", LOG_TAG,companyOwner, departmentId, billNums, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},内容版企业成员添加-部门Id:{},billNums:{},响应:{}", LOG_TAG,companyOwner, departmentId, billNums, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},内容版企业成员添加-部门Id:{},billNums:{},异常:", LOG_TAG,companyOwner, departmentId, billNums, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 内容版企业成员删除接口
     *
     * @param billNums 用户手机号码(限制50个号码量)
     * @return
     */
    public @Nonnull EntVrbtResult deleteContentMembersByChannel(String departmentId,String companyOwner,String channel, String ...billNums) {

//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},内容版企业成员删除-部门Id:{},billNums:{},异常:", LOG_TAG,companyOwner, departmentId, billNums);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId())
                .put("departmentId", departmentId)
                .putPOJO("billNums", mapper.valueToTree(billNums));

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getDeleteContentMembersUrl());
        log.info("{}-companyOwner:{},内容版企业成员删除-部门Id:{},billNums:{},请求:{}", LOG_TAG,companyOwner, departmentId, billNums, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},内容版企业成员删除-部门Id:{},billNums:{},响应:{}", LOG_TAG,companyOwner, departmentId, billNums, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},内容版企业成员删除-部门Id:{},billNums:{},异常:", LOG_TAG,companyOwner, departmentId, billNums, e);
            return EntVrbtResult.fail();
        }
    }

    /**
     * 内容版企业成员查询接口
     *
     * {"code":"000000","info":"操作成功","data":[{"orderId":"62500046123","departmentId":"3099953377845","departmentName":"叶哥的small公司","billNum":"13438828200","createTime":"2023-01-19 23:04:33","provinceId":"280","provinceName":"四川","locationId":"2800","locationName":"成都","userStatus":"02"}]}
     *
     * @param billNum 查询手机号 可选参数
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.QYCL_DEPARTMENT_CACHE,key = " #p0 + ':' + #p1 + ':'+ #p2",unless = "#result==null")
    public @Nonnull EntVrbtResult queryContentMembersByChannel(String departmentId, String billNum,String companyOwner,String channel) {
        return queryContentMembersImmediateByChannel(departmentId, billNum,companyOwner,channel);
    }


    /**
     * 内容版企业成员查询接口
     *
     * {"code":"000000","info":"操作成功","data":[{"orderId":"62500046123","departmentId":"3099953377845","departmentName":"叶哥的small公司","billNum":"13438828200","createTime":"2023-01-19 23:04:33","provinceId":"280","provinceName":"四川","locationId":"2800","locationName":"成都","userStatus":"02"}]}
     *
     * @param billNum 查询手机号 可选参数
     * @return
     */
    public @Nonnull EntVrbtResult queryContentMembersImmediateByChannel(String departmentId, String billNum,String companyOwner,String channel) {
//        EnterpriseVrbtConfig enterpriseVrbtConfig = enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(companyOwner);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,companyOwner);
        if (portCrackConfig == null) {
            log.info("{}-配置错误-companyOwner:{},内容版企业成员查询-部门Id:{},billNum:{},异常:", LOG_TAG,companyOwner, departmentId, billNum);
            return EntVrbtResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode()
                .put("orderId", enterpriseVrbtConfig.getOrderId());
        if(StringUtils.isNotBlank(departmentId)){
            node.put("departmentId", departmentId);
        }
        if(StringUtils.isNotBlank(billNum)){
            node.put("billNum", billNum);
        }

        RequestBody body = RequestBody.create(JSON, node.toString());
        final HttpUrl httpUrl = HttpUrl.parse(enterpriseVrbtProperties.getQueryContentMembersUrl());
        log.info("{}-companyOwner:{},内容版企业成员查询-部门Id:{},billNum:{},请求:{}", LOG_TAG, companyOwner,departmentId, billNum, httpUrl.toString());
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .tag(enterpriseVrbtConfig)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-companyOwner:{},内容版企业成员查询-部门Id:{},billNum:{},响应:{}", LOG_TAG,companyOwner, departmentId, billNum, content);
            return mapper.readValue(content, EntVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-companyOwner:{},内容版企业成员查询-部门Id:{},billNum:{},异常:", LOG_TAG,companyOwner, departmentId, billNum, e);
            return EntVrbtResult.fail();
        }
    }


    public boolean verifyMonth(String mobile, String channel) {
        String companyOwner = QyclConstant.getCompanyOwnerByChannel(channel);
        EntVrbtResult entVrbtResult = queryContentMembersImmediateByChannel("", mobile, companyOwner,channel);
        if (entVrbtResult.isOK() && entVrbtResult.getData() != null) {
            final List<String> userStatusList = JsonPath.read(entVrbtResult.getData().toString(), "$[?(@.billNum == '" + mobile + "')].userStatus");
            String userStatus = (!userStatusList.isEmpty() ? userStatusList.get(0) : "");
            if ("02".equals(userStatus)) {
                return true;
            }
        }
        return false;
    }


        public static void main(String[] args) {
            System.out.println("args = " + QyclConstant.getCompanyOwnerByChannel("QYCL_GR"));


//        List<String> ringIds = Lists.newArrayList("ringId1", "ringId2");
//        final ObjectMapper mapper = new ObjectMapper();
//        ObjectNode node = mapper.createObjectNode()
//                .put("orderId", "111")
//                .put("departmentId", "222")
//                .put("startTime", "00:00:00")
//                .put("endTime", "23:59:59")
//                .put("ringType", "1")
//                .putPOJO("ringIds", mapper.valueToTree(ringIds));
//        System.out.println(node.toPrettyString());
//
//        System.out.println(StringUtils.substring("4a53edf78716b16c41cac5375f89f8d1.mp4",0,35));
    }

    public void sendSms(String companyOwnerName, String result) {
        enterpriseVrbtProperties.getWarnMobiles().forEach(alertMobile -> {
            datangSmsService.sendSms(alertMobile, "【企业彩铃】创建部门失败告警，公司名称:" + companyOwnerName + "，创建部门失败，失败原因:" + result);
        });
    }
}
