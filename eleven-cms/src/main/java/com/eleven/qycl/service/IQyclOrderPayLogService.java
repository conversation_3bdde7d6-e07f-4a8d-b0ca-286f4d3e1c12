package com.eleven.qycl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.qycl.entity.QyclOrderPayLog;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: qycl_order_pay_log
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
public interface IQyclOrderPayLogService extends IService<QyclOrderPayLog> {
    QyclOrderPayLog queryOrder(String orderId);

    Boolean queryOrderPaySuccess(String transactionId);

    String saveNotPaymentOrder(String openId,String companyTitle, String mobile, String companyOwner,String channel,String subChannel,String transactionId);

    void updatePayStatus(String outTradeNo, String transactionId, Integer status);

    Result douYinRefund(String outTradeNo, String refund);

    QyclOrderPayLog queryRefundOrder(String orderId);

    void upadateRefundStatus(String outTradeNo, String refundAmount, String refundOrderNo, String refundRemark, Integer refundStatus);


}
