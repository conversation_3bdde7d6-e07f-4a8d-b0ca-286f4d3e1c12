package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eleven.cms.queue.QyclDelayedMessage;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.entity.QyclCompany;
import com.eleven.qycl.entity.QyclCompanyMember;
import com.eleven.qycl.mapper.QyclCompanyMemberMapper;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.service.IQyclCompanyMemberService;
import com.eleven.qycl.service.IQyclCompanyService;
import com.google.common.base.Strings;
import com.jayway.jsonpath.JsonPath;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_EXTRA_3_DAY;

/**
 * @Description: qycl_company_member
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Service
public class QyclCompanyMemberServiceImpl extends ServiceImpl<QyclCompanyMemberMapper, QyclCompanyMember> implements IQyclCompanyMemberService {
    @Autowired
    IQyclCompanyService qyclCompanyService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;



    @Override
    public List<QyclCompanyMember> getListByOpenId(String openId) {
        return lambdaQuery().eq(QyclCompanyMember::getOpenId, openId).list();
    }

    @Override
    public Integer getMemberCountByOpenId(String openId) {
        return lambdaQuery().eq(QyclCompanyMember::getOpenId, openId).count();
    }

    @Override
    public int deleteByOpenId(String openId) {
        return this.baseMapper.delete(new QueryWrapper<QyclCompanyMember>().lambda().in(QyclCompanyMember::getOpenId, openId));
    }

    @Override
    public void updateQyclFunStatus(String mobile, String qyclFunStatus) {
        QyclCompanyMember qyclCompanyMember = lambdaQuery().eq(QyclCompanyMember::getMobile, mobile).orderByDesc(QyclCompanyMember::getCreateTime).last("limit 1").one();
        if (qyclCompanyMember != null) {
            qyclCompanyMember.setQyclFun(qyclFunStatus);
            this.updateById(qyclCompanyMember);
        }
    }

    @Override
    public Result addMember(String openId, String mobiles, String companyOwner) {
        if (StringUtils.isEmpty(mobiles)) {
            return Result.error("员工手机号码错误");
        }
        QyclCompany qyclCompany = qyclCompanyService.findPayCompanyByOpenId(openId);
        if (qyclCompany == null || StringUtils.isBlank(qyclCompany.getDepartmentId())) {
            return Result.error("无法添加员工");
        }
        List<String> memberMobiles = this.getListByOpenId(openId).stream().map(QyclCompanyMember::getMobile).collect(Collectors.toList());
        String[] mobileArray = mobiles.split(",");
        List<QyclCompanyMember> members = new ArrayList<>();
        for (String mobile : mobileArray) {
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return Result.error("请输入正确格式手机号");
            }
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (!mobileRegionResult.isIspYidong()) {
                return Result.error("手机号【" + mobile + "】不是移动用户");
            }
            if (memberMobiles.contains(mobile)) {
                return Result.error("手机号【" + mobile + "】已存在请勿重复添加");
            }
            QyclCompanyMember qyclCompanyMember = new QyclCompanyMember();
            qyclCompanyMember.setOpenId(openId);
            qyclCompanyMember.setMobile(mobile);
            qyclCompanyMember.setCompanyTitle(qyclCompany.getTitle());
            members.add(qyclCompanyMember);
        }
        try {
            //将成员加入部门
            if (StringUtils.isNotEmpty(qyclCompany.getDepartmentId())) {
                EntVrbtResult entVrbtResult = enterpriseVrbtService.addContentMembersByChannel(qyclCompany.getDepartmentId(), companyOwner,qyclCompany.getChannel(), mobileArray);
                //将成员加入延时队列
                for (int i = 0; i < members.size(); i++) {
                    QyclCompanyMember qyclCompanyMember = members.get(i);
                    redisDelayedQueueManager.addQycl(QyclDelayedMessage.builder().mobile(qyclCompanyMember.getMobile()).openId(openId).msg("企业彩铃未开通包月用户").tag(MESSAG_EXTRA_3_DAY).build(), 24, TimeUnit.HOURS);
                    final List<String> errorMsgList = JsonPath.read(entVrbtResult.getData().toString(), "$.list[?(@.billNum =='" + qyclCompanyMember.getMobile() + "')].errorMsg");
                    if (!errorMsgList.isEmpty()) {
                        qyclCompanyMember.setRemark(errorMsgList.get(0));
                    }
                }
            }
            this.saveBatch(members);
            qyclCompany.setOperationTime(new Date());
            qyclCompanyService.updateById(qyclCompany);
            return Result.ok("添加成员成功");
//            //写入系统通知
//            sysBaseAPI.sendSysAnnouncement("admin", "admin", "企业彩铃添加成员", "手机号:" + qyclCompany.getMobile() + ",添加成员");
//            sysBaseAPI.sendSysAnnouncement("admin", "yrjy", "企业彩铃添加成员", "手机号:" + qyclCompany.getMobile() + ",添加成员");
//            sysBaseAPI.sendSysAnnouncement("admin", "haiyan", "企业彩铃添加成员", "手机号:" + qyclCompany.getMobile() + ",添加成员");
//            sysBaseAPI.sendSysAnnouncement("admin", "hanwen", "企业彩铃添加成员", "手机号:" + qyclCompany.getMobile() + ",添加成员");
        }catch (Exception e){
            return Result.ok("添加成员失败");
        }
    }

    @Override
    public QyclCompanyMember getMemberByOpenId(String openId) {
        return lambdaQuery().eq(QyclCompanyMember::getOpenId, openId).orderByDesc(QyclCompanyMember::getCreateTime).last("limit 1").one();
    }
}
