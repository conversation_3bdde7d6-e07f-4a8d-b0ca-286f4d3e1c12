package com.eleven.qycl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IColumnMusicService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.config.TtsProperties;
import com.eleven.qycl.entity.*;
import com.eleven.qycl.mapper.QyclRingMapper;
import com.eleven.qycl.service.*;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.IPUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.qycl.util.QyclConstant.QYCL_BACKGROUND_VIDEO_URL;

/**
 * @Description: qycl_ring
 * @Author: jeecg-boot
 * @Date: 2022-11-09
 * @Version: V1.0
 */
@Service
@Slf4j
public class QyclRingServiceImpl extends ServiceImpl<QyclRingMapper, QyclRing> implements IQyclRingService {

    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Autowired
    IQyclCompanyMemberService qyclCompanyMemberService;
    @Autowired
    private IQyclOrderPayService qyclOrderPayService;
    @Autowired
    private TtsService ttsService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private TtsProperties ttsProperties;
    @Autowired
    private EnterpriseVrbtService enterpriseVrbtService;
    @Lazy
    @Autowired
    IQyclCompanyService qyclCompanyService;
    @Autowired
    IChannelService channelService;
    @Autowired
    AliMediaService aliMediaService;
    @Autowired
    AliMediaProperties aliMediaProperties;
    @Autowired
    MiguApiService miguApiService;
    @Autowired
    IColumnMusicService columnMusicService;
    @Autowired
    IQyclRingVideoService qyclRingVideoService;


    private String regionId;
    private String bucket;

    @PostConstruct
    public void init() {
        regionId = aliMediaProperties.getRegionId();
        bucket = aliMediaProperties.getBucketName();
    }

    @Override
    public List<QyclRing> getListByOpenIdAndPaySuccess(String openId) {
        return lambdaQuery().eq(QyclRing::getOpenId, openId).eq(QyclRing::getOrderPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS).orderByDesc(QyclRing::getCreateTime).list();
    }

    @Override
    public List<QyclRing> getListByOpenId(String openId) {
        return lambdaQuery().eq(QyclRing::getOpenId, openId).orderByDesc(QyclRing::getCreateTime).list();
    }


    @Override
    public QyclRing getOriginalByIdAndOpenId(String ringId, String openId) {
        return lambdaQuery().eq(QyclRing::getId, ringId).eq(QyclRing::getOpenId, openId).eq(QyclRing::getModifyType, 0).one();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modifyRing(QyclRing originalRing, QyclRing modifyRing) {
        originalRing.setModifyCount(originalRing.getModifyCount() + 1);
        originalRing.setUpdateTime(new Date());
        String filePath = ttsService.textToVoiceFile(modifyRing.getRingTxt());
        modifyRing.setFilePath(filePath);
        String videoPath = ttsService.genVideoRelativePath();
        modifyRing.setVideoPath(videoPath);
        ttsService.mergeVideo(filePath, videoPath);
        //用户修改铃音内容后上传铃声
        String ringFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), videoPath);
//        String departmentId = qyclCompanyService.getDepartmentId(originalRing.getOpenId());
        QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(originalRing.getOpenId());
        String departmentId=qyclCompany != null ? qyclCompany.getDepartmentId() : "";
        String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
        if (StringUtils.isNotEmpty(departmentId)) {
            EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, originalRing.getCompanyTitle(), new File(ringFullPath), EnterpriseVrbtService.RING_TYPE_VEDIO, modifyRing.getCompanyOwner(),channel);
            if (entVrbtResult.isOK()) {
                String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                modifyRing.setStreamNumber(streamNumber);
            }
            String audioRingFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), filePath);
            EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, originalRing.getCompanyTitle(), new File(audioRingFullPath), EnterpriseVrbtService.RING_TYPE_AUDIO, modifyRing.getCompanyOwner(),channel);
            if (audioEntVrbtResult.isOK()) {
                String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                modifyRing.setStreamNumberAudio(streamNumber);
            }
        }
        this.updateById(originalRing);
        this.save(modifyRing);
        //qyclCompanyMemberService.lambdaUpdate().set(QyclCompanyMember::getSettingStatus, -1).eq(QyclCompanyMember::getOpenId, originalRing.getOpenId()).update();
    }

    @Override
    public void modifyNewRing(QyclRing originalRing, QyclRing modifyRing, Integer ringType, VoiceGender voiceGender, String[] imageArray,BackgroundMusic backgroundMusic) {
        originalRing.setModifyCount(originalRing.getModifyCount() + 1);
        originalRing.setUpdateTime(new Date());
        if (QyclConstant.RING_TYPE_DEFAULT.equals(modifyRing.getRingType())) {
            String filePath = ttsService.textToVoiceFile(modifyRing.getRingTxt());
            modifyRing.setFilePath(filePath);
            String videoPath = ttsService.genVideoRelativePath();
            modifyRing.setVideoPath(videoPath);
            ttsService.mergeVideo(filePath, videoPath);
            //用户修改铃音内容后上传铃声
            String ringFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), videoPath);
//            String departmentId = qyclCompanyService.getDepartmentId(originalRing.getOpenId());


            QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(originalRing.getOpenId());
            String departmentId=qyclCompany != null ? qyclCompany.getDepartmentId() : "";
            String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
            if (StringUtils.isNotEmpty(departmentId)) {
                EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, originalRing.getCompanyTitle(), new File(ringFullPath), EnterpriseVrbtService.RING_TYPE_VEDIO,modifyRing.getCompanyOwner(),channel);
                if (entVrbtResult.isOK()) {
                    String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                    modifyRing.setStreamNumber(streamNumber);
                }
                String audioRingFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), filePath);
                EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, originalRing.getCompanyTitle(), new File(audioRingFullPath), EnterpriseVrbtService.RING_TYPE_AUDIO,modifyRing.getCompanyOwner(),channel);
                if (audioEntVrbtResult.isOK()) {
                    String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                    modifyRing.setStreamNumberAudio(streamNumber);
                }
            }
        } else {
            modifyRing.setRingMakeStatus(QyclConstant.RING_MAKE_IN);
            modifyRing.setImageUrls(StringUtils.join(imageArray, ","));
            if (StringUtils.isEmpty(modifyRing.getRingTxt())) {
                String[] imageUrlArray = modifyRing.getImageUrls().split(",");
                String urlPre = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/";
                List<String> imageList = Arrays.stream(imageUrlArray).map(item -> urlPre + item).collect(Collectors.toList());
                String videoJobId = aliMediaService.produceVideo(AliMediaProperties.JOB_QUEUE_TAG_QYCL, imageList, backgroundMusic != null ? backgroundMusic.getMusicPath() : "", "", "", "");
                modifyRing.setAliVideoJobId(videoJobId);
            } else {
                String jobId = aliMediaService.tts(AliMediaProperties.JOB_QUEUE_TAG_QYCL, modifyRing.getRingTxt(), voiceGender, ImmutableMap.of());
                modifyRing.setAliTtsJobId(jobId);
            }

        }
        this.updateById(originalRing);
        this.save(modifyRing);
    }

    @Override
    public Map<String, String> createRing(String ringTxt, String companyTitle, String openId, String mobile, String companyOwner) {
        //生成铃声
        QyclRing ringDto = new QyclRing();
        Map<String, String> map = new HashMap<>();
        String filePath = ttsService.textToVoiceFile(ringTxt);
        if (StringUtils.isNotBlank(companyTitle)) {
            ringDto.setOpenId(openId);
            ringDto.setMobile(mobile);
            ringDto.setCompanyTitle(companyTitle);
            ringDto.setRingTxt(ringTxt);
            ringDto.setFilePath(filePath);
            ringDto.setCompanyOwner(companyOwner);
            this.save(ringDto);
        }
        map.put("playUrl", QyclConstant.RING_FILE_BASE_URL + filePath);
        map.put("ringId", ringDto == null ? null : ringDto.getId());
        map.put("filePath", filePath);
        return map;
    }

    @Override
    public Map<String, String> createNewRing(String ringTxt, String companyTitle, String openId, String mobile, Integer ringType, VoiceGender voiceGender, String[] imageArray, BackgroundMusic backgroundMusic, String subChannel, String companyOwner) {
        if (QyclConstant.RING_TYPE_DEFAULT.equals(ringType)) {
            //生成铃声
            QyclRing ringDto = new QyclRing();
            Map<String, String> map = new HashMap<>();
            String filePath = ttsService.textToVoiceFile(ringTxt);
            if (StringUtils.isNotBlank(companyTitle)) {
                ringDto.setOpenId(openId);
                ringDto.setMobile(mobile);
                ringDto.setCompanyTitle(companyTitle);
                ringDto.setRingTxt(ringTxt);
                ringDto.setFilePath(filePath);
                ringDto.setCompanyOwner(companyOwner);
                this.save(ringDto);
            }
            map.put("playUrl", QyclConstant.RING_FILE_BASE_URL + filePath);
            map.put("ringId", ringDto == null ? null : ringDto.getId());
            map.put("filePath", filePath);
            return map;
        } else {
            //查询用户是否已开通成功后设置的铃音
            QyclRing originalRing = lambdaQuery().eq(QyclRing::getOpenId, openId).eq(QyclRing::getMobile, mobile).eq(QyclRing::getOrderPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS).eq(QyclRing::getModifyType, 0).one();
            QyclRing ringDto = new QyclRing();
            if (originalRing != null) {
                originalRing.setModifyCount(originalRing.getModifyCount() + 1);
                originalRing.setUpdateTime(new Date());
                updateById(originalRing);
                ringDto.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
                ringDto.setModifyType(1);
            }
            //生成铃声
            Map<String, String> map = new HashMap<>();
            ringDto.setOpenId(openId);
            ringDto.setMobile(mobile);
            ringDto.setCompanyTitle(mobile);
            ringDto.setRingType(ringType);
            ringDto.setRingTxt(ringTxt);
            ringDto.setImageUrls(StringUtils.join(imageArray, ","));
            ringDto.setRingMakeStatus(QyclConstant.RING_MAKE_IN);
            ringDto.setSubChannel(subChannel);
            ringDto.setCompanyOwner(companyOwner);
            ringDto.setBgmUrl(backgroundMusic.getMusicPath());
            if (StringUtils.isEmpty(ringTxt)) {
                String[] imageUrlArray = ringDto.getImageUrls().split(",");
                String urlPre = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/";
                List<String> imageList = Arrays.stream(imageUrlArray).map(item -> urlPre + item).collect(Collectors.toList());
                String videoJobId = aliMediaService.produceVideo(AliMediaProperties.JOB_QUEUE_TAG_QYCL, imageList, backgroundMusic != null ? backgroundMusic.getMusicPath() : "","","", "");
                ringDto.setAliVideoJobId(videoJobId);
            } else {
                String jobId = aliMediaService.tts(AliMediaProperties.JOB_QUEUE_TAG_QYCL, ringTxt, voiceGender, ImmutableMap.of());
                ringDto.setAliTtsJobId(jobId);
            }
            this.save(ringDto);
            map.put("ringId", ringDto == null ? null : ringDto.getId());
            return map;
        }
    }

    @Override
    public Result<?> ringPay(String title, String companyTitle, String filePath, String openId, String ringTxt, String ringId, String source, String sourceType, HttpServletRequest req, String companyOwner) {

        QyclCompany companyDto = new QyclCompany();
        Subscribe subscribe = new Subscribe();
        if (StringUtils.isNotBlank(source)) {
            subscribe = parseLink(source, subscribe);
        }
        if (qyclOrderPayService.lambdaQuery()
                .eq(QyclOrderPay::getOpenId, openId)
                .eq(QyclOrderPay::getPayStatus, 1).list().size() > 0) {
            return Result.error("请勿重复下单!");
        }

        QyclRing qyclRing = this.getById(ringId);
        String mobile = qyclRing.getMobile();
        //保存公司信息
        QyclCompany qyclCompany = qyclCompanyService.findPayCompanyByOpenId(openId);
        if (qyclCompany == null) {
            companyDto.setOpenId(openId);
            companyDto.setIndustry(title);
            companyDto.setTitle(companyTitle);
            companyDto.setMobile(mobile);
            companyDto.setSubChannel(subscribe.getSubChannel());
            companyDto.setSourceType(sourceType);
            companyDto.setCompanyOwner(companyOwner);
            qyclCompanyService.save(companyDto);
        } else {
            qyclCompany.setMobile(qyclRing.getMobile());
            qyclCompany.setTitle(companyTitle);
            companyDto.setIndustry(title);
            companyDto.setSubChannel(subscribe.getSubChannel());
            qyclCompanyService.updateById(qyclCompany);
        }
        //更新铃音信息
        String orderId = qyclOrderPayService.savePay(openId, companyTitle, mobile, companyOwner);
        if (qyclRing != null) {
            //重置订单号
            this.lambdaUpdate()
                    .eq(QyclRing::getOpenId, openId)
                    .eq(QyclRing::getOrderPayStatus, -1)
                    .eq(QyclRing::getOrderId, orderId)
                    .set(QyclRing::getOrderId, "").update();

            qyclRing.setOrderId(orderId);
            this.updateById(qyclRing);
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        //保存sub信息
        try {
            String ipAddr = IPUtils.getIpAddr(req);
            String userAgent = req.getHeader("User-Agent");
            if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
                userAgent = userAgent.substring(0, 512);
            }
            subscribe.setUserAgent(userAgent);
            //referer字段存储指纹
            final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
            subscribe.setReferer(finger);
            subscribe.setIp(ipAddr);
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            String isp = mobileRegionResult.getOperator();
            subscribe.setIsp(isp);
            subscribe.setStatus(-1);
            subscribe.setIspOrderNo(orderId);
            subscribe.setCreateTime(new Date());
            subscribe.setMobile(mobile);
            subscribe.setChannel(QyclConstant.QYCL_COMPANY_OWNER_YRJY.equals(companyOwner) ? BizConstant.BIZ_QYCL : BizConstant.BIZ_QYCL_MH);
            subscribe.setBizType(BizConstant.BIZ_TYPE_QYCL);
            subscribeService.createSubscribeDbAndEs(subscribe);
        } catch (Exception e) {
            log.error("企业彩铃sub订单出错：", e);
            return Result.error("请求参数错误");
        }

        //调用支付
        return Result.ok("OK", orderId);
    }

    @Override
    public Result<?> sdkCallback(String mobile, String title, String openId, String ringId, String resCode, String resMsg, String source, String sourceType, Integer ringType, String crack, HttpServletRequest req, String companyOwner, String channel) {
        Subscribe subscribe = new Subscribe();
        if (StringUtils.isNotBlank(source)) {
            subscribe = parseLink(source, subscribe);
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }

        if(StringUtils.isBlank(channel)){
            channel = QyclConstant.getChannelByCompanyOwner(companyOwner);
        }
        if (StringUtils.equals(crack, "0")) {
            //保存sub信息
            try {
                String ipAddr = IPUtils.getIpAddr(req);
                String userAgent = req.getHeader("User-Agent");
                if (!Strings.isNullOrEmpty(userAgent) && userAgent.length() > 512) {
                    userAgent = userAgent.substring(0, 512);
                }
                subscribe.setUserAgent(userAgent);
                //referer字段存储指纹
                final String finger = Strings.nullToEmpty(subscribe.getReferer()) + ipAddr;
                subscribe.setReferer(finger);
                subscribe.setIp(ipAddr);
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                subscribe.setIspOrderNo(ringId);
                subscribe.setCreateTime(new Date());
                subscribe.setMobile(mobile);
                //subscribe.setChannel(QyclConstant.RING_TYPE_DEFAULT.equals(ringType) ? BizConstant.BIZ_QYCL : BizConstant.BIZ_QYCL_GR);
                subscribe.setChannel(channel);
                subscribe.setBizType(BizConstant.BIZ_TYPE_QYCL);
                if ("000000".equals(resCode)) {
                    subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                } else {
                    subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                }
                subscribe.setResult(resMsg);
                subscribe.setOpenTime(new Date());
                subscribeService.createSubscribeDbAndEs(subscribe);
                //信息流广告转化上报
                //channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
            } catch (Exception e) {
                log.error("企业彩铃sub订单出错：", e);
                return Result.error("请求参数错误");
            }
        }

        if ("000000".equals(resCode)) {
            if (QyclConstant.RING_TYPE_DEFAULT.equals(ringType)) {
                QyclRing qyclRing = this.getById(ringId);
                if (qyclRing == null) {
                    return Result.error("无效的铃音!");
                }
                QyclCompany qyclCompany = qyclCompanyService.findPayCompanyByOpenId(openId);
                if (qyclCompany == null) {
                    //保存公司信息
                    QyclCompany companyDto = new QyclCompany();
                    companyDto.setOpenId(openId);
                    companyDto.setIndustry(title);
                    companyDto.setTitle(qyclRing.getCompanyTitle());
                    companyDto.setMobile(mobile);
                    companyDto.setSubChannel(subscribe.getSubChannel());
                    companyDto.setChannel(channel);
                    companyDto.setSourceType(sourceType);
                    companyDto.setCompanyOwner(companyOwner);
                    qyclCompanyService.save(companyDto);
                } else {
                    qyclCompany.setMobile(mobile);
                    qyclCompany.setIndustry(title);
                    qyclCompany.setTitle(qyclRing.getCompanyTitle());
                    qyclCompany.setSubChannel(subscribe.getSubChannel());
                    qyclCompany.setChannel(channel);
                    qyclCompany.setSourceType(sourceType);
                    qyclCompany.setCompanyOwner(companyOwner);
                    qyclCompanyService.updateById(qyclCompany);
                }
                QyclCompanyMember companyMember = qyclCompanyMemberService.lambdaQuery().eq(QyclCompanyMember::getOpenId, openId).eq(QyclCompanyMember::getMobile, mobile).orderByDesc(QyclCompanyMember::getCreateTime).last("limit 1").one();
                if (companyMember == null) {
                    QyclCompanyMember qycl = new QyclCompanyMember();
                    qycl.setOpenId(openId);
                    qycl.setMobile(mobile);
                    qycl.setCompanyTitle(qyclRing.getCompanyTitle());
                    qyclCompanyMemberService.save(qycl);
                } else {
                    companyMember.setMobile(mobile);
                    companyMember.setCompanyTitle(qyclCompany.getTitle());
                    qyclCompanyMemberService.updateById(companyMember);
                }
                //保存未支付订单
                String orderId = qyclOrderPayService.saveNotPaymentOrder(openId, qyclRing.getCompanyTitle(), mobile, companyOwner);
                log.info("获取无需支付订单id:{}", orderId);
                qyclRing.setOrderId(orderId);
                this.updateById(qyclRing);
            } else {
                QyclCompany qyclCompany = qyclCompanyService.findPayCompanyByOpenId(openId);
                if (qyclCompany == null) {
                    //保存公司信息
                    QyclCompany companyDto = new QyclCompany();
                    companyDto.setOpenId(openId);
                    companyDto.setIndustry(title);
                    companyDto.setTitle(mobile);
                    companyDto.setMobile(mobile);
                    companyDto.setSubChannel(subscribe.getSubChannel());
                    companyDto.setSourceType(sourceType);
                    companyDto.setChannel(channel);
                    companyDto.setCompanyOwner(companyOwner);
                    qyclCompanyService.save(companyDto);
                } else {
                    qyclCompany.setMobile(mobile);
                    qyclCompany.setIndustry(title);
                    qyclCompany.setTitle(mobile);
                    qyclCompany.setSubChannel(subscribe.getSubChannel());
                    qyclCompany.setChannel(channel);
                    qyclCompany.setSourceType(sourceType);
                    qyclCompany.setCompanyOwner(companyOwner);
                    qyclCompanyService.updateById(qyclCompany);
                }
                QyclCompanyMember companyMember = qyclCompanyMemberService.lambdaQuery().eq(QyclCompanyMember::getOpenId, openId).eq(QyclCompanyMember::getMobile, mobile).orderByDesc(QyclCompanyMember::getCreateTime).last("limit 1").one();
                if (companyMember == null) {
                    QyclCompanyMember qycl = new QyclCompanyMember();
                    qycl.setOpenId(openId);
                    qycl.setMobile(mobile);
                    qycl.setCompanyTitle(mobile);
                    qyclCompanyMemberService.save(qycl);
                } else {
                    companyMember.setMobile(mobile);
                    companyMember.setCompanyTitle(qyclCompany.getTitle());
                    qyclCompanyMemberService.updateById(companyMember);
                }
            }
        }
        return Result.ok("OK");
    }

    @Override
    public Result useRing(String ringId) {
        QyclRing qyclRing = getById(ringId);
        if (qyclRing != null) {
            this.lambdaUpdate().set(QyclRing::getAuditStatus, 0).eq(QyclRing::getOpenId, qyclRing.getOpenId()).update();
            qyclRing.setAuditStatus(1);
            this.updateById(qyclRing);
        }
        return Result.ok("设置成功");
    }

    @Override
    public int deleteByOpenId(String openId) {
        return this.baseMapper.delete(new QueryWrapper<QyclRing>().lambda().in(QyclRing::getOpenId, openId));
    }

    //删除一周之前的无效铃声
    @Override
    public void deleteInvalidRing() {
        LocalDateTime start = LocalDate.now().minusDays(7).atTime(LocalTime.MAX);
//        LocalDateTime start = LocalDate.now().atTime(LocalTime.MIN);
        List<QyclRing> list = this.lambdaQuery().ne(QyclRing::getOrderPayStatus, "1").lt(QyclRing::getCreateTime, start).list();
        list.forEach(qyclRing -> {
            File file = new File(ttsProperties.getAudioFileBaseDir() + qyclRing.getFilePath());
            if (file.isFile()) {
                file.delete();
            }
            log.info("删除无效铃音,订购状态:{},订购时间:{},文件路径:{}", qyclRing.getOrderPayStatus(), qyclRing.getCreateTime(), qyclRing.getFilePath());
        });

    }

    @Override
    public void busModifyRing(QyclRing originalRing) {
        originalRing.setUpdateTime(new Date());
        String filePath = ttsService.textToVoiceFile(originalRing.getRingTxt());
        originalRing.setFilePath(filePath);
        String videoPath = ttsService.genVideoRelativePath();
        originalRing.setVideoPath(videoPath);
        ttsService.mergeVideo(filePath, videoPath);
        //业务修改铃音内容后上传铃声
        String ringFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), videoPath);
        String audioRingFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), filePath);
//        String departmentId = qyclCompanyService.getDepartmentId(originalRing.getOpenId());
        QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(originalRing.getOpenId());
        String departmentId=qyclCompany != null ? qyclCompany.getDepartmentId() : "";
        String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
        if (StringUtils.isNotEmpty(departmentId)) {
            EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, originalRing.getCompanyTitle(), new File(ringFullPath), EnterpriseVrbtService.RING_TYPE_VEDIO,originalRing.getCompanyOwner(),channel);
            if (entVrbtResult.isOK()) {
                String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                originalRing.setStreamNumber(streamNumber);
            }
            EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, originalRing.getCompanyTitle(), new File(audioRingFullPath), EnterpriseVrbtService.RING_TYPE_AUDIO,originalRing.getCompanyOwner(),channel);
            if (audioEntVrbtResult.isOK()) {
                String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                originalRing.setStreamNumberAudio(streamNumber);
            }
        }
        this.updateById(originalRing);
        //qyclCompanyMemberService.lambdaUpdate().set(QyclCompanyMember::getSettingStatus, -1).eq(QyclCompanyMember::getOpenId, originalRing.getOpenId()).update();
    }

    @Async
    @Override
    public void submitRingNotify(JsonNode jsonNode) {
        String streamNumber = jsonNode.at("/streamNumber").asText();
        String ringStatus = jsonNode.at("/ringStatus").asText();
        String ringId = jsonNode.at("/ringId").asText();
        String ringType = jsonNode.at("/ringType").asText();
        String ringFilePath = jsonNode.at("/ringFilePath").asText();
        QyclRing qyclRing = this.lambdaQuery().eq(EnterpriseVrbtService.RING_TYPE_VEDIO.equals(ringType) ? QyclRing::getStreamNumber : QyclRing::getStreamNumberAudio, streamNumber).orderByDesc(QyclRing::getCreateTime).last("limit 1").one();
        if (qyclRing != null) {
            if (EnterpriseVrbtService.RING_TYPE_VEDIO.equals(ringType)) {
                qyclRing.setRingStatus(ringStatus);
                qyclRing.setRingId(ringId);
                if (QyclConstant.RING_TYPE_DIY.equals(qyclRing.getRingType()) && StringUtils.isNotBlank(ringFilePath) && !StringUtils.contains(qyclRing.getVideoPath(), "d.musicapp.migu.cn")) {
                    qyclRing.setVideoPath(ringFilePath);
                }
                this.updateById(qyclRing);
            } else if (EnterpriseVrbtService.RING_TYPE_AUDIO.equals(ringType)) {
                qyclRing.setRingStatusAudio(ringStatus);
                qyclRing.setRingIdAudio(ringId);
                this.updateById(qyclRing);
            }
            //分发成功设置铃音
            if ("06".equals(ringStatus)) {
//                String departmentId = qyclCompanyService.getDepartmentId(qyclRing.getOpenId());
                QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(qyclRing.getOpenId());
                String departmentId=qyclCompany != null ? qyclCompany.getDepartmentId() : "";
                String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
                String companyOwner = qyclRing.getCompanyOwner();
                if (EnterpriseVrbtService.RING_TYPE_VEDIO.equals(ringType)) {
                    EntVrbtResult entVrbtResult = enterpriseVrbtService.setDeptRingsByTimeChannel(departmentId, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,channel,ringId);
                    if (entVrbtResult.isOK()) {
                        //将上传成功的铃音设置为使用中
                        this.useRing(qyclRing.getRingId());
//                        //将成员状态设置为审核通过
//                        qyclCompanyMemberService.lambdaUpdate().set(QyclCompanyMember::getSettingStatus, 1).eq(QyclCompanyMember::getOpenId, qyclRing.getOpenId()).update();
                        log.info("公司:{}视频铃音设置成功", qyclRing.getCompanyTitle());
                    }
                } else if (EnterpriseVrbtService.RING_TYPE_AUDIO.equals(ringType)) {
                    EntVrbtResult audioEntVrtbResult = enterpriseVrbtService.setDeptRingsByTimeChannel(departmentId, EnterpriseVrbtService.RING_TYPE_AUDIO, companyOwner,channel, ringId);
                    if (audioEntVrtbResult.isOK()) {
                        //将上传成功的铃音设置为使用中
                        this.useRing(qyclRing.getRingId());
//                        //将成员状态设置为审核通过
//                        qyclCompanyMemberService.lambdaUpdate().set(QyclCompanyMember::getSettingStatus, 1).eq(QyclCompanyMember::getOpenId, qyclRing.getOpenId()).update();
                        log.info("公司:{}音频铃音设置成功", qyclRing.getCompanyTitle());
                    }
                }
            }
        }
    }

    @Override
    @Async
    public void submitRing(QyclRing qyclRing, String departmentId,String channel) {
        if (QyclConstant.RING_TYPE_DEFAULT.equals(qyclRing.getRingMakeStatus())) {
            String ringFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), qyclRing.getVideoPath());
            EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, qyclRing.getCompanyTitle(), new File(ringFullPath), EnterpriseVrbtService.RING_TYPE_VEDIO,qyclRing.getCompanyOwner(),channel);
            if (entVrbtResult.isOK()) {
                String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                qyclRing.setStreamNumber(streamNumber);
            }
            String audioRingFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), qyclRing.getFilePath());
            EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, qyclRing.getCompanyTitle(), new File(audioRingFullPath), EnterpriseVrbtService.RING_TYPE_AUDIO,qyclRing.getCompanyOwner(),channel);
            if (audioEntVrbtResult.isOK()) {
                String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                qyclRing.setStreamNumberAudio(streamNumber);
            }
            this.updateById(qyclRing);
        } else {
            try {
                InputStream videoIns = aliMediaService.getObjectInputStream(qyclRing.getVideoPath());
                InputStream audioIns = aliMediaService.getObjectInputStream(qyclRing.getFilePath());
                EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO,qyclRing.getCompanyOwner(),channel);
                if (entVrbtResult.isOK()) {
                    String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                    qyclRing.setStreamNumber(streamNumber);
                }
                EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), audioIns, EnterpriseVrbtService.RING_TYPE_AUDIO,qyclRing.getCompanyOwner(),channel);
                if (audioEntVrbtResult.isOK()) {
                    String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                    qyclRing.setStreamNumberAudio(streamNumber);
                }
                this.updateById(qyclRing);
            } catch (Exception e) {
                log.error("上传div铃音错误:{}", e);
            }
        }
    }

    @Override
    public void aliTtsJobFinishHandle(String ttsJobId) {
        QyclRing qyclRing = lambdaQuery().eq(QyclRing::getAliTtsJobId, ttsJobId).one();
        if (qyclRing == null) {
            return;
        }
        Pair<String, String> pair = aliMediaService.fetchTtsResult(ttsJobId, ImmutableMap.of());
        String audioUrl = pair.getLeft();
        String subtitleContent = pair.getRight();
        List<String> mediaList = new ArrayList<>();
        if (StringUtils.isNotBlank(qyclRing.getImageUrls())) {
            String[] imageUrlArray = qyclRing.getImageUrls().split(",");
            String urlPre = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/";
            mediaList = Arrays.stream(imageUrlArray).map(item -> urlPre + item).collect(Collectors.toList());
        } else {
            mediaList.add(QYCL_BACKGROUND_VIDEO_URL);
        }
        String videoJobId = aliMediaService.produceVideo(AliMediaProperties.JOB_QUEUE_TAG_QYCL, mediaList, qyclRing.getBgmUrl(), audioUrl, subtitleContent, qyclRing.getCompanyTitle());
        lambdaUpdate().set(QyclRing::getFilePath, audioUrl).set(QyclRing::getAliVideoJobId, videoJobId).eq(QyclRing::getId, qyclRing.getId()).update();
    }

    @Override
    public void aliVideoJobFinishHandle(String videoJobId, String videoPath) {
        QyclRing qyclRing = lambdaQuery().eq(QyclRing::getAliVideoJobId, videoJobId).one();
        if (qyclRing == null) {
            return;
        }
        lambdaUpdate().eq(QyclRing::getId, qyclRing.getId()).set(QyclRing::getRingMakeStatus, QyclConstant.RING_MAKE_COMPLETE).set(QyclRing::getVideoPath, videoPath).update();
        qyclRing = getById(qyclRing.getId());
        if (QyclConstant.PAYMENT_STATUS_SUCCESS.equals(qyclRing.getOrderPayStatus())) {
            //查询支付状态
            QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, qyclRing.getOpenId()).eq(QyclCompany::getMobile, qyclRing.getMobile()).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
            if (qyclCompany != null && StringUtils.isNotBlank(qyclCompany.getDepartmentId())) {
                try {
                    InputStream videoIns = aliMediaService.getObjectInputStream(qyclRing.getVideoPath());
                    EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(qyclCompany.getDepartmentId(), qyclRing.getCompanyTitle(), videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO,qyclRing.getCompanyOwner(),qyclCompany.getChannel());
                    if (entVrbtResult.isOK()) {
                        String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                        qyclRing.setStreamNumber(streamNumber);
                    }
                    if (StringUtils.isNotBlank(qyclRing.getFilePath())) {
                        InputStream audioIns = aliMediaService.getObjectInputStream(qyclRing.getFilePath());
                        EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(qyclCompany.getDepartmentId(), qyclRing.getCompanyTitle(), audioIns, EnterpriseVrbtService.RING_TYPE_AUDIO,qyclRing.getCompanyOwner(),qyclCompany.getChannel());
                        if (audioEntVrbtResult.isOK()) {
                            String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                            qyclRing.setStreamNumberAudio(streamNumber);
                        }
                    }
                    this.updateById(qyclRing);
                } catch (Exception e) {
                    log.error("上传div铃音错误:{}", e);
                }
            }
        }
    }

    @Override
    public void settingRing(String mobile, String copyrightId, String openId, String companyOwner) {
        //查询用户是否已开通成功后设置的铃音
        QyclRing originalRing = lambdaQuery().eq(QyclRing::getOpenId, openId).eq(QyclRing::getMobile, mobile).eq(QyclRing::getOrderPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS).eq(QyclRing::getModifyType, 0).one();
        QyclRing qyclRing = new QyclRing();
        QyclRingVideo qyclRingVideo = qyclRingVideoService.getQyclRingVideoByRingId(copyrightId);
        if (qyclRingVideo != null) {
            qyclRing.setVideoPath(qyclRingVideo.getRingUrl());
            qyclRing.setRingStatus("06");
            qyclRing.setRingId(copyrightId);
        }
        if (originalRing != null) {
            originalRing.setModifyCount(originalRing.getModifyCount() + 1);
            originalRing.setUpdateTime(new Date());
            updateById(originalRing);
            qyclRing.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
            qyclRing.setModifyType(1);
//            String departmentId = qyclCompanyService.getDepartmentId(originalRing.getOpenId());
            QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(originalRing.getOpenId());
            String departmentId=qyclCompany != null ? qyclCompany.getDepartmentId() : "";
            String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
            if (qyclRingVideo != null) {
                enterpriseVrbtService.setDeptRingsByTimeChannel(departmentId, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,channel, copyrightId);
            }
        }
        qyclRing.setOpenId(openId);
        qyclRing.setMobile(mobile);
        qyclRing.setCompanyTitle(mobile);
        qyclRing.setRingType(QyclConstant.RING_TYPE_PREFABRICATE);
        qyclRing.setCompanyOwner(companyOwner);
        this.save(qyclRing);
    }

    @Override
    public void settingRingDiy(String mobile, String ringId, String openId) {
//        QyclRing qyclRing = new QyclRing();
//        qyclRing.setOpenId(openId);
//        qyclRing.setMobile(mobile);
//        qyclRing.setCompanyTitle(mobile);
//        //String videoPath = miguApiService.vrbtTryToSeeUrl("", MiguApiService.CENTRALITY_CHANNEL_CODE_04C, copyrightId);
//        qyclRing.setRingType(QyclConstant.RING_TYPE_PERSON);
//        qyclRing.setVideoPath(ttsProperties.getVrbtFilePath() + copyrightId + ".mp4");
//        this.save(qyclRing);
    }

    @Override
    public QyclRing randomSettingRing(String mobile, String openId) {
        QyclRing qyclRing = new QyclRing();
        qyclRing.setOpenId(openId);
        qyclRing.setMobile(mobile);
        qyclRing.setCompanyTitle(mobile);
        qyclRing.setRingType(QyclConstant.RING_TYPE_PREFABRICATE);
        QyclRingVideo qyclRingVideo = qyclRingVideoService.randomQyclGrRingVideo();
        if (qyclRingVideo != null) {
            qyclRing.setVideoPath(qyclRingVideo.getRingUrl());
            qyclRing.setRingId(qyclRingVideo.getRingId());
            qyclRing.setRingStatus("06");
        }
//        //保存未支付订单
//        String orderId = qyclOrderPayService.saveNotPaymentOrder(openId, qyclRing.getCompanyTitle(), mobile);
//        qyclRing.setOrderId(orderId);
        this.save(qyclRing);
        return qyclRing;
    }

    @Override
    public QyclRing randomSettingQyclRing(String mobile, String openId) {
        QyclRing qyclRing = new QyclRing();
        qyclRing.setOpenId(openId);
        qyclRing.setMobile(mobile);
        qyclRing.setCompanyTitle(mobile);
        qyclRing.setRingType(QyclConstant.RING_TYPE_QYCL_PREFABRICATE);
        QyclRingVideo qyclRingVideo = qyclRingVideoService.randomQyclRingVideo();
        if (qyclRingVideo != null) {
            qyclRing.setVideoPath(qyclRingVideo.getRingUrl());
            qyclRing.setRingId(qyclRingVideo.getRingId());
            qyclRing.setRingStatus("06");
        }
//        //保存未支付订单
//        String orderId = qyclOrderPayService.saveNotPaymentOrder(openId, qyclRing.getCompanyTitle(), mobile);
//        qyclRing.setOrderId(orderId);
        this.save(qyclRing);
        return qyclRing;
    }

    @Override
    public void webSdkCallback(String mobile, String openId, String ringId, String departmentId, String companyOwner) throws IOException {
        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, openId).eq(QyclCompany::getMobile, mobile).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        if (qyclCompany != null) {
            qyclCompany.setDepartmentId(departmentId);
            qyclCompany.setPayTime(new Date());
            //分配处理人
            qyclCompany.setOperationTime(new Date());
            qyclCompanyService.updateById(qyclCompany);
        }
        QyclRing qyclRing = this.getById(ringId);
        if (qyclRing != null) {
            String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
            //修改铃音为已支付
            qyclRing.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
            //修改订单状态未已支付
            qyclOrderPayService.lambdaUpdate()
                    .set(QyclOrderPay::getPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS)
                    .set(QyclOrderPay::getNotifyTime, new Date())
                    .set(QyclOrderPay::getUpdateTime, new Date())
                    .eq(QyclOrderPay::getId, qyclRing.getOrderId()).update();
            if (QyclConstant.RING_TYPE_DEFAULT.equals(qyclRing.getRingType())) {
                String videoPath = ttsService.genVideoRelativePath();
                ttsService.mergeVideo(qyclRing.getFilePath(), videoPath);
                qyclRing.setVideoPath(videoPath);
                String videoRingFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), qyclRing.getVideoPath());
                EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, qyclRing.getCompanyTitle(), new File(videoRingFullPath), EnterpriseVrbtService.RING_TYPE_VEDIO,companyOwner,channel);
                if (entVrbtResult.isOK()) {
                    String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                    qyclRing.setStreamNumber(streamNumber);
                }
                String audioRingFullPath = FilenameUtils.concat(ttsProperties.getAudioFileBaseDir(), qyclRing.getFilePath());
                EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByChannel(departmentId, qyclRing.getCompanyTitle(), new File(audioRingFullPath), EnterpriseVrbtService.RING_TYPE_AUDIO,companyOwner,channel);
                if (audioEntVrbtResult.isOK()) {
                    String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                    qyclRing.setStreamNumberAudio(streamNumber);
                }
                this.updateById(qyclRing);
            } else {
                //制作完成铃声开始上传
                if (QyclConstant.RING_MAKE_COMPLETE.equals(qyclRing.getRingMakeStatus())) {
                    InputStream videoIns = aliMediaService.getObjectInputStream(qyclRing.getVideoPath());
                    InputStream audioIns = aliMediaService.getObjectInputStream(qyclRing.getFilePath());
                    EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO,companyOwner,channel);
                    if (entVrbtResult.isOK()) {
                        String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                        qyclRing.setStreamNumber(streamNumber);
                    }
                    EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), audioIns, EnterpriseVrbtService.RING_TYPE_AUDIO,companyOwner,channel);
                    if (audioEntVrbtResult.isOK()) {
                        String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                        qyclRing.setStreamNumberAudio(streamNumber);
                    }
                }
                this.updateById(qyclRing);
            }
        }
//        //信息流广告转化上报
//        Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getChannel, BizConstant.BIZ_QYCL).eq(Subscribe::getIspOrderNo, ringId).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
//        subscribe.setOpenTime(new Date());
//        subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
//        subscribeService.updateSubscribeDbAndEs(subscribe);
//        channelService.AdEffectFeedbackNew(subscribe, BizConstant.SUBSCRIBE_STATUS_SUCCESS);
    }

    @Override
    public void webSdkPersonCallback(String mobile, String openId, String departmentId, String companyOwner) throws IOException {
        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, openId).eq(QyclCompany::getMobile, mobile).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        QyclRing qyclRing = lambdaQuery().eq(QyclRing::getMobile, mobile).eq(QyclRing::getOpenId, openId).in(QyclRing::getRingType, QyclConstant.RING_TYPE_DIY, QyclConstant.RING_TYPE_PREFABRICATE, QyclConstant.RING_TYPE_TEMPLATE, QyclConstant.RING_TYPE_VIDEO).orderByDesc(QyclRing::getCreateTime).last("limit 1").one();
        String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
        if (qyclCompany != null) {
            qyclCompany.setDepartmentId(departmentId);
            qyclCompany.setPayTime(new Date());
            qyclCompany.setOperationTime(new Date());
            qyclCompanyService.updateById(qyclCompany);
        } else {
            //保存公司信息
            QyclCompany companyDto = new QyclCompany();
            companyDto.setOpenId(openId);
            companyDto.setTitle(mobile);
            companyDto.setMobile(mobile);
            companyDto.setDepartmentId(departmentId);
            companyDto.setPayTime(new Date());
            companyDto.setOperationTime(new Date());
            companyDto.setCompanyOwner(companyOwner);
            channel=QyclConstant.getChannelByCompanyOwner(companyOwner);
            companyDto.setChannel(channel);
            qyclCompanyService.save(companyDto);
            //保存成员信息
            QyclCompanyMember qyclCompanyMember = new QyclCompanyMember();
            qyclCompanyMember.setOpenId(openId);
            qyclCompanyMember.setMobile(mobile);
            qyclCompanyMember.setCompanyTitle(mobile);
            qyclCompanyMemberService.save(qyclCompanyMember);
        }

        if (qyclRing == null) {
            //随机设置铃声一首
            qyclRing = randomSettingRing(mobile, openId);
        }
        //修改铃音为已支付
        qyclRing.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
        //保存未支付订单
        String orderId = qyclOrderPayService.saveNotPaymentOrder(openId, mobile, mobile, companyOwner);
        qyclRing.setOrderId(orderId);
        //修改订单状态未已支付
        qyclOrderPayService.lambdaUpdate()
                .set(QyclOrderPay::getPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS)
                .set(QyclOrderPay::getNotifyTime, new Date())
                .set(QyclOrderPay::getUpdateTime, new Date())
                .eq(QyclOrderPay::getId, qyclRing.getOrderId()).update();

        if (QyclConstant.RING_TYPE_PREFABRICATE.equals(qyclRing.getRingType())) {
            enterpriseVrbtService.setDeptRingsByTimeChannel(departmentId, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,channel, qyclRing.getRingId());
        } else if (QyclConstant.RING_TYPE_DIY.equals(qyclRing.getRingType())) {
            //制作完成铃声开始上传
            if (QyclConstant.RING_MAKE_COMPLETE.equals(qyclRing.getRingMakeStatus())) {
                InputStream videoIns = aliMediaService.getObjectInputStream(qyclRing.getVideoPath());
                EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,channel);
                if (entVrbtResult.isOK()) {
                    String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                    qyclRing.setStreamNumber(streamNumber);
                }
                if (StringUtils.isNotBlank(qyclRing.getFilePath())) {
                    InputStream audioIns = aliMediaService.getObjectInputStream(qyclRing.getFilePath());
                    EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), audioIns, EnterpriseVrbtService.RING_TYPE_AUDIO, companyOwner,channel);
                    if (audioEntVrbtResult.isOK()) {
                        String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                        qyclRing.setStreamNumberAudio(streamNumber);
                    }
                }
            } else if (QyclConstant.RING_TYPE_TEMPLATE.equals(qyclRing.getRingType()) || QyclConstant.RING_TYPE_VIDEO.equals(qyclRing.getRingType())) {
                //制作完成铃声开始上传
                if (QyclConstant.RING_MAKE_COMPLETE.equals(qyclRing.getRingMakeStatus())) {
                    InputStream videoIns = aliMediaService.getObjectInputStream(qyclRing.getVideoPath());
                    EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,channel);
                    if (entVrbtResult.isOK()) {
                        String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                        qyclRing.setStreamNumber(streamNumber);
                    }
                }
            }
        }
        this.updateById(qyclRing);
    }

    @Override
    public void webSdkQyclCallback(String mobile, String openId, String departmentId, String companyOwner) throws IOException {
        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, openId).eq(QyclCompany::getMobile, mobile).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        QyclRing qyclRing = lambdaQuery().eq(QyclRing::getMobile, mobile).eq(QyclRing::getOpenId, openId).in(QyclRing::getRingType, QyclConstant.RING_TYPE_QYCL_DIY, QyclConstant.RING_TYPE_QYCL_TEMPLATE, QyclConstant.RING_TYPE_QYCL_PREFABRICATE).orderByDesc(QyclRing::getCreateTime).last("limit 1").one();
        String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
        if (qyclCompany != null) {
            qyclCompany.setDepartmentId(departmentId);
            qyclCompany.setPayTime(new Date());
            qyclCompany.setOperationTime(new Date());
            qyclCompanyService.updateById(qyclCompany);
        } else {
            //保存公司信息
            QyclCompany companyDto = new QyclCompany();
            companyDto.setOpenId(openId);
            companyDto.setTitle(mobile);
            companyDto.setMobile(mobile);
            companyDto.setDepartmentId(departmentId);
            companyDto.setPayTime(new Date());
            companyDto.setOperationTime(new Date());
            companyDto.setCompanyOwner(companyOwner);
            channel=QyclConstant.getChannelByCompanyOwner(companyOwner);
            companyDto.setChannel(channel);
            qyclCompanyService.save(companyDto);
            //保存成员信息
            QyclCompanyMember qyclCompanyMember = new QyclCompanyMember();
            qyclCompanyMember.setOpenId(openId);
            qyclCompanyMember.setMobile(mobile);
            qyclCompanyMember.setCompanyTitle(mobile);
            qyclCompanyMemberService.save(qyclCompanyMember);
        }

        if (qyclRing == null) {
            //随机设置铃声一首
            qyclRing = randomSettingQyclRing(mobile, openId);
        }
        //修改铃音为已支付
        qyclRing.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
        //保存未支付订单
        String orderId = qyclOrderPayService.saveNotPaymentOrder(openId, mobile, mobile, companyOwner);
        qyclRing.setOrderId(orderId);
        //修改订单状态未已支付
        qyclOrderPayService.lambdaUpdate()
                .set(QyclOrderPay::getPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS)
                .set(QyclOrderPay::getNotifyTime, new Date())
                .set(QyclOrderPay::getUpdateTime, new Date())
                .eq(QyclOrderPay::getId, qyclRing.getOrderId()).update();

        if (QyclConstant.RING_TYPE_QYCL_PREFABRICATE.equals(qyclRing.getRingType())) {
            enterpriseVrbtService.setDeptRingsByTimeChannel(departmentId, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,channel, qyclRing.getRingId());
        } else if (QyclConstant.RING_TYPE_QYCL_DIY.equals(qyclRing.getRingType())) {
            //制作完成铃声开始上传
            if (QyclConstant.RING_MAKE_COMPLETE.equals(qyclRing.getRingMakeStatus())) {
                InputStream videoIns = aliMediaService.getObjectInputStream(qyclRing.getVideoPath());
                EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,channel);
                if (entVrbtResult.isOK()) {
                    String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                    qyclRing.setStreamNumber(streamNumber);
                }
                if (StringUtils.isNotBlank(qyclRing.getFilePath())) {
                    InputStream audioIns = aliMediaService.getObjectInputStream(qyclRing.getFilePath());
                    EntVrbtResult audioEntVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), audioIns, EnterpriseVrbtService.RING_TYPE_AUDIO, companyOwner,channel);
                    if (audioEntVrbtResult.isOK()) {
                        String streamNumber = audioEntVrbtResult.getData().at("/streamNumber").asText();
                        qyclRing.setStreamNumberAudio(streamNumber);
                    }
                }
            } else if (QyclConstant.RING_TYPE_QYCL_TEMPLATE.equals(qyclRing.getRingType())) {
                //制作完成铃声开始上传
                if (QyclConstant.RING_MAKE_COMPLETE.equals(qyclRing.getRingMakeStatus())) {
                    InputStream videoIns = aliMediaService.getObjectInputStream(qyclRing.getVideoPath());
                    EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(departmentId, qyclRing.getCompanyTitle(), videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,channel);
                    if (entVrbtResult.isOK()) {
                        String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                        qyclRing.setStreamNumber(streamNumber);
                    }
                }
            }
        }
        this.updateById(qyclRing);
    }

    @Override
    public String createTemplateRing(String mobile, String openId, String templateId, String clipsParam, String subChannel, String companyOwner) {
        QyclRing originalRing = lambdaQuery().eq(QyclRing::getOpenId, openId).eq(QyclRing::getMobile, mobile).eq(QyclRing::getOrderPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS).eq(QyclRing::getModifyType, 0).one();
        QyclRing ringDto = new QyclRing();
        if (originalRing != null) {
            originalRing.setModifyCount(originalRing.getModifyCount() + 1);
            originalRing.setUpdateTime(new Date());
            updateById(originalRing);
            ringDto.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
            ringDto.setModifyType(1);
        }
        //生成铃声
        Map<String, String> map = new HashMap<>();
        ringDto.setOpenId(openId);
        ringDto.setMobile(mobile);
        ringDto.setCompanyTitle(mobile);
        ringDto.setRingType(QyclConstant.RING_TYPE_TEMPLATE);
        ringDto.setImageUrls(clipsParam);
        ringDto.setRingMakeStatus(QyclConstant.RING_MAKE_IN);
        ringDto.setSubChannel(subChannel);
        ringDto.setCompanyOwner(companyOwner);
        String videoJobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_QYCL, templateId, clipsParam);
        ringDto.setAliVideoJobId(videoJobId);
        this.save(ringDto);
        return ringDto.getId();
    }

    @Override
    public String createQyclTemplateRing(String mobile, String openId, String templateId, String clipsParam, String subChannel, String companyOwner) {
        QyclRing originalRing = lambdaQuery().eq(QyclRing::getOpenId, openId).eq(QyclRing::getOrderPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS).eq(QyclRing::getModifyType, 0).one();
        QyclRing ringDto = new QyclRing();
        if (originalRing != null) {
            originalRing.setModifyCount(originalRing.getModifyCount() + 1);
            originalRing.setUpdateTime(new Date());
            updateById(originalRing);
            ringDto.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
            ringDto.setModifyType(1);
        }
        //生成铃声
        ringDto.setOpenId(openId);
        ringDto.setMobile(mobile);
        ringDto.setCompanyTitle(mobile);
        ringDto.setRingType(QyclConstant.RING_TYPE_QYCL_TEMPLATE);
        ringDto.setImageUrls(clipsParam);
        ringDto.setRingMakeStatus(QyclConstant.RING_MAKE_IN);
        ringDto.setSubChannel(subChannel);
        ringDto.setCompanyOwner(companyOwner);
        String videoJobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_QYCL, templateId, clipsParam);
        ringDto.setAliVideoJobId(videoJobId);
        this.save(ringDto);
        return ringDto.getId();
    }

    @Override
    public String createVideoRing(String mobile, String openId, String videoPath, String subChannel, String companyOwner) {
        QyclRing originalRing = lambdaQuery().eq(QyclRing::getOpenId, openId).eq(QyclRing::getMobile, mobile).eq(QyclRing::getOrderPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS).eq(QyclRing::getModifyType, 0).one();
        QyclRing ringDto = new QyclRing();
        if (originalRing != null) {
            originalRing.setModifyCount(originalRing.getModifyCount() + 1);
            originalRing.setUpdateTime(new Date());
            updateById(originalRing);
            ringDto.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
            ringDto.setModifyType(1);
            QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, openId).eq(QyclCompany::getMobile, mobile).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
            if (StringUtils.isNotBlank(videoPath) && qyclCompany != null && StringUtils.isNotBlank(qyclCompany.getDepartmentId())) {
                try {
                    InputStream videoIns = aliMediaService.getObjectInputStream(videoPath);
                    EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(qyclCompany.getDepartmentId(), mobile, videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,qyclCompany.getChannel());
                    if (entVrbtResult.isOK()) {
                        String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                        ringDto.setStreamNumber(streamNumber);
                    }
                } catch (Exception e) {
                    log.error("上传视频铃音错误:", e);
                }
            }
        }
        //生成铃声
        Map<String, String> map = new HashMap<>();
        ringDto.setOpenId(openId);
        ringDto.setMobile(mobile);
        ringDto.setCompanyTitle(mobile);
        ringDto.setRingType(QyclConstant.RING_TYPE_VIDEO);
        ringDto.setRingMakeStatus(QyclConstant.RING_MAKE_COMPLETE);
        ringDto.setVideoPath(videoPath);
        ringDto.setSubChannel(subChannel);
        ringDto.setCompanyOwner(companyOwner);
        this.save(ringDto);
        return ringDto.getId();
    }

    @Override
    public String createVideoRingApp(String mobile, String openId, String videoPath, String subChannel, String companyOwner) {
        //生成铃声
        QyclRing ringDto = new QyclRing();
        ringDto.setOpenId(openId);
        ringDto.setMobile(mobile);
        ringDto.setCompanyTitle(mobile);
        ringDto.setRingType(QyclConstant.RING_TYPE_VIDEO);
        ringDto.setRingMakeStatus(QyclConstant.RING_MAKE_COMPLETE);
        ringDto.setVideoPath(videoPath);
        ringDto.setSubChannel(subChannel);
        ringDto.setCompanyOwner(companyOwner);
        this.save(ringDto);

        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, openId).eq(QyclCompany::getMobile, mobile).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        if (StringUtils.isNotBlank(videoPath) && qyclCompany != null && StringUtils.isNotBlank(qyclCompany.getDepartmentId())) {
            try {
                InputStream videoIns = aliMediaService.getObjectInputStream(videoPath);
                EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByCompanyOwner(qyclCompany.getDepartmentId(), mobile, videoIns, EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,qyclCompany.getChannel());
                if (entVrbtResult.isOK()) {
                    String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
                    ringDto.setStreamNumber(streamNumber);
                }
            } catch (Exception e) {
                log.error("上传视频铃音错误:", e);
            }
        }
        return ringDto.getId();
    }

    @Override
    public QyclRing getByRingId(String ringId) {
        return lambdaQuery().eq(QyclRing::getRingId, ringId).orderByDesc(QyclRing::getCreateTime).last("limit 1").one();
    }

    @Override
    public String createQyclDiyRing(String ringTxt, String companyTitle, String openId, String mobile, VoiceGender voiceGender, String[] imageArray, String subChannel, String companyOwner) {
        //查询用户是否已开通成功后设置的铃音
        QyclRing originalRing = lambdaQuery().eq(QyclRing::getOpenId, openId).eq(QyclRing::getOrderPayStatus, QyclConstant.PAYMENT_STATUS_SUCCESS).eq(QyclRing::getModifyType, 0).one();
        QyclRing ringDto = new QyclRing();
        if (originalRing != null) {
            originalRing.setModifyCount(originalRing.getModifyCount() + 1);
            originalRing.setUpdateTime(new Date());
            updateById(originalRing);
            ringDto.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
            ringDto.setModifyType(1);
        }
        //生成铃声
        ringDto.setOpenId(openId);
        ringDto.setMobile(mobile);
        ringDto.setCompanyTitle(companyTitle);
        ringDto.setRingType(QyclConstant.RING_TYPE_QYCL_DIY);
        ringDto.setRingTxt(ringTxt);
        if (imageArray != null && imageArray.length > 0) {
            ringDto.setImageUrls(StringUtils.join(imageArray, ","));
        }
        ringDto.setRingMakeStatus(QyclConstant.RING_MAKE_IN);
        ringDto.setSubChannel(subChannel);
        ringDto.setCompanyOwner(companyOwner);
//        ringDto.setBgmUrl(backgroundMusic.getMusicPath());
        String jobId = aliMediaService.tts(AliMediaProperties.JOB_QUEUE_TAG_QYCL, ringTxt, voiceGender, ImmutableMap.of());
        ringDto.setAliTtsJobId(jobId);
        this.save(ringDto);
        return ringDto.getId();
    }


    private Subscribe parseLink(String source, Subscribe subscribe) {
        try {
            source = URLDecoder.decode(source, "UTF-8");
            String subChannel = HttpUrl.parse(source.replace("#/", "")).queryParameter("subChannel");
            subscribe.setSubChannel(subChannel);
            subscribe.setSource(source);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return subscribe;
    }

}
