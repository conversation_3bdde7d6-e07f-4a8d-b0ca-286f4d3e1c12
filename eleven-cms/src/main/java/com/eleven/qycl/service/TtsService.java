package com.eleven.qycl.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.qycl.config.TtsProperties;
import com.eleven.qycl.util.FfmpegUtils;
import com.eleven.qycl.util.WavFileUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tts.v20190823.TtsClient;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceRequest;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceResponse;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;

/**
 * Author: <EMAIL>
 * Date: 2022/11/10 16:26
 * Desc: 语音合成转换服务(使用腾讯云语音合成)
 */
@Service
@Slf4j
public class TtsService {

    public static final String LOG_TAG = "语音合成api";
    @Autowired
    TtsProperties ttsProperties;
    @Autowired
    private Environment environment;

    //private OkHttpClient client;
    private TtsClient ttsClient;

    private ObjectMapper mapper;
    //public static final String DELIMITER_AMP = "&";
    //private static final MediaType mediaType = MediaType.parse("application/json");


    @PostConstruct
    public void init() {
        //this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        //if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
        //    this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
        //            message -> System.out.println(message))).build();
        //}
        // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey,此处还需注意密钥对的保密
        // 密钥可前往https://console.cloud.tencent.com/cam/capi网站进行获取
        //final String secretId = "AKIDudEdorvpjZXN2xgCYjjfRzJoDZyJe4Z5";
        final String secretId = ttsProperties.getSecretId();
        //final String secretKey = "kXSK2iMB5xd3INEsIj9TktvlRyKWYPAp";
        final String secretKey = ttsProperties.getSecretKey();
        Credential cred = new Credential(secretId, secretKey);
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("tts.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        ttsClient = new TtsClient(cred, "ap-beijing", clientProfile);
        
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public TextToVoiceResponse textToVoice(String text, String sessionId){
        try{
            // 实例化一个请求对象,每个接口都会对应一个request对象
            TextToVoiceRequest req = new TextToVoiceRequest();
            req.setText(text);
            req.setSessionId(sessionId);
            req.setVolume(4F); //音量大小，范围：[0，10]，分别对应11个等级的音量，默认为0，代表正常音量。没有静音选项。
            req.setSpeed(-0.3F); //语速，范围：[-2，2]，分别对应不同语速：-2代表0.6倍 -1代表0.8倍 0代表1.0倍（默认）1代表1.2倍 2代表1.5倍 如果需要更细化的语速，可以保留小数点后一位，例如0.5 1.1 1.8等。
            req.setProjectId(0L);
            req.setModelType(1L);
            //101009-智芸，知性女声
            req.setVoiceType(101009L);
            req.setPrimaryLanguage(1L);
            req.setSampleRate(16000L);
            req.setCodec("wav"); //返回音频格式，可取值：wav（默认），mp3，pcm
            log.info("{}-腾讯语音合成,text:{}", LOG_TAG, text);
            // 返回的resp是一个TextToVoiceResponse的实例，与请求对象对应
            TextToVoiceResponse resp = ttsClient.TextToVoice(req);
            //// 输出json格式的字符串回包
            //log.info("{}-腾讯语音合成,响应json:{}", LOG_TAG, TextToVoiceResponse.toJsonString(resp));
            //final String audio = resp.getAudio();
            //File file = new File("D:\\testTts.wav");
            //FileUtils.writeByteArrayToFile(file, Base64.decodeBase64(audio));

            return resp;
        } catch (Exception e) {
            log.info("{}-腾讯语音合成,异常:", LOG_TAG, e);
            return null;
        }
    }

    /**
     * 文字转语音文件,返回路径后缀(日期加文件名)
     * @param text 要转换的文字,长度不超过150字
     * @return
     */
    public String textToVoiceFile(String text){
        String uuid = IdWorker.get32UUID();
        //yyMMdd
        String dateString = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        //String subPath = "samples" + "/" + uuid + ".mp3";
        String subPath = dateString + "/" + uuid + ".mp3";
        final String audioFileBaseDir = ttsProperties.getAudioFileBaseDir();
        String fullPath = FilenameUtils.concat(audioFileBaseDir, subPath);
        
        final TextToVoiceResponse response = this.textToVoice(text,uuid);
        if(Objects.isNull(response)){
            return null;
        }
        try {
            final byte[] voiceBytes = Base64.decodeBase64(response.getAudio());
            String backgroundAudioPath = ttsProperties.getBackgroundAudioPath();
            //final byte[] backgroundBytes = Files.readAllBytes(new DefaultResourceLoader().getResource("audio/background.wav").getFile().toPath());
            final byte[] backgroundBytes = Files.readAllBytes(Paths.get(backgroundAudioPath));
            WavFileUtils.mergeAndConvertMp3(voiceBytes,backgroundBytes,fullPath);
        } catch (Exception e) {
            log.info("{}-语音合并转换,异常:", LOG_TAG, e);
            return null;
        }

        return subPath;
    }

    public String genVideoRelativePath(){
        String uuid = IdWorker.get32UUID();
        //yyMMdd
        String dateString = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        //String subPath = "samples" + "/" + uuid + ".mp3";
        return  dateString + "/" + uuid + ".mp4";
    }

    public void mergeVideo(String audioPath, String videoRelativePath){
        final String audioFileBaseDir = ttsProperties.getAudioFileBaseDir();
        String audioFullPath = FilenameUtils.concat(audioFileBaseDir, audioPath);
        String outputFullPath = FilenameUtils.concat(audioFileBaseDir, videoRelativePath);
        try {
            String templateVideoPath = ttsProperties.getTemplateVideoPath();
            log.info("{}-视频合并音频,templateVideoPath:{},audioFullPath:{},outputFullPath:{}",LOG_TAG,templateVideoPath,audioFullPath,outputFullPath);
            FfmpegUtils.merge(templateVideoPath,audioFullPath,outputFullPath);
        } catch (Exception e) {
            log.info("{}-视频合并音频异常:", LOG_TAG, e);
        }
    }

    
}
