package com.eleven.qycl.util;

import org.bytedeco.javacpp.Loader;

import java.io.IOException;

/**
 * Author: ye<PERSON><PERSON><PERSON>@qq.com
 * Date: 2022/11/9 18:38
 * Desc: 视频处理工具类
 */
public class FfmpegUtils {

    /**
     * 视频音频合并(给视频添加背景声音)
     * ffmpeg -i video.mp4 -i audio.mp3 -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -y output.mp4
     * @param videoPath
     * @param audioPath
     * @param outputPath
     * @throws IOException
     * @throws InterruptedException
     */
    public static void merge(String videoPath, String audioPath, String outputPath) throws IOException, InterruptedException {
            String ffmpeg = Loader.load(org.bytedeco.ffmpeg.ffmpeg.class);
            ProcessBuilder pb = new ProcessBuilder(ffmpeg, "-i", videoPath,
                    "-i", audioPath,
                    "-c:v", "copy",
                    "-c:a", "aac",
                    "-map", "0:v:0",
                    "-map", "1:a:0",
                    "-shortest",
                    "-y",outputPath);
            pb.inheritIO().start().waitFor();
    }


    /**
     * 视频音频合并(给视频添加背景声音)
     * ffmpeg -an -i video.mp4 -stream_loop -1 -i audio.wav -t 60 -y out2.mp4
     * -an -i video.mp4 代表消除视频中的音频
     * -stream_loop -1 -i audio.wav
     * -stream_loop -1 参数-1代表循环输入源
     * -t 60 裁剪60秒
     *
     * @param videoPath
     * @param audioPath
     * @param outputPath
     * @throws IOException
     * @throws InterruptedException
     */
    public static void replaceAudio(String videoPath, String audioPath, String outputPath) throws IOException, InterruptedException {
        String ffmpeg = Loader.load(org.bytedeco.ffmpeg.ffmpeg.class);
        ProcessBuilder pb = new ProcessBuilder(ffmpeg, "-an", "-i", videoPath, "-stream_loop", "-1", "-i", audioPath, "-c:v", "copy", "-t", "30", "-y", outputPath);
        pb.inheritIO().start().waitFor();
    }


    public static void main(String[] args) throws IOException, InterruptedException {
        replaceAudio("D:\\source.mp4",
            "D:\\bgm.mp3",
            "D:\\java-out.mp4");
    }




}
