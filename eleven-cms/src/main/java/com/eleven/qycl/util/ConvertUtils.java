package com.eleven.qycl.util;

import com.cloudburst.lame.lowlevel.LameEncoder;
import com.cloudburst.lame.mp3.Lame;
import com.cloudburst.lame.mp3.MPEGMode;
import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.time.StopWatch;
//import ws.schild.jave.AudioAttributes;
//import ws.schild.jave.Encoder;
//import ws.schild.jave.EncodingAttributes;
//import ws.schild.jave.MultimediaObject;

import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * @Author: clf
 * @Date: 2020-03-08
 * @Description: 语音合成工具类
 */
@Slf4j
public class ConvertUtils {

    ///**
    // * 转换音频文件
    // * @param src 需要转换的pcm音频路径
    // * @param target 保存转换后wav格式的音频路径
    // * @throws Exception
    // */
    //public static void convertPcm2Wav(String src, String target) throws Exception {
    //    FileInputStream fis = new FileInputStream(src);
    //    FileOutputStream fos = new FileOutputStream(target);
    //
    //    //计算长度
    //    byte[] buf = new byte[1024 * 4];
    //    int size = fis.read(buf);
    //    int PCMSize = 0;
    //    while (size != -1) {
    //        PCMSize += size;
    //        size = fis.read(buf);
    //    }
    //    fis.close();
    //
    //    //填入参数，比特率等等。这里用的是16位单声道 8000 hz
    //    WaveHeader header = new WaveHeader();
    //    //长度字段 = 内容的大小（PCMSize) + 头部字段的大小(不包括前面4字节的标识符RIFF以及fileLength本身的4字节)
    //    header.fileLength = PCMSize + (44 - 8);
    //    header.FmtHdrLeth = 16;
    //    header.BitsPerSample = 16;
    //    header.Channels = 2;
    //    header.FormatTag = 0x0001;
    //    header.SamplesPerSec = 8000;
    //    header.BlockAlign = (short)(header.Channels * header.BitsPerSample / 8);
    //    header.AvgBytesPerSec = header.BlockAlign * header.SamplesPerSec;
    //    header.DataHdrLeth = PCMSize;
    //
    //    byte[] h = header.getHeader();
    //
    //    assert h.length == 44; //WAV标准，头部应该是44字节
    //    //write header
    //    fos.write(h, 0, h.length);
    //    //write data stream
    //    fis = new FileInputStream(src);
    //    size = fis.read(buf);
    //    while (size != -1) {
    //        fos.write(buf, 0, size);
    //        size = fis.read(buf);
    //    }
    //    fis.close();
    //    fos.close();
    //    System.out.println("Convert OK!");
    //}


    ///**
    // * wav格式转换成mp3格式
    // * @param source  源文件
    // * @param target 目标文件
    // * @return
    // */
    //public static boolean convertWav2Mp3(File source, File target) {
    //    boolean succeeded = true;
    //    try {
    //        AudioAttributes audio = new AudioAttributes();
    //        audio.setCodec("libmp3lame");
    //        audio.setBitRate(128000);
    //        audio.setChannels(2);
    //        audio.setSamplingRate(44100);
    //        audio.setVolume(new Integer(256));
    //
    //        EncodingAttributes attrs = new EncodingAttributes();
    //        attrs.setFormat("mp3");
    //        attrs.setAudioAttributes(audio);
    //        Encoder encoder = new Encoder();
    //        encoder.encode(new MultimediaObject(source), target, attrs);
    //    } catch (Exception ex) {
    //        ex.printStackTrace();
    //        succeeded = false;
    //    }
    //    return succeeded;
    //}

    public static byte[] encodeToMp3(InputStream inputStream) throws IOException, UnsupportedAudioFileException {
        // Stream流的方式
        //InputStream in = new ByteArrayInputStream(wavData);
        // import javax.sound.sampled.AudioSystem;
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(inputStream);
        // import net.sourceforge.lame.mp3.Lame;
        // 官方样例
        // LameEncoder encoder = new LameEncoder(audioInputStream.getFormat(), 256, MPEGMode.STEREO, Lame.QUALITY_HIGHEST, false);
        LameEncoder encoder = new LameEncoder(audioInputStream.getFormat(), 128, MPEGMode.STEREO, Lame.QUALITY_HIGHEST, false);
        ByteArrayOutputStream mp3 = new ByteArrayOutputStream();
        byte[] inputBuffer = new byte[encoder.getPCMBufferSize()];
        byte[] outputBuffer = new byte[encoder.getPCMBufferSize()];
        int bytesRead;
        int bytesWritten;
        while(0 < (bytesRead = audioInputStream.read(inputBuffer))) {
            bytesWritten = encoder.encodeBuffer(inputBuffer, 0, bytesRead, outputBuffer);
            mp3.write(outputBuffer, 0, bytesWritten);
        }
        encoder.close();
        return mp3.toByteArray();
    }


    //public static void main(String[] args) {
    //
    //
    //    StopWatch watch3 = StopWatch.createStarted();
    //    convertWav2Mp3(new File("D:\\merge.wav"), new File("D:\\merge_convert.mp3"));
    //    log.info("wav转mp3用时:{}毫秒",watch3.getTime(TimeUnit.MILLISECONDS));
    //    watch3.stop();
    //
    //
    //}

}
