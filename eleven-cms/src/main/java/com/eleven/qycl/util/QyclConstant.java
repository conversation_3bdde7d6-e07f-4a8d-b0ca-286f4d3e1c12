package com.eleven.qycl.util;

import com.eleven.cms.util.BizConstant;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: cai lei
 * @create: 2022-11-09 16:57
 */
public class QyclConstant {

    public static final Integer PAYMENT_STATUS_INIT = -1;  //待支付
    public static final Integer PAYMENT_STATUS_FAIL = 0; //支付失败
    public static final Integer PAYMENT_STATUS_SUCCESS = 1; //支付成功
    //铃音文件基地址
    public static final String RING_FILE_BASE_URL = "https://crbt.cdyrjygs.com/file/ring/";

    public static final String QYCL_FUN_STATUS_INIT = "0"; //未开通
    public static final String QYCL_FUN_STATUS_ORDER = "1"; //已开通
    public static final String QYCL_FUN_STATUS_UNSUB = "2"; //已退订

    public static final String QYCL_MIGU_CHANNEL_CODE_YRJY = "014X0DJ";
    public static final String QYCL_MIGU_CHANNEL_CODE_MAIHE = "014X0EQ";
    public static final String QYCL_ORDER_ID_YRJY = "62500046123";
    public static final String QYCL_ORDER_ID_MAIHE = "62500120711";
    public static final String QYCL_ORDER_ID_YRJY_18 = "62500113742";
    public static final String QYCL_COMPANY_OWNER_YRJY = "yrjy";
    public static final String QYCL_COMPANY_OWNER_YRJY_18 = "yrjy_18";
    public static final String QYCL_COMPANY_OWNER_MAIHE = "maihe";

    public static final Integer RING_TYPE_DEFAULT = 1; //默认类型
    public static final Integer RING_TYPE_DIY = 2; //diy类型
    public static final Integer RING_TYPE_PREFABRICATE = 3; //个人彩铃(预制铃声)
    public static final Integer RING_TYPE_TEMPLATE = 4; //模板类型
    public static final Integer RING_TYPE_VIDEO = 5; //视频铃声
    public static final Integer RING_TYPE_QYCL_DIY = 6; //企业彩铃diy类型
    public static final Integer RING_TYPE_QYCL_TEMPLATE = 7; //企业彩铃模板类型
    public static final Integer RING_TYPE_QYCL_PREFABRICATE = 8; //企业彩铃(预制铃声)

    public static final Integer RING_MAKE_COMPLETE = 1; //制作完成
    public static final Integer RING_MAKE_IN = 0; //制作中

    public static final String QYCL_COMPANY_OWNER_HEAD = "company-owner";

    public static final String QYCL_PRICE_10 = "1000";

    public static final String QYCL_PRICE_18 = "1800";

    //企业彩铃背景视频
    public static final String QYCL_BACKGROUND_VIDEO_URL = "https://ims-media.oss-cn-beijing.aliyuncs.com/test/video/20241015145854.mp4";

    public static String getCompanyOwnerByMiguChannelCode(String miguChannelCode, String price) {
        if (QYCL_MIGU_CHANNEL_CODE_YRJY.equals(miguChannelCode)) {
            if (QYCL_PRICE_18.equals(price)) {
                return QYCL_COMPANY_OWNER_YRJY_18;
            } else {
                return QYCL_COMPANY_OWNER_YRJY;
            }
        } else {
            return QYCL_COMPANY_OWNER_MAIHE;
        }
    }

    public static String getCompanyOwnerByOrderId(String orderId) {
        if (QyclConstant.QYCL_ORDER_ID_YRJY.equals(orderId)) {
            return QYCL_COMPANY_OWNER_YRJY;
        } else if (QyclConstant.QYCL_ORDER_ID_MAIHE.equals(orderId)) {
            return QYCL_COMPANY_OWNER_MAIHE;
        } else {
            return QYCL_COMPANY_OWNER_YRJY_18;
        }
    }

    public static String getChannelByCompanyOwner(String companyOwner) {
        if (QyclConstant.QYCL_COMPANY_OWNER_YRJY.equals(companyOwner)) {
            return BizConstant.BIZ_QYCL_GR;
        } else if (QyclConstant.QYCL_COMPANY_OWNER_YRJY_18.equals(companyOwner)) {
            return BizConstant.BIZ_QYCL_GR_18;
        } else {
            return BizConstant.BIZ_QYCL_GR_MH;
        }
    }

    public static String getCompanyOwnerByChannel(String channel) {
        if (StringUtils.endsWith(channel, "MH")) {
            return QYCL_COMPANY_OWNER_MAIHE;
        } else {
            if (StringUtils.contains(channel, "18")) {
                return QYCL_COMPANY_OWNER_YRJY_18;
            }
            return QYCL_COMPANY_OWNER_YRJY;
        }
    }

}
