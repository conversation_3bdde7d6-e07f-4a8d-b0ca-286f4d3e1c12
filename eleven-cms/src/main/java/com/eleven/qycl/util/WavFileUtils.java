package com.eleven.qycl.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.StopWatch;

import javax.sound.sampled.AudioFormat;
import java.io.*;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

import static java.nio.ByteOrder.LITTLE_ENDIAN;
import static java.nio.charset.StandardCharsets.ISO_8859_1;
import static java.util.Arrays.copyOfRange;
import static javax.sound.sampled.AudioFormat.Encoding.PCM_SIGNED;

/**
 * Author: <EMAIL>
 * Date: 2022/11/9 18:38
 * Desc: wav音频合并(是mixin混合,不是拼接)
 */
@Slf4j
public class WavFileUtils {
    //private static Logger LOG = LoggerFactory.getLogger(WavFileUtil.class);

    private static final int HEAD_LENGTH = 12;

    private static final int FORMAT_LENGTH = 24;

    private static boolean isWav(byte[] head) {
        return ("RIFF".equals(new String(head, 0, 4, ISO_8859_1)) &&
                "WAVE".equals(new String(head, 8, 4, ISO_8859_1)));
    }

    private static void fileTooSmall(byte[] file) {
        if (file.length < HEAD_LENGTH + FORMAT_LENGTH) {
            log.warn("file is too small, size if {}.", file.length);
            throw new RuntimeException("file is too small");
        }
    }

    private static int headSize() {
        return HEAD_LENGTH + FORMAT_LENGTH;
    }

    /**
     * resolve wav file head.
     * ChunkID,ChunkSize,Format, everyone 4 bytes.
     * 解析wav文件头,解析出文件的pcm数据大小
     */
    public static int fileSize(byte[] file) {
        fileTooSmall(file);

        byte[] head = copyOfRange(file, 0, HEAD_LENGTH);

        if (isWav(head)) {
            return ByteBuffer.wrap(copyOfRange(head, 4, 8))
                    .order(LITTLE_ENDIAN)
                    .getInt() + 8;
        } else {
            log.warn("file format error: expected {}, actual {}.",
                    "[82, 73, 70, 70, *, *, *, *, 87, 65, 86, 69]",
                    head);
            throw new RuntimeException("file format error");
        }
    }

    public static AudioFormat fileFormat(byte[] file) {
        fileTooSmall(file);

        byte[] head = copyOfRange(file, 0, HEAD_LENGTH);

        if (isWav(head)) {
            byte[] format = copyOfRange(file, 12, HEAD_LENGTH + FORMAT_LENGTH);
            String chuckID = new String(format, 0, 4, ISO_8859_1);
            int chunkSize = ByteBuffer.wrap(copyOfRange(format, 4, 8))
                    .order(LITTLE_ENDIAN).getInt();
            int audioFmt = ByteBuffer.wrap(copyOfRange(format, 8, 10))
                    .order(LITTLE_ENDIAN).getShort();
            int channels = ByteBuffer.wrap(copyOfRange(format, 10, 12))
                    .order(LITTLE_ENDIAN).getShort();
            int sampleRate = ByteBuffer.wrap(copyOfRange(format, 12, 16))
                    .order(LITTLE_ENDIAN).getInt();
            int byteRate = ByteBuffer.wrap(copyOfRange(format, 16, 20))
                    .order(LITTLE_ENDIAN).getInt();
            int frameSize = ByteBuffer.wrap(copyOfRange(format, 20, 22))
                    .order(LITTLE_ENDIAN).getShort();
            int sampleSizeInBits = ByteBuffer.wrap(copyOfRange(format, 22, 24))
                    .order(LITTLE_ENDIAN).getShort();

            return new AudioFormat(PCM_SIGNED, sampleRate,
                    sampleSizeInBits, channels, frameSize, sampleRate, false);
        } else {
            log.warn("file is not a wav.");
            throw new RuntimeException("file is not a wav.");
        }
    }

    /**
     * 默认合并两个单声道到双声道,以尺寸大的为最红合并时长,注意,由于使用了RandomAccessFile多次写入,效率非常低,耗时长
     * @param left
     * @param right
     * @param path
     */
    public static void merge(final byte[] left, final byte[] right, final String path) {
        int leftSize = fileSize(left);
        int rightSize = fileSize(right);
        int mergeSize = mergeSizeField(leftSize, rightSize);
        int mergeDataSize = mergeDataSize(leftSize, rightSize);

        int max = Math.max(leftSize, rightSize);
        try (RandomAccessFile file = new RandomAccessFile(path, "rw")) {
            file.write(mergeHead(left, mergeSize));
            file.write(dataChunkHead(mergeDataSize));
            StopWatch stopWatch = StopWatch.createStarted();
            for (int i = headSize() + 8; i < max + 8; i += 2) {
                file.write(read(left, i));
                file.write(read(right, i));
            }
            log.info("合并写入用时:{}毫秒",stopWatch.getTime(TimeUnit.MILLISECONDS));
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * 合并两个单声道wave文件到双声道,以尺寸小的为最后合并的大小
     * wave文件结构参看https://zhumulangma.blog.csdn.net/article/details/105792456?spm=1001.2101.3001.6650.14&utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7ECTRLIST%7ERate-14-105792456-blog-106058199.pc_relevant_recovery_v2&depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7ECTRLIST%7ERate-14-105792456-blog-106058199.pc_relevant_recovery_v2&utm_relevant_index=19
     * @param left
     * @param right
     * @param path
     */
    public static void mergeStereo(final byte[] left, final byte[] right, final String path) {
        //左侧文件pcm数据size
        int leftSize = fileSize(left);
        //右侧文件pcm数据size
        int rightSize = fileSize(right);
        //按时长短的合并
        int minSize = Math.min(leftSize, rightSize);
        
        int mergeSize = mergeSizeField(minSize, minSize);
        int mergeDataSize = mergeDataSize(minSize, minSize);
        
        int bufferSize = headSize() + 8 + minSize*2;
        ByteBuffer byteBuffer = ByteBuffer.allocate(bufferSize);
        byteBuffer.put(mergeHead(left, mergeSize));
        byteBuffer.put(dataChunkHead(mergeDataSize));
        for (int i = headSize() + 8; i < minSize + 8; i += 2) {
            byteBuffer.put(read(left, i));
            byteBuffer.put(read(right, i));
        }
        // Flips this buffer.  The limit is set to the current position and then
        // the position is set to zero.  If the mark is defined then it is discarded.
        //直接调用再java8会报错 java.lang.NoSuchMethodError: No virtual method flip()Ljava.nio.ByteBuffer in class java.nio.ByteBuffe
        ((Buffer)byteBuffer).flip();
        try (final FileOutputStream fileOutputStream = new FileOutputStream(new File(path), false)) {
            StopWatch stopWatch = StopWatch.createStarted();
            FileChannel channel = fileOutputStream.getChannel();
            // Writes a sequence of bytes to this channel from the given buffer.
            channel.write(byteBuffer);
            // close the channel
            channel.close();
        } catch (IOException e) {
            log.info("写入文件异常",e);
        }

    }

    /**
     * 合并两个单声道wave文件到双声道,以尺寸小的为最后合并的大小
     * wave文件结构参看https://zhumulangma.blog.csdn.net/article/details/105792456?spm=1001.2101.3001.6650.14&utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7ECTRLIST%7ERate-14-105792456-blog-106058199.pc_relevant_recovery_v2&depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7ECTRLIST%7ERate-14-105792456-blog-106058199.pc_relevant_recovery_v2&utm_relevant_index=19
     * @param left
     * @param right
     * @param path
     */
    public static void mergeAndConvertMp3(final byte[] left, final byte[] right, final String path) {
        //左侧文件pcm数据size
        int leftSize = fileSize(left);
        //右侧文件pcm数据size
        int rightSize = fileSize(right);
        //按时长短的合并
        int minSize = Math.min(leftSize, rightSize);

        int mergeSize = mergeSizeField(minSize, minSize);
        int mergeDataSize = mergeDataSize(minSize, minSize);

        int bufferSize = headSize() + 8 + minSize*2;
        ByteBuffer byteBuffer = ByteBuffer.allocate(bufferSize);
        byteBuffer.put(mergeHead(left, mergeSize));
        byteBuffer.put(dataChunkHead(mergeDataSize));
        for (int i = headSize() + 8; i < minSize + 8; i += 2) {
            byteBuffer.put(read(left, i));
            byteBuffer.put(read(right, i));
        }

        // Flips this buffer.  The limit is set to the current position and then
        // the position is set to zero.  If the mark is defined then it is discarded.
        //直接调用再java8会报错 java.lang.NoSuchMethodError: No virtual method flip()Ljava.nio.ByteBuffer in class java.nio.ByteBuffe
        ((Buffer)byteBuffer).flip();
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(byteBuffer.array())) {
            final byte[] bytes = ConvertUtils.encodeToMp3(inputStream);
            FileUtils.writeByteArrayToFile(new File(path),bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static byte[] read(final byte[] content, int offset) {
        if (content.length > offset) {
            return copyOfRange(content, offset, offset + 2);
        } else {
            return "\0\0".getBytes(ISO_8859_1);
        }
    }

    private static int mergeSizeField(int left, int right) {
        int max = Math.max(left - 8, right - 8);
        return max * 2;
    }

    private static int mergeDataSize(int left, int right) {
        int max = Math.max(left - headSize() - 8, right - headSize() - 8);
        return max * 2;
    }

    private static byte[] mergeHead(final byte[] left, final int mergeSize) {
        AudioFormat format = fileFormat(left);
        ByteBuffer size = ByteBuffer.allocate(4).order(LITTLE_ENDIAN).putInt(mergeSize);

        ByteBuffer channels = ByteBuffer.allocate(2).order(LITTLE_ENDIAN).putShort((short) 2);
        ByteBuffer sampleRate = ByteBuffer.allocate(4).order(LITTLE_ENDIAN)
                .putInt((int) format.getSampleRate());
        ByteBuffer byteRate = ByteBuffer.allocate(4).order(LITTLE_ENDIAN)
                .putInt((int) format.getSampleRate() * 2 * format.getSampleSizeInBits() / 8);
        ByteBuffer blockAlign = ByteBuffer.allocate(2).order(LITTLE_ENDIAN)
                .putShort((short) (2 * format.getSampleSizeInBits() / 8));
        ByteBuffer bitsPerSample = ByteBuffer.allocate(2).order(LITTLE_ENDIAN)
                .putShort((short) format.getSampleSizeInBits());

        ByteBuffer head = ByteBuffer.allocate(headSize());
        head.put(left, 0, 4);

        head.put(size.array());
        head.put(left, 8, 14);

        head.put(channels.array());
        head.put(sampleRate.array());
        head.put(byteRate.array());
        head.put(blockAlign.array());
        head.put(bitsPerSample.array());
        return head.array();
    }

    private static byte[] dataChunkHead(final int length) {
        ByteBuffer head = ByteBuffer.allocate(8);
        head.put("data".getBytes(ISO_8859_1));
        ByteBuffer size = ByteBuffer.allocate(4).order(LITTLE_ENDIAN).putInt(length);
        head.put(size.array());
        return head.array();
    }

    public static void main(String[] args) {
        try {
            //log.info("读取文件");
            //StopWatch watch1 = StopWatch.createStarted();
            //byte[] left = Files.readAllBytes(Paths.get("D:\\ring.wav"));
            //byte[] right = Files.readAllBytes(Paths.get("D:\\background.wav"));
            //log.info("读取文件用时:{}毫秒",watch1.getTime(TimeUnit.MILLISECONDS));
            //watch1.stop();
            //log.info("合并文件");
            //StopWatch watch2 = StopWatch.createStarted();
            //mergeStereo(left, right, "D:\\merge.wav");
            //log.info("合并文件用时:{}毫秒",watch2.getTime(TimeUnit.MILLISECONDS));
            //watch2.stop();


            //final byte[] wavData = Files.readAllBytes(Paths.get("D:\\merge.wav"));
            //log.info("wav转mp3");
            //StopWatch watch3 = StopWatch.createStarted();
            //final byte[] bytes = ConvertUtils.encodeToMp3(wavData);
            //log.info("wav转mp3用时:{}毫秒",watch3.getTime(TimeUnit.MILLISECONDS));
            //FileUtils.writeByteArrayToFile(new File("D:\\merge.mp3"),bytes);
            //watch3.stop();

            log.info("读取文件");
            StopWatch watch1 = StopWatch.createStarted();
            byte[] left = Files.readAllBytes(Paths.get("D:\\ring.wav"));
            byte[] right = Files.readAllBytes(Paths.get("D:\\background.wav"));
            log.info("读取文件用时:{}毫秒",watch1.getTime(TimeUnit.MILLISECONDS));
            watch1.stop();
            log.info("合并文件");
            StopWatch watch2 = StopWatch.createStarted();
            mergeAndConvertMp3(left, right, "D:\\merge.mp3");
            log.info("合并文件用时:{}毫秒",watch2.getTime(TimeUnit.MILLISECONDS));
            watch2.stop();
            
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
