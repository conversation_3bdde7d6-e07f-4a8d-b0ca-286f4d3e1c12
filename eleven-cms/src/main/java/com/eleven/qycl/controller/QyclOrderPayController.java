package com.eleven.qycl.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.qycl.entity.QyclOrderPay;
import com.eleven.qycl.service.IQyclOrderPayService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: qycl_order_pay
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Api(tags="qycl_order_pay")
@RestController
@RequestMapping("/qycl/qyclOrderPay")
@Slf4j
public class QyclOrderPayController extends JeecgController<QyclOrderPay, IQyclOrderPayService> {
	@Autowired
	private IQyclOrderPayService qyclOrderPayService;

	/**
	 * 分页列表查询
	 *
	 * @param qyclOrderPay
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay-分页列表查询")
	@ApiOperation(value="qycl_order_pay-分页列表查询", notes="qycl_order_pay-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(QyclOrderPay qyclOrderPay,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<QyclOrderPay> queryWrapper = QueryGenerator.initQueryWrapper(qyclOrderPay, req.getParameterMap());
		Page<QyclOrderPay> page = new Page<QyclOrderPay>(pageNo, pageSize);
		IPage<QyclOrderPay> pageList = qyclOrderPayService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param qyclOrderPay
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay-添加")
	@ApiOperation(value="qycl_order_pay-添加", notes="qycl_order_pay-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody QyclOrderPay qyclOrderPay) {
		qyclOrderPayService.save(qyclOrderPay);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param qyclOrderPay
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay-编辑")
	@ApiOperation(value="qycl_order_pay-编辑", notes="qycl_order_pay-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody QyclOrderPay qyclOrderPay) {
		qyclOrderPayService.updateById(qyclOrderPay);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay-通过id删除")
	@ApiOperation(value="qycl_order_pay-通过id删除", notes="qycl_order_pay-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		qyclOrderPayService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay-批量删除")
	@ApiOperation(value="qycl_order_pay-批量删除", notes="qycl_order_pay-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.qyclOrderPayService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay-通过id查询")
	@ApiOperation(value="qycl_order_pay-通过id查询", notes="qycl_order_pay-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		QyclOrderPay qyclOrderPay = qyclOrderPayService.getById(id);
		if(qyclOrderPay==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(qyclOrderPay);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param qyclOrderPay
    */
    @RequestMapping(value = "/exportXls")
	@RequiresPermissions("qyclOrderPay:import")
    public ModelAndView exportXls(HttpServletRequest request, QyclOrderPay qyclOrderPay) {
        return super.exportXls(request, qyclOrderPay, QyclOrderPay.class, "qycl_order_pay");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QyclOrderPay.class);
    }
	 /**
	  * 微信退款
	  * @param outTradeNo 订单号
	  * @param refund
	  * @return
	  */
	 @ApiOperation(value = "微信退款", notes = "微信退款")
	 @PostMapping(value = "/mall/wechat/refund")
	 @ResponseBody
	 public Result wechatRefund(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo,@RequestParam(value = "refund", required = false, defaultValue ="")String refund){
		 if(StringUtils.isEmpty(outTradeNo)){
			 return Result.error("订单号不能为空");
		 }
		 if(StringUtils.isEmpty(refund)){
			 return Result.error("退款金额不能为空");
		 }
		 return qyclOrderPayService.wechatRefund(outTradeNo,refund);
	 }
}
