package com.eleven.qycl.controller;

import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;
import java.net.URLDecoder;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.toolkit.ArrayUtils;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.qycl.config.TtsProperties;
import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.entity.QyclCompanyMember;
import com.eleven.qycl.entity.QyclRing;
import com.eleven.qycl.mapper.QyclCompanyMapper;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.service.IQyclCompanyMemberService;
import com.eleven.qycl.service.IQyclRingService;
import com.eleven.qycl.util.QyclConstant;
import com.google.common.base.Strings;
import com.jayway.jsonpath.JsonPath;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.qycl.entity.QyclCompany;
import com.eleven.qycl.service.IQyclCompanyService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: qycl_company
 * @Author: jeecg-boot
 * @Date: 2022-11-09
 * @Version: V1.0
 */
@Api(tags = "qycl_company")
@RestController
@RequestMapping("/qycl/qyclCompany")
@Slf4j
public class QyclCompanyController extends JeecgController<QyclCompany, IQyclCompanyService> {
    @Autowired
    private IQyclCompanyService qyclCompanyService;
    @Autowired
    private IQyclCompanyMemberService qyclCompanyMemberService;
    @Autowired
    TtsProperties ttsProperties;
    @Autowired
    IQyclRingService qyclRingService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;
    @Autowired
    MobileRegionService mobileRegionService;

    /**
     * 分页列表查询
     *
     * @param qyclCompany
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "qycl_company-分页列表查询")
    @ApiOperation(value = "qycl_company-分页列表查询", notes = "qycl_company-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(QyclCompany qyclCompany,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        Page<QyclCompany> page = new Page<>(pageNo, pageSize);
        IPage<QyclCompany> pageList = qyclCompanyService.findByPage(page, qyclCompany);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param qyclCompany
     * @return
     */
    //@AutoLog(value = "qycl_company-添加")
    @ApiOperation(value = "qycl_company-添加", notes = "qycl_company-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody QyclCompany qyclCompany) {
        qyclCompanyService.save(qyclCompany);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param qyclCompany
     * @return
     */
    //@AutoLog(value = "qycl_company-编辑")
    @ApiOperation(value = "qycl_company-编辑", notes = "qycl_company-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody QyclCompany qyclCompany) {
        qyclCompanyService.updateById(qyclCompany);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "qycl_company-通过id删除")
    @ApiOperation(value = "qycl_company-通过id删除", notes = "qycl_company-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        qyclCompanyService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "qycl_company-批量删除")
    @ApiOperation(value = "qycl_company-批量删除", notes = "qycl_company-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.qyclCompanyService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "qycl_company-通过id查询")
    @ApiOperation(value = "qycl_company-通过id查询", notes = "qycl_company-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        QyclCompany qyclCompany = qyclCompanyService.getById(id);
        if (qyclCompany == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(qyclCompany);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param qyclCompany
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("qyclCompany:import")
    public ModelAndView exportXls(HttpServletRequest request, QyclCompany qyclCompany) {
        System.out.println("开始组装查询条件");
//      Step.1 组装查询条件
        Integer handleStatus = qyclCompany.getHandleStatus();
        Double totalFee = qyclCompany.getTotalFee();
        String orderId = qyclCompany.getOrderId();
        String mobile = qyclCompany.getMobile();
        qyclCompany.setHandleStatus(null);
        qyclCompany.setTotalFee(null);
        qyclCompany.setOrderId(null);
        qyclCompany.setMobile(null);
        QueryWrapper<QyclCompany> queryWrapper = QueryGenerator.initQueryWrapper(qyclCompany, request.getParameterMap());
        queryWrapper.select("*,(select qycl_order_pay.total_fee from qycl_order_pay where qycl_company.open_id = qycl_order_pay.open_id and pay_status = 1 order by create_time desc limit 1) total_fee");
        queryWrapper.exists("select 1 from qycl_order_pay where qycl_company.open_id = qycl_order_pay.open_id and pay_status = 1 ");
//        if (QyclConstant.PAYMENT_STATUS_SUCCESS.equals(handleStatus)) { //已处理
//            queryWrapper.notExists("select 1 from qycl_company_member where qycl_company.open_id = qycl_company_member.open_id and qycl_fun <> 1");
//            queryWrapper.exists("select 1 from qycl_ring where qycl_company.open_id = qycl_ring.open_id and ring_status is not null");
//        } else if (QyclConstant.PAYMENT_STATUS_FAIL.equals(handleStatus)) { //未处理
//            queryWrapper.and(wrapper -> wrapper.exists("select 1 from qycl_company_member where qycl_company.open_id = qycl_company_member.open_id and qycl_fun <> 1")
//                    .or().notExists("select 1 from qycl_ring where qycl_company.open_id = qycl_ring.open_id and ring_status is not null"));
//        }
        if(totalFee!=null){
            queryWrapper.exists("  select 1 from qycl_order_pay where qycl_company.open_id = qycl_order_pay.open_id and pay_status = 1 and qycl_order_pay.total_fee=" + totalFee );
        }
        if(StringUtils.isNotBlank(orderId)){
            queryWrapper.exists("  select 1 from qycl_order_pay where qycl_company.open_id = qycl_order_pay.open_id and pay_status = 1 and qycl_order_pay.id='" + orderId+"'" );
        }
        if(StringUtils.isNotBlank(mobile)){
            queryWrapper.exists("  select 1 from qycl_company_member where qycl_company.open_id = qycl_company_member.open_id and  qycl_company_member.mobile='" + mobile+"'" );
        }
        List<QyclCompany> pageList = qyclCompanyService.list(queryWrapper);
        pageList.forEach(record -> {
            List<QyclCompanyMember> members = qyclCompanyMemberService.getListByOpenId(record.getOpenId());
            StringBuilder membersTxt = new StringBuilder();
            members.forEach(member -> {
                String settingTxt = "";
                if ("1".equals(member.getQyclFun())) {
                    settingTxt = "已开通";
                } else if ("0".equals(member.getQyclFun())) {
                    settingTxt = "未开通";
                } else if ("2".equals(member.getQyclFun())) {
                    settingTxt = "已退订";
                }
                membersTxt.append(member.getMobile() + "(" + settingTxt + ")\r\n");
            });
            record.setMemberTxt(membersTxt.toString());
            record.setMemberCount(members.size());
        });
        LoginUser sysUser = HttpUtil.getCurrUser();
        //System.out.println("开始执行查询");
        // Step.2 获取导出数据
        //System.out.println("查询完成,导出数据条数=" + pageList.size());
        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "企业列表"); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.CLASS, QyclCompany.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("企业列表报表", "导出人:" + sysUser.getRealname(), "企业列表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        //System.out.println("已完成报表设置");
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QyclCompany.class);
    }

    /**
     * 批量添加成员
     *
     * @param request
     * @param mobiles
     * @return
     */
    @RequestMapping(value = "/batchAddMember", method = RequestMethod.POST)
    public Result<?> batchAddMember(HttpServletRequest request, @RequestParam String mobiles) {
        if (StringUtils.isEmpty(mobiles)) {
            return Result.error("手机号码不能为空");
        }
        String[] mobileArray = mobiles.split("\n");
        for (String mobile : mobileArray) {
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return Result.error("请输入正确格式手机号");
            }
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (!mobileRegionResult.isIspYidong()) {
                return Result.error("手机号【" + mobile + "】不是移动用户");
            }
        }
        StringBuilder info = new StringBuilder();
        for (String mobile : mobileArray) {
            List<QyclCompanyMember> members = qyclCompanyMemberService.lambdaQuery().eq(QyclCompanyMember::getMobile, mobile).orderByDesc(QyclCompanyMember::getCreateTime).list();
            if (members.size() > 0) {
                QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(members.get(0).getOpenId());
                if (qyclCompany!=null && StringUtils.isNotBlank(qyclCompany.getDepartmentId())) {
                    EntVrbtResult entVrbtResult = enterpriseVrbtService.addContentMembersByChannel(qyclCompany.getDepartmentId(), qyclCompany.getCompanyOwner(),qyclCompany.getChannel(),mobile);
                    if (entVrbtResult.getData() != null) {
                        final List<String> errorMsgList = JsonPath.read(entVrbtResult.getData().toString(), "$.list[?(@.billNum =='" + mobile + "')].errorMsg");
                        if (errorMsgList.size() > 0) {
                            info.append("手机号:" + mobile + "," + errorMsgList.get(0) + "\n");
                        }
                    }else{
                        info.append("手机号:" + mobile + "," + entVrbtResult.getInfo() + "\n");
                    }
                } else {
                    info.append("手机号:" + mobile + ",未查询到部门\n");
                }
            } else {
                info.append("手机号:" + mobile + ",未查询到相关信息\n");
            }
        }
        return Result.ok(info.toString());
    }


    /**
     * 打包下载铃音
     *
     * @param openId
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/downLoadZipRing/{openId}")
    public void downLoadRing(@PathVariable String openId, HttpServletResponse response) throws IOException {
        String zipName = "";
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            List<QyclRing> rings = qyclRingService.getListByOpenIdAndPaySuccess(openId);
            for (int i = 0; i < rings.size(); i++) {
                QyclRing qyclRing = rings.get(i);
                zipName = qyclRing.getCompanyTitle() + ".zip";
                zipOutputStream.putNextEntry(new ZipEntry((i + 1) + ".mp3"));
                BufferedInputStream bis = new BufferedInputStream(new FileInputStream(new File(ttsProperties.getAudioFileBaseDir() + qyclRing.getFilePath())));
                IOUtils.copy(bis, zipOutputStream);
            }
            response.setHeader("Content-Disposition", "attachment;fileName=" + zipName);
            response.setContentType("application/octet-stream");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 打包下载视频铃音
     *
     * @param openId
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/downLoadZipVideoRing/{openId}")
    public void downLoadVideoRing(@PathVariable String openId, HttpServletResponse response) throws IOException {
        String zipName = "";
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            List<QyclRing> rings = qyclRingService.getListByOpenIdAndPaySuccess(openId);
            for (int i = 0; i < rings.size(); i++) {
                QyclRing qyclRing = rings.get(i);
                zipName = qyclRing.getCompanyTitle() + "video.zip";
                zipOutputStream.putNextEntry(new ZipEntry((i + 1) + ".mp4"));
                BufferedInputStream bis = new BufferedInputStream(new FileInputStream(new File(ttsProperties.getAudioFileBaseDir() + qyclRing.getVideoPath())));
                IOUtils.copy(bis, zipOutputStream);
            }
            response.setHeader("Content-Disposition", "attachment;fileName=" + zipName);
            response.setContentType("application/octet-stream");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * @param cacheType 1会员 2铃音
     * @param openId
     */
    @RequestMapping(value = "/deleteCacheStatus/{cacheType}/{openId}")
    public void deleteCacheStatus(@PathVariable String cacheType, @PathVariable String openId) {
    }



    @RequestMapping(value = "/analysis")
    public Result analysis(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate, @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,Double totalFee) {
        List<Map<String, Object>> data = qyclCompanyService.analysis(startDate, endDate, totalFee);
        IPage pageList = new Page<>();
        pageList.setRecords(data);
        return Result.ok(pageList);
    }

}
