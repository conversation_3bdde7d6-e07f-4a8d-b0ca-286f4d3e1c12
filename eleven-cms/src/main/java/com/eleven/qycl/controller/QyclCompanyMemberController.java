package com.eleven.qycl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.queue.QyclDelayedMessage;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.qycl.entity.*;
import com.eleven.qycl.service.*;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.jayway.jsonpath.JsonPath;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_EXTRA_3_DAY;

/**
 * @Description: qycl_company_member
 * @Author: jeecg-boot
 * @Date: 2022-11-09
 * @Version: V1.0
 */
@Api(tags = "qycl_company_member")
@RestController
@RequestMapping("/qycl/qyclCompanyMember")
@Slf4j
public class QyclCompanyMemberController extends JeecgController<QyclCompanyMember, IQyclCompanyMemberService> {
    @Autowired
    private IQyclCompanyMemberService qyclCompanyMemberService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private IQyclCompanyService qyclCompanyService;
    @Autowired
    private MobileRegionService mobileRegionService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;
    @Autowired
    IQyclActionLogService qyclActionLogService;
    @Autowired
    IQyclQywxService qyclQywxService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    /**
     * 分页列表查询
     *
     * @param qyclCompanyMember
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @SneakyThrows
    //@AutoLog(value = "qycl_company_member-分页列表查询")
    @ApiOperation(value = "qycl_company_member-分页列表查询", notes = "qycl_company_member-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(QyclCompanyMember qyclCompanyMember,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        String departmentId = req.getParameter("departmentId");
        String openId = qyclCompanyMember.getOpenId();
        if (StringUtils.isBlank(openId)) {
            return Result.ok();
        }
        QueryWrapper<QyclCompanyMember> queryWrapper = QueryGenerator.initQueryWrapper(qyclCompanyMember, req.getParameterMap());
        queryWrapper.lambda().orderByDesc(QyclCompanyMember::getCreateTime);
        Page<QyclCompanyMember> page = new Page<>(pageNo, pageSize);
        IPage<QyclCompanyMember> pageList = qyclCompanyMemberService.page(page, queryWrapper);
        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getDepartmentId, departmentId).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        EntVrbtResult entVrbtResult = null;
        if (StringUtils.isNotEmpty(departmentId)) {
            String channel=qyclCompany != null ? qyclCompany.getChannel() : "";
            entVrbtResult = enterpriseVrbtService.queryContentMembersByChannel(departmentId, null, qyclCompany.getCompanyOwner(),channel);
        }
        for (int i = 0; i < pageList.getRecords().size(); i++) {
            QyclCompanyMember record = pageList.getRecords().get(i);
            if (entVrbtResult != null && entVrbtResult.getData() != null) {
                final List<String> userStatusList = JsonPath.read(entVrbtResult.getData().toString(), "$[?(@.billNum == '" + record.getMobile() + "')].userStatus");
                record.setUserStatus(!userStatusList.isEmpty() ? userStatusList.get(0) : "");
            }
        }
        return Result.ok(pageList);
    }

    /**
     * 分页列表查询
     *
     * @param qyclCompanyMember
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @SneakyThrows
    //@AutoLog(value = "qycl_company_member-分页列表查询")
    @ApiOperation(value = "qycl_company_member-分页列表查询", notes = "qycl_company_member-分页列表查询")
    @GetMapping(value = "/qywx/list")
    public Result<?> queryQywxPageList(QyclCompanyMember qyclCompanyMember,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                       HttpServletRequest req) {
        String departmentId = req.getParameter("departmentId");
        String openId = qyclCompanyMember.getOpenId();
        if (StringUtils.isBlank(openId)) {
            return Result.ok();
        }
        QueryWrapper<QyclCompanyMember> queryWrapper = QueryGenerator.initQueryWrapper(qyclCompanyMember, req.getParameterMap());
        queryWrapper.lambda().orderByDesc(QyclCompanyMember::getCreateTime);
        Page<QyclCompanyMember> page = new Page<>(pageNo, pageSize);
        IPage<QyclCompanyMember> pageList = qyclCompanyMemberService.page(page, queryWrapper);
        EntVrbtResult entVrbtResult = null;
        if (StringUtils.isNotEmpty(departmentId)) {
            entVrbtResult = enterpriseVrbtService.queryContentMembersByChannel(departmentId, null, QyclConstant.QYCL_COMPANY_OWNER_YRJY,null);
        }
        for (int i = 0; i < pageList.getRecords().size(); i++) {
            QyclCompanyMember record = pageList.getRecords().get(i);
            if (entVrbtResult != null && entVrbtResult.getData() != null) {
                final List<String> userStatusList = JsonPath.read(entVrbtResult.getData().toString(), "$[?(@.billNum == '" + record.getMobile() + "')].userStatus");
                record.setUserStatus(!userStatusList.isEmpty() ? userStatusList.get(0) : "");
            }
        }
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param qyclCompanyMember
     * @return
     */
    //@AutoLog(value = "qycl_company_member-添加")
    @ApiOperation(value = "qycl_company_member-添加", notes = "qycl_company_member-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody QyclCompanyMember qyclCompanyMember) {
        qyclCompanyMemberService.save(qyclCompanyMember);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param qyclCompanyMember
     * @return
     */
    //@AutoLog(value = "qycl_company_member-编辑")
    @ApiOperation(value = "qycl_company_member-编辑", notes = "qycl_company_member-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody QyclCompanyMember qyclCompanyMember) {
        qyclCompanyMemberService.updateById(qyclCompanyMember);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "qycl_company_member-通过id删除")
    @ApiOperation(value = "qycl_company_member-通过id删除", notes = "qycl_company_member-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        qyclCompanyMemberService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "qycl_company_member-批量删除")
    @ApiOperation(value = "qycl_company_member-批量删除", notes = "qycl_company_member-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.qyclCompanyMemberService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "qycl_company_member-通过id查询")
    @ApiOperation(value = "qycl_company_member-通过id查询", notes = "qycl_company_member-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        QyclCompanyMember qyclCompanyMember = qyclCompanyMemberService.getById(id);
        if (qyclCompanyMember == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(qyclCompanyMember);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param qyclCompanyMember
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QyclCompanyMember qyclCompanyMember) {
        return super.exportXls(request, qyclCompanyMember, QyclCompanyMember.class, "qycl_company_member");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QyclCompanyMember.class);
    }

    @PostMapping(value = "/addMember/{openId}")
    public Result<?> addMember(@PathVariable String openId, @RequestParam String mobiles) {
        if (StringUtils.isEmpty(mobiles)) {
            return Result.error("员工手机号码错误");
        }
        QyclCompany qyclCompany = qyclCompanyService.findPayCompanyByOpenId(openId);
        if (qyclCompany == null) {
            return Result.error("无法添加员工");
        }
        List<String> memberMobiles = qyclCompanyMemberService.getListByOpenId(openId).stream().map(QyclCompanyMember::getMobile).collect(Collectors.toList());
        String[] mobileArray = mobiles.split("\n");
        List<QyclCompanyMember> members = new ArrayList<>();
        for (String mobile : mobileArray) {
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return Result.error("请输入正确格式手机号");
            }
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (!mobileRegionResult.isIspYidong()) {
                return Result.error("手机号【" + mobile + "】不是移动用户");
            }
            if (memberMobiles.contains(mobile)) {
                return Result.error("手机号【" + mobile + "】已存在请勿重复添加");
            }
            QyclCompanyMember qyclCompanyMember = new QyclCompanyMember();
            qyclCompanyMember.setOpenId(openId);
            qyclCompanyMember.setMobile(mobile);
            qyclCompanyMember.setCompanyTitle(qyclCompany.getTitle());
            members.add(qyclCompanyMember);
        }
        try {
            if (StringUtils.isNotEmpty(qyclCompany.getDepartmentId())) {
                //将成员加入部门
                EntVrbtResult entVrbtResult = enterpriseVrbtService.addContentMembersByChannel(qyclCompany.getDepartmentId(),qyclCompany.getCompanyOwner(),qyclCompany.getChannel(), mobileArray);
                for (int i = 0; i < members.size(); i++) {
                    QyclCompanyMember qyclCompanyMember = members.get(i);
                    //将成员加入延时队列
                    redisDelayedQueueManager.addQycl(QyclDelayedMessage.builder().mobile(qyclCompanyMember.getMobile()).openId(openId).msg("企业彩铃未开通包月用户").tag(MESSAG_EXTRA_3_DAY).build(), 24, TimeUnit.HOURS);
                    final List<String> errorMsgList = JsonPath.read(entVrbtResult.getData().toString(), "$.list[?(@.billNum =='" + qyclCompanyMember.getMobile() + "')].errorMsg");
                    if (!errorMsgList.isEmpty()) {
                        qyclCompanyMember.setRemark(errorMsgList.get(0));
                    }
                }
            }
            qyclCompanyMemberService.saveBatch(members);
            qyclCompany.setOperationTime(new Date());
            qyclCompanyService.updateById(qyclCompany);
        } catch (Exception e) {
            return Result.error("请勿添加相同的员工手机号");
        }
        return Result.ok("添加员工成功");
    }


    @PostMapping(value = "/addQywxMember/{qywxId}")
    public Result<?> addQywxMember(@PathVariable String qywxId, @RequestParam String mobiles) {
        if (StringUtils.isEmpty(mobiles)) {
            return Result.error("员工手机号码错误");
        }
        QyclQywx qyclQywx = qyclQywxService.getById(qywxId);
        List<String> memberMobiles = qyclCompanyMemberService.getListByOpenId(qywxId).stream().map(QyclCompanyMember::getMobile).collect(Collectors.toList());
        String[] mobileArray = mobiles.split("\n");
        List<QyclCompanyMember> members = new ArrayList<>();
        for (String mobile : mobileArray) {
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                return Result.error("请输入正确格式手机号");
            }
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (!mobileRegionResult.isIspYidong()) {
                return Result.error("手机号【" + mobile + "】不是移动用户");
            }
            if (memberMobiles.contains(mobile)) {
                return Result.error("手机号【" + mobile + "】已存在请勿重复添加");
            }
            QyclCompanyMember qyclCompanyMember = new QyclCompanyMember();
            qyclCompanyMember.setOpenId(qywxId);
            qyclCompanyMember.setMobile(mobile);
            qyclCompanyMember.setCompanyTitle(qyclQywx.getTitle());
            members.add(qyclCompanyMember);
        }
        try {
            //将成员加入部门
            EntVrbtResult entVrbtResult = enterpriseVrbtService.addContentMembersByChannel(qyclQywx.getDepartmentId(), QyclConstant.QYCL_COMPANY_OWNER_YRJY,qyclQywx.getChannel(), mobileArray);
            members.forEach(qyclCompanyMember -> {
                if (entVrbtResult.isOK()) {
                    final List<String> errorMsgList = JsonPath.read(entVrbtResult.getData().toString(), "$.list[?(@.billNum =='" + qyclCompanyMember.getMobile() + "')].errorMsg");
                    if (!errorMsgList.isEmpty()) {
                        qyclCompanyMember.setRemark(errorMsgList.get(0));
                    }
                } else {
                    qyclCompanyMember.setRemark(entVrbtResult.getInfo().toString());
                }
            });
            qyclCompanyMemberService.saveBatch(members);
        } catch (Exception e) {
            return Result.error("请勿添加相同的员工手机号");
        }
        return Result.ok("添加员工成功");
    }


    @PostMapping(value = "/operatePlatform/{id}")
    public Result<?> operatePlatform(@PathVariable String id) {
        QyclCompanyMember qyclCompanyMember = qyclCompanyMemberService.getById(id);
        QyclCompany qyclCompany = qyclCompanyService.findPayCompanyByOpenId(qyclCompanyMember.getOpenId());
        QyclActionLog qyclActionLog = new QyclActionLog();
        LoginUser loginUser = HttpUtil.getCurrUser();
        String username = loginUser.getUsername();
        qyclActionLog.setCreateBy(username);
        qyclActionLog.setActionName("platform_member_retry_add");
        qyclActionLog.setCreateTime(new Date());
        qyclActionLog.setDepartmentId(qyclCompany.getDepartmentId());
        qyclActionLog.setCompanyName(qyclCompany.getTitle());
        qyclActionLog.setActionTarget(qyclCompanyMember.getMobile());
        try {
            if (StringUtils.isNotEmpty(qyclCompany.getDepartmentId())) {
                //将成员删除部门
                EntVrbtResult entVrbtResult = enterpriseVrbtService.deleteContentMembersByChannel(qyclCompany.getDepartmentId(),qyclCompany.getCompanyOwner(),qyclCompany.getChannel(), qyclCompanyMember.getMobile());
                qyclActionLog.setExtra(mapper.writeValueAsString(entVrbtResult));
                //将成员加入部门
                entVrbtResult = enterpriseVrbtService.addContentMembersByChannel(qyclCompany.getDepartmentId(), qyclCompany.getCompanyOwner(),qyclCompany.getChannel(),qyclCompanyMember.getMobile());
                qyclActionLog.setActionResult(mapper.writeValueAsString(entVrbtResult));
            } else {
                qyclActionLog.setExtra("部门id不存在");
                qyclActionLog.setActionResult("部门id不存在");
                return Result.error("平台短信发送错误,部门id不存在");
            }
            return Result.ok("平台短信操作完成");
        } catch (Exception e) {
            qyclActionLog.setExtra("系统错误");
            qyclActionLog.setActionResult("系统错误");
            return Result.error("平台短信发送错误");
        } finally {
            qyclActionLogService.save(qyclActionLog);
        }
    }

}
