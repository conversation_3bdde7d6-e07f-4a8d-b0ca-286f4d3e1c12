package com.eleven.qycl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.SubscribeResultNotifyService;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.AlarmService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.qycl.config.EnterpriseVrbtProperties;
import com.eleven.qycl.config.TtsProperties;
import com.eleven.qycl.entity.*;
import com.eleven.qycl.service.*;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @author: cai lei
 * @create: 2022-11-09 15:56
 */
@Api(tags = "qycl_api")
@RestController
@RequestMapping("/qycl/api")
@Slf4j
public class QyclApiController {
    private static final Interner<String> interner = Interners.newWeakInterner();
    @Autowired
    IQyclCompanyMemberService qyclCompanyMemberService;

    @Autowired
    IDataNotifyLogService dataNotifyLogService;
    @Autowired
    IQyclCompanyService qyclCompanyService;
    @Autowired
    IQyclRingService qyclRingService;
    @Autowired
    IQyclIndustryTemplateService qyclIndustryTemplateService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IDatangSmsService datangSmsService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    EnterpriseVrbtProperties enterpriseVrbtProperties;
    @Autowired
    private EnterpriseVrbtService enterpriseVrbtService;
    @Autowired
    TtsProperties ttsProperties;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    TtsService ttsService;
    @Autowired
    ISmsValidateService smsValidateService;
    @Autowired
    IQyclOrderPayService qyclOrderPayService;
    @Autowired
    IChannelService channelService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    AliMediaService aliMediaService;

    @Autowired
    ISysBaseAPI sysBaseAPI;
    @Autowired
    IQyclFeedbackService qyclFeedbackService;
    @Autowired
    MiguApiService miguApiService;
    @Autowired
    IMusicService musicService;
    @Autowired
    IQyclRingColumnService qyclRingColumnService;
    @Autowired
    IQyclRingVideoService qyclRingVideoService;
    @Autowired
    IQyclRingTemplateService qyclRingTemplateService;
    @Autowired
    private IQyclQaService qyclQaService;
    @Autowired
    SubscribeResultNotifyService subscribeResultNotifyService;
    @Autowired
    AlarmService alarmService;
    @Autowired
    IQyclCustomerServiceRingService qyclCustomerServiceRingService;

    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    @ApiOperation(value = "查询企业包含员工", notes = "查询企业包含员工")
    @RequestMapping(value = "/queryCompanyContainMembers")
    public Result<?> queryCompanyContainMembers(HttpServletRequest req) {
        String openId = getOpenId(req);
        QyclCompany qyclCompany = qyclCompanyService.getContainMemberByOpenId(openId);
        return Result.ok(qyclCompany);
    }

    @ApiOperation(value = "查询铃音列表", notes = "查询铃音列表")
    @RequestMapping(value = "/queryRing")
    public Result<?> queryRing(HttpServletRequest req) {
        String openId = getOpenId(req);
        List<QyclRing> data = qyclRingService.getListByOpenId(openId);
        List<DictModel> dictItemList = sysBaseAPI.queryDictItemsByCode("ring_status");
        data.forEach(qyclRing -> {
            if (QyclConstant.RING_TYPE_DEFAULT.equals(qyclRing.getRingType())) {
                qyclRing.setPlayUrl(QyclConstant.RING_FILE_BASE_URL + qyclRing.getFilePath());
            } else {
                qyclRing.setPlayUrl(qyclRing.getVideoPath());
            }
            String ringStatusText = dictItemList.stream().filter(dictModel -> dictModel.getValue().equals(qyclRing.getRingStatus())).map(dictModel -> dictModel.getText()).collect(Collectors.joining());
            qyclRing.setRingStatus(ringStatusText);
            String ringStatusAudioText = dictItemList.stream().filter(dictModel -> dictModel.getValue().equals(qyclRing.getRingStatusAudio())).map(dictModel -> dictModel.getText()).collect(Collectors.joining());
            qyclRing.setRingStatusAudio(ringStatusAudioText);
        });
        return Result.ok(data);
    }

    @ApiOperation(value = "查询用户使用铃音", notes = "查询用户使用铃音")
    @RequestMapping(value = "/queryUsedRing")
    public Result<?> queryUsedRing(HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(openId);
        if (qyclCompany != null && StringUtils.isNotBlank(qyclCompany.getDepartmentId())) {
            EntVrbtResult entVrbtResult = enterpriseVrbtService.searchEcRingSettingByChannel(qyclCompany.getDepartmentId(), companyOwner,qyclCompany.getChannel());
            String ringId = entVrbtResult.getData().at("/settingsList/0/ringsList/0").asText();
            if (StringUtils.isNotBlank(ringId)) {
                QyclRing qyclRing = qyclRingService.getByRingId(ringId);
                return Result.ok(qyclRing);
            } else {
                return Result.ok();
            }
        } else {
            return Result.error("未查询用户铃音信息");
        }
    }

    @ApiOperation(value = "查询用户闲置铃音", notes = "查询用户闲置铃音")
    @RequestMapping(value = "/queryIdleRing")
    public Result<?> queryIdleRing(HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(openId);
        if (qyclCompany != null && StringUtils.isNotBlank(qyclCompany.getDepartmentId())) {
            List<QyclRing> idleRingList = new ArrayList<>();
            EntVrbtResult entVrbtResult = enterpriseVrbtService.searchEcRingSettingByChannel(qyclCompany.getDepartmentId(), companyOwner,qyclCompany.getChannel());
            String usedRingId = entVrbtResult.getData().at("/settingsList/0/ringsList/0").asText();
            entVrbtResult = enterpriseVrbtService.searchEcRingByChannel(qyclCompany.getDepartmentId(), companyOwner,qyclCompany.getChannel());
            JsonNode ringList = entVrbtResult.getData();
            for (JsonNode ringNode : ringList) {
                String ringId = ringNode.at("/ringId").asText();
                if (!StringUtils.equals(ringId, usedRingId)) {
                    QyclRing qyclRing = qyclRingService.getByRingId(ringId);
                    idleRingList.add(qyclRing);
                }
            }
            return Result.ok(idleRingList);
        } else {
            return Result.error("未查询用户铃音信息");
        }
    }

    @ApiOperation(value = "闲置铃音设置", notes = "闲置铃音设置")
    @RequestMapping(value = "/idleRingSetting")
    public Result<?> idleRingSetting(@RequestParam String ringId, HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        QyclCompany qyclCompany = qyclCompanyService.getQyclCompanyByOpenId(openId);
        if (qyclCompany != null && StringUtils.isNotBlank(qyclCompany.getDepartmentId())) {
            enterpriseVrbtService.setDeptRingsByTimeChannel(qyclCompany.getDepartmentId(), EnterpriseVrbtService.RING_TYPE_VEDIO, companyOwner,qyclCompany.getChannel(), ringId);
            return Result.ok("设置成功");
        } else {
            return Result.error("未查询用户铃音信息");
        }
    }

    @ApiOperation(value = "闲置铃音删除", notes = "闲置铃音删除")
    @RequestMapping(value = "/deleteRing")
    public Result<?> deleteRing(@RequestParam String id, HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        QyclRing qyclRing = qyclRingService.getById(id);
        if (qyclRing != null && openId.equals(qyclRing.getOpenId())) {
            qyclRingService.removeById(qyclRing.getId());
            return Result.ok("删除成功");
        } else {
            return Result.error("未查询用户铃音信息");
        }
    }

    @ApiOperation(value = "修改企业名称", notes = "修改企业名称")
    @RequestMapping(value = "/updateCompanyName/{companyId}")
    public Result<?> updateCompanyName(HttpServletRequest req, @PathVariable String companyId, @RequestParam String companyName) {
        qyclCompanyService.lambdaUpdate().eq(QyclCompany::getId, companyId).set(QyclCompany::getTitle, companyName).update();
        return Result.ok("修改成功");
    }


    @ApiOperation(value = "查询铃声制作状态", notes = "查询铃声制造状态")
    @RequestMapping(value = "/queryRingMakeStatus/{ringId}")
    public Result<?> queryRingMakeStatus(HttpServletRequest req, @PathVariable String ringId) {
        QyclRing qyclRing = qyclRingService.getById(ringId);
        return Result.ok(qyclRing.getRingMakeStatus());
    }

    @ApiOperation(value = "查询铃声制作状态", notes = "查询铃声制造状态")
    @RequestMapping(value = "/queryRingById/{ringId}")
    public Result<?> queryRingById(HttpServletRequest req, @PathVariable String ringId) {
        QyclRing qyclRing = qyclRingService.getById(ringId);
        if (QyclConstant.RING_TYPE_DEFAULT.equals(qyclRing.getRingType())) {
            qyclRing.setPlayUrl(QyclConstant.RING_FILE_BASE_URL + qyclRing.getFilePath());
        }
        return Result.ok(qyclRing);
    }

    @ApiOperation(value = "查询背景音乐列表", notes = "查询背景音乐列表")
    @RequestMapping(value = "/queryBackgroundMusic")
    public Result queryBackgroundMusic() {
        return Result.ok(BackgroundMusic.getListBackgroundMusic());
    }


    @ApiOperation(value = "根据id查询原始铃音", notes = "根据id查询原始铃音")
    @RequestMapping(value = "/queryRing/{ringId}")
    public Result<?> queryRing(HttpServletRequest req, @PathVariable String ringId) {
        String openId = getOpenId(req);
        QyclRing qyclRing = qyclRingService.getOriginalByIdAndOpenId(ringId, openId);
        if (qyclRing == null) {
            return Result.error("未查询到铃音");
        }
        if (QyclConstant.RING_TYPE_DEFAULT.equals(qyclRing.getRingType())) {
            qyclRing.setPlayUrl(QyclConstant.RING_FILE_BASE_URL + qyclRing.getFilePath());
        }
        return Result.ok(qyclRing);
    }

    //实际是生成一条新的铃音
    @ApiOperation(value = "修改原始铃音", notes = "修改原始铃音")
    @RequestMapping(value = "/modifyRing/{ringId}")
    public Result<?> modifyRing(HttpServletRequest req,
                                @PathVariable String ringId,
                                @RequestParam("ringTxt") String ringTxt) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        synchronized (interner.intern(openId)) {
            QyclRing qyclRing = qyclRingService.getOriginalByIdAndOpenId(ringId, openId);
            if (qyclRing == null) {
                return Result.error("未查询到铃音");
            }
            if (!QyclConstant.PAYMENT_STATUS_SUCCESS.equals(qyclRing.getOrderPayStatus())) {
                return Result.error("当前铃音未支付不可修改");
            }
            if (qyclRing.getModifyCount() >= 3) {
                return Result.error("该铃音只能修改3次");
            }
            QyclRing newRing = new QyclRing();
            newRing.setOpenId(openId);
            newRing.setMobile(qyclRing.getMobile());
            newRing.setModifyType(1);
            newRing.setRingTxt(ringTxt);
            newRing.setCompanyTitle(qyclRing.getCompanyTitle());
            newRing.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
            newRing.setAuditStatus(-1);
            newRing.setCompanyOwner(companyOwner);
            qyclRingService.modifyRing(qyclRing, newRing);
            return Result.ok("修改铃音成功");
        }
    }

    //实际是生成一条新的铃音
    @ApiOperation(value = "修改原始铃音(新版本)", notes = "修改原始铃音(新版本)")
    @RequestMapping(value = "/modifyRingNew/{ringId}")
    public Result<?> modifyNewRing(HttpServletRequest req,
                                   @PathVariable String ringId,
                                   @RequestParam(required = false, name = "ringTxt", defaultValue = "") String ringTxt,
                                   @RequestParam(defaultValue = "1") Integer ringType,
                                   @RequestParam(defaultValue = "FEMALE") String voiceGender,
                                   @RequestParam(required = false) String[] imageArray,
                                   @RequestParam(defaultValue = "BGM1") String bgm) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        synchronized (interner.intern(openId)) {
            QyclRing qyclRing = qyclRingService.getOriginalByIdAndOpenId(ringId, openId);
            if (qyclRing == null) {
                return Result.error("未查询到铃音");
            }
            if (!QyclConstant.PAYMENT_STATUS_SUCCESS.equals(qyclRing.getOrderPayStatus())) {
                return Result.error("当前铃音未支付不可修改");
            }
            QyclRing newRing = new QyclRing();
            newRing.setOpenId(openId);
            newRing.setMobile(qyclRing.getMobile());
            newRing.setModifyType(1);
            newRing.setRingTxt(ringTxt);
            newRing.setCompanyTitle(qyclRing.getCompanyTitle());
            newRing.setOrderPayStatus(QyclConstant.PAYMENT_STATUS_SUCCESS);
            newRing.setAuditStatus(-1);
            newRing.setRingType(ringType);
            newRing.setCompanyOwner(companyOwner);
            qyclRingService.modifyNewRing(qyclRing, newRing, ringType, VoiceGender.valueOf(voiceGender), imageArray, BackgroundMusic.valueOf(bgm));
            return Result.ok("修改铃音成功");
        }
    }


    @ApiOperation(value = "添加成员", notes = "添加成员")
    @RequestMapping(value = "/addMember")
    public Result<?> addMember(@RequestParam(name = "mobiles") String mobiles,
                               HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        return qyclCompanyMemberService.addMember(openId, mobiles, companyOwner);
    }

    @ApiOperation(value = "删除成员", notes = "删除成员")
    @RequestMapping(value = "/delMember")
    public Result<?> delMember(@RequestParam(name = "id") String id,
                               HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        QyclCompanyMember qyclCompanyMember = qyclCompanyMemberService.getById(id);
        if (qyclCompanyMember != null && openId.equals(qyclCompanyMember.getOpenId())) {
            qyclRingService.removeById(id);
            return Result.ok("删除成功");
        } else {
            return Result.error("未查询到用户信息");
        }
    }


    public String getOpenId(HttpServletRequest request) {
        String openId = request.getHeader("openId");
        if (StringUtils.isEmpty(openId)) {
            throw new JeecgBootException("缺少用户信息");
        }
        return openId;
    }

    public String getCompanyOwner(HttpServletRequest request) {
        String companyOwner = request.getHeader(QyclConstant.QYCL_COMPANY_OWNER_HEAD);
        if (StringUtils.isEmpty(companyOwner)) {
            companyOwner = QyclConstant.QYCL_COMPANY_OWNER_YRJY;
        }
        return companyOwner;
    }

    @ApiOperation(value = "生成铃音文本", notes = "生成铃音文本")
    @RequestMapping(value = "/createRingTxt")
    public Result<?> createRingTxt(@RequestParam(name = "title") String title,
                                   String companyTitle,
                                   HttpServletRequest req) {
        log.info("title:{},companyTitle:{}", title, companyTitle);
        QyclIndustryTemplate qyclIndustryTemplate = qyclIndustryTemplateService.createRingTxt(companyTitle, title);
        return Result.ok(qyclIndustryTemplate);
    }

    @ApiOperation(value = "查询行业名称", notes = "查询行业名称")
    @RequestMapping(value = "/findIndustryTitles")
    public Result<?> findIndustryTitles(HttpServletRequest req) {
        List<String> list = qyclIndustryTemplateService.findIndustryTitles();
        return Result.ok(list);
    }


    @ApiOperation(value = "生成试听铃音", notes = "生成试听铃音")
    @RequestMapping(value = "/createRing")
    public Result<?> createRing(@RequestParam String mobile,
                                @RequestParam(name = "ringTxt") String ringTxt,
                                String companyTitle,
                                HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        Map<String, String> map = qyclRingService.createRing(ringTxt, companyTitle, openId, mobile, companyOwner);
        return Result.ok(map);
    }


    @ApiOperation(value = "生成试听铃音(新版本)", notes = "生成试听铃音(新版本)")
    @RequestMapping(value = "/createNewRing")
    public Result<?> createNewRing(@RequestParam String mobile,
                                   @RequestParam(name = "ringTxt", defaultValue = "") String ringTxt,
                                   String companyTitle,
                                   @RequestParam(defaultValue = "2") Integer ringType,
                                   @RequestParam(defaultValue = "FEMALE") String voiceGender,
                                   @RequestParam(required = false) String[] imageArray,
                                   @RequestParam(defaultValue = "BGM1") String bgm,
                                   String subChannel,
                                   HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        if (QyclConstant.RING_TYPE_DIY.equals(ringType) && imageArray.length < 2) {
            return Result.error("请上传正确的图片");
        }
        Map<String, String> map = qyclRingService.createNewRing(ringTxt, companyTitle, openId, mobile, ringType, VoiceGender.valueOf(voiceGender), imageArray, BackgroundMusic.valueOf(bgm), subChannel, companyOwner);
        return Result.ok(map);
    }

    @ApiOperation(value = "创建diy铃声(企业彩铃)", notes = "创建diy铃声(企业彩铃)")
    @RequestMapping(value = "/createQyclDiyRing")
    public Result<?> createQyclRing(@RequestParam String mobile,
                                    @RequestParam String ringTxt,
                                    @RequestParam String companyTitle,
                                    @RequestParam(defaultValue = "FEMALE") String voiceGender,
                                    @RequestParam(required = false) String[] imageArray,
                                    String subChannel,
                                    HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
//        if (imageArray.length > 6 || imageArray.length < 2) {
//            return Result.error("请上传正确的图片");
//        }
        String id = qyclRingService.createQyclDiyRing(ringTxt, companyTitle, openId, mobile, VoiceGender.valueOf(voiceGender), imageArray, subChannel, companyOwner);
        Map<String, String> map = new HashMap<>();
        map.put("ringId", id);
        return Result.ok(map);
    }

    @ApiOperation(value = "生成铃音(模板)", notes = "生成铃音(模板)")
    @RequestMapping(value = "/createQyclTemplateRing")
    public Result<?> createQyclTemplateRing(@RequestBody JsonNode jsonNode,
                                            HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        String mobile = jsonNode.at("/mobile").asText();
        String templateId = jsonNode.at("/templateId").asText();
        String subChannel = jsonNode.at("/subChannel").asText();
        String clipsParam = jsonNode.at("/clipsParam").asText();
        log.info("生成模板铃声数据:{},", jsonNode);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        String id = qyclRingService.createQyclTemplateRing(mobile, openId, templateId, clipsParam, subChannel, companyOwner);
        Map<String, String> map = new HashMap<>();
        map.put("ringId", id);
        return Result.ok(map);
    }


    @ApiOperation(value = "生成铃音(模板)", notes = "生成铃音(模板)")
    @RequestMapping(value = "/createTemplateRing")
    public Result<?> createTemplateRing(@RequestBody JsonNode jsonNode,
                                        HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        String mobile = jsonNode.at("/mobile").asText();
        String templateId = jsonNode.at("/templateId").asText();
        String subChannel = jsonNode.at("/subChannel").asText();
        String clipsParam = jsonNode.at("/clipsParam").asText();
        log.info("生成模板铃声数据:{},", jsonNode);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        String id = qyclRingService.createTemplateRing(mobile, openId, templateId, clipsParam, subChannel, companyOwner);
        Map<String, String> map = new HashMap<>();
        map.put("ringId", id);
        return Result.ok(map);
    }


    @ApiOperation(value = "创建视频铃音", notes = "创建视频铃音")
    @RequestMapping(value = "/createVideoRing")
    public Result<?> createVideoRing(@RequestBody JsonNode jsonNode,
                                     HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        String mobile = jsonNode.at("/mobile").asText();
        String subChannel = jsonNode.at("/subChannel").asText();
        String videoPath = jsonNode.at("/videoPath").asText();
        log.info("上传视频铃音:{},", jsonNode);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        String id = qyclRingService.createVideoRing(mobile, openId, videoPath, subChannel, companyOwner);
        Map<String, String> map = new HashMap<>();
        map.put("ringId", id);
        return Result.ok(map);
    }

    @ApiOperation(value = "创建视频铃音", notes = "创建视频铃音")
    @RequestMapping(value = "/createVideoRingApp")
    public Result<?> createVideoRingApp(@RequestBody JsonNode jsonNode,
                                     HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        String mobile = jsonNode.at("/mobile").asText();
        String subChannel = jsonNode.at("/subChannel").asText();
        String videoPath = jsonNode.at("/videoPath").asText();
        log.info("上传视频铃音:{},", jsonNode);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        String id = qyclRingService.createVideoRing(mobile, openId, videoPath, subChannel, companyOwner);
        Map<String, String> map = new HashMap<>();
        map.put("ringId", id);
        return Result.ok(map);
    }

    @ApiOperation(value = "确认购买", notes = "确认购买")
    @RequestMapping(value = "/ringPay")
    public Result<?> ringPay(String title,
                             String companyTitle,
                             String filePath,
                             String ringTxt,
                             @RequestParam String ringId,
                             String source,
                             @RequestParam(defaultValue = "2") String sourceType,
                             HttpServletRequest req) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        if (StringUtils.isEmpty(ringId)) {
            log.error("ringId不能为空");
            return Result.error("铃音生成错误，请稍后再试");
        }
        log.info("企业彩铃用户确认购买,openId:{},ringId:{},sourceType:{}", openId, ringId, sourceType);
        synchronized (interner.intern(openId)) {
            return qyclRingService.ringPay(title, companyTitle, filePath, openId, ringTxt, ringId, source, sourceType, req, companyOwner);
        }
    }

    @ApiOperation(value = "sdk支付成功后调用", notes = "sdk支付成功后调用")
    @RequestMapping(value = "/sdkCallback")
    public Result<?> sdkCallback(@RequestParam(name = "mobile") String mobile,
                                 @RequestParam String openId,
                                 @RequestParam String title,
                                 @RequestParam String ringId,
                                 @RequestParam String resCode,
                                 @RequestParam String resMsg,
                                 @RequestParam(defaultValue = "2") String sourceType,
                                 @RequestParam(defaultValue = "1") Integer ringType,
                                 @RequestParam(defaultValue = "1") String crack,
                                 @RequestParam(required = false) String channel,
                                 String source,
                                 HttpServletRequest req) {
        String companyOwner = getCompanyOwner(req);
        log.info("企业彩铃用户sdk支付回调,companyOwner:{},手机号:{},openId:{},ringId:{},开通结果:{},sourceType:{},source:{},crack:{},channel:{}", companyOwner, mobile, openId, ringId, resCode, sourceType, source, crack, channel);
        synchronized (interner.intern(openId)) {
            try {
                return qyclRingService.sdkCallback(mobile, title, openId, ringId, resCode, resMsg, source, sourceType, ringType, crack, req, companyOwner, channel);
            } catch (Exception e) {
                log.error("企业彩铃用户sdk支付回调错误", e);
                return Result.error("sdk支付回调错误");
            }
        }
    }

    @ApiOperation(value = "用户意见反馈", notes = "用户意见反馈")
    @RequestMapping(value = "/feedback")
    public Result<?> feedback(@RequestParam String mobile,
                              @RequestParam String idea,
                              @RequestParam(defaultValue = "QYCL") String bizType,
                              HttpServletRequest req) {
//        String openId = getOpenId(req);
        QyclFeedback qyclFeedback = new QyclFeedback();
        qyclFeedback.setMobile(mobile);
        qyclFeedback.setIdea(idea);
        qyclFeedback.setBizType(bizType);
        qyclFeedbackService.save(qyclFeedback);
        return Result.ok("意见反馈成功");
    }


    @ApiOperation(value = "企业彩铃公众号发送登录短信", notes = "企业彩铃公众号发送登录短信")
    @RequestMapping(value = "/sendSms")
    public Result<?> sendSms(@RequestParam(name = "mobile") String mobile,
                             HttpServletRequest req) {
        boolean isSend = smsValidateService.createUnify(mobile, BizConstant.BIZ_TYPE_QYCL, null);
        if (!isSend) {
            return Result.error("发送短信验证码失败,请稍后再试");
        }
        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }


    @ApiOperation(value = "企业彩铃公众号登录", notes = "企业彩铃公众号登录")
    @RequestMapping(value = "/login")
    public Result<?> login(@RequestParam(name = "mobile") String mobile,
                           @RequestParam String code,
                           HttpServletRequest req) {

        boolean loginAll = redisUtil.hasKey(CacheConstant.BIZ_QYCL_XCX_LOGIN_ALL);
        //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
        if (!loginAll) {
            try {
                smsValidateService.check(mobile, code);
            } catch (JeecgBootException e) {
                return Result.error(e.getMessage());
            }
            QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile, mobile).isNotNull(QyclCompany::getPayTime).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
            if (qyclCompany == null) {
                return Result.error("未查询到用户信息");
            }
            boolean isMonth = enterpriseVrbtService.verifyMonth(mobile, qyclCompany.getChannel());
            if (!isMonth) {
                return Result.error("未查询到用户包月信息");
            }
            Result<Object> result = Result.ok("登录成功", qyclCompany.getOpenId());
            result.setServiceId(qyclCompany.getCompanyOwner());
            return result;
        } else {
            Result<Object> result = Result.ok("登录成功", "test");
            result.setServiceId("yrjy");
            return result;
        }
    }


    @ApiOperation(value = "查询企业", notes = "查询企业")
    @RequestMapping(value = "/findCompanyByOpenId")
    public Result<?> findCompanyByOpenId(HttpServletRequest req) {
        String openId = getOpenId(req);
        QyclCompany qyclCompany = qyclCompanyService.findPayCompanyByOpenId(openId);
        return Result.ok(qyclCompany);
    }

    @ApiOperation(value = "查询部门id", notes = "查询部门id")
    @RequestMapping(value = "/findDepartmentIdByOpenId")
    public Result<?> findDepartmentIdByOpenId(HttpServletRequest req, @RequestParam String openId) {
        String departmentId = qyclCompanyService.getDepartmentId(openId);
        return Result.okAndSetData(departmentId);
    }

    @ApiOperation(value = "设置铃声", notes = "设置铃声")
    @RequestMapping(value = "/settingRing/{copyrightId}")
    public Result<?> settingRing(HttpServletRequest req, @RequestParam String mobile, @PathVariable String copyrightId) {
        String openId = getOpenId(req);
        String companyOwner = getCompanyOwner(req);
        qyclRingService.settingRing(mobile, copyrightId, openId, companyOwner);
        return Result.ok("设置铃声完成");
    }

    /**
     * 企业彩铃发送短信通知
     *
     * @param openId
     * @return
     */
    @ApiOperation(value = "企业彩铃发送短信通知", notes = "企业彩铃发送短信通知")
    @PostMapping(value = "/send/sms")
    @ResponseBody
    public FebsResponse sendSms(@RequestParam(value = "openId", required = false, defaultValue = "") String openId, @RequestParam(value = "mobile", required = false, defaultValue = "") String mobile) {
        if (org.springframework.util.StringUtils.isEmpty(openId)) {
            return new FebsResponse().fail().message("openId不能为空");
        }
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, openId).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        if (qyclCompany == null) {
            return new FebsResponse().fail().message("企业不存在！");
        }
        if (StringUtils.isEmpty(qyclCompany.getDepartmentId())) {
            return new FebsResponse().fail().message("发送失败,部门不存在！");
        }
        String smsContentUrl = enterpriseVrbtProperties.getSmsContentUrl();
        try {
            smsContentUrl += "&phone=" + mobile + "&departmentId=" + URLEncoder.encode(qyclCompany.getDepartmentId(), StandardCharsets.UTF_8.name()) + "&openid=" + openId;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        boolean result = smsModelService.sendSms(mobile, BizConstant.BIZ_TYPE_QYCL, BizConstant.BIZ_QYCL_SERVICE_ID, BizConstant.BUSINESS_TYPE_MAIN, smsContentUrl);
        if (!result) {
            return new FebsResponse().code(String.valueOf(CommonConstant.SC_JEECG_NO_AUTH)).message("短信发送失败");
        }
        return new FebsResponse().success("短信发送成功");
    }

    /**
     * 企业彩铃发送来电闪信
     *
     * @param mobile
     * @return
     */
    @ApiOperation(value = "企业彩铃发送来电闪信", notes = "企业彩铃发送来电闪信")
    @PostMapping(value = "/send/flashSms")
    @ResponseBody
    public FebsResponse sendFlashSms(@RequestParam(value = "mobile", required = false, defaultValue = "") String mobile) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return new FebsResponse().fail().message("请输入正确格式手机号");
        }
        boolean result = datangSmsService.sendFlashSms(mobile);
        if (!result) {
            return new FebsResponse().code(String.valueOf(CommonConstant.SC_JEECG_NO_AUTH)).message("来电闪信发送失败");
        }
        return new FebsResponse().success("来电闪信发送成功");
    }

    /**
     * 咪咕音乐企业视频彩铃-成员状态回执(第三方高姐)
     * {"accSeq":null,"type":2,"orderId":"62500046123","billNum":"18428144590","state":"000000","desc":"请求成功"}
     *
     * @return
     */
    ////@AutoLog(value = "咪咕企业视频彩铃-成员状态回执")
    @ApiOperation(value = "咪咕企业视频彩铃-成员状态回执(第三方)", notes = "咪咕企业视频彩铃-成员状态回执(第三方)")
    @RequestMapping(value = "/thirdPartyNotify/memberStatus")
    public String miguNotifyMemberStatusThirdParty(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            //type 1开通 2退订
            log.info("咪咕企业视频彩铃-成员状态回执接口(第三方),参数map:{},json数据:{}", requestMap, jsonNode);
            if (jsonNode != null) {
                String mobile = jsonNode.at("/billNum").asText();
                int type = jsonNode.at("/type").asInt();
                String orderId = jsonNode.at("/orderId").asText();
                String state = jsonNode.at("/state").asText();
                String operSystem = jsonNode.at("/operSystem").asText();
                String finishedTime = jsonNode.at("/finishedTime").asText();
                String desc = jsonNode.at("/desc").asText();
                //String companyOwner = QyclConstant.getCompanyOwnerByOrderId(orderId);
                boolean isSuccess = "000000".equals(state);
                if (1 == type) {
                    //开通成功后处理
                    subscribeResultNotifyService.qyclNotifyHandle(mobile, true, isSuccess, desc);
                }
            }

        } catch (Exception e) {
            log.info("咪咕企业视频彩铃-成员状态回执接口(第三方),参数map:{},json数据:{},异常!", requestMap, jsonNode, e);
            return "FAIL";
        }

        return "OK";
    }

    /**
     * 咪咕音乐企业视频彩铃-成员状态回执
     * {"accSeq":null,"type":2,"orderId":"62500046123","billNum":"18428144590","state":"000000","desc":"请求成功"}
     *
     * @return
     */
    ////@AutoLog(value = "咪咕企业视频彩铃-成员状态回执")
    @ApiOperation(value = "咪咕企业视频彩铃-成员状态回执", notes = "咪咕企业视频彩铃-成员状态回执")
    @RequestMapping(value = "/miguNotify/memberStatus")
    public String miguNotifyMemberStatus(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            //type 1开通 2退订
            log.info("咪咕企业视频彩铃-成员状态回执接口,参数map:{},json数据:{}", requestMap, jsonNode);
            if (jsonNode != null) {
                String mobile = jsonNode.at("/billNum").asText();
                int type = jsonNode.at("/type").asInt();
                String orderId = jsonNode.at("/orderId").asText();
                String state = jsonNode.at("/state").asText();
                String operSystem = jsonNode.at("/operSystem").asText();
                String finishedTime = jsonNode.at("/finishedTime").asText();
                String desc = jsonNode.at("/desc").asText();
                String companyOwner = QyclConstant.getCompanyOwnerByOrderId(orderId);

                //监测开通结果
                alarmService.monitorQyclMemberStatus(mobile, desc);

                boolean isSuccess = "000000".equals(state);
                if (1 == type) {
                    if (isSuccess) {
                        //开通成功后处理
                        qyclCompanyMemberService.updateQyclFunStatus(mobile, QyclConstant.QYCL_FUN_STATUS_ORDER);
                    }
                    subscribeResultNotifyService.qyclNotifyHandle(mobile, false, isSuccess, desc);
                    //                    //开通成功发送权益领取短信
                    //                    smsModelService.sendSmsAsync(mobile, "QYCL", "9527004", BizConstant.BUSINESS_TYPE_RIGHTS);
                    dataNotifyLogService.saveDataNotifyLog(mobile, "MEMBER", QyclConstant.QYCL_FUN_STATUS_ORDER, companyOwner, state, desc, operSystem, finishedTime);
                } else if (2 == type) {
                    if (isSuccess) {
                        qyclCompanyMemberService.updateQyclFunStatus(mobile, QyclConstant.QYCL_FUN_STATUS_UNSUB);
                    }
                    dataNotifyLogService.saveDataNotifyLog(mobile, "MEMBER", QyclConstant.QYCL_FUN_STATUS_UNSUB, companyOwner, state, desc, operSystem, finishedTime);
                }
            }

        } catch (Exception e) {
            log.info("咪咕企业视频彩铃-成员状态回执接口,参数map:{},json数据:{},异常!", requestMap, jsonNode, e);
            return "FAIL";
        }

        return "OK";
    }

    /**
     * 咪咕音乐企业视频彩铃-上传铃音结果回执
     *
     * @return {"streamNumber":"14153820","ringId":null,"transactionId":"fa9ae06c6d4fddb4fc6fe66c806232","ringName":"麦禾公司测试.20230115fw","ringFilePath":"http://10.25.246.20/data2/nfs-data/stable/clp-ring-service/ring/preview/clp-qiyeportal-service/20230115/10459b8d406740de9929402bf2e50532_audio.mp4","createTime":"20230115143409","phoneType":null,"ringType":"1","ringAttribution":"0","ringStatus":"01","desc":"审核中（转码成功）"}
     * {"streamNumber":"14153820","ringId":"600926100600296853","transactionId":"fa9ae06c6d4fddb4fc6fe66c806232","ringName":"麦禾公司测试.20230115fw","ringFilePath":"http://10.25.246.20/data2/nfs-data/stable/clp-ring-service/ring/preview/clp-qiyeportal-service/20230115/10459b8d406740de9929402bf2e50532_audio.mp4","createTime":"20230115143629","phoneType":null,"ringType":"1","ringAttribution":"0","ringStatus":"03","desc":"操作成功！"}
     */
    ////@AutoLog(value = "咪咕企业视频彩铃-上传铃音结果回执")
    @ApiOperation(value = "咪咕企业视频彩铃-上传铃音结果回执", notes = "咪咕企业视频彩铃-上传铃音结果回执")
    @RequestMapping(value = "/miguNotify/ringUploadResult")
    public String miguNotifyringUploadResult(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            log.info("咪咕企业视频彩铃-上传铃音结果回执,参数map:{},json数据:{}", requestMap, jsonNode);
            qyclRingService.submitRingNotify(jsonNode);
        } catch (Exception e) {
            log.info("咪咕企业视频彩铃-上传铃音结果回执,参数map:{},json数据:{},异常!", requestMap, jsonNode, e);
            //return "FAIL";
        }

        return "OK";
    }

    /**
     * 咪咕音乐企业视频彩铃-铃音分发回执
     *
     * @return
     */
    ////@AutoLog(value = "咪咕企业视频彩铃-铃音分发回执")
    @ApiOperation(value = "咪咕企业视频彩铃-铃音分发回执", notes = "咪咕企业视频彩铃-铃音分发回执")
    @RequestMapping(value = "/miguNotify/ringDistributeResult")
    public String miguNotifyRingDistributeResult(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            log.info("咪咕企业视频彩铃-铃音分发回执,参数map:{},json数据:{}", requestMap, jsonNode);
        } catch (Exception e) {
            log.info("咪咕企业视频彩铃-铃音分发回执,参数map:{},json数据:{},异常!", requestMap, jsonNode, e);
            return "FAIL";
        }

        return "OK";
    }

    /**
     * 咪咕音乐企业视频彩铃-网页SDK企业视频彩铃功能开通/退订结果同步
     *
     * @return 订购 {"orderType":"00","orderId":"EVRBT20230111eFogVaHng","price":"1000","oprType":"06","msisdn":"18428144590","serviceId":"62500046123","timestamp":"20230111145349","channelCode":"014X0DJ"}
     * 退订 {"orderType":"00","orderId":"EVRBT20230112S4U2j9hvK","price":"1000","oprType":"07","msisdn":"18428144590","serviceId":"62500046123","timestamp":"20230112152730","channelCode":"014X0DJ"}
     */
    ////@AutoLog(value = "咪咕企业视频彩铃-网页SDK企业视频彩铃功能开通结果同步")
    @ApiOperation(value = "咪咕企业视频彩铃-网页SDK企业视频彩铃功能开通结果回执", notes = "咪咕企业视频彩铃-网页SDK企业视频彩铃功能开通结果回执")
    @RequestMapping(value = "/miguNotify/webSdkCompanyVrbtOrderResult")
    public String miguNotifyWebSdkCompanyVrbtOrderResult(@RequestParam Map<String, Object> requestMap, @RequestBody(required = false) JsonNode jsonNode) {
        try {
            log.info("咪咕企业视频彩铃-网页SDK企业视频彩铃功能开通结果同步,json数据:{}", jsonNode);
            //msisdn手机号
            //"oprType":"06" 开通成功
            if (jsonNode != null) {
                String mobile = jsonNode.at("/msisdn").asText();
                String oprType = jsonNode.at("/oprType").asText();
                String defSeq = jsonNode.at("/defSeq").asText();
                String departmentId = jsonNode.at("/departmentId").asText();
                String channelCode = jsonNode.at("/channelCode").asText();
                String price = jsonNode.at("/price").asText();
                String companyOwner = QyclConstant.getCompanyOwnerByMiguChannelCode(channelCode, price);
                if ("06".equals(oprType)) {
                    if (StringUtils.indexOf(defSeq, "@") > 0) {
                        String[] defSeqArray = defSeq.split("@");
                        String openId = defSeqArray[0];
                        String ringId = defSeqArray[1];
                        qyclRingService.webSdkCallback(mobile, openId, ringId, departmentId, companyOwner);
                    }
                    if (StringUtils.indexOf(defSeq, "diy") > 0) {
                        String openId = StringUtils.substring(defSeq, 0, defSeq.indexOf("diy"));
                        qyclRingService.webSdkPersonCallback(mobile, openId, departmentId, companyOwner);
                    }
                    if (StringUtils.indexOf(defSeq, "qycl") > 0) {
                        String openId = StringUtils.substring(defSeq, 0, defSeq.indexOf("qycl"));
                        qyclRingService.webSdkQyclCallback(mobile, openId, departmentId, companyOwner);
                    }
                    qyclCompanyMemberService.updateQyclFunStatus(mobile, QyclConstant.QYCL_FUN_STATUS_ORDER);
//                    dataNotifyLogService.saveDataNotifyLog(mobile,"SDK",QyclConstant.QYCL_FUN_STATUS_ORDER,companyOwner);
                } else if ("07".equals(oprType)) {
                    qyclCompanyMemberService.updateQyclFunStatus(mobile, QyclConstant.QYCL_FUN_STATUS_UNSUB);
//                    dataNotifyLogService.saveDataNotifyLog(mobile,"SDK",QyclConstant.QYCL_FUN_STATUS_UNSUB,companyOwner);
                }
            }
        } catch (Exception e) {
            log.info("咪咕企业视频彩铃-网页SDK企业视频彩铃功能开通结果同步,json数据:{},异常!", jsonNode, e);
            return "FAIL";
        }
        return "OK";
    }


    @ApiOperation(value = "内容版企业成员查询接口", notes = "内容版企业成员查询接口")
    @RequestMapping(value = "/queryMemberStatus")
    public EntVrbtResult queryMemberStatus(@RequestParam(value = "departmentId", required = false, defaultValue = "") String departmentId, @RequestParam(value = "mobile") String mobile) {
//        String companyOwner = qyclCompanyService.getCompanyOwnerByMobile(mobile);

        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile, mobile).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        if (qyclCompany == null) {
            return EntVrbtResult.FAIL_RESULT;
        }
        String companyOwner =StringUtils.isNotBlank(qyclCompany.getCompanyOwner()) ? qyclCompany.getCompanyOwner() : QyclConstant.QYCL_COMPANY_OWNER_YRJY;
        return enterpriseVrbtService.queryContentMembersImmediateByChannel(departmentId, mobile, companyOwner,qyclCompany.getChannel());
    }

    @ApiOperation(value = "内容版企业成员查询接口-根据openId查询", notes = "内容版企业成员查询接口-根据openId查询")
    @RequestMapping(value = "/queryMemberStatusByOpenId")
    public EntVrbtResult queryMemberStatusByOpenId(@RequestParam(value = "openId") String openId) {
        log.info("内容版企业成员查询接口-根据openId查询,openId:{}", openId);
        QyclCompanyMember qyclCompanyMember = qyclCompanyMemberService.getMemberByOpenId(openId);
        if (qyclCompanyMember == null) {
            return EntVrbtResult.FAIL_RESULT;
        }
//        String companyOwner = qyclCompanyService.getCompanyOwnerByMobile(qyclCompanyMember.getMobile());
        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile, qyclCompanyMember.getMobile()).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        if (qyclCompany == null) {
            return EntVrbtResult.FAIL_RESULT;
        }
        String companyOwner =StringUtils.isNotBlank(qyclCompany.getCompanyOwner()) ? qyclCompany.getCompanyOwner() : QyclConstant.QYCL_COMPANY_OWNER_YRJY;
        if (companyOwner != null) {
            return enterpriseVrbtService.queryContentMembersImmediateByChannel("", qyclCompanyMember.getMobile(), companyOwner,qyclCompany.getChannel());
        }
        return EntVrbtResult.FAIL_RESULT;
    }

    @ApiOperation(value = "获取阿里云媒体模板信息", notes = "获取阿里云媒体模板信息")
    @RequestMapping(value = "/fetchTemplateInfo")
    public String fetchTemplateInfo(@RequestParam(value = "templateId") String templateId) {
        return aliMediaService.fetchTemplateInfo(templateId);
    }

    @ApiOperation(value = "查询预制铃音栏目", notes = "查询预制铃音栏目")
    @RequestMapping(value = "/ring/column")
    public Result<?> ringColumn() {
        List<QyclRingColumn> list = qyclRingColumnService.ringColumn();
        return Result.ok(list);
    }

    @ApiOperation(value = "查询企业铃音模板栏目", notes = "查询企业铃音模板栏目")
    @RequestMapping(value = "/ring/template/column")
    public Result<?> ringTemplateColumn() {
        List<QyclRingColumn> list = qyclRingColumnService.ringTemplateColumn();
        return Result.ok(list);
    }

    @ApiOperation(value = "查询个人DIY铃音模板栏目", notes = "查询个人DIY铃音模板栏目")
    @RequestMapping(value = "/ring/diy/template/column")
    public Result<?> ringDiyTemplateColumn() {
        List<QyclRingColumn> list = qyclRingColumnService.ringDiyTemplateColumn();
        return Result.ok(list);
    }

    @ApiOperation(value = "查询铃音列表", notes = "查询铃音列表")
    @RequestMapping(value = "/ring/queryByColumnId")
    public Result<?> ringList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                              String columnId) {
        Page<QyclRingVideo> page = new Page<QyclRingVideo>(pageNo, pageSize);
        IPage<QyclRingVideo> pageList = qyclRingVideoService.ringListByColumnId(page, columnId);
        return Result.ok(pageList);
    }

    @ApiOperation(value = "查询铃音模板", notes = "查询铃音模板")
    @RequestMapping(value = "/ring/template/queryByColumnId")
    public Result<?> ringTemplateList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                      String columnId) {
        Page<QyclRingTemplate> page = new Page<QyclRingTemplate>(pageNo, pageSize);
        IPage<QyclRingTemplate> list = qyclRingTemplateService.ringTemplateListByColumnId(page, columnId);
        return Result.ok(list);
    }

    /**
     * 列表查询
     *
     * @return
     */
    //@AutoLog(value = "qycl_qa-列表查询")
    @ApiOperation(value = "qycl_qa-列表查询", notes = "qycl_qa-列表查询")
    @GetMapping(value = "/qa/listAll")
    public Result<?> queryQaList(@RequestParam(defaultValue = "QYCL") String type) {
        List<QyclQa> pageList = qyclQaService.listByType(type);
        return Result.ok(pageList);
    }

    @ApiOperation(value = "创建客服铃声", notes = "创建客服铃声")
    @RequestMapping(value = "/createCustomerServiceRing")
    public Result<?> createCustomerServiceRing(@RequestParam String mobile,
                                               @RequestParam String ringTxt,
                                               @RequestParam String companyTitle,
                                               @RequestParam(defaultValue = "FEMALE") String voiceGender,
                                               @RequestParam(required = false) String[] imageArray,
                                               @RequestParam(defaultValue = "BGM1") String bgm,
                                               HttpServletRequest req) {
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (!mobileRegionResult.isIspYidong()) {
            return Result.error("当前业务只支持移动用户!");
        }
        String id = qyclCustomerServiceRingService.createRing(ringTxt, companyTitle, mobile, VoiceGender.valueOf(voiceGender), imageArray, BackgroundMusic.valueOf(bgm));
        Map<String, String> map = new HashMap<>();
        map.put("id", id);
        return Result.ok(map);
    }

    @ApiOperation(value = "查询客服铃声列表", notes = "查询客服铃声列表")
    @RequestMapping(value = "/queryCustomerServiceRingList")
    public Result<?> queryCustomerServiceRingList(QyclCustomerServiceRing qyclCustomerServiceRing,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  HttpServletRequest req) {
        QueryWrapper<QyclCustomerServiceRing> queryWrapper = QueryGenerator.initQueryWrapper(qyclCustomerServiceRing, req.getParameterMap());
        Page<QyclCustomerServiceRing> page = new Page<>(pageNo, pageSize);
        queryWrapper.orderByDesc("create_time");
        IPage<QyclCustomerServiceRing> pageList = qyclCustomerServiceRingService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    @ApiOperation(value = "下载客服铃声", notes = "下载客服铃声")
    @RequestMapping(value = "/downloadCustomerServiceRingList")
    public void downloadCustomerServiceRingList(@RequestParam String id,
                                                HttpServletResponse response) throws IOException {
        QyclCustomerServiceRing qyclCustomerServiceRing = qyclCustomerServiceRingService.getById(id);
        if (qyclCustomerServiceRing != null) {
            String fileName = qyclCustomerServiceRing.getCompanyTitle() + ".mp4";
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            response.setContentType("application/octet-stream");
            OutputStream outputStream = response.getOutputStream();
            IOUtils.copy(aliMediaService.getObjectInputStream(qyclCustomerServiceRing.getVideoPath()), outputStream);
        }
    }
}
