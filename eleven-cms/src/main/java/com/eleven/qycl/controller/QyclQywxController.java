package com.eleven.qycl.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.util.QyclConstant;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.qycl.entity.QyclQywx;
import com.eleven.qycl.service.IQyclQywxService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: qycl_qywx
 * @Author: jeecg-boot
 * @Date: 2024-11-05
 * @Version: V1.0
 */
@Api(tags = "qycl_qywx")
@RestController
@RequestMapping("/qycl/qyclQywx")
@Slf4j
public class QyclQywxController extends JeecgController<QyclQywx, IQyclQywxService> {
    @Autowired
    private IQyclQywxService qyclQywxService;
    @Autowired
    private EnterpriseVrbtService enterpriseVrbtService;

    /**
     * 分页列表查询
     *
     * @param qyclQywx
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "qycl_qywx-分页列表查询")
    @ApiOperation(value = "qycl_qywx-分页列表查询", notes = "qycl_qywx-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(QyclQywx qyclQywx,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<QyclQywx> queryWrapper = QueryGenerator.initQueryWrapper(qyclQywx, req.getParameterMap());
        Page<QyclQywx> page = new Page<QyclQywx>(pageNo, pageSize);
        IPage<QyclQywx> pageList = qyclQywxService.page(page, queryWrapper);
        pageList.getRecords().forEach(qywx -> {
            final EntVrbtResult entVrbtResult = enterpriseVrbtService.searchEcRingSettingByChannel(qywx.getDepartmentId(), QyclConstant.QYCL_COMPANY_OWNER_YRJY,qywx.getChannel());
            qywx.setRingStatus(entVrbtResult.getData().at("/settingsList").size() > 0);
        });
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param qyclQywx
     * @return
     */
    @AutoLog(value = "qycl_qywx-添加")
    @ApiOperation(value = "qycl_qywx-添加", notes = "qycl_qywx-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody QyclQywx qyclQywx) {
        return qyclQywxService.createDepartmentAndAddMember(qyclQywx);
    }

    /**
     * 编辑
     *
     * @param qyclQywx
     * @return
     */
    @AutoLog(value = "qycl_qywx-编辑")
    @ApiOperation(value = "qycl_qywx-编辑", notes = "qycl_qywx-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody QyclQywx qyclQywx) {
        qyclQywxService.updateById(qyclQywx);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "qycl_qywx-通过id删除")
    @ApiOperation(value = "qycl_qywx-通过id删除", notes = "qycl_qywx-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        qyclQywxService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "qycl_qywx-批量删除")
    @ApiOperation(value = "qycl_qywx-批量删除", notes = "qycl_qywx-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.qyclQywxService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "qycl_qywx-通过id查询")
    @ApiOperation(value = "qycl_qywx-通过id查询", notes = "qycl_qywx-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        QyclQywx qyclQywx = qyclQywxService.getById(id);
        if (qyclQywx == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(qyclQywx);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param qyclQywx
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QyclQywx qyclQywx) {
        return super.exportXls(request, qyclQywx, QyclQywx.class, "qycl_qywx");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QyclQywx.class);
    }

}
