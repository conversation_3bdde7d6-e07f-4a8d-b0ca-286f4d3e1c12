package com.eleven.qycl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.qycl.entity.QyclOrderPayLog;
import com.eleven.qycl.service.IQyclOrderPayLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: qycl_order_pay_log
 * @Author: jeecg-boot
 * @Date:   2023-12-27
 * @Version: V1.0
 */
@Api(tags="qycl_order_pay_log")
@RestController
@RequestMapping("/qycl/qyclOrderPayLog")
@Slf4j
public class QyclOrderPayLogController extends JeecgController<QyclOrderPayLog, IQyclOrderPayLogService> {
	@Autowired
	private IQyclOrderPayLogService qyclOrderPayLogService;

	/**
	 * 分页列表查询
	 *
	 * @param qyclOrderPayLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay_log-分页列表查询")
	@ApiOperation(value="qycl_order_pay_log-分页列表查询", notes="qycl_order_pay_log-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(QyclOrderPayLog qyclOrderPayLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<QyclOrderPayLog> queryWrapper = QueryGenerator.initQueryWrapper(qyclOrderPayLog, req.getParameterMap());
		Page<QyclOrderPayLog> page = new Page<QyclOrderPayLog>(pageNo, pageSize);
		IPage<QyclOrderPayLog> pageList = qyclOrderPayLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param qyclOrderPayLog
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay_log-添加")
	@ApiOperation(value="qycl_order_pay_log-添加", notes="qycl_order_pay_log-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody QyclOrderPayLog qyclOrderPayLog) {
		qyclOrderPayLogService.save(qyclOrderPayLog);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param qyclOrderPayLog
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay_log-编辑")
	@ApiOperation(value="qycl_order_pay_log-编辑", notes="qycl_order_pay_log-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody QyclOrderPayLog qyclOrderPayLog) {
		qyclOrderPayLogService.updateById(qyclOrderPayLog);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay_log-通过id删除")
	@ApiOperation(value="qycl_order_pay_log-通过id删除", notes="qycl_order_pay_log-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		qyclOrderPayLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay_log-批量删除")
	@ApiOperation(value="qycl_order_pay_log-批量删除", notes="qycl_order_pay_log-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.qyclOrderPayLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_order_pay_log-通过id查询")
	@ApiOperation(value="qycl_order_pay_log-通过id查询", notes="qycl_order_pay_log-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		QyclOrderPayLog qyclOrderPayLog = qyclOrderPayLogService.getById(id);
		if(qyclOrderPayLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(qyclOrderPayLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param qyclOrderPayLog
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QyclOrderPayLog qyclOrderPayLog) {
        return super.exportXls(request, qyclOrderPayLog, QyclOrderPayLog.class, "qycl_order_pay_log");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QyclOrderPayLog.class);
    }


	 /**
	  * 抖音退款
	  * @param outTradeNo
	  * @param refund
	  * @return
	  */
	 @PostMapping(value = "/douyin/refund")
	 @ResponseBody
	 @RequiresPermissions("qyclOrderPayLog:refund")
	 public Result wechatRefund(@RequestParam(value = "outTradeNo", required = false, defaultValue ="")String outTradeNo,@RequestParam(value = "refund", required = false, defaultValue ="")String refund){
		 if(StringUtils.isEmpty(outTradeNo)){
			 return Result.error("订单号不能为空");
		 }
		 if(StringUtils.isEmpty(refund)){
			 return Result.error("退款金额不能为空");
		 }
		 return qyclOrderPayLogService.douYinRefund(outTradeNo,refund);
	 }
}
