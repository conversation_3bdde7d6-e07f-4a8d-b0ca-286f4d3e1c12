package com.eleven.qycl.controller;

import java.io.*;
import java.net.URL;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.util.FileUtil;
import com.eleven.qycl.config.TtsProperties;
import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.util.QyclConstant;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.qycl.entity.QyclRing;
import com.eleven.qycl.service.IQyclRingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: qycl_ring
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Api(tags="qycl_ring")
@RestController
@RequestMapping("/qycl/qyclRing")
@Slf4j
public class QyclRingController extends JeecgController<QyclRing, IQyclRingService> {
	@Autowired
	private IQyclRingService qyclRingService;
    @Autowired
    TtsProperties ttsPropertiest;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
     EnterpriseVrbtService enterpriseVrbtService;

	/**
	 * 分页列表查询
	 *
	 * @param qyclRing
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "qycl_ring-分页列表查询")
	@ApiOperation(value="qycl_ring-分页列表查询", notes="qycl_ring-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(QyclRing qyclRing,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<QyclRing> queryWrapper = QueryGenerator.initQueryWrapper(qyclRing, req.getParameterMap());
		Page<QyclRing> page = new Page<QyclRing>(pageNo, pageSize);
        queryWrapper.orderByDesc("create_time");
		IPage<QyclRing> pageList = qyclRingService.page(page, queryWrapper);
        pageList.getRecords().forEach(obj -> {
            obj.setPlayUrl(QyclConstant.RING_FILE_BASE_URL + obj.getFilePath());
        });
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param qyclRing
	 * @return
	 */
	//@AutoLog(value = "qycl_ring-添加")
	@ApiOperation(value="qycl_ring-添加", notes="qycl_ring-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody QyclRing qyclRing) {
		qyclRingService.save(qyclRing);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param qyclRing
	 * @return
	 */
	//@AutoLog(value = "qycl_ring-编辑")
	@ApiOperation(value="qycl_ring-编辑", notes="qycl_ring-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody QyclRing qyclRing) {
		qyclRingService.updateById(qyclRing);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_ring-通过id删除")
	@ApiOperation(value="qycl_ring-通过id删除", notes="qycl_ring-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		qyclRingService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "qycl_ring-批量删除")
	@ApiOperation(value="qycl_ring-批量删除", notes="qycl_ring-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.qyclRingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_ring-通过id查询")
	@ApiOperation(value="qycl_ring-通过id查询", notes="qycl_ring-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		QyclRing qyclRing = qyclRingService.getById(id);
		if(qyclRing==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(qyclRing);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param qyclRing
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QyclRing qyclRing) {
        return super.exportXls(request, qyclRing, QyclRing.class, "qycl_ring");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QyclRing.class);
    }

     /**
      * 修改铃音
      * @param id
      * @param ringTxt
      * @throws IOException
      */
     @RequestMapping(value = "/modifyRing/{id}")
     public Result<?> modifyRing(@PathVariable String id,@RequestParam String ringTxt){
         QyclRing qyclRing = qyclRingService.getById(id);
         qyclRing.setRingTxt(ringTxt);
         try {
             qyclRingService.busModifyRing(qyclRing);
             return Result.ok("修改铃音成功");
         }catch (Exception e){
             return Result.ok("修改铃音失败");
         }
     }

     /**
      * 下载铃音
      *
      * @param companyTitle
      * @param filePath
      * @return
      */
     @RequestMapping(value = "/downLoadRing")
     public void downLoadRing(@RequestParam String companyTitle, @RequestParam String filePath, HttpServletResponse response) throws IOException {
         File downLoadFile = new File(ttsPropertiest.getAudioFileBaseDir() + filePath);
         if (downLoadFile.exists()) {
             String fileName = companyTitle + ".mp3";
             response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
             response.setContentType("application/octet-stream");
             OutputStream outputStream = response.getOutputStream();
             IOUtils.copy(new FileInputStream(downLoadFile), outputStream);
         }
     }

     /**
      * 下载视频铃音
      *
      * @param companyTitle
      * @param videoPath
      * @return
      */
     @RequestMapping(value = "/downLoadVideoRing")
     public void downLoadVideoRing(@RequestParam String companyTitle, @RequestParam String videoPath, HttpServletResponse response) throws IOException {
         File downLoadFile = new File(ttsPropertiest.getAudioFileBaseDir() + videoPath);
         if (downLoadFile.exists()) {
             String fileName = companyTitle + ".mp4";
             response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
             response.setContentType("application/octet-stream");
             OutputStream outputStream = response.getOutputStream();
             IOUtils.copy(new FileInputStream(downLoadFile), outputStream);
         }
     }

     @RequestMapping(value = "/useRing/{id}", method = RequestMethod.GET)
     public Result<?> useRing(@PathVariable String id) {
         return qyclRingService.useRing(id);
     }


     /**
      * 上传视频铃音到咪咕
      * @param id
      * @param response
      * @throws IOException
      */
     @RequestMapping(value = "/uploadRing/{id}")
     public void uploadRing(@PathVariable String id, HttpServletResponse response) throws IOException {
         QyclRing qyclRing = qyclRingService.getById(id);
         File uploadFile = new File(ttsPropertiest.getAudioFileBaseDir() + qyclRing.getVideoPath());
         EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRingByChannel("", qyclRing.getCompanyTitle(), uploadFile,EnterpriseVrbtService.RING_TYPE_VEDIO,qyclRing.getCompanyOwner(),null);
         if(entVrbtResult.isOK()) {
             String streamNumber = entVrbtResult.getData().at("/streamNumber").asText();
             qyclRing.setStreamNumber(streamNumber);
             qyclRingService.updateById(qyclRing);
         }
     }
}
