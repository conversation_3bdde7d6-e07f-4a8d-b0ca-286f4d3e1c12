package com.eleven.qycl.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.qycl.entity.QyclRingTemplate;
import com.eleven.qycl.service.IQyclRingTemplateService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: qycl_ring_template
 * @Author: jeecg-boot
 * @Date:   2023-05-18
 * @Version: V1.0
 */
@Api(tags="qycl_ring_template")
@RestController
@RequestMapping("/qycl/qyclRingTemplate")
@Slf4j
public class QyclRingTemplateController extends JeecgController<QyclRingTemplate, IQyclRingTemplateService> {
	@Autowired
	private IQyclRingTemplateService qyclRingTemplateService;
	
	/**
	 * 分页列表查询
	 *
	 * @param qyclRingTemplate
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_template-分页列表查询")
	@ApiOperation(value="qycl_ring_template-分页列表查询", notes="qycl_ring_template-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(QyclRingTemplate qyclRingTemplate,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<QyclRingTemplate> queryWrapper = QueryGenerator.initQueryWrapper(qyclRingTemplate, req.getParameterMap());
		Page<QyclRingTemplate> page = new Page<QyclRingTemplate>(pageNo, pageSize);
		IPage<QyclRingTemplate> pageList = qyclRingTemplateService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param qyclRingTemplate
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_template-添加")
	@ApiOperation(value="qycl_ring_template-添加", notes="qycl_ring_template-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody QyclRingTemplate qyclRingTemplate) {
		qyclRingTemplateService.save(qyclRingTemplate);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param qyclRingTemplate
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_template-编辑")
	@ApiOperation(value="qycl_ring_template-编辑", notes="qycl_ring_template-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody QyclRingTemplate qyclRingTemplate) {
		qyclRingTemplateService.updateById(qyclRingTemplate);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_template-通过id删除")
	@ApiOperation(value="qycl_ring_template-通过id删除", notes="qycl_ring_template-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		qyclRingTemplateService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_template-批量删除")
	@ApiOperation(value="qycl_ring_template-批量删除", notes="qycl_ring_template-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.qyclRingTemplateService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_template-通过id查询")
	@ApiOperation(value="qycl_ring_template-通过id查询", notes="qycl_ring_template-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		QyclRingTemplate qyclRingTemplate = qyclRingTemplateService.getById(id);
		if(qyclRingTemplate==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(qyclRingTemplate);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param qyclRingTemplate
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QyclRingTemplate qyclRingTemplate) {
        return super.exportXls(request, qyclRingTemplate, QyclRingTemplate.class, "qycl_ring_template");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QyclRingTemplate.class);
    }

}
