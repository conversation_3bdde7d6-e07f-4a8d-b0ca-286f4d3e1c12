package com.eleven.qycl.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.qycl.entity.QyclRingVideo;
import com.eleven.qycl.service.IQyclRingVideoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: qycl_ring_video
 * @Author: jeecg-boot
 * @Date:   2023-04-21
 * @Version: V1.0
 */
@Api(tags="qycl_ring_video")
@RestController
@RequestMapping("/cms/qyclRingVideo")
@Slf4j
public class QyclRingVideoController extends JeecgController<QyclRingVideo, IQyclRingVideoService> {
	@Autowired
	private IQyclRingVideoService qyclRingVideoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param qyclRingVideo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_video-分页列表查询")
	@ApiOperation(value="qycl_ring_video-分页列表查询", notes="qycl_ring_video-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(QyclRingVideo qyclRingVideo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<QyclRingVideo> queryWrapper = new QueryWrapper<>();
		queryWrapper.setEntity(qyclRingVideo);
		queryWrapper.orderByAsc("order_by");
		Page<QyclRingVideo> page = new Page<QyclRingVideo>(pageNo, pageSize);
		IPage<QyclRingVideo> pageList = qyclRingVideoService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param qyclRingVideo
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_video-添加")
	@ApiOperation(value="qycl_ring_video-添加", notes="qycl_ring_video-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody QyclRingVideo qyclRingVideo) {
		qyclRingVideoService.save(qyclRingVideo);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param qyclRingVideo
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_video-编辑")
	@ApiOperation(value="qycl_ring_video-编辑", notes="qycl_ring_video-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody QyclRingVideo qyclRingVideo) {
		qyclRingVideoService.updateById(qyclRingVideo);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_video-通过id删除")
	@ApiOperation(value="qycl_ring_video-通过id删除", notes="qycl_ring_video-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		qyclRingVideoService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_video-批量删除")
	@ApiOperation(value="qycl_ring_video-批量删除", notes="qycl_ring_video-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.qyclRingVideoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "qycl_ring_video-通过id查询")
	@ApiOperation(value="qycl_ring_video-通过id查询", notes="qycl_ring_video-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		QyclRingVideo qyclRingVideo = qyclRingVideoService.getById(id);
		if(qyclRingVideo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(qyclRingVideo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param qyclRingVideo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QyclRingVideo qyclRingVideo) {
        return super.exportXls(request, qyclRingVideo, QyclRingVideo.class, "qycl_ring_video");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QyclRingVideo.class);
    }

}
