package com.eleven.qycl.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.dto.DataCollectDto;
import com.eleven.cms.util.DateUtil;
import com.eleven.qycl.entity.DataCollect;
import com.eleven.qycl.service.IDataCollectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

 /**
 * @Description: 企业彩铃数据汇总
 * @Author: jeecg-boot
 * @Date:   2024-01-19
 * @Version: V1.0
 */
@Api(tags="企业彩铃数据汇总")
@RestController
@RequestMapping("/qycl/dataCollect")
@Slf4j
public class DataCollectController extends JeecgController<DataCollect, IDataCollectService> {
	@Autowired
	private IDataCollectService dataCollectService;

	/**
	 * 分页列表查询
	 *
	 * @param dataCollect
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "企业彩铃数据汇总-分页列表查询")
	@ApiOperation(value="企业彩铃数据汇总-分页列表查询", notes="企业彩铃数据汇总-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DataCollect dataCollect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DataCollect> queryWrapper = QueryGenerator.initQueryWrapper(dataCollect, req.getParameterMap());
		Page<DataCollect> page = new Page<DataCollect>(pageNo, pageSize);
		IPage<DataCollect> pageList = dataCollectService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param dataCollect
	 * @return
	 */
	//@AutoLog(value = "企业彩铃数据汇总-添加")
	@ApiOperation(value="企业彩铃数据汇总-添加", notes="企业彩铃数据汇总-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody DataCollect dataCollect) {
		dataCollectService.save(dataCollect);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param dataCollect
	 * @return
	 */
	//@AutoLog(value = "企业彩铃数据汇总-编辑")
	@ApiOperation(value="企业彩铃数据汇总-编辑", notes="企业彩铃数据汇总-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody DataCollect dataCollect) {
		dataCollectService.updateById(dataCollect);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "企业彩铃数据汇总-通过id删除")
	@ApiOperation(value="企业彩铃数据汇总-通过id删除", notes="企业彩铃数据汇总-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		dataCollectService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "企业彩铃数据汇总-批量删除")
	@ApiOperation(value="企业彩铃数据汇总-批量删除", notes="企业彩铃数据汇总-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.dataCollectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "企业彩铃数据汇总-通过id查询")
	@ApiOperation(value="企业彩铃数据汇总-通过id查询", notes="企业彩铃数据汇总-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		DataCollect dataCollect = dataCollectService.getById(id);
		if(dataCollect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(dataCollect);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param dataCollect
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DataCollect dataCollect) {
        return super.exportXls(request, dataCollect, DataCollect.class, "企业彩铃数据汇总");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DataCollect.class);
    }
	 /**
	  * 企业彩铃数据省份汇总
	  * @return
	  */
	 @GetMapping("provinceList")
	 public Result<List<DataCollectDto>> ProvinceList(DataCollectDto dto) {
		 Result<List<DataCollectDto>> result = new Result<List<DataCollectDto>>();
		 List<DataCollectDto> list = dataCollectService.findProvinceList(dto);
		 result.setResult(list);
		 return result;
	 }
	 /**
	  * 企业彩铃数据日期汇总
	  * @return
	  */
	 @GetMapping("executeDateList")
	 public Result<List<DataCollectDto>> ExecuteDateList(DataCollectDto dto) {
		 Result<List<DataCollectDto>> result = new Result<List<DataCollectDto>>();
		 List<DataCollectDto> list = dataCollectService.findExecuteDateList(dto);
		 result.setResult(list);
		 return result;
	 }


	 /**
	  * 企业彩铃数据汇总
	  * @return
	  */
	 @GetMapping("dataCollect")
	 public Result<JSONObject> dataCollect(DataCollectDto dto) {
		 Result<JSONObject> result = new Result<JSONObject>();
		 JSONObject obj = new JSONObject();
		 //执行日期
		 String executeDate=DateUtil.formatYearMonthDay(LocalDate.now().plusDays(-2).atTime(LocalTime.MIN));
		 dto.setExecuteDate(executeDate);
		 DataCollectDto dataCollectDto = dataCollectService.findDataCollectCount(dto);
		 obj.put("totalSubCount", dataCollectDto!=null?dataCollectDto.getSub():"0");
		 obj.put("totalUnSubCount", dataCollectDto!=null?dataCollectDto.getUnSub():"0");
		 obj.put("totalUnSubRateCount", dataCollectDto!=null?dataCollectDto.getUnSubRate():"0");
		 obj.put("totalVerifyStatusCount", dataCollectDto!=null?dataCollectDto.getVerifyStatus():"0");
		 obj.put("totalVerifyStatusRateCount", dataCollectDto!=null?dataCollectDto.getVerifyStatusRate():"0");
		 result.setResult(obj);
		 return result;
	 }



	 /**
	  * 业务充值统计
	  * @param dto
	  * @return
	  */
	 //@AutoLog(value = "业务充值统计-分页列表查询")
	 @ApiOperation(value="业务充值统计-分页列表查询", notes="业务充值统计-分页列表查询")
	 @GetMapping(value = "/dataCollectList")
	 public Result<List<DataCollectDto>> queryDataCollectList(DataCollectDto dto) {
		 Result<List<DataCollectDto>> result = new Result<>();
		 List<DataCollectDto> pageList = dataCollectService.pageDataCollectList(dto);
		 result.setSuccess(true);
		 result.setResult(pageList);
		 return result;
	 }


	 /**
	  * 导出excel
	  * @param dto
	  * @return
	  */
	 @RequestMapping(value = "/downXlsxDataCollect")
	 public ModelAndView downXlsxDataCollect(DataCollectDto dto) {
		 List<DataCollect> pageList = dataCollectService.pageDownXlsxDataCollectList(dto);
		 return super.downXlsx(DataCollect.class, "企业彩铃数据汇总",pageList);
	 }
 }
