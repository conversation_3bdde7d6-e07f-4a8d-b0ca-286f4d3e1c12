package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_hand_ring
 * @Author: jeecg-boot
 * @Date:   2023-12-13
 * @Version: V1.0
 */
@Data
@TableName("qycl_hand_ring")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_hand_ring对象", description="qycl_hand_ring")
public class QyclHandRing implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**铃声路径*/
	@Excel(name = "铃声路径", width = 15)
    @ApiModelProperty(value = "铃声路径")
    private String ringPath;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**铃音标识*/
	@Excel(name = "铃音标识", width = 15)
    @ApiModelProperty(value = "铃音标识")
    private String streamNumber;
	/**铃音状态  00 已上传 01 转码成功审核中 02 审核未通过 03 审核通过 06 分发成功 07 已过期 08 转码失败*/
	@Excel(name = "铃音状态  00 已上传 01 转码成功审核中 02 审核未通过 03 审核通过 06 分发成功 07 已过期 08 转码失败", width = 15)
    @ApiModelProperty(value = "铃音状态  00 已上传 01 转码成功审核中 02 审核未通过 03 审核通过 06 分发成功 07 已过期 08 转码失败")
    private String ringStatus;
	/**铃音id*/
	@Excel(name = "铃音id", width = 15)
    @ApiModelProperty(value = "铃音id")
    private String ringId;
	/**阿里云文字转语音任务id*/
	@Excel(name = "阿里云文字转语音任务id", width = 15)
    @ApiModelProperty(value = "阿里云文字转语音任务id")
    private String aliTtsJobId;
	/**阿里云视频合成任务id*/
	@Excel(name = "阿里云视频合成任务id", width = 15)
    @ApiModelProperty(value = "阿里云视频合成任务id")
    private String aliVideoJobId;
	/**铃音类型 1预制铃声/2上传视频/3剪同款/4合成视频*/
	@Excel(name = "铃音类型 1预制铃声/2上传视频/3剪同款/4合成视频", width = 15)
    @ApiModelProperty(value = "铃音类型 1预制铃声/2上传视频/3剪同款/4合成视频")
    private Integer ringType;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
	/**imageUrls*/
	@Excel(name = "imageUrls", width = 15)
    @ApiModelProperty(value = "imageUrls")
    private String imageUrls;
	/**铃声制作状态 0制作中1制作完成*/
	@Excel(name = "铃声制作状态 0制作中1制作完成", width = 15)
    @ApiModelProperty(value = "铃声制作状态 0制作中1制作完成")
    private Integer ringMakeStatus;
	/**companyOwner*/
	@Excel(name = "companyOwner", width = 15)
    @ApiModelProperty(value = "companyOwner")
    private String companyOwner;
	/**bgmUrl*/
	@Excel(name = "bgmUrl", width = 15)
    @ApiModelProperty(value = "bgmUrl")
    private String bgmUrl;
	/**channel*/
	@Excel(name = "channel", width = 15)
    @ApiModelProperty(value = "channel")
    private String channel;
}
