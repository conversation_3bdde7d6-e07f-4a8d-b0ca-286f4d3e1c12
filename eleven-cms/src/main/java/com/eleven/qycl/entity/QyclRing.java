package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_ring
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Data
@TableName("qycl_ring")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_ring对象", description="qycl_ring")
public class QyclRing implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**微信openId*/
	@Excel(name = "微信openId", width = 15)
    @ApiModelProperty(value = "微信openId")
    private java.lang.String openId;
    /**手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String mobile;
	/**公司名字*/
	@Excel(name = "公司名字", width = 15)
    @ApiModelProperty(value = "公司名字")
    private java.lang.String companyTitle;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private java.lang.String orderId;
	/**铃音文本内容*/
	@Excel(name = "铃音文本内容", width = 15)
    @ApiModelProperty(value = "铃音文本内容")
    private java.lang.String ringTxt;
	/**铃声路径*/
	@Excel(name = "铃声路径", width = 15)
    @ApiModelProperty(value = "铃声路径")
    private java.lang.String filePath;
    /**铃声路径*/
    @Excel(name = "视频路径", width = 15)
    @ApiModelProperty(value = "视频路径")
    private java.lang.String videoPath;
	/**支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中*/
	@Excel(name = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中", width = 15, dicCode = "pay_status")
	@Dict(dicCode = "pay_status")
    @ApiModelProperty(value = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中")
    private java.lang.Integer orderPayStatus;
	/**审核状态:-1=审核中,0=审核失败,1审核成功*/
	@Excel(name = "审核状态:-1=审核中,0=审核失败,1审核成功", width = 15, dicCode = "audit_status")
	@Dict(dicCode = "audit_status")
    @ApiModelProperty(value = "审核状态:-1=审核中,0=审核失败,1审核成功")
    private java.lang.Integer auditStatus;
	/**铃声修改类型:0=原始铃声,1=修改后的铃声*/
	@Excel(name = "铃声修改类型:0=原始铃声,1=修改后的铃声", width = 15)
    @ApiModelProperty(value = "铃声修改类型:0=原始铃声,1=修改后的铃声")
    private java.lang.Integer modifyType;
	/**修改次数,最多允许修改3次*/
	@Excel(name = "修改次数,最多允许修改3次", width = 15)
    @ApiModelProperty(value = "修改次数,最多允许修改3次")
    private java.lang.Integer modifyCount;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;

    /**视频铃音标识*/
    @Excel(name = "视频铃音标识", width = 15)
    @ApiModelProperty(value = "视频铃音标识")
    private java.lang.String streamNumber;
    /**视频铃音标识*/
    @Excel(name = "视频铃音Id", width = 15)
    @ApiModelProperty(value = "视频铃音Id")
    private java.lang.String ringId;



    /**视频视频铃音状态
     * 铃音状态：
     * 00 已上传
     * 01 转码成功审核中
     * 02 审核未通过
     * 03 审核通过
     * 06 分发成功
     * 07 已过期
     * 08 转码失败
     * */
    @Excel(name = "视频铃音状态", width = 15)
    @ApiModelProperty(value = "视频铃音状态")
    @Dict(dicCode = "ring_status")
    private java.lang.String ringStatus;
    /**音频铃音标识*/
    @Excel(name = "音频铃音标识", width = 15)
    @ApiModelProperty(value = "音频铃音标识")
    private java.lang.String streamNumberAudio;
    /**音频铃音标识*/
    @Excel(name = "音频铃音Id", width = 15)
    @ApiModelProperty(value = "音频铃音Id")
    private java.lang.String ringIdAudio;
    /**音频铃音状态
     * 铃音状态：
     * 00 已上传
     * 01 转码成功审核中
     * 02 审核未通过
     * 03 审核通过
     * 06 分发成功
     * 07 已过期
     * 08 转码失败
     * */
    @Excel(name = "音频铃音状态", width = 15)
    @ApiModelProperty(value = "音频铃音状态")
    @Dict(dicCode = "ring_status")
    private java.lang.String ringStatusAudio;
    @ApiModelProperty(value = "阿里云文字转语音任务id")
    private String aliTtsJobId;
    @ApiModelProperty(value = "阿里云视频合成任务id")
    private String aliVideoJobId;
    /**1=最早的文字合成,2=diy类型(图片合成),3=个人彩铃(预制铃声),4=模板类型,5=视频上传铃声*/
    @ApiModelProperty(value = "铃音类型")
    @Dict(dicCode = "ring_type")
    private Integer ringType;
    @ApiModelProperty(value = "铃音图片地址")
    private String imageUrls;
    /** 0制作中1制作完成*/
    @ApiModelProperty(value = "铃声制作状态")
    private Integer ringMakeStatus;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
    /**播放地址*/
    @TableField(exist = false)
    private transient String playUrl;
    /**播放地址*/
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;
    /**归属公司*/
    @ApiModelProperty(value = "company_owner")
    private String companyOwner;
    /**归属公司*/
    @ApiModelProperty(value = "bgm_url")
    private String bgmUrl;


}
