package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_action_log
 * @Author: jeecg-boot
 * @Date:   2023-03-29
 * @Version: V1.0
 */
@Data
@TableName("qycl_action_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_action_log对象", description="qycl_action_log")
public class QyclActionLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**操作名*/
    @ApiModelProperty(value = "操作名")
    private String actionName;
    /**操作对象*/
    @Excel(name = "操作对象", width = 15)
    @ApiModelProperty(value = "操作对象")
    private String actionTarget;
	/**公司名称*/
	@Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private String companyName;
	/**部门ID*/
    @ApiModelProperty(value = "部门ID")
    private String departmentId;
	/**操作结果(添加人员返回信息)*/
	@Excel(name = "添加人员结果", width = 15)
    @ApiModelProperty(value = "操作结果")
    private String actionResult;
	/**额外信息(删除人员返回信息)*/
	@Excel(name = "删除人员结果", width = 15)
    @ApiModelProperty(value = "额外信息")
    private String extra;
	/**备注*/
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    @Excel(name = "操作日期",format = "yyyy-MM-dd HH:mm:ss", width = 15)
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
