package com.eleven.qycl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: qycl_order_pay_log
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Data
@TableName("qycl_order_pay_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_order_pay_log对象", description="qycl_order_pay_log")
public class QyclOrderPayLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "订单号")
    private String id;
	/**商户订单号*/
	@Excel(name = "抖音订单号", width = 15)
    @ApiModelProperty(value = "抖音订单号")
    private String outTradeNo;
	/**交易类型:ALIPAY=支付宝H5支付,JSAPI=微信公众号支付,MWEB=微信H5支付*/
	@Excel(name = "交易类型:ALIPAY=支付宝H5支付,JSAPI=微信公众号支付,MWEB=微信H5支付,TIKTOK=抖音小程序支付", width = 15)
    @ApiModelProperty(value = "交易类型:ALIPAY=支付宝H5支付,JSAPI=微信公众号支付,MWEB=微信H5支付,TIKTOK=抖音小程序支付")
    private String tradeType;
	/**微信openId*/
	@Excel(name = "微信openId", width = 15)
    @ApiModelProperty(value = "微信openId")
    private String openId;
	/**公司名称*/
	@Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private String companyTitle;
    /**手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**订单金额*/
	@Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private Double totalFee;
	/**支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中*/
	@Excel(name = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中", width = 15, dicCode = "pay_status")
	@Dict(dicCode = "pay_status")
    @ApiModelProperty(value = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中")
    private Integer payStatus;
	/**支付时间*/
	@Excel(name = "支付时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
	/**通知时间*/
	@Excel(name = "通知时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "通知时间")
    private Date notifyTime;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**退款订单号*/
    @Excel(name = "退款订单号", width = 15)
    @ApiModelProperty(value = "退款订单号")
    private String refundOrderNo;
    /**退款备注*/
    @Excel(name = "退款备注", width = 15)
    @ApiModelProperty(value = "退款备注")
    private String refundRemark;
	/**退款金额*/
	@Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private String refundAmount;
	/**退款时间*/
	@Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "退款时间")
    private Date refundTime;
    /**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/
    @Excel(name = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败", width = 15)
    @ApiModelProperty(value = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败")
    private Integer refundStatus;



	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**公众号appId*/
    @Excel(name = "公众号appId", width = 15)
    @ApiModelProperty(value = "公众号appId")
    private String appId;

    /**商户号mchId*/
    @Excel(name = "商户号mchId", width = 15)
    @ApiModelProperty(value = "商户号mchId")
    private String mchId;

    /**归属公司*/
    @ApiModelProperty(value = "company_owner")
    private String companyOwner;


    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;


    /**子渠道号*/
    @Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;

    /**渠道订单主键id*/
    @Excel(name = "渠道订单主键id", width = 15)
    @ApiModelProperty(value = "渠道订单主键id")
    private String transactionId;

}
