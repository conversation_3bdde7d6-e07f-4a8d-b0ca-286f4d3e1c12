package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.hibernate.annotations.Source;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_company
 * @Author: jeecg-boot
 * @Date: 2022-11-09
 * @Version: V1.0
 */
@Data
@TableName("qycl_company")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "qycl_company对象", description = "qycl_company")
public class QyclCompany implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 微信openId
     */
    @Excel(name = "微信openId", width = 15)
    @ApiModelProperty(value = "微信openId")
    private java.lang.String openId;
    /**
     * 公司名称
     */
    @Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private java.lang.String title;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String mobile;
    /**
     * 公司行业
     */
    @Excel(name = "公司行业", width = 15)
    @ApiModelProperty(value = "公司行业")
    private java.lang.String industry;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    @Excel(name = "支付时间", format = "yyyy-MM-dd HH:mm:ss", width = 15)
    private java.util.Date payTime;
    /**
     * 子渠道号
     */
    @Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;

    /**
     * 子渠道号
     */
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
    /**
     * 部门id
     */
    @Excel(name = "部门id", width = 15)
    @ApiModelProperty(value = "部门id")
    private String departmentId;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**退款订单号*/
    @ApiModelProperty(value = "退款订单号")
    private java.lang.String refundOrderNo;
    /**退款备注*/
    @ApiModelProperty(value = "退款备注")
    private java.lang.String refundRemark;
    /**退款金额*/
    @ApiModelProperty(value = "退款金额")
    private java.lang.String refundAmount;
    /**退款时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "退款时间")
    private java.util.Date refundTime;
    /**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/
    @Excel(name = "退款状态", dicCode = "refund_status",width = 15)
    @ApiModelProperty(value = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败")
    private java.lang.Integer refundStatus;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作时间")
    private java.util.Date operationTime;

    /**
     * 处理人
     */
    @Excel(name = "处理人", width = 15)
    private String handleBy;

    @TableField(exist = false)
    private List<QyclCompanyMember> members;

    @TableField(exist = false)
    private Boolean updateStatus;

    @TableField(exist = false)
    @Excel(name = "成员数")
    private Integer memberCount;

    @TableField(exist = false)
    @Excel(name = "成员管理", width = 30)
    private String memberTxt;

    //用于后台显示红点
    @TableField(exist = false)
    private Boolean memberStatus;

    //用于后台显示红点
    @TableField(exist = false)
    private Boolean ringStatus;

    //处理状态
    @TableField(exist = false)
    private Integer handleStatus;

    //处理状态中文
    @TableField(exist = false)
    @Excel(name = "用户状态")
    private String handleStatusTxt;

    //支付金额
    @TableField(exist = false)
    @Excel(name = "金额")
    private Double totalFee;

    //支付金额
    @TableField(exist = false)
//    @Excel(name = "订单号")
    private String orderId;

    /**
     * 来源类型 1h5 2微信 3抖音
     */
    @Excel(name = "来源类型", width = 15, dicCode = "source_code")
    @ApiModelProperty(value = "来源类型")
    @Dict(dicCode = "source_code")
    private String sourceType;

    /**
     * 归属公司
     */
    @ApiModelProperty(value = "company_owner")
    private String companyOwner;

    @TableField(exist = false)
    private String payTime_begin;
    @TableField(exist = false)
    private String payTime_end;


    @TableField(exist = false)
    private String refundTime_begin;
    @TableField(exist = false)
    private String refundTime_end;


}
