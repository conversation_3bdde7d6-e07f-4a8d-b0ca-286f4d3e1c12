package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_order_pay
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Data
@TableName("qycl_order_pay")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_order_pay对象", description="qycl_order_pay")
public class QyclOrderPay implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**商户订单号*/
	@Excel(name = "商户订单号", width = 15)
    @ApiModelProperty(value = "商户订单号")
    private java.lang.String outTradeNo;
	/**交易类型:ALIPAY=支付宝H5支付,JSAPI=微信公众号支付,MWEB=微信H5支付*/
	@Excel(name = "交易类型:ALIPAY=支付宝H5支付,JSAPI=微信公众号支付,MWEB=微信H5支付,TIKTOK=抖音小程序支付", width = 15)
    @ApiModelProperty(value = "交易类型:ALIPAY=支付宝H5支付,JSAPI=微信公众号支付,MWEB=微信H5支付,TIKTOK=抖音小程序支付")
    private java.lang.String tradeType;
	/**微信openId*/
	@Excel(name = "微信openId", width = 15)
    @ApiModelProperty(value = "微信openId")
    private java.lang.String openId;
	/**公司名称*/
	@Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private java.lang.String companyTitle;
    /**手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String mobile;
	/**订单金额*/
	@Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private java.lang.Double totalFee;
	/**支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中*/
	@Excel(name = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中", width = 15, dicCode = "pay_status")
	@Dict(dicCode = "pay_status")
    @ApiModelProperty(value = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中")
    private java.lang.Integer payStatus;
	/**支付时间*/
	@Excel(name = "支付时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private java.util.Date payTime;
	/**通知时间*/
	@Excel(name = "通知时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "通知时间")
    private java.util.Date notifyTime;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**退款订单号*/
    @Excel(name = "退款订单号", width = 15)
    @ApiModelProperty(value = "退款订单号")
    private java.lang.String refundOrderNo;
    /**退款备注*/
    @Excel(name = "退款备注", width = 15)
    @ApiModelProperty(value = "退款备注")
    private java.lang.String refundRemark;
	/**退款金额*/
	@Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private java.lang.String refundAmount;
	/**退款时间*/
	@Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "退款时间")
    private java.util.Date refundTime;
    /**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/
    @Excel(name = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败", width = 15)
    @ApiModelProperty(value = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败")
    private java.lang.Integer refundStatus;



	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
    /**公众号appId*/
    @Excel(name = "公众号appId", width = 15)
    @ApiModelProperty(value = "公众号appId")
    private java.lang.String appId;

    /**商户号mchId*/
    @Excel(name = "商户号mchId", width = 15)
    @ApiModelProperty(value = "商户号mchId")
    private java.lang.String mchId;

    /**归属公司*/
    @ApiModelProperty(value = "company_owner")
    private String companyOwner;
}
