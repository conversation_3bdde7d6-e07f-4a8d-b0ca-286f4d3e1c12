package com.eleven.qycl.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 破解计费返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EntVrbtResult {

    public static final EntVrbtResult FAIL_RESULT = new EntVrbtResult("9999", "通讯失败",null);

    /**
     * code : 0
     * message : success
     * transId : 202003261022439000
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("info")
    private String info;
    @JsonProperty("data")
    private JsonNode data;

    public static EntVrbtResult fail() {
        return FAIL_RESULT;
    }

    public boolean isOK(){
        return "000000".equals(code);
    }
}
