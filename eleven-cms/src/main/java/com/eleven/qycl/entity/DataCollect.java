package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 企业彩铃数据汇总
 * @Author: jeecg-boot
 * @Date:   2024-01-19
 * @Version: V1.0
 */
@Data
@TableName("qycl_data_collect")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_data_collect对象", description="企业彩铃数据汇总")
public class DataCollect implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;
	/**订购数量*/
	@Excel(name = "订购数量", width = 15)
    @ApiModelProperty(value = "订购数量")
    private String sub;
	/**退订数量*/
	@Excel(name = "退订数量", width = 15)
    @ApiModelProperty(value = "退订数量")
    private String unSub;
    /**退订率*/
    @Excel(name = "退订率", width = 15)
    @ApiModelProperty(value = "退订率")
    private String unSubRate;
    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
    @Excel(name = "订购日期", width = 15)
    @ApiModelProperty(value = "执行日期")
    private String executeDate;

    /**60分钟退订数量*/
    @Excel(name = "60分钟退订数量", width = 15)
    @ApiModelProperty(value = "60分钟退订数量")
    private String verifyStatus;
    /**60分钟退订率*/
    @Excel(name = "60分钟退订率", width = 15)
    @ApiModelProperty(value = "60分钟退订率")
    private String verifyStatusRate;
	/**省份*/

	/**城市*/
    @ApiModelProperty(value = "城市")
    private String city;

    /**执行日期*/

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;



}
