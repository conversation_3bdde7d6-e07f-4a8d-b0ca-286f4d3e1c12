package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_customer_service_ring
 * @Author: jeecg-boot
 * @Date: 2024-07-01
 * @Version: V1.0
 */
@Data
@TableName("qycl_customer_service_ring")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "qycl_customer_service_ring对象", description = "qycl_customer_service_ring")
public class QyclCustomerServiceRing implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 公司名字
     */
    @Excel(name = "公司名字", width = 15)
    @ApiModelProperty(value = "公司名字")
    private String companyTitle;
    /**
     * 铃音文本内容
     */
    @Excel(name = "铃音文本内容", width = 15)
    @ApiModelProperty(value = "铃音文本内容")
    private String ringTxt;
    /**
     * 视频铃声路径
     */
    @Excel(name = "视频铃声路径", width = 15)
    @ApiModelProperty(value = "视频铃声路径")
    private String videoPath;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 阿里云文字转语音任务id
     */
    @Excel(name = "阿里云文字转语音任务id", width = 15)
    @ApiModelProperty(value = "阿里云文字转语音任务id")
    private String aliTtsJobId;
    /**
     * 阿里云视频合成任务id
     */
    @Excel(name = "阿里云视频合成任务id", width = 15)
    @ApiModelProperty(value = "阿里云视频合成任务id")
    private String aliVideoJobId;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * imageUrls
     */
    @Excel(name = "imageUrls", width = 15)
    @ApiModelProperty(value = "imageUrls")
    private String imageUrls;
    /**
     * 铃声制作状态 0制作中1制作完成
     */
    @Excel(name = "铃声制作状态 0制作中1制作完成", width = 15)
    @ApiModelProperty(value = "铃声制作状态 0制作中1制作完成")
    private Integer ringMakeStatus;
    /**
     * 背景音乐地址
     */
    @Excel(name = "背景音乐地址", width = 15)
    @ApiModelProperty(value = "背景音乐地址")
    private String bgmUrl;
}
