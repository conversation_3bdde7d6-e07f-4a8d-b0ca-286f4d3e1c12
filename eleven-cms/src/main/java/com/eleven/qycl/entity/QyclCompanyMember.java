package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_company_member
 * @Author: jeecg-boot
 * @Date:   2022-11-09
 * @Version: V1.0
 */
@Data
@TableName("qycl_company_member")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_company_member对象", description="qycl_company_member")
public class QyclCompanyMember implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**微信openId*/
	@Excel(name = "微信openId", width = 15)
    @ApiModelProperty(value = "微信openId")
    private java.lang.String openId;
    /**微信openId*/
    @Excel(name = "企业名称", width = 15)
    @ApiModelProperty(value = "企业名称")
    private java.lang.String companyTitle;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String mobile;
	/**铃音设置状态:-1=设置中,0=设置失败,1=设置成功*/
	@Excel(name = "铃音设置状态:-1=设置中,0=设置失败,1=设置成功", width = 15, dicCode = "setting_status")
	@Dict(dicCode = "setting_status")
    @ApiModelProperty(value = "铃音设置状态:-1=设置中,0=设置失败,1=设置成功")
    private java.lang.Integer settingStatus;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;


    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
    /**企业彩铃功能*/
    @Excel(name = "企业彩铃功能", width = 15, dicCode="qycl_fun_status")
    @ApiModelProperty(value = "企业彩铃功能")
    @Dict(dicCode = "qycl_fun_status")
    private String qyclFun;
    @TableField(exist = false)
    private Boolean vrbtFun;
    @TableField(exist = false)
    @Dict(dicCode = "qycl_user_status")
    private String userStatus ;
}
