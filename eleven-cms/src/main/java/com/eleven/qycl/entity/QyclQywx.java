package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_qywx
 * @Author: jeecg-boot
 * @Date: 2024-11-05
 * @Version: V1.0
 */
@Data
@TableName("qycl_qywx")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "qycl_qywx对象", description = "qycl_qywx")
public class QyclQywx implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 公司名称
     */
    @Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private String title;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 客服id
     */
    @Excel(name = "客服id", width = 15)
    @ApiModelProperty(value = "客服id")
    private String kfId;
    /**
     * 开通状态
     */
    @Dict(dicCode = "qxwx_qycl_order_code")
    @Excel(name = "开通状态", width = 15)
    @ApiModelProperty(value = "开通状态")
    private String status;
    /**
     * 部门id
     */
    @Excel(name = "部门id", width = 15)
    @ApiModelProperty(value = "部门id")
    private String departmentId;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * QYCL企业 QYCL_GR个人
     */
    @Excel(name = "QYCL企业 QYCL_GR个人", width = 15)
    @ApiModelProperty(value = "QYCL企业 QYCL_GR个人")
    private String channel;

    @TableField(exist = false)
    private Boolean ringStatus;
}
