package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_ring_video
 * @Author: jeecg-boot
 * @Date:   2023-04-21
 * @Version: V1.0
 */
@Data
@TableName("qycl_ring_video")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_ring_video对象", description="qycl_ring_video")
public class QyclRingVideo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**铃音id*/
	@Excel(name = "铃音id", width = 15)
    @ApiModelProperty(value = "铃音id")
    private String ringId;
	/**铃音名称*/
	@Excel(name = "铃音名称", width = 15)
    @ApiModelProperty(value = "铃音名称")
    private String ringName;
    @Excel(name = "铃音图片地址", width = 15)
    @ApiModelProperty(value = "铃音图片地址")
    private String ringPic;
	/**铃音播放地址*/
	@Excel(name = "铃音播放地址", width = 15)
    @ApiModelProperty(value = "铃音播放地址")
    private String ringUrl;
	/**状态:0=无效,1=有效*/
	@Excel(name = "状态:0=无效,1=有效", width = 15)
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    @Dict(dicCode = "valid_status")
    private Integer status;
	/**栏目id*/
	@Excel(name = "栏目id", width = 15)
    @ApiModelProperty(value = "栏目id")
    private String columnId;
	/**栏目名称*/
	@Excel(name = "栏目名称", width = 15)
    @ApiModelProperty(value = "栏目名称")
    private String columnName;
    /**栏目分类名称*/
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderBy;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
