package com.eleven.qycl.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_ring_column
 * @Author: jeecg-boot
 * @Date:   2023-04-21
 * @Version: V1.0
 */
@Data
@TableName("qycl_ring_column")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_ring_column对象", description="qycl_ring_column")
public class QyclRingColumn implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**栏目分类名称*/
	@Excel(name = "栏目分类名称", width = 15)
    @ApiModelProperty(value = "栏目分类名称")
    private String columnName;
    /**状态:0=无效,1=有效*/
    @Excel(name = "状态:0=无效,1=有效", width = 15)
    @Dict(dicCode = "valid_status")
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    private Integer status;
    /**栏目归属 1=预制铃音,2=企业铃音模板,3=个人DIY模板铃音*/
    @Excel(name = "栏目归属", width = 15)
    @Dict(dicCode = "column_type")
    @ApiModelProperty(value = "栏目归属")
    private Integer type;
    /**栏目分类名称*/
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderBy;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
