package com.eleven.qycl.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum BackgroundMusic {

    BGM1("BGM1", "背景音乐1", "https://ims-media.oss-cn-beijing.aliyuncs.com/bgm/bgm1.mp3"), BGM2("BGM2", "背景音乐2", "https://ims-media.oss-cn-beijing.aliyuncs.com/bgm/bgm2.mp3"),
    BGM3("BGM3", "背景音乐3", "https://ims-media.oss-cn-beijing.aliyuncs.com/bgm/bgm3.mp3"), BGM4("BGM4", "背景音乐4", "https://ims-media.oss-cn-beijing.aliyuncs.com/bgm/bgm4.mp4");

    private String musicName;
    private String title;
    private String musicPath;

    BackgroundMusic(String musicName, String title, String musicPath) {
        this.musicName = musicName;
        this.title = title;
        this.musicPath = musicPath;
    }

    public String getMusicName() {
        return musicName;
    }

    public String getTitle() {
        return title;
    }

    public String getMusicPath() {
        return musicPath;
    }


    public static List<BackgroundMusic> getListBackgroundMusic() {
        List<BackgroundMusic> list = new ArrayList<>();
        for (BackgroundMusic backgroundMusic : BackgroundMusic.values()) {
            list.add(backgroundMusic);
        }
        return list;
    }

}
