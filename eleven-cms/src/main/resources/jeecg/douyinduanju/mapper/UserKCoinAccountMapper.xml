<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.UserKCoinAccountMapper">

    <!-- 乐观锁更新账户余额 -->
    <update id="updateBalanceWithVersion">
        UPDATE user_kcoin_account
        SET available_balance     = available_balance + #{availableBalanceChange},
            frozen_balance        = frozen_balance + #{frozenBalanceChange},
            total_recharge        = total_recharge + #{totalRechargeChange},
            total_consume         = total_consume + #{totalConsumeChange},
            last_transaction_time = NOW(),
            update_time           = NOW(),
            version               = version + 1
        WHERE user_id = #{userId}
          AND version = #{version}
          AND is_deleted = 0
          AND available_balance + #{availableBalanceChange} >= 0
          AND frozen_balance + #{frozenBalanceChange} >= 0
          AND total_recharge + #{totalRechargeChange} >= 0
          AND total_consume + #{totalConsumeChange} >= 0
    </update>

</mapper>
