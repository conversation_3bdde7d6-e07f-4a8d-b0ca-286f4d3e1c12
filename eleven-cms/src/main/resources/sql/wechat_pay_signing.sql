-- 微信支付签约记录表
CREATE TABLE `wechat_pay_signing` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '用户openid',
  `appid` varchar(32) NOT NULL COMMENT '小程序AppID',
  `mchid` varchar(32) NOT NULL COMMENT '商户号',
  `plan_id` varchar(32) NOT NULL COMMENT '模板ID',
  `contract_code` varchar(64) NOT NULL COMMENT '签约协议号',
  `out_contract_code` varchar(64) NOT NULL COMMENT '商户签约号',
  `contract_id` varchar(64) DEFAULT NULL COMMENT '微信签约ID',
  `signing_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '签约状态：0-未签约，1-已签约，2-已解约，3-签约失败',
  `contract_display_account` varchar(128) DEFAULT NULL COMMENT '签约显示账户',
  `contract_display_name` varchar(128) DEFAULT NULL COMMENT '签约显示名称',
  `contract_sign_time` datetime DEFAULT NULL COMMENT '签约时间',
  `contract_expired_time` datetime DEFAULT NULL COMMENT '签约到期时间',
  `contract_bank_type` varchar(32) DEFAULT NULL COMMENT '签约银行类型',
  `notify_url` varchar(512) DEFAULT NULL COMMENT '回调通知地址',
  `return_web` varchar(512) DEFAULT NULL COMMENT '签约跳转页面',
  `signing_url` text COMMENT '签约请求URL',
  `err_code` varchar(32) DEFAULT NULL COMMENT '错误代码',
  `err_code_des` varchar(256) DEFAULT NULL COMMENT '错误描述',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `business_type` varchar(32) DEFAULT NULL COMMENT '业务类型',
  `channel` varchar(32) DEFAULT NULL COMMENT '渠道来源',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_code` (`contract_code`),
  UNIQUE KEY `uk_out_contract_code` (`out_contract_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_openid` (`openid`),
  KEY `idx_signing_status` (`signing_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付签约记录表';

-- 插入示例配置数据（需要根据实际情况修改）
INSERT INTO `douyin_app_config` (`id`, `app_id`, `app_secret`, `mch_id`, `api_key`, `cert_path`, `key_path`, `notify_url`, `return_url`, `business_type`, `status`, `create_time`, `update_time`) 
VALUES ('wechat_pay_config', 'your_wechat_appid', 'your_wechat_app_secret', 'your_mch_id', 'your_api_key', '', '', 'https://your-domain.com/douyinduanju/wechat-pay-signing/callback', 'https://your-domain.com/success', 'wechat_pay', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`app_id` = VALUES(`app_id`),
`app_secret` = VALUES(`app_secret`),
`mch_id` = VALUES(`mch_id`),
`api_key` = VALUES(`api_key`),
`update_time` = NOW();

-- 创建索引优化查询性能
CREATE INDEX `idx_user_signing_status` ON `wechat_pay_signing` (`user_id`, `signing_status`, `is_deleted`);
CREATE INDEX `idx_contract_info` ON `wechat_pay_signing` (`contract_code`, `contract_id`, `is_deleted`);
