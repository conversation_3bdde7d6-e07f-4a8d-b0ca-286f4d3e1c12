# 微信支付V3委托代扣小程序签约功能使用说明

## 功能概述

根据微信支付V3版本API实现的委托代扣小程序签约功能，支持用户在小程序中进行自动续费签约。

**API地址**: `https://apihk.mch.weixin.qq.com/v3/global/papay/contracts/miniprogram-pre-entrust-sign`

## 主要功能

1. **创建小程序预签约** - 调用微信支付V3 API创建预签约
2. **处理签约回调** - 处理微信支付的签约结果通知
3. **查询签约状态** - 查询用户的签约状态
4. **解约功能** - 支持用户主动解约
5. **获取签约信息** - 获取用户的详细签约信息

## API接口说明

### 1. 创建小程序预签约
```http
POST /douyinduanju/wechat-v3-signing/create-pre-entrust
```

**请求参数：**
- `openid` (必填): 用户openid
- `planId` (必填): 委托代扣模板ID
- `notifyUrl` (必填): 签约结果回调通知地址
- `returnWeb` (可选): 签约完成后跳转页面

**响应示例：**
```json
{
  "success": true,
  "message": "创建预签约成功",
  "result": {
    "userId": "user123",
    "openid": "openid123",
    "planId": "plan_id_123",
    "preEntrustwebId": "pre_entrust_123",
    "miniprogramInfo": {
      "appid": "wx**********",
      "path": "pages/pay/entrust",
      "query": "pre_entrustweb_id=pre_entrust_123"
    }
  }
}
```

### 2. 签约回调处理
```http
POST /douyinduanju/wechat-v3-signing/callback
```

**说明：** 此接口由微信支付系统调用，用于通知签约结果。

### 3. 查询签约状态
```http
GET /douyinduanju/wechat-v3-signing/query-status?outContractCode=xxx
```

**请求参数：**
- `outContractCode` (可选): 商户签约号

### 4. 解约
```http
POST /douyinduanju/wechat-v3-signing/terminate
```

**请求参数：**
- `outContractCode` (必填): 商户签约号
- `terminateReason` (可选): 解约原因

### 5. 获取签约信息
```http
GET /douyinduanju/wechat-v3-signing/info
```

## 配置说明

### 1. 微信支付配置

在数据库中配置微信支付相关信息：

```sql
INSERT INTO `douyin_app_config` (`id`, `app_id`, `app_secret`, `mch_id`, `api_key`, `private_key`, `serial_no`, `business_type`, `status`) 
VALUES ('wechat_pay', 'your_wechat_appid', 'your_wechat_app_secret', 'your_mch_id', 'your_api_v3_key', 'your_private_key', 'your_cert_serial_no', 'wechat_pay', 1);
```

### 2. 微信支付商户平台配置

1. 登录微信支付商户平台
2. 进入"产品中心" -> "委托代扣"
3. 创建委托代扣模板，获取模板ID (planId)
4. 配置回调通知地址
5. 上传API证书，获取证书序列号

## 使用流程

### 1. 小程序端发起签约

```javascript
// 小程序端调用
wx.request({
  url: '/douyinduanju/wechat-v3-signing/create-pre-entrust',
  method: 'POST',
  data: {
    openid: 'user_openid',
    planId: 'your_plan_id',
    notifyUrl: 'https://your-domain.com/callback',
    returnWeb: 'https://your-domain.com/success'
  },
  success: function(res) {
    if (res.data.success) {
      const miniprogramInfo = res.data.result.miniprogramInfo;
      // 跳转到微信支付签约页面
      wx.navigateToMiniProgram({
        appId: miniprogramInfo.appid,
        path: miniprogramInfo.path,
        extraData: {
          query: miniprogramInfo.query
        },
        success: function() {
          console.log('跳转签约页面成功');
        }
      });
    }
  }
});
```

### 2. 处理签约结果

签约完成后，微信会调用回调接口通知结果。

### 3. 查询签约状态

```javascript
// 查询签约状态
wx.request({
  url: '/douyinduanju/wechat-v3-signing/query-status',
  method: 'GET',
  success: function(res) {
    if (res.data.success) {
      console.log('签约状态:', res.data.result.contractState);
    }
  }
});
```

## 请求体结构

### 创建预签约请求体
```json
{
  "appid": "wx**********",
  "mchid": "**********",
  "plan_id": "plan_id_123",
  "out_contract_code": "CONTRACT_user123_**********",
  "openid": "openid123",
  "notify_url": "https://your-domain.com/callback",
  "contract_display_account": "短剧会员服务",
  "user_display_name": "用户",
  "return_web": "https://your-domain.com/success"
}
```

### 预签约响应体
```json
{
  "pre_entrustweb_id": "pre_entrust_123",
  "miniprogram_info": {
    "appid": "wx**********",
    "path": "pages/pay/entrust",
    "query": "pre_entrustweb_id=pre_entrust_123"
  }
}
```

## 签名验证

微信支付V3使用SHA256-RSA签名算法：

1. **请求签名**：使用商户私钥对请求进行签名
2. **回调验签**：使用微信支付平台证书验证回调签名

## 注意事项

1. **证书管理**：
   - 需要上传商户API证书到微信支付平台
   - 定期更新证书，避免过期

2. **签名安全**：
   - 妥善保管商户私钥
   - 验证所有回调请求的签名

3. **回调处理**：
   - 回调接口需要返回正确的JSON格式响应
   - 做好幂等性处理，避免重复处理

4. **错误处理**：
   - 记录详细的错误日志
   - 提供友好的错误提示

## 状态说明

- `ENTRUST_SUCCESS` - 签约成功
- `ENTRUST_FAIL` - 签约失败
- `TERMINATE_SUCCESS` - 解约成功
- `TERMINATE_FAIL` - 解约失败

## 常见问题

### Q1: 预签约创建失败
A1: 检查微信支付配置是否正确，特别是appid、mchid、planId等参数。

### Q2: 签名验证失败
A2: 检查私钥格式是否正确，确保使用正确的签名算法。

### Q3: 回调处理异常
A3: 检查回调URL是否可访问，确保返回正确的响应格式。

### Q4: 小程序跳转失败
A4: 确认小程序AppID配置正确，检查跳转路径和参数。

## 开发建议

1. **测试环境**：先在沙箱环境进行测试
2. **日志记录**：记录详细的请求和响应日志
3. **异常处理**：做好各种异常情况的处理
4. **用户体验**：提供清晰的签约流程指引

## 技术支持

如有问题，请查看日志文件或联系技术支持团队。
