# 微信支付小程序签约功能使用说明

## 功能概述

根据微信支付官方文档实现的小程序签约功能，支持用户在小程序中进行自动续费签约，实现会员服务的自动扣费。

参考文档：https://pay.weixin.qq.com/doc/global/v3/zh/4013015440

## 主要功能

1. **创建签约请求** - 为用户生成签约跳转URL
2. **处理签约回调** - 处理微信支付的签约结果通知
3. **查询签约状态** - 查询用户的签约状态和详细信息
4. **解约功能** - 支持用户主动解约
5. **签约状态检查** - 检查用户是否已签约

## API接口说明

### 1. 创建签约请求
```http
POST /douyinduanju/wechat-pay-signing/create
```

**请求参数：**
- `openid` (必填): 用户openid
- `planId` (必填): 微信支付模板ID
- `notifyUrl` (必填): 签约结果回调通知地址
- `returnWeb` (必填): 签约完成后跳转页面

**响应示例：**
```json
{
  "success": true,
  "message": "创建签约请求成功",
  "result": {
    "signingUrl": "https://api.mch.weixin.qq.com/papay/entrustweb?appid=xxx&mch_id=xxx...",
    "userId": "user123",
    "openid": "openid123"
  }
}
```

### 2. 签约回调处理
```http
POST /douyinduanju/wechat-pay-signing/callback
```

**说明：** 此接口由微信支付系统调用，用于通知签约结果。

### 3. 查询签约状态
```http
GET /douyinduanju/wechat-pay-signing/status?contractCode=xxx
```

**请求参数：**
- `contractCode` (可选): 签约协议号，不传则查询用户最新签约信息

### 4. 检查签约状态
```http
GET /douyinduanju/wechat-pay-signing/check
```

**响应示例：**
```json
{
  "success": true,
  "message": "检查签约状态成功",
  "result": {
    "userId": "user123",
    "isSigned": true,
    "status": "已签约"
  }
}
```

### 5. 解约
```http
POST /douyinduanju/wechat-pay-signing/cancel
```

**请求参数：**
- `contractCode` (必填): 签约协议号

### 6. 获取签约信息
```http
GET /douyinduanju/wechat-pay-signing/info
```

## 配置说明

### 1. 数据库配置

执行SQL文件创建相关表：
```sql
-- 执行 eleven-cms/src/main/resources/sql/wechat_pay_signing.sql
```

### 2. 应用配置

在 `application.yml` 中添加微信支付配置：

```yaml
wechat:
  pay:
    app-id: your_wechat_appid
    app-secret: your_wechat_app_secret
    mch-id: your_mch_id
    api-key: your_api_key
    cert-path: /path/to/cert.p12
    key-path: /path/to/key.pem
    notify-url: https://your-domain.com/douyinduanju/wechat-pay-signing/callback
    return-url: https://your-domain.com/success
    default-plan-id: your_plan_id
    contract-display-account: 短剧会员服务
    contract-display-name: 短剧会员自动续费
    enabled: true
    environment: sandbox  # sandbox-沙箱环境，production-生产环境
```

### 3. 微信支付商户平台配置

1. 登录微信支付商户平台
2. 进入"产品中心" -> "委托代扣"
3. 创建代扣模板，获取模板ID (planId)
4. 配置回调通知地址
5. 配置签约跳转页面

## 使用流程

### 1. 前端发起签约

```javascript
// 小程序端调用
wx.request({
  url: '/douyinduanju/wechat-pay-signing/create',
  method: 'POST',
  data: {
    openid: 'user_openid',
    planId: 'your_plan_id',
    notifyUrl: 'https://your-domain.com/callback',
    returnWeb: 'https://your-domain.com/success'
  },
  success: function(res) {
    if (res.data.success) {
      // 跳转到签约页面
      wx.navigateTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(res.data.result.signingUrl)
      });
    }
  }
});
```

### 2. 处理签约结果

签约完成后，微信会调用回调接口通知结果，同时用户会跳转到指定页面。

### 3. 检查签约状态

```javascript
// 检查用户是否已签约
wx.request({
  url: '/douyinduanju/wechat-pay-signing/check',
  method: 'GET',
  success: function(res) {
    if (res.data.success && res.data.result.isSigned) {
      console.log('用户已签约');
    } else {
      console.log('用户未签约');
    }
  }
});
```

## 签约状态说明

- `0` - 未签约：用户尚未完成签约
- `1` - 已签约：用户已成功签约，可以进行自动扣费
- `2` - 已解约：用户已解约，无法进行自动扣费
- `3` - 签约失败：签约过程中出现错误

## 注意事项

1. **安全性**：
   - 所有API接口都需要Token验证
   - 回调接口需要验证微信签名
   - 敏感配置信息不要硬编码

2. **回调处理**：
   - 回调接口需要返回正确的XML格式响应
   - 需要做好幂等性处理，避免重复处理

3. **错误处理**：
   - 签约失败时要记录错误信息
   - 提供友好的错误提示给用户

4. **测试环境**：
   - 开发阶段使用沙箱环境
   - 生产环境需要使用正式的商户号和证书

## 常见问题

### Q1: 签约URL生成失败
A1: 检查微信支付配置是否正确，特别是appId、mchId、apiKey等参数。

### Q2: 回调验签失败
A2: 检查API密钥是否正确，确保回调参数完整。

### Q3: 签约状态查询不到
A3: 确认签约协议号是否正确，检查数据库记录是否存在。

### Q4: 解约失败
A4: 确认用户当前状态为已签约，检查签约协议号是否有效。

## 技术支持

如有问题，请查看日志文件或联系技术支持团队。
