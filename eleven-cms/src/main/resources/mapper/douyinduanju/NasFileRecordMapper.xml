<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.NasFileRecordMapper">

    <!-- 批量插入文件记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO nas_file_record (
        id, name, full_path, relative_path, parent_path, file_type, file_size, extension,
        last_modified, scan_batch, scan_time, level, readable, writable, remark,
        create_time, update_time, create_by, update_by, is_deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
            #{record.id}, #{record.name}, #{record.fullPath}, #{record.relativePath},
            #{record.parentPath}, #{record.fileType}, #{record.fileSize}, #{record.extension},
            #{record.lastModified}, #{record.scanBatch}, #{record.scanTime}, #{record.level},
            #{record.readable}, #{record.writable}, #{record.remark},
            #{record.createTime}, #{record.updateTime}, #{record.createBy},
            #{record.updateBy}, #{record.isDeleted}
            )
        </foreach>
    </insert>

    <!-- 根据扫描批次删除记录 -->
    <delete id="deleteByScanBatch">
        DELETE
        FROM nas_file_record
        WHERE scan_batch = #{scanBatch}
    </delete>

    <!-- 根据路径前缀删除记录 -->
    <delete id="deleteByPathPrefix">
        DELETE
        FROM nas_file_record
        WHERE full_path LIKE CONCAT(#{pathPrefix}, '%')
    </delete>

    <!-- 基础查询结果映射 -->
    <resultMap id="BaseResultMap" type="com.eleven.cms.douyinduanju.entity.NasFileRecord">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="full_path" property="fullPath" jdbcType="VARCHAR"/>
        <result column="relative_path" property="relativePath" jdbcType="VARCHAR"/>
        <result column="parent_path" property="parentPath" jdbcType="VARCHAR"/>
        <result column="file_type" property="fileType" jdbcType="VARCHAR"/>
        <result column="file_size" property="fileSize" jdbcType="BIGINT"/>
        <result column="extension" property="extension" jdbcType="VARCHAR"/>
        <result column="last_modified" property="lastModified" jdbcType="TIMESTAMP"/>
        <result column="scan_batch" property="scanBatch" jdbcType="VARCHAR"/>
        <result column="scan_time" property="scanTime" jdbcType="TIMESTAMP"/>
        <result column="level" property="level" jdbcType="INTEGER"/>
        <result column="readable" property="readable" jdbcType="INTEGER"/>
        <result column="writable" property="writable" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id
        , name, full_path, relative_path, parent_path, file_type, file_size, extension,
        last_modified, scan_batch, scan_time, level, readable, writable, remark,
        create_time, update_time, create_by, update_by, is_deleted
    </sql>

</mapper>
